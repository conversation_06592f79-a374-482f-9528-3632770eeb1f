from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTableWidget, QTableWidgetItem, QDoubleSpinBox, QHeaderView,
    QLineEdit, QMessageBox, QFileDialog, QCheckBox, QSpinBox,
    QGroupBox, QFormLayout, QProgressBar
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont
import json
import os
from typing import Dict, List


class IndividualScaleConfigDialog(QDialog):
    """Dialog for configuring individual scale factors for batch processing."""
    
    scale_factors_updated = Signal(dict)  # Emits {image_path: scale_factor}
    
    def __init__(self, parent=None, image_paths=None, current_scale_factors=None):
        super().__init__(parent)
        self.image_paths = image_paths or []
        self.scale_factors = current_scale_factors.copy() if current_scale_factors else {}
        
        self.setWindowTitle("Configure Individual Scale Factors")
        self.setModal(True)
        self.resize(800, 600)
        
        self.setup_ui()
        self.populate_table()
        
    def setup_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout(self)
        
        # Header
        header_label = QLabel("Configure Scale Factors for Individual Images")
        header_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(header_label)
        
        # Bulk operations group
        bulk_group = QGroupBox("Bulk Operations")
        bulk_layout = QFormLayout(bulk_group)
        
        # Set all to value
        set_all_layout = QHBoxLayout()
        self.set_all_spinbox = QDoubleSpinBox()
        self.set_all_spinbox.setRange(0.1, 10.0)
        self.set_all_spinbox.setSingleStep(0.1)
        self.set_all_spinbox.setValue(1.0)
        self.set_all_button = QPushButton("Set All")
        self.set_all_button.clicked.connect(self._set_all_scale_factors)
        set_all_layout.addWidget(self.set_all_spinbox)
        set_all_layout.addWidget(self.set_all_button)
        bulk_layout.addRow("Set all to:", set_all_layout)
        
        # Apply to selected
        apply_selected_layout = QHBoxLayout()
        self.apply_selected_spinbox = QDoubleSpinBox()
        self.apply_selected_spinbox.setRange(0.1, 10.0)
        self.apply_selected_spinbox.setSingleStep(0.1)
        self.apply_selected_spinbox.setValue(1.0)
        self.apply_selected_button = QPushButton("Apply to Selected")
        self.apply_selected_button.clicked.connect(self._apply_to_selected)
        apply_selected_layout.addWidget(self.apply_selected_spinbox)
        apply_selected_layout.addWidget(self.apply_selected_button)
        bulk_layout.addRow("Apply to selected:", apply_selected_layout)
        
        # Increment/Decrement operations
        increment_layout = QHBoxLayout()
        self.increment_spinbox = QDoubleSpinBox()
        self.increment_spinbox.setRange(-5.0, 5.0)
        self.increment_spinbox.setSingleStep(0.1)
        self.increment_spinbox.setValue(0.1)
        self.increment_button = QPushButton("Add to Selected")
        self.increment_button.clicked.connect(self._increment_selected)
        increment_layout.addWidget(self.increment_spinbox)
        increment_layout.addWidget(self.increment_button)
        bulk_layout.addRow("Increment selected by:", increment_layout)
        
        layout.addWidget(bulk_group)
        
        # Search/Filter
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("Filter:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Type to filter images...")
        self.search_edit.textChanged.connect(self._filter_table)
        search_layout.addWidget(self.search_edit)
        layout.addLayout(search_layout)
        
        # Table
        self.table = QTableWidget()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels(["Image Name", "Scale Factor", "Full Path"])
        
        # Set column widths
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Fixed)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        self.table.setColumnWidth(1, 120)
        
        # Enable selection
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        
        layout.addWidget(self.table)
        
        # Progress bar (hidden by default)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # File operations
        file_layout = QHBoxLayout()
        self.save_config_button = QPushButton("Save Configuration")
        self.save_config_button.clicked.connect(self._save_configuration)
        self.load_config_button = QPushButton("Load Configuration")
        self.load_config_button.clicked.connect(self._load_configuration)
        file_layout.addWidget(self.save_config_button)
        file_layout.addWidget(self.load_config_button)
        layout.addLayout(file_layout)
        
        # Dialog buttons
        button_layout = QHBoxLayout()
        self.ok_button = QPushButton("OK")
        self.ok_button.clicked.connect(self.accept)
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.reject)
        self.reset_button = QPushButton("Reset All")
        self.reset_button.clicked.connect(self._reset_all)
        
        button_layout.addWidget(self.reset_button)
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.ok_button)
        layout.addLayout(button_layout)
        
    def populate_table(self):
        """Populate the table with image paths and scale factors."""
        self.table.setRowCount(len(self.image_paths))
        
        for row, image_path in enumerate(self.image_paths):
            # Image name
            name_item = QTableWidgetItem(os.path.basename(image_path))
            name_item.setFlags(name_item.flags() & ~Qt.ItemIsEditable)
            self.table.setItem(row, 0, name_item)
            
            # Scale factor spinbox
            scale_spinbox = QDoubleSpinBox()
            scale_spinbox.setRange(0.1, 10.0)
            scale_spinbox.setSingleStep(0.1)
            scale_spinbox.setValue(self.scale_factors.get(image_path, 1.0))
            scale_spinbox.valueChanged.connect(lambda value, path=image_path: self._update_scale_factor(path, value))
            self.table.setCellWidget(row, 1, scale_spinbox)
            
            # Full path
            path_item = QTableWidgetItem(image_path)
            path_item.setFlags(path_item.flags() & ~Qt.ItemIsEditable)
            path_item.setToolTip(image_path)
            self.table.setItem(row, 2, path_item)
            
    def _update_scale_factor(self, image_path: str, value: float):
        """Update scale factor for an image."""
        self.scale_factors[image_path] = value
        
    def _set_all_scale_factors(self):
        """Set all scale factors to the specified value."""
        value = self.set_all_spinbox.value()
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setMaximum(len(self.image_paths))
        
        for i, image_path in enumerate(self.image_paths):
            self.scale_factors[image_path] = value
            spinbox = self.table.cellWidget(i, 1)
            if spinbox:
                spinbox.setValue(value)
            self.progress_bar.setValue(i + 1)
            
        self.progress_bar.setVisible(False)
        
    def _apply_to_selected(self):
        """Apply scale factor to selected rows."""
        selected_rows = set()
        for item in self.table.selectedItems():
            selected_rows.add(item.row())
            
        if not selected_rows:
            QMessageBox.information(self, "No Selection", "Please select one or more images.")
            return
            
        value = self.apply_selected_spinbox.value()
        
        for row in selected_rows:
            image_path = self.image_paths[row]
            self.scale_factors[image_path] = value
            spinbox = self.table.cellWidget(row, 1)
            if spinbox:
                spinbox.setValue(value)
                
    def _increment_selected(self):
        """Increment scale factor for selected rows."""
        selected_rows = set()
        for item in self.table.selectedItems():
            selected_rows.add(item.row())
            
        if not selected_rows:
            QMessageBox.information(self, "No Selection", "Please select one or more images.")
            return
            
        increment = self.increment_spinbox.value()
        
        for row in selected_rows:
            image_path = self.image_paths[row]
            spinbox = self.table.cellWidget(row, 1)
            if spinbox:
                new_value = max(0.1, min(10.0, spinbox.value() + increment))
                spinbox.setValue(new_value)
                self.scale_factors[image_path] = new_value
                
    def _filter_table(self):
        """Filter table based on search text."""
        search_text = self.search_edit.text().lower()
        
        for row in range(self.table.rowCount()):
            name_item = self.table.item(row, 0)
            path_item = self.table.item(row, 2)
            
            if name_item and path_item:
                show_row = (search_text in name_item.text().lower() or 
                           search_text in path_item.text().lower())
                self.table.setRowHidden(row, not show_row)
                
    def _reset_all(self):
        """Reset all scale factors to 1.0."""
        reply = QMessageBox.question(
            self, "Reset All", 
            "Are you sure you want to reset all scale factors to 1.0?",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            for i, image_path in enumerate(self.image_paths):
                self.scale_factors[image_path] = 1.0
                spinbox = self.table.cellWidget(i, 1)
                if spinbox:
                    spinbox.setValue(1.0)
                    
    def _save_configuration(self):
        """Save scale factor configuration to file."""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Scale Configuration", 
            "scale_config.json", "JSON Files (*.json)"
        )
        
        if file_path:
            try:
                # Create configuration with relative paths for portability
                config = {
                    "scale_factors": {
                        os.path.basename(path): factor 
                        for path, factor in self.scale_factors.items()
                    },
                    "version": "1.0",
                    "description": "Individual scale factor configuration for batch processing"
                }
                
                with open(file_path, 'w') as f:
                    json.dump(config, f, indent=2)
                    
                QMessageBox.information(
                    self, "Configuration Saved", 
                    f"Scale factor configuration saved to {file_path}"
                )
            except Exception as e:
                QMessageBox.critical(
                    self, "Save Error", 
                    f"Failed to save configuration: {str(e)}"
                )
                
    def _load_configuration(self):
        """Load scale factor configuration from file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Load Scale Configuration", 
            "", "JSON Files (*.json)"
        )
        
        if file_path:
            try:
                with open(file_path, 'r') as f:
                    config = json.load(f)
                    
                if "scale_factors" not in config:
                    QMessageBox.warning(
                        self, "Invalid Configuration", 
                        "The selected file does not contain valid scale factor configuration."
                    )
                    return
                    
                # Apply loaded configuration
                loaded_factors = config["scale_factors"]
                applied_count = 0
                
                for i, image_path in enumerate(self.image_paths):
                    image_name = os.path.basename(image_path)
                    if image_name in loaded_factors:
                        factor = loaded_factors[image_name]
                        self.scale_factors[image_path] = factor
                        spinbox = self.table.cellWidget(i, 1)
                        if spinbox:
                            spinbox.setValue(factor)
                        applied_count += 1
                        
                QMessageBox.information(
                    self, "Configuration Loaded", 
                    f"Scale factors applied to {applied_count} out of {len(self.image_paths)} images."
                )
                
            except Exception as e:
                QMessageBox.critical(
                    self, "Load Error", 
                    f"Failed to load configuration: {str(e)}"
                )
                
    def accept(self):
        """Accept the dialog and emit the updated scale factors."""
        self.scale_factors_updated.emit(self.scale_factors)
        super().accept()
        
    def get_scale_factors(self) -> Dict[str, float]:
        """Get the current scale factors."""
        return self.scale_factors.copy()