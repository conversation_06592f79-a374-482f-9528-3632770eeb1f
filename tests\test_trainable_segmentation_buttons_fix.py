"""
Test script to verify the trainable segmentation button functionality fix.

This test checks that all buttons in the Trainable Segmentation page have proper
signal-slot connections and that their handler methods exist.
"""

import os
import sys
import inspect

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_annotation_button_connections():
    """Test that all annotation buttons have proper signal connections."""
    print("Testing annotation button signal connections...")
    
    # Read the trainable segmentation handlers file
    handlers_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'gui', 'handlers', 'trainable_segmentation_handlers.py')
    
    if not os.path.exists(handlers_file):
        print(f"✗ FAILED: Handlers file not found: {handlers_file}")
        return False
    
    with open(handlers_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for all annotation button connections
    annotation_connections = [
        ('save_segmentation_results_button.clicked.connect(self.save_segmentation_results)', 'Save Results'),
        ('save_all_annotations_button.clicked.connect(self.save_all_annotations)', 'Save All Annotations'),
        ('load_all_annotations_button.clicked.connect(self.load_all_annotations)', 'Load Annotations'),
        ('load_exported_annotations_button.clicked.connect(self.load_exported_annotations)', 'Load Exported'),
        ('quick_save_annotations_button.clicked.connect(self.quick_save_annotations)', 'Quick Save'),
        ('quick_load_annotations_button.clicked.connect(self.quick_load_annotations)', 'Quick Load')
    ]
    
    all_passed = True
    for connection, button_name in annotation_connections:
        if connection in content:
            print(f"✓ {button_name} button connection found")
        else:
            print(f"✗ FAILED: {button_name} button connection missing: {connection}")
            all_passed = False
    
    return all_passed


def test_drawing_tool_button_connections():
    """Test that all drawing tool buttons have proper signal connections."""
    print("\nTesting drawing tool button signal connections...")
    
    handlers_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'gui', 'handlers', 'trainable_segmentation_handlers.py')
    
    with open(handlers_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for drawing tool button connections
    drawing_connections = [
        ('draw_button.clicked.connect(self.toggle_draw_mode)', 'Draw Label'),
        ('erase_button.clicked.connect(self.toggle_erase_mode)', 'Erase Label'),
        ('clear_labels_button.clicked.connect(self.clear_all_labels)', 'Clear All Labels'),
        ('undo_last_button.clicked.connect(self.undo_last_annotation)', 'Undo Last'),
        ('sam_magic_wand_button.clicked.connect(self.toggle_sam_magic_wand)', 'Magic Wand'),
        ('accept_sam_button.clicked.connect(self.accept_sam_prediction)', 'Accept SAM'),
        ('reject_sam_button.clicked.connect(self.reject_sam_prediction)', 'Reject SAM')
    ]
    
    all_passed = True
    for connection, button_name in drawing_connections:
        if connection in content:
            print(f"✓ {button_name} button connection found")
        else:
            print(f"✗ FAILED: {button_name} button connection missing: {connection}")
            all_passed = False
    
    return all_passed


def test_training_button_connections():
    """Test that all training buttons have proper signal connections."""
    print("\nTesting training button signal connections...")
    
    handlers_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'gui', 'handlers', 'trainable_segmentation_handlers.py')
    
    with open(handlers_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for training button connections
    training_connections = [
        ('train_button.clicked.connect(self.train_classifier)', 'Train Classifier'),
        ('apply_to_current_button.clicked.connect(self.apply_to_current_image)', 'Apply to Current Image'),
        ('apply_to_new_button.clicked.connect(self.apply_to_new_image)', 'Apply to New Image'),
        ('save_classifier_button.clicked.connect(self.save_classifier)', 'Save Classifier'),
        ('load_classifier_button.clicked.connect(self.load_classifier)', 'Load Classifier')
    ]
    
    all_passed = True
    for connection, button_name in training_connections:
        if connection in content:
            print(f"✓ {button_name} button connection found")
        else:
            print(f"✗ FAILED: {button_name} button connection missing: {connection}")
            all_passed = False
    
    return all_passed


def test_handler_methods_exist():
    """Test that all handler methods exist in the class."""
    print("\nTesting handler method existence...")
    
    try:
        from src.gui.handlers.trainable_segmentation_handlers import TrainableSegmentationHandlers
        
        # List of all handler methods that should exist
        required_methods = [
            # Annotation methods
            'save_segmentation_results',
            'save_all_annotations', 
            'load_all_annotations',
            'load_exported_annotations',
            'quick_save_annotations',
            'quick_load_annotations',
            # Drawing tool methods
            'toggle_draw_mode',
            'toggle_erase_mode',
            'clear_all_labels',
            'undo_last_annotation',
            'toggle_sam_magic_wand',
            'accept_sam_prediction',
            'reject_sam_prediction',
            # Training methods
            'train_classifier',
            'apply_to_current_image',
            'apply_to_new_image',
            'save_classifier',
            'load_classifier',
            # Other methods
            'show_label_management_dialog',
            'on_trainable_gallery_image_clicked',
            'update_brush_size'
        ]
        
        all_passed = True
        for method_name in required_methods:
            if hasattr(TrainableSegmentationHandlers, method_name):
                print(f"✓ Method {method_name} exists")
            else:
                print(f"✗ FAILED: Method {method_name} missing")
                all_passed = False
        
        return all_passed
        
    except ImportError as e:
        print(f"✗ FAILED: Could not import TrainableSegmentationHandlers: {e}")
        return False


def test_ui_buttons_exist():
    """Test that all buttons are defined in the UI."""
    print("\nTesting UI button definitions...")
    
    try:
        ui_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'gui', 'ui', 'trainable_segmentation_page_ui.py')
        
        if not os.path.exists(ui_file):
            print(f"✗ FAILED: UI file not found: {ui_file}")
            return False
        
        with open(ui_file, 'r', encoding='utf-8') as f:
            ui_content = f.read()
        
        # Check for all button definitions
        required_buttons = [
            # Annotation buttons
            'save_segmentation_results_button',
            'save_all_annotations_button',
            'load_all_annotations_button', 
            'load_exported_annotations_button',
            'quick_save_annotations_button',
            'quick_load_annotations_button',
            # Drawing tool buttons
            'draw_button',
            'erase_button',
            'clear_labels_button',
            'undo_last_button',
            'sam_magic_wand_button',
            'accept_sam_button',
            'reject_sam_button',
            # Training buttons
            'train_button',
            'apply_to_current_button',
            'apply_to_new_button',
            'save_classifier_button',
            'load_classifier_button',
            # Other buttons
            'manage_labels_button',
            'clear_trainable_gallery_button'
        ]
        
        all_passed = True
        for button_name in required_buttons:
            if f'self.{button_name} = QPushButton(' in ui_content:
                print(f"✓ Button {button_name} defined in UI")
            else:
                print(f"✗ FAILED: Button {button_name} not found in UI")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"✗ FAILED: Error checking UI buttons: {e}")
        return False


def main():
    """Run all tests."""
    print("Trainable Segmentation Button Functionality Fix Test")
    print("=" * 60)
    
    # Run tests
    test1_passed = test_annotation_button_connections()
    test2_passed = test_drawing_tool_button_connections()
    test3_passed = test_training_button_connections()
    test4_passed = test_handler_methods_exist()
    test5_passed = test_ui_buttons_exist()
    
    print("\n" + "=" * 60)
    if test1_passed and test2_passed and test3_passed and test4_passed and test5_passed:
        print("✓ ALL TESTS PASSED - All buttons in Trainable Segmentation should work correctly!")
        print("\nFixed button categories:")
        print("1. ✓ Annotation buttons: Save Results, Save All, Load, Quick Save/Load")
        print("2. ✓ Drawing tool buttons: Draw, Erase, Clear, Undo, Magic Wand")
        print("3. ✓ Training buttons: Train, Apply, Save/Load Classifier")
        print("4. ✓ Gallery buttons: Image selection, Clear gallery")
        print("5. ✓ Other buttons: Manage Labels, SAM Accept/Reject")
        print("\nUsers should now be able to interact with all controls as expected!")
        return 0
    else:
        print("✗ SOME TESTS FAILED - Check the implementation")
        return 1


if __name__ == "__main__":
    sys.exit(main())
