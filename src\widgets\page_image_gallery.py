# src/widgets/page_image_gallery.py

from PySide6.QtWidgets import QWidget, QHBoxLayout, QLabel, QScrollArea, QVBoxLayout, QPushButton
from PySide6.QtGui import QPixmap, QImage
from PySide6.QtCore import Qt, Signal
import numpy as np
from PIL import Image as PILImage
import logging

# Configure logger
logger = logging.getLogger(__name__)

class ThumbnailWidget(QWidget):
    """Custom widget for displaying an image thumbnail with a remove button."""

    remove_clicked = Signal(int)  # Signal emitted when the remove button is clicked
    thumbnail_clicked = Signal(int)  # Signal emitted when the thumbnail is clicked

    def __init__(self, image, index, size=80, parent=None):
        super().__init__(parent)
        self.index = index
        self.size = size
        self.image = image

        # Set up the layout
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)

        # Create a container widget to hold both the image and the remove button
        self.container = QWidget()
        self.container.setFixedSize(size, size)
        self.container.setStyleSheet('border: 2px solid #ccc; border-radius: 4px;')

        # Create a layout for the container
        self.container.setLayout(QVBoxLayout())
        self.container.layout().setContentsMargins(0, 0, 0, 0)
        self.container.layout().setSpacing(0)

        # Create the image label to use the full container size
        self.image_label = QLabel(self.container)
        self.image_label.setFixedSize(size, size)  # Use the full size
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.container.layout().addWidget(self.image_label)

        # Create the remove button as an overlay
        self.remove_button = QPushButton("X", self.container)  # Using the multiplication sign as an X
        self.remove_button.setFixedSize(16, 16)
        self.remove_button.setStyleSheet("""
            QPushButton {
                background-color: rgba(255, 0, 0, 180);
                color: white;
                border-radius: 8px;
                font-weight: bold;
                font-size: 10px;
                padding: 0px;
                margin: 0px;
                text-align: center;
                min-width: 16px;
                min-height: 16px;
                max-width: 16px;
                max-height: 16px;
            }
            QPushButton:hover {
                background-color: rgba(255, 0, 0, 255);
            }
        """)
        # Position the button in the top-left corner as an overlay
        self.remove_button.move(2, 2)
        self.remove_button.raise_()  # Ensure button stays on top
        self.remove_button.clicked.connect(self._on_remove_clicked)

        # Add the container to the main layout
        self.layout.addWidget(self.container)

        # Set the pixmap
        self.set_pixmap(image)

        # Make the whole widget clickable
        self.mousePressEvent = self._on_mouse_press

    def set_pixmap(self, pixmap):
        """Sets the pixmap for the thumbnail."""
        scaled_pixmap = pixmap.scaled(
            self.size, self.size,
            Qt.AspectRatioMode.KeepAspectRatio,
            Qt.TransformationMode.SmoothTransformation
        )
        self.image_label.setPixmap(scaled_pixmap)

    def set_selected(self, selected):
        """Sets the selected state of the thumbnail."""
        if selected:
            self.container.setStyleSheet('border: 2px solid #00a0ff; border-radius: 4px;')
        else:
            self.container.setStyleSheet('border: 2px solid #ccc; border-radius: 4px;')

    def _on_remove_clicked(self):
        """Handles the remove button click."""
        self.remove_clicked.emit(self.index)

    def _on_mouse_press(self, event):
        """Handles mouse press events."""
        if event.button() == Qt.MouseButton.LeftButton:
            self.thumbnail_clicked.emit(self.index)


class PageImageGallery(QWidget):
    """A scrollable horizontal gallery for displaying multiple image thumbnails.
    This is a base class that can be extended for specific page needs.
    """

    image_clicked = Signal(int)  # Signal emitted when an image is clicked, passes the image index
    remove_clicked = Signal(int)  # Signal emitted when an image remove button is clicked, passes the image index

    def __init__(self, parent=None, thumbnail_size=80):
        super().__init__(parent)
        self.thumbnail_size = thumbnail_size
        self.images = []  # List to store image data
        self.thumbnails = []  # List to store thumbnail widgets
        self.filenames = []  # List to store image filenames
        self.file_paths = []  # List to store full file paths
        self.selected_index = -1  # Currently selected image index

        self.setup_ui()

    def setup_ui(self):
        """Sets up the UI components."""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(5)

        # Create a scroll area for the thumbnails
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.scroll_area.setMinimumHeight(self.thumbnail_size + 20)  # Height for thumbnails + scrollbar

        # Create a container widget for the thumbnails
        self.container = QWidget()
        self.container_layout = QHBoxLayout(self.container)
        self.container_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.container_layout.setContentsMargins(5, 5, 5, 5)
        self.container_layout.setSpacing(10)

        # Add a spacer at the end to push thumbnails to the left
        self.container_layout.addStretch()

        self.scroll_area.setWidget(self.container)
        self.layout.addWidget(self.scroll_area)

        # Add a label to show the current image count
        self.count_label = QLabel("No images")
        self.count_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout.addWidget(self.count_label)

    def add_image(self, image, filename, file_path=None, index=None):
        """Adds a new image thumbnail to the gallery.

        Args:
            image: The image data (numpy array or QPixmap)
            filename: The filename of the image
            file_path: The full file path of the image
            index: The index to insert at, if None appends to the end
        """
        logger.debug(f"Adding image to gallery: {filename}, path: {file_path}, index: {index}")

        # Convert image to QPixmap based on its type
        if isinstance(image, QPixmap):
            pixmap = image
        elif isinstance(image, QImage):
            pixmap = QPixmap.fromImage(image)
        elif isinstance(image, np.ndarray):
            # Convert numpy array to QImage
            height, width, channels = image.shape
            bytes_per_line = channels * width
            # Convert to RGB format if needed
            if channels == 3:
                q_img = QImage(image.data, width, height, bytes_per_line, QImage.Format_RGB888)
            elif channels == 4:
                q_img = QImage(image.data, width, height, bytes_per_line, QImage.Format_RGBA8888)
            else:
                # Fallback for grayscale or other formats
                q_img = QImage(image.data, width, height, bytes_per_line, QImage.Format_Grayscale8)
            pixmap = QPixmap.fromImage(q_img)
        elif isinstance(image, PILImage.Image):
            # Convert PIL Image to QImage
            if image.mode == "RGB":
                q_img = QImage(image.tobytes(), image.width, image.height, 3 * image.width, QImage.Format_RGB888)
            elif image.mode == "RGBA":
                q_img = QImage(image.tobytes(), image.width, image.height, 4 * image.width, QImage.Format_RGBA8888)
            else:
                # Convert to RGB if not already
                rgb_image = image.convert("RGB")
                q_img = QImage(rgb_image.tobytes(), rgb_image.width, rgb_image.height, 3 * rgb_image.width, QImage.Format_RGB888)
            pixmap = QPixmap.fromImage(q_img)
        else:
            # Try to convert to string representation as fallback
            pixmap = QPixmap()
            pixmap.fill(Qt.GlobalColor.lightGray)  # Gray placeholder

        if index is None:
            # Add to the end
            idx = len(self.images)
            self.images.append(image)
            self.filenames.append(filename)
            self.file_paths.append(file_path)

            # Create the thumbnail widget
            thumbnail = ThumbnailWidget(pixmap, idx, self.thumbnail_size)
            thumbnail.setToolTip(filename)
            thumbnail.thumbnail_clicked.connect(self._on_thumbnail_clicked)
            # Connect to gallery's remove_clicked signal instead of directly to remove_image
            # This allows external handlers to intercept and show confirmation dialogs
            thumbnail.remove_clicked.connect(lambda index: self.remove_clicked.emit(index))

            self.thumbnails.append(thumbnail)

            # Add thumbnail to container before the stretch
            self.container_layout.insertWidget(self.container_layout.count() - 1, thumbnail)
        else:
            # Insert at specific index
            if 0 <= index <= len(self.images):
                self.images.insert(index, image)
                self.filenames.insert(index, filename)
                self.file_paths.insert(index, file_path)

                # Create the thumbnail widget
                thumbnail = ThumbnailWidget(pixmap, index, self.thumbnail_size)
                thumbnail.setToolTip(filename)
                thumbnail.thumbnail_clicked.connect(self._on_thumbnail_clicked)
                # Connect to gallery's remove_clicked signal instead of directly to remove_image
                # This allows external handlers to intercept and show confirmation dialogs
                thumbnail.remove_clicked.connect(lambda index: self.remove_clicked.emit(index))

                self.thumbnails.insert(index, thumbnail)

                # Update all thumbnail indices to reflect new positions
                self._update_thumbnail_indices()

                # Add thumbnail to container at the correct position
                self.container_layout.insertWidget(index, thumbnail)

        # Update the count label
        self.update_count_label()

    def _on_thumbnail_clicked(self, index):
        """Internal handler for thumbnail clicks."""
        self.select_image(index)
        self.image_clicked.emit(index)

    def _update_thumbnail_indices(self):
        """Updates the indices of all thumbnails to match their position in the list."""
        for i, thumbnail in enumerate(self.thumbnails):
            thumbnail.index = i

    def select_image(self, index):
        """Selects an image in the gallery."""
        if 0 <= index < len(self.images):
            # Update the selected index
            self.selected_index = index

            # Update thumbnail styling
            for i, thumbnail in enumerate(self.thumbnails):
                thumbnail.set_selected(i == index)

            # Update the count label
            self.update_count_label()

    def clear(self):
        """Removes all thumbnails from the gallery."""
        logger.debug(f"Clearing gallery with {len(self.images)} images")
        # Remove all thumbnail widgets from the layout and delete them
        for thumbnail in self.thumbnails:
            thumbnail.deleteLater()
        self.thumbnails.clear()
        self.images.clear()
        self.filenames.clear()
        self.file_paths.clear()
        self.selected_index = -1
        self.update_count_label()
        logger.debug("Gallery cleared")

    def clear_images(self):
        """Removes all images and thumbnails from the gallery."""
        # Call the clear method to handle the actual clearing
        self.clear()
        logger.debug("PageImageGallery: All images cleared.")

    def remove_image(self, index):
        """Removes an image from the gallery."""
        if 0 <= index < len(self.images):
            logger.debug(f"Removing image at index {index} from gallery")
            
            # Store the current selection state
            was_selected = (self.selected_index == index)

            # Remove the thumbnail widget
            thumbnail = self.thumbnails.pop(index)
            thumbnail.deleteLater()

            # Remove the image data
            self.images.pop(index)
            self.filenames.pop(index)
            self.file_paths.pop(index)

            # Update the selected index
            if was_selected:
                # The removed image was selected
                if len(self.images) > 0:
                    # Select the next image, or the last one if this was the last
                    new_index = min(index, len(self.images) - 1)
                    logger.debug(f"Removed selected image. Selecting new image at index {new_index}")
                    self.selected_index = new_index
                else:
                    # No images left
                    logger.debug("No images left after removal")
                    self.selected_index = -1
            elif self.selected_index > index:
                # The selected image was after the removed one, adjust index
                self.selected_index -= 1
                logger.debug(f"Adjusted selected index to {self.selected_index} after removal")
            else:
                # Selected image was before the removed one, no change needed
                logger.debug(f"Selected index {self.selected_index} unchanged after removal")

            # Update all thumbnail indices to reflect new positions
            self._update_thumbnail_indices()

            # Update thumbnail styling to reflect the new selection
            for i, thumbnail in enumerate(self.thumbnails):
                thumbnail.set_selected(i == self.selected_index)

            # Update the count label
            self.update_count_label()

            logger.debug(f"After removal: {len(self.images)} images, selected index: {self.selected_index}")

    def update_image(self, image, index):
        """Updates an existing image thumbnail in the gallery."""
        if 0 <= index < len(self.thumbnails):
            # Convert image to QPixmap based on its type
            if isinstance(image, QPixmap):
                pixmap = image
            elif isinstance(image, QImage):
                pixmap = QPixmap.fromImage(image)
            elif isinstance(image, np.ndarray):
                # Convert numpy array to QImage
                height, width, channels = image.shape
                bytes_per_line = channels * width
                # Convert to RGB format if needed
                if channels == 3:
                    q_img = QImage(image.data, width, height, bytes_per_line, QImage.Format_RGB888)
                elif channels == 4:
                    q_img = QImage(image.data, width, height, bytes_per_line, QImage.Format_RGBA8888)
                else:
                    # Fallback for grayscale or other formats
                    q_img = QImage(image.data, width, height, bytes_per_line, QImage.Format_Grayscale8)
                pixmap = QPixmap.fromImage(q_img)
            elif isinstance(image, PILImage.Image):
                # Convert PIL Image to QImage
                if image.mode == "RGB":
                    q_img = QImage(image.tobytes(), image.width, image.height, 3 * image.width, QImage.Format_RGB888)
                elif image.mode == "RGBA":
                    q_img = QImage(image.tobytes(), image.width, image.height, 4 * image.width, QImage.Format_RGBA8888)
                else:
                    # Convert to RGB if not already
                    rgb_image = image.convert("RGB")
                    q_img = QImage(rgb_image.tobytes(), rgb_image.width, rgb_image.height, 3 * rgb_image.width, QImage.Format_RGB888)
                pixmap = QPixmap.fromImage(q_img)
            else:
                # Try to convert to string representation as fallback
                pixmap = QPixmap()
                pixmap.fill(Qt.GlobalColor.lightGray)  # Gray placeholder

            # Update the thumbnail
            self.thumbnails[index].set_pixmap(pixmap)
            self.images[index] = image

    def update_count_label(self):
        """Updates the count label."""
        if not self.images:
            self.count_label.setText("No images")
        elif self.selected_index >= 0:
            self.count_label.setText(f"Image {self.selected_index + 1} of {len(self.images)}")
        else:
            self.count_label.setText(f"{len(self.images)} images")

    def get_selected_image(self):
        """Returns the currently selected image."""
        if 0 <= self.selected_index < len(self.images):
            return self.images[self.selected_index]
        return None

    def get_selected_filename(self):
        """Returns the filename of the currently selected image."""
        if 0 <= self.selected_index < len(self.filenames):
            return self.filenames[self.selected_index]
        return None

    def get_selected_file_path(self):
        """Returns the file path of the currently selected image."""
        if 0 <= self.selected_index < len(self.file_paths):
            return self.file_paths[self.selected_index]
        return None

    def get_selected_index(self):
        """Returns the index of the currently selected image."""
        return self.selected_index

