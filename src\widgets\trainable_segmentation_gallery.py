# src/widgets/trainable_segmentation_gallery.py

from PySide6.QtWidgets import QPushButton, QHBoxLayout, QWidget
# QIcon is used in the parent class
from PySide6.QtCore import Signal
import logging

from src.widgets.page_image_gallery import PageImageGallery

# Configure logger
logger = logging.getLogger(__name__)

class TrainableSegmentationGallery(PageImageGallery):
    """Image gallery specifically for the Trainable Segmentation Page."""

    # Additional signals specific to trainable segmentation page
    training_requested = Signal(int)  # Signal to request training on an image

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_training_controls()

    def clear_images(self):
        """Removes all images and thumbnails from the gallery."""
        # Call the parent class's clear method to handle the actual clearing
        self.clear()
        logger.debug("TrainableSegmentationGallery: All images cleared.")

    def reinitialize(self):
        """Reinitializes the gallery if it's in an invalid state."""
        logger.debug("Reinitializing TrainableSegmentationGallery")
        # Check if the container layout is valid
        if not self._ensure_valid_layout():
            # If not, recreate the UI
            self.setup_ui()
            # Recreate the training controls
            self.setup_training_controls()
            return True
        return False
    
    def _ensure_valid_layout(self):
        """Ensures the gallery layout is valid and properly initialized."""
        try:
            # Check if essential UI components exist
            if not hasattr(self, 'container') or self.container is None:
                logger.debug("Container widget is missing")
                return False
            if not hasattr(self, 'container_layout') or self.container_layout is None:
                logger.debug("Container layout is missing")
                return False
            if not hasattr(self, 'scroll_area') or self.scroll_area is None:
                logger.debug("Scroll area is missing")
                return False
            return True
        except Exception as e:
            logger.error(f"Error checking layout validity: {e}")
            return False

    def setup_training_controls(self):
        """Adds training-specific controls to the gallery."""
        # Create a container for additional controls
        controls_container = QWidget()
        controls_layout = QHBoxLayout(controls_container)
        controls_layout.setContentsMargins(0, 0, 0, 0)
        controls_layout.setSpacing(5)

        # Train Selected button removed as requested

        # Add the controls container to the layout
        self.layout.addWidget(controls_container)

    def _on_train_clicked(self):
        """Handler for train button clicks."""
        if self.selected_index >= 0:
            self.training_requested.emit(self.selected_index)

    def get_all_images(self):
        """Returns all images in the gallery."""
        return self.images.copy()

    def update_display(self):
        """Updates the gallery display to reflect current state."""
        logger.debug("Updating TrainableSegmentationGallery display")
        
        # Ensure layout is valid before updating
        if not self._ensure_valid_layout():
            logger.warning("Invalid layout detected, reinitializing...")
            self.reinitialize()
        
        # Update the count label to reflect current state
        self.update_count_label()
        
        # Update thumbnail styling to reflect current selection
        for i, thumbnail in enumerate(self.thumbnails):
            thumbnail.set_selected(i == self.selected_index)
            # Ensure thumbnail is visible
            thumbnail.show()
            thumbnail.update()
        
        # Force layout updates
        if hasattr(self, 'container_layout'):
            self.container_layout.update()
        if hasattr(self, 'container'):
            self.container.update()
        if hasattr(self, 'scroll_area'):
            self.scroll_area.update()
        
        # Force a repaint of the widget
        self.update()
        self.repaint()
        
        logger.debug(f"Gallery display updated: {len(self.images)} images, {len(self.thumbnails)} thumbnails, selected index: {self.selected_index}")

    def get_all_file_paths(self):
        """Returns all file paths in the gallery."""
        return self.file_paths.copy()
    
    def force_layout_refresh(self):
        """Forces a complete refresh of the gallery layout and thumbnails."""
        logger.debug("Forcing layout refresh for TrainableSegmentationGallery")
        
        # Ensure all thumbnails are properly added to the layout
        if hasattr(self, 'container_layout') and hasattr(self, 'thumbnails'):
            # Remove all widgets from layout first
            while self.container_layout.count() > 1:  # Keep the stretch item
                item = self.container_layout.takeAt(0)
                if item and item.widget():
                    item.widget().setParent(None)
            
            # Re-add all thumbnails
            for i, thumbnail in enumerate(self.thumbnails):
                self.container_layout.insertWidget(i, thumbnail)
                thumbnail.show()
                thumbnail.update()
            
            # Ensure stretch is at the end
            self.container_layout.addStretch()
        
        # Force updates
        self.update_display()
        
        logger.debug(f"Layout refresh complete: {len(self.thumbnails)} thumbnails in layout")
