<svg width="800" height="600" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .node-circle { fill: #1A202C; stroke: #4A5568; stroke-width: 2; }
            .center-circle { fill: #2D3748; stroke: #4A5568; stroke-width: 2; }
            .node-text { fill: #E2E8F0; font-family: 'Inter', sans-serif; font-size: 14px; text-anchor: middle; }
            .center-text { fill: #FFFFFF; font-family: 'Inter', sans-serif; font-size: 24px; font-weight: bold; text-anchor: middle; }
            .connector-line { stroke: #4A5568; stroke-width: 1.5; }
        </style>
    </defs>

    <!-- Center Node -->
    <circle cx="400" cy="300" r="80" class="center-circle"/>
    <text x="400" y="305" class="center-text">VisionLab AI</text>

    <!-- Interface Nodes and Connectors -->
    <g id="node-unsupervised">
        <line x1="400" y1="300" x2="400" y2="130" class="connector-line"/>
        <circle cx="400" cy="100" r="60" class="node-circle"/>
        <text x="400" y="95" class="node-text">Unsupervised</text>
        <text x="400" y="115" class="node-text">Segmentation</text>
    </g>
    <g id="node-advanced">
        <line x1="400" y1="300" x2="573" y2="173" class="connector-line"/>
        <circle cx="600" cy="150" r="60" class="node-circle"/>
        <text x="600" y="145" class="node-text">Advanced</text>
        <text x="600" y="165" class="node-text">Segmentation</text>
    </g>
    <g id="node-trainable">
        <line x1="400" y1="300" x2="573" y2="427" class="connector-line"/>
        <circle cx="600" cy="450" r="60" class="node-circle"/>
        <text x="600" y="445" class="node-text">Trainable</text>
        <text x="600" y="465" class="node-text">Segmentation</text>
    </g>
    <g id="node-grain">
        <line x1="400" y1="300" x2="400" y2="470" class="connector-line"/>
        <circle cx="400" cy="500" r="60" class="node-circle"/>
        <text x="400" y="495" class="node-text">Grain Analysis</text>
        <text x="400" y="515" class="node-text">Interface</text>
    </g>
    <g id="node-batch">
        <line x1="400" y1="300" x2="227" y2="427" class="connector-line"/>
        <circle cx="200" cy="450" r="60" class="node-circle"/>
        <text x="200" y="445" class="node-text">Batch Processing</text>
        <text x="200" y="465" class="node-text">Interface</text>
    </g>
    <g id="node-imagelab">
        <line x1="400" y1="300" x2="227" y2="173" class="connector-line"/>
        <circle cx="200" cy="150" r="60" class="node-circle"/>
        <text x="200" y="145" class="node-text">Image Lab</text>
        <text x="200" y="165" class="node-text">Interface</text>
    </g>
    <g id="node-ai">
        <line x1="400" y1="300" x2="288" y2="238" class="connector-line"/>
        <circle cx="250" cy="250" r="60" class="node-circle"/>
        <text x="250" y="245" class="node-text">AI Assistant</text>
        <text x="250" y="265" class="node-text">Interface</text>
    </g>
    <g id="node-pointcount">
        <line x1="400" y1="300" x2="512" y2="238" class="connector-line"/>
        <circle cx="550" cy="250" r="60" class="node-circle"/>
        <text x="550" y="245" class="node-text">Point Counting</text>
        <text x="550" y="265" class="node-text">Interface</text>
    </g>
</svg>