"""
Build script for VisionLab Ai with scipy fix
"""
import os
import sys
import subprocess
import logging
import shutil

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def find_xgboost_dll():
    """Find the XGBoost DLL in the virtual environment."""
    try:
        import xgboost
        xgboost_path = os.path.dirname(xgboost.__file__)
        dll_path = os.path.join(xgboost_path, 'xgboost.dll')
        if os.path.exists(dll_path):
            return dll_path
        
        # Try lib directory
        lib_path = os.path.join(xgboost_path, 'lib', 'xgboost.dll')
        if os.path.exists(lib_path):
            return lib_path
        
        return None
    except ImportError:
        return None

def build_executable():
    """Build the executable with PyInstaller."""
    try:
        # First, patch scipy
        logger.info("Patching scipy...")
        subprocess.run([sys.executable, 'patch_scipy.py'], check=True)
        
        # Build with PyInstaller
        logger.info("Building executable with PyInstaller...")
        subprocess.run([sys.executable, '-m', 'PyInstaller', 'visionlab_ai_fixed.spec'], check=True)
        
        # Handle XGBoost DLL manually
        output_path = os.path.join('dist', 'VisionLab Ai')
        xgboost_dll_path = find_xgboost_dll()
        if xgboost_dll_path:
            # Create directories if they don't exist
            for dll_dir in ['lib', 'bin', 'Library/bin']:
                target_dir = os.path.join(output_path, dll_dir)
                os.makedirs(target_dir, exist_ok=True)
                
                # Copy the DLL to each possible location
                target_path = os.path.join(target_dir, 'xgboost.dll')
                logger.info(f"Copying XGBoost DLL to: {target_path}")
                shutil.copy2(xgboost_dll_path, target_path)
        
        logger.info("Build completed successfully!")
        return True
    
    except Exception as e:
        logger.error(f"Error building executable: {e}")
        return False

if __name__ == "__main__":
    success = build_executable()
    sys.exit(0 if success else 1)
