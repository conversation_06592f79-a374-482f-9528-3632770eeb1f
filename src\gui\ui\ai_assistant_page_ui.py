# src/gui/ui/ai_assistant_page_ui.py

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QComboBox, QLabel, QTextEdit,
    QPushButton, QGraphicsView, QGraphicsScene, QGraphicsPixmapItem,
    QTableWidget, QTableWidgetItem, QHeaderView, QSplitter, QGraphicsRectItem,
    QGraphicsSimpleTextItem, QSizePolicy, QMessageBox, QScrollArea, QGroupBox,
    QGridLayout
)
from PySide6.QtGui import QPixmap, QPen, QColor, QBrush, QFont, QIcon, QPainter
from PySide6.QtCore import Qt, Signal, Slot, QRectF

from src.widgets.page_image_gallery import PageImageGallery
from src.widgets.zoomable_graphics_view import ZoomableGraphicsView

class AIAssistantPageUI:
    """UI class for the AI Assistant page."""

    def setup_ai_assistant_page(self):
        """Sets up the AI Assistant page UI components."""
        # Setting up AI Assistant page UI components (debug message removed)

        # Create the main widget and layout
        self.ai_assistant_page = QWidget()
        self.ai_assistant_layout = QVBoxLayout(self.ai_assistant_page)
        self.ai_assistant_layout.setContentsMargins(10, 10, 10, 10)
        self.ai_assistant_layout.setSpacing(10)

        # --- Top Section: Prompt Template ---
        prompt_template_group = QGroupBox("Prompt template")
        prompt_template_layout = QHBoxLayout(prompt_template_group)

        # Prompt template selector
        prompt_template_layout.addWidget(QLabel("Template:"))
        self.ai_assistant_prompt_selector = QComboBox()
        self.ai_assistant_prompt_selector.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        prompt_template_layout.addWidget(self.ai_assistant_prompt_selector)

        # Analyze button
        self.ai_assistant_analyze_button = QPushButton("Analyze")
        self.ai_assistant_analyze_button.setToolTip("Start analysis with the selected image and prompt")
        self.ai_assistant_analyze_button.clicked.connect(self.on_ai_assistant_analyze_clicked)
        prompt_template_layout.addWidget(self.ai_assistant_analyze_button)

        # Cancel button
        self.ai_assistant_cancel_button = QPushButton("Cancel")
        self.ai_assistant_cancel_button.setToolTip("Cancel ongoing analysis")
        self.ai_assistant_cancel_button.setEnabled(False)  # Disabled initially
        self.ai_assistant_cancel_button.clicked.connect(self.on_ai_assistant_cancel_clicked)
        prompt_template_layout.addWidget(self.ai_assistant_cancel_button)

        # Manage Templates button
        self.ai_assistant_manage_templates_button = QPushButton("Manage Templates")
        self.ai_assistant_manage_templates_button.setToolTip("Manage custom prompt templates")
        self.ai_assistant_manage_templates_button.clicked.connect(self.on_ai_assistant_manage_templates_clicked)
        prompt_template_layout.addWidget(self.ai_assistant_manage_templates_button)

        # Settings button
        self.ai_assistant_settings_button = QPushButton("API Settings")
        self.ai_assistant_settings_button.setToolTip("Configure Gemini API settings")
        self.ai_assistant_settings_button.clicked.connect(self.on_ai_assistant_settings_clicked)
        prompt_template_layout.addWidget(self.ai_assistant_settings_button)

        # Add prompt template section to main layout
        self.ai_assistant_layout.addWidget(prompt_template_group)

        # --- Main Content: Horizontal Splitter Layout ---
        self.ai_assistant_main_splitter = QSplitter(Qt.Orientation.Horizontal)
        self.ai_assistant_layout.addWidget(self.ai_assistant_main_splitter)

        # --- Left Column: Image Preview and Gallery ---
        left_column = QWidget()
        left_column_layout = QVBoxLayout(left_column)
        left_column_layout.setContentsMargins(10, 10, 5, 10)
        left_column_layout.setSpacing(10)
        self.ai_assistant_main_splitter.addWidget(left_column)

        # --- Image Preview ---
        image_preview_group = QGroupBox("Image preview")
        image_preview_layout = QVBoxLayout(image_preview_group)

        # Zoom controls toolbar
        zoom_toolbar = QHBoxLayout()
        zoom_toolbar.setContentsMargins(0, 0, 0, 5)

        # Add a label for the zoom controls
        zoom_label = QLabel("Zoom:")
        zoom_toolbar.addWidget(zoom_label)

        # Zoom in button
        self.ai_assistant_zoom_in_button = QPushButton("+")
        self.ai_assistant_zoom_in_button.setToolTip("Zoom In (+ or =)")
        self.ai_assistant_zoom_in_button.setFixedSize(30, 30)
        self.ai_assistant_zoom_in_button.setEnabled(False)  # Initially disabled
        zoom_toolbar.addWidget(self.ai_assistant_zoom_in_button)

        # Zoom out button
        self.ai_assistant_zoom_out_button = QPushButton("-")
        self.ai_assistant_zoom_out_button.setToolTip("Zoom Out (-)")
        self.ai_assistant_zoom_out_button.setFixedSize(30, 30)
        self.ai_assistant_zoom_out_button.setEnabled(False)  # Initially disabled
        zoom_toolbar.addWidget(self.ai_assistant_zoom_out_button)

        # Fit to view button
        self.ai_assistant_fit_view_button = QPushButton("⤢")
        self.ai_assistant_fit_view_button.setToolTip("Fit to View (0)")
        self.ai_assistant_fit_view_button.setFixedSize(30, 30)
        self.ai_assistant_fit_view_button.setEnabled(False)  # Initially disabled
        zoom_toolbar.addWidget(self.ai_assistant_fit_view_button)

        # Add spacer to push buttons to the left
        zoom_toolbar.addStretch()

        # Add toolbar to layout
        image_preview_layout.addLayout(zoom_toolbar)

        # Image viewer with zoomable view
        self.ai_assistant_image_scene = QGraphicsScene()
        self.ai_assistant_image_viewer = ZoomableGraphicsView(self.ai_assistant_image_scene)
        self.ai_assistant_image_item = QGraphicsPixmapItem()
        self.ai_assistant_image_scene.addItem(self.ai_assistant_image_item)
        image_preview_layout.addWidget(self.ai_assistant_image_viewer, 1)  # Stretch factor

        # Connect zoom buttons to the zoomable view
        self.ai_assistant_zoom_in_button.clicked.connect(self.ai_assistant_image_viewer.zoomIn)
        self.ai_assistant_zoom_out_button.clicked.connect(self.ai_assistant_image_viewer.zoomOut)
        self.ai_assistant_fit_view_button.clicked.connect(lambda: self.ai_assistant_image_viewer.fitInView(self.ai_assistant_image_item, Qt.AspectRatioMode.KeepAspectRatio))

        # Enable zoom buttons when an image is loaded
        self.ai_assistant_zoom_in_button.setEnabled(True)
        self.ai_assistant_zoom_out_button.setEnabled(True)
        self.ai_assistant_fit_view_button.setEnabled(True)

        # --- Image Gallery ---
        gallery_group = QGroupBox("Image gallery")
        gallery_layout = QVBoxLayout(gallery_group)

        # Add image gallery
        # Creating AI Assistant gallery (debug message removed)
        self.ai_assistant_gallery = PageImageGallery(self.ai_assistant_page)
        self.ai_assistant_gallery.setMaximumHeight(150)  # Limit gallery height
        gallery_layout.addWidget(self.ai_assistant_gallery)
        # AI Assistant gallery created and added to layout (debug message removed)

        # Clear Image Gallery button
        self.clear_ai_assistant_gallery_button = QPushButton("Clear Image Gallery")
        self.clear_ai_assistant_gallery_button.setStyleSheet("padding: 6px; margin-top: 5px;")
        gallery_layout.addWidget(self.clear_ai_assistant_gallery_button)

        # Add image preview and gallery to left column
        left_column_layout.addWidget(image_preview_group, 8)  # Give more space to image preview
        left_column_layout.addWidget(gallery_group, 2)  # Less space for gallery

        # --- Right Column: Context, Prompts, and Results ---
        right_column = QWidget()
        right_column_layout = QVBoxLayout(right_column)
        right_column_layout.setContentsMargins(5, 10, 10, 10)
        right_column_layout.setSpacing(10)
        self.ai_assistant_main_splitter.addWidget(right_column)

        # --- Context and Prompts Combined ---
        input_group = QGroupBox("Context & Prompts")
        input_layout = QVBoxLayout(input_group)

        # Context input
        context_label = QLabel("Context (optional):")
        context_label.setToolTip("Add optional context information about the image being analyzed")
        input_layout.addWidget(context_label)

        self.ai_assistant_context_input = QTextEdit()
        self.ai_assistant_context_input.setPlaceholderText("Add optional context information here. This will be prepended to your prompt when analyzing images.")
        self.ai_assistant_context_input.setMaximumHeight(100)  # Limit context height
        input_layout.addWidget(self.ai_assistant_context_input)

        # Prompt input
        input_layout.addWidget(QLabel("Prompt:"))
        self.ai_assistant_prompt_input = QTextEdit()
        self.ai_assistant_prompt_input.setPlaceholderText("Select a template or enter your custom prompt here.")
        self.ai_assistant_prompt_input.setMaximumHeight(125)  # Limit prompt height
        input_layout.addWidget(self.ai_assistant_prompt_input)

        # --- Results Section ---
        results_group = QGroupBox("Results")
        results_layout = QVBoxLayout(results_group)

        # Text results with markdown support and Full View button
        text_results_container = QWidget()
        text_results_container_layout = QVBoxLayout(text_results_container)
        text_results_container_layout.setContentsMargins(0, 0, 0, 0)
        text_results_container_layout.setSpacing(5)
        
        self.ai_assistant_text_results = QTextEdit()
        self.ai_assistant_text_results.setReadOnly(True)
        self.ai_assistant_text_results.setPlaceholderText("Analysis results will appear here...")
        self.ai_assistant_text_results.setStyleSheet("font-family: monospace;")
        text_results_container_layout.addWidget(self.ai_assistant_text_results, 1)
        
        # Full View and Export buttons container
        ai_buttons_container = QWidget()
        ai_buttons_layout = QHBoxLayout(ai_buttons_container)
        ai_buttons_layout.setContentsMargins(0, 5, 0, 0)
        ai_buttons_layout.addStretch()
        
        self.ai_assistant_full_view_button = QPushButton("Full View")
        self.ai_assistant_full_view_button.setToolTip("Open results in full view with export options")
        self.ai_assistant_full_view_button.setEnabled(False)
        self.ai_assistant_full_view_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        ai_buttons_layout.addWidget(self.ai_assistant_full_view_button)
        
        text_results_container_layout.addWidget(ai_buttons_container)
        results_layout.addWidget(text_results_container, 7)  # Give more space to text results

        # Structured data table
        self.ai_assistant_structured_data_table = QTableWidget(0, 3)  # 0 rows, 3 columns
        self.ai_assistant_structured_data_table.setHorizontalHeaderLabels(["Label", "Confidence", "Description"])
        self.ai_assistant_structured_data_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        self.ai_assistant_structured_data_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.ai_assistant_structured_data_table.setMinimumHeight(150)  # Set minimum height for table
        results_layout.addWidget(self.ai_assistant_structured_data_table, 3)  # Less space for table

        # Add context/prompts and results to right column
        right_column_layout.addWidget(input_group, 4)  # Less space for inputs
        right_column_layout.addWidget(results_group, 6)  # More space for results

        # Set splitter proportions (50/50 split)
        self.ai_assistant_main_splitter.setSizes([400, 400])
        self.ai_assistant_main_splitter.setStretchFactor(0, 1)
        self.ai_assistant_main_splitter.setStretchFactor(1, 1)

        # Status label
        self.ai_assistant_status_label = QLabel("Ready.")
        self.ai_assistant_layout.addWidget(self.ai_assistant_status_label)

        # Add the page to the stacked widget
        self.stacked_widget.addTab(self.ai_assistant_page, "AI Assistant")
        # AI Assistant page added to stacked widget (debug message removed)

        # Setup connections - handled in main app initialization
        # Connections are set up in app.py to avoid duplicates

    def on_ai_assistant_analyze_clicked(self):
        """Direct handler for the Analyze button click."""
        # on_ai_assistant_analyze_clicked called (debug message removed)
        try:
            if hasattr(self, 'start_ai_assistant_analysis'):
                # Calling start_ai_assistant_analysis (debug message removed)
                self.start_ai_assistant_analysis()
            else:
                # start_ai_assistant_analysis method not found (debug message removed)
                from src.gui.handlers.ai_assistant_handlers import AIAssistantHandlers
                # Calling AIAssistantHandlers.start_ai_assistant_analysis (debug message removed)
                AIAssistantHandlers.start_ai_assistant_analysis(self)
        except Exception as e:
            # Error in on_ai_assistant_analyze_clicked (debug message removed)
            QMessageBox.critical(self, "Error", f"An error occurred: {e}")

    def on_ai_assistant_cancel_clicked(self):
        """Direct handler for the Cancel button click."""
        # on_ai_assistant_cancel_clicked called (debug message removed)
        try:
            if hasattr(self, 'cancel_ai_assistant_analysis'):
                # Calling cancel_ai_assistant_analysis (debug message removed)
                self.cancel_ai_assistant_analysis()
            else:
                # cancel_ai_assistant_analysis method not found (debug message removed)
                from src.gui.handlers.ai_assistant_handlers import AIAssistantHandlers
                # Calling AIAssistantHandlers.cancel_ai_assistant_analysis (debug message removed)
                AIAssistantHandlers.cancel_ai_assistant_analysis(self)
        except Exception as e:
            # Error in on_ai_assistant_cancel_clicked (debug message removed)
            pass

    def on_ai_assistant_manage_templates_clicked(self):
        """Direct handler for the Manage Templates button click."""
        # on_ai_assistant_manage_templates_clicked called (debug message removed)
        try:
            from src.gui.dialogs.custom_prompt_dialog import CustomPromptDialog
            from src.ai_assistant_components.src.gemini.custom_prompts import CustomPromptManager
            # Creating CustomPromptDialog (debug message removed)
            dialog = CustomPromptDialog(self)

            # Connect the prompt_saved signal to update the prompt selector
            dialog.prompt_saved.connect(self.on_custom_prompt_saved)

            result = dialog.exec()
            # Custom prompt dialog result (debug message removed)

            # Reload all custom prompts after dialog is closed
            self.reload_custom_prompts()
        except Exception as e:
            # Error in on_ai_assistant_manage_templates_clicked (debug message removed)
            QMessageBox.critical(self, "Error", f"An error occurred: {e}")

    def reload_custom_prompts(self):
        """Reload all custom prompts into the selector."""
        # Reloading custom prompts (debug message removed)
        try:
            from src.ai_assistant_components.src.gemini.custom_prompts import CustomPromptManager
            from src.ai_assistant_components.src.gemini.prompts import DEFAULT_PROMPTS

            # Remember the current selection
            current_text = self.ai_assistant_prompt_selector.currentText()

            # Clear the selector
            self.ai_assistant_prompt_selector.clear()

            # Add default prompts
            self.ai_assistant_prompt_selector.addItems(DEFAULT_PROMPTS.keys())

            # Add custom prompts
            prompt_manager = CustomPromptManager()
            custom_prompts = prompt_manager.get_all_custom_prompts()
            if custom_prompts:
                self.ai_assistant_prompt_selector.addItems(custom_prompts.keys())
                # Loaded custom prompts (debug message removed)

            # Restore the previous selection if possible
            index = self.ai_assistant_prompt_selector.findText(current_text)
            if index != -1:
                self.ai_assistant_prompt_selector.setCurrentIndex(index)

            # Custom prompts reloaded (debug message removed)
        except Exception as e:
            # Error reloading custom prompts (debug message removed)
            pass

    def on_custom_prompt_saved(self, name, prompt_text):
        """Handler for when a custom prompt is saved."""
        # Custom prompt saved (debug message removed)
        try:
            # Reload all prompts to ensure the list is up to date
            self.reload_custom_prompts()

            # Select the newly saved prompt
            index = self.ai_assistant_prompt_selector.findText(name)
            if index != -1:
                self.ai_assistant_prompt_selector.setCurrentIndex(index)
                # Selected custom prompt (debug message removed)
            else:
                # Could not find custom prompt (debug message removed)
                pass
        except Exception as e:
            # Error in on_custom_prompt_saved (debug message removed)
            pass

    def on_ai_assistant_settings_clicked(self):
        """Direct handler for the Settings button click."""
        # on_ai_assistant_settings_clicked called (debug message removed)
        try:
            if hasattr(self, 'show_gemini_settings_dialog'):
                # Calling show_gemini_settings_dialog (debug message removed)
                self.show_gemini_settings_dialog()
            else:
                # show_gemini_settings_dialog method not found, using handler (debug message removed)
                from src.gui.handlers.ai_assistant_handlers import AIAssistantHandlers
                AIAssistantHandlers.show_gemini_settings_dialog(self)
        except Exception as e:
            # Error in on_ai_assistant_settings_clicked (debug message removed)
            QMessageBox.critical(self, "Error", f"An error occurred: {e}")

    # --- Zoom Control Methods ---
    # These methods are now handled by the ZoomableGraphicsView class
    # The buttons are connected directly to the view's methods

    def wheelEvent(self, event):
        """Handle mouse wheel events for the main window.

        Note: The image viewer has its own wheelEvent handler in ZoomableGraphicsView.
        This handler is only for wheel events outside the image viewer.
        """
        # Pass the event to the parent for normal scrolling
        super().wheelEvent(event)

    def keyPressEvent(self, event):
        """Handle keyboard shortcuts for zoom."""
        try:
            if hasattr(self, 'ai_assistant_image_viewer'):
                if event.key() == Qt.Key.Key_Plus or event.key() == Qt.Key.Key_Equal:
                    # Zoom in
                    self.ai_assistant_image_viewer.zoomIn()
                    event.accept()
                elif event.key() == Qt.Key.Key_Minus:
                    # Zoom out
                    self.ai_assistant_image_viewer.zoomOut()
                    event.accept()
                elif event.key() == Qt.Key.Key_0:
                    # Fit to view
                    self.ai_assistant_image_viewer.fitInView(self.ai_assistant_image_item, Qt.AspectRatioMode.KeepAspectRatio)
                    event.accept()
                else:
                    # Pass the event to the parent for normal key handling
                    super().keyPressEvent(event)
            else:
                super().keyPressEvent(event)
        except Exception as e:
            # Error in keyPressEvent (debug message removed)
            super().keyPressEvent(event)
