# src/segmentation/segmentation_algorithms.py
# Contains the segmentation algorithm implemented with PyTorch (replacing TensorFlow/Keras)

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
from skimage import segmentation
import os


def perform_custom_segmentation(image, params, stop_event, progress_callback=None):
    """Performs custom image segmentation."""
    class Args(object):
        def __init__(self, params):
            self.train_epoch = params.get('train_epoch', 8)
            self.mod_dim1 = params.get('mod_dim1', 64)
            self.mod_dim2 = params.get('mod_dim2', 32)
            self.gpu_id = params.get('gpu_id', 0)
            self.min_label_num = params.get('min_label_num', 6)
            self.max_label_num = params.get('max_label_num', 256)
            self.segmentation_method = params.get('segmentation_method', 'felzenszwalb')
            self.learning_rate = params.get('learning_rate', 1e-3)  # Add learning rate parameter, default to 1e-3
            self.use_adaptive_lr = params.get('use_adaptive_lr', False)  # Enable/disable adaptive learning rate
            self.lr_patience = params.get('lr_patience', 3)  # Patience for learning rate reduction
            self.lr_factor = params.get('lr_factor', 0.5)  # Factor to reduce learning rate by
            
            # New optimization parameters
            self.momentum = params.get('momentum', 0.9)  # Momentum for SGD
            self.use_adaptive_momentum = params.get('use_adaptive_momentum', False)  # Enable/disable adaptive momentum
            self.weight_decay = params.get('weight_decay', 1e-4)  # L2 regularization
            self.optimizer_type = params.get('optimizer_type', 'SGD')  # Optimizer type
            self.use_gradient_clipping = params.get('use_gradient_clipping', False)  # Enable gradient clipping
            self.gradient_clip_value = params.get('gradient_clip_value', 1.0)  # Gradient clipping value
            
            # Adaptive momentum parameters
            self.momentum_min = params.get('momentum_min', 0.5)  # Minimum momentum value
            self.momentum_max = params.get('momentum_max', 0.95)  # Maximum momentum value
            self.momentum_patience = params.get('momentum_patience', 5)  # Patience for momentum adjustment

    args = Args(params)

    # Define the PyTorch model
    # Helper Residual Block for MyNet
    class DepthwiseSeparableConv(nn.Module):
        def __init__(self, in_channels, out_channels, kernel_size, stride, padding, bias=False):
            super(DepthwiseSeparableConv, self).__init__()
            self.depthwise = nn.Conv2d(in_channels, in_channels, kernel_size=kernel_size, 
                                       stride=stride, padding=padding, groups=in_channels, bias=bias)
            self.pointwise = nn.Conv2d(in_channels, out_channels, kernel_size=1, bias=bias)

        def forward(self, x):
            out = self.depthwise(x)
            out = self.pointwise(out)
            return out

    class ResidualBlock(nn.Module):
        def __init__(self, channels):
            super(ResidualBlock, self).__init__()
            self.conv1 = DepthwiseSeparableConv(channels, channels, kernel_size=3, stride=1, padding=1, bias=False)
            self.bn1 = nn.BatchNorm2d(channels)
            self.selu = nn.SELU(inplace=True)
            self.conv2 = DepthwiseSeparableConv(channels, channels, kernel_size=3, stride=1, padding=1, bias=False)
            self.bn2 = nn.BatchNorm2d(channels)

        def forward(self, x):
            identity = x
            
            out = self.conv1(x)
            out = self.bn1(out)
            out = self.selu(out)

            out = self.conv2(out)
            out = self.bn2(out)

            out += identity  # Skip connection
            out = self.selu(out)  # SELU after addition
            return out

    class MyNet(nn.Module):
        def __init__(self, inp_dim, mod_dim1, mod_dim2):
            super(MyNet, self).__init__()

            self.conv_initial = nn.Sequential(
                DepthwiseSeparableConv(inp_dim, mod_dim1, kernel_size=3, stride=1, padding=1, bias=False),
                nn.BatchNorm2d(mod_dim1),
                nn.SELU(inplace=True)
            )

            # Residual Block 1
            self.res_block1 = ResidualBlock(mod_dim1)

            # Residual Block 2
            self.res_block2 = ResidualBlock(mod_dim1)

            # Residual Block 3
            self.res_block3 = ResidualBlock(mod_dim1)
            
            # Final convolution to change to mod_dim2
            self.conv_final = nn.Sequential(
                DepthwiseSeparableConv(mod_dim1, mod_dim2, kernel_size=1, stride=1, padding=0, bias=False),
                nn.BatchNorm2d(mod_dim2)
                # No SELU here, to match original network's final layer output behavior if it's intended to be linear
            )

        def forward(self, x):
            x = self.conv_initial(x)
            x = self.res_block1(x)
            x = self.res_block2(x)
            x = self.res_block3(x)
            x = self.conv_final(x)
            return x

    # Set random seeds for reproducibility using a fixed seed
    np.random.seed(1943)
    torch.manual_seed(1943)

    # Set CUDA device if available
    if torch.cuda.is_available():
        torch.cuda.set_device(args.gpu_id)
        torch.cuda.manual_seed(1943)

    '''segmentation ML'''
    # Initialize seg_lab as an empty list by default
    seg_lab = []

    # Convert segmentation method to lowercase for case-insensitive comparison
    segmentation_method = args.segmentation_method.lower()

    if segmentation_method == 'felzenszwalb':
        # Perform Felzenszwalb segmentation
        # Adjusted parameters for finer segmentation:
        # - Lower scale for smaller segments
        # - Lower sigma for more edge sensitivity
        # - Smaller min_size to allow tiny segments
        seg_map = segmentation.felzenszwalb(image, scale=8, sigma=0.02, min_size=3)
        seg_map = seg_map.flatten()
        seg_lab = [np.where(seg_map == u_label)[0] for u_label in np.unique(seg_map)]

    elif segmentation_method == 'kmeans':
        # Perform KMeans clustering
        image_flatten = image.reshape((-1, 3))
        kmeans = KMeans(n_clusters=args.max_label_num, random_state=0).fit(image_flatten)
        seg_map = kmeans.labels_
        seg_lab = [np.where(seg_map == u_label)[0] for u_label in np.unique(seg_map)]

    elif segmentation_method == 'pca':
        # Perform PCA-based segmentation
        image_flatten = image.reshape((-1, 3))

        # Apply PCA to reduce dimensionality to 1 component for segmentation
        n_components = min(3, image_flatten.shape[1])  # Use at most 3 components
        pca = PCA(n_components=n_components)
        reduced_data = pca.fit_transform(image_flatten)

        # Apply KMeans on the PCA-reduced data
        kmeans = KMeans(n_clusters=args.max_label_num, random_state=0).fit(reduced_data)
        seg_map = kmeans.labels_
        seg_lab = [np.where(seg_map == u_label)[0] for u_label in np.unique(seg_map)]

    else:
        # Default to simple grid segmentation if method is not recognized
        print(f"Warning: Unrecognized segmentation method '{segmentation_method}'. Using simple grid segmentation.")
        h, w = image.shape[:2]
        grid_size = max(h, w) // 16  # Simple grid division
        seg_map = np.zeros((h, w), dtype=np.int32)

        # Create a simple grid segmentation
        for i in range(0, h, grid_size):
            for j in range(0, w, grid_size):
                seg_map[i:min(i+grid_size, h), j:min(j+grid_size, w)] = (i//grid_size) * (w//grid_size) + (j//grid_size)

        seg_map = seg_map.flatten()
        seg_lab = [np.where(seg_map == u_label)[0] for u_label in np.unique(seg_map)]

    # Prepare input tensor for PyTorch
    tensor = image.astype(np.float32) / 255.0
    tensor = np.transpose(tensor, (2, 0, 1))  # Convert from HWC to CHW format
    tensor = np.expand_dims(tensor, axis=0)  # Add batch dimension
    tensor = torch.from_numpy(tensor)  # Convert to PyTorch tensor

    # Set device to GPU if available, otherwise CPU
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    tensor = tensor.to(device)

    segmented_images = []

    # Initialize the model
    model = MyNet(inp_dim=3, mod_dim1=args.mod_dim1, mod_dim2=args.mod_dim2).to(device)
    
    # Initialize optimizer based on selected type
    if args.optimizer_type == 'Adam':
        optimizer = optim.Adam(model.parameters(), lr=args.learning_rate, weight_decay=args.weight_decay)
    elif args.optimizer_type == 'AdamW':
        optimizer = optim.AdamW(model.parameters(), lr=args.learning_rate, weight_decay=args.weight_decay)
    elif args.optimizer_type == 'RMSprop':
        optimizer = optim.RMSprop(model.parameters(), lr=args.learning_rate, weight_decay=args.weight_decay, momentum=args.momentum)
    else:  # Default to SGD
        optimizer = optim.SGD(model.parameters(), lr=args.learning_rate, momentum=args.momentum, weight_decay=args.weight_decay)
    
    # Initialize adaptive learning rate scheduler if enabled
    scheduler = None
    if args.use_adaptive_lr:
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, 
            mode='min', 
            factor=args.lr_factor, 
            patience=args.lr_patience, 
            verbose=True
        )
    
    # Initialize adaptive momentum tracking
    momentum_tracker = {
        'losses': [],
        'no_improvement_count': 0,
        'current_momentum': args.momentum,
        'best_loss': float('inf')
    }

    # Flatten image for color assignment
    image_flatten = image.reshape(-1, 3)
    color_avg = np.random.randint(255, size=(args.max_label_num, 3))
    show = image.copy()

    for batch_idx in range(args.train_epoch):
        if stop_event.is_set():
            print("Training stopped by user.")
            break  # Exit the training loop

        # Forward pass
        model.train()
        optimizer.zero_grad()
        output = model(tensor)

        # Reshape output and compute argmax
        output_reshaped = output[0].permute(1, 2, 0).reshape(-1, args.mod_dim2)
        target = torch.argmax(output_reshaped, dim=1)
        im_target = target.cpu().numpy()

        # Update target labels based on segmentation
        for inds in seg_lab:
            u_labels, hist = np.unique(im_target[inds], return_counts=True)
            if len(u_labels) > 0:
                im_target[inds] = u_labels[np.argmax(hist)]

        # Convert back to tensor for loss calculation
        target = torch.from_numpy(im_target).long().to(device)

        # Compute loss (CrossEntropyLoss expects raw logits)
        loss = F.cross_entropy(output_reshaped, target)

        # Backward pass and optimization
        loss.backward()
        
        # Apply gradient clipping if enabled
        if args.use_gradient_clipping:
            torch.nn.utils.clip_grad_norm_(model.parameters(), args.gradient_clip_value)
        
        optimizer.step()
        
        # Update learning rate if adaptive learning rate is enabled
        if scheduler is not None:
            scheduler.step(loss)
        
        # Adaptive momentum implementation for SGD
        if args.use_adaptive_momentum and args.optimizer_type == 'SGD':
            current_loss = loss.item()
            momentum_tracker['losses'].append(current_loss)
            
            # Check if loss improved
            if current_loss < momentum_tracker['best_loss']:
                momentum_tracker['best_loss'] = current_loss
                momentum_tracker['no_improvement_count'] = 0
                # Increase momentum when improving
                new_momentum = min(args.momentum_max, momentum_tracker['current_momentum'] + 0.01)
            else:
                momentum_tracker['no_improvement_count'] += 1
                # Decrease momentum when not improving
                if momentum_tracker['no_improvement_count'] >= args.momentum_patience:
                    new_momentum = max(args.momentum_min, momentum_tracker['current_momentum'] - 0.05)
                    momentum_tracker['no_improvement_count'] = 0
                else:
                    new_momentum = momentum_tracker['current_momentum']
            
            # Update momentum if it changed
            if new_momentum != momentum_tracker['current_momentum']:
                momentum_tracker['current_momentum'] = new_momentum
                # Update optimizer momentum
                for param_group in optimizer.param_groups:
                    if 'momentum' in param_group:
                        param_group['momentum'] = new_momentum
                print(f"Adaptive momentum updated to: {new_momentum:.3f}")

        # Update segmented image
        un_label, lab_inverse = np.unique(im_target, return_inverse=True)
        if un_label.shape[0] < args.max_label_num:
            img_flatten = image_flatten.copy()
            if len(color_avg) != un_label.shape[0]:
                # Recompute color_avg based on current labels
                color_avg = [np.mean(img_flatten[im_target == label], axis=0, dtype=int) for label in un_label]
                color_avg = np.array(color_avg)
            for lab_id, color in enumerate(color_avg):
                img_flatten[lab_inverse == lab_id] = color
            show = img_flatten.reshape(image.shape)
            show = show.astype(np.uint8)  # Ensure proper image data type

        segmented_images.append(show.copy())

        # Update progress bar and display image
        if progress_callback:
            progress_callback(batch_idx + 1, show)

    return segmented_images