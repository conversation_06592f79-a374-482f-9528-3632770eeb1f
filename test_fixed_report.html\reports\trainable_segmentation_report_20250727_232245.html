
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trainable Segmentation Report</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .section {
            background-color: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .section-title {
            color: #2c3e50;
            font-size: 1.8em;
            margin-bottom: 20px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #ecf0f1, #bdc3c7);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
        }
        .stat-label {
            color: #7f8c8d;
            font-size: 0.9em;
        }
        .mask-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .mask-card {
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: 1px solid #e0e0e0;
        }
        .mask-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 6px;
            margin-bottom: 10px;
        }
        .segment-stats {
            margin-top: 10px;
        }
        .segment-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .model-info {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .status-success {
            color: #27ae60;
            font-weight: bold;
        }
        .status-failed {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 Trainable Segmentation Analysis Report</h1>
        <p>Generated on: 2025-07-27 23:22:45</p>
    </div>
    
    <!-- Processing Summary Section -->
    <div class="section">
        <h2 class="section-title">📊 Processing Summary</h2>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">2</div>
                <div class="stat-label">Total Images</div>
            </div>
            <div class="stat-card">
                <div class="stat-value status-success">2</div>
                <div class="stat-label">Successfully Processed</div>
            </div>
            <div class="stat-card">
                <div class="stat-value status-failed">0</div>
                <div class="stat-label">Failed</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">100.0%</div>
                <div class="stat-label">Success Rate</div>
            </div>
        </div>
    </div>
    
    <!-- Section 1: Quantitative Analysis (Bar Chart) -->
    <div class="section">
        <h2 class="section-title">📊 Section 1: Quantitative Analysis (Bar Chart)</h2>
        <p>This section provides a quantitative summary of the segmentation results using a clustered bar chart to compare performance metrics across different images and object classes.</p>
        <div class="chart-container">
            <canvas id="barChart"></canvas>
        </div>
    </div>
    
    <!-- Section 2: Quantitative Analysis (Scatter Plot) -->
    <div class="section">
        <h2 class="section-title">🎯 Section 2: Quantitative Analysis (Scatter Plot)</h2>
        <p>This section displays the same quantitative data as the bar chart but in a scatter plot format for different visualization perspective.</p>
        <div class="chart-container">
            <canvas id="scatterChart"></canvas>
        </div>
    </div>
    
    <!-- Section 3: Visual Segmentation Results (Mask Showcase) -->
    <div class="section">
        <h2 class="section-title">🎨 Section 3: Visual Segmentation Results (Mask Showcase)</h2>
        <p>This is the most critical section, displaying the actual visual outputs of the segmentation process. The charts above are derived from this data.</p>
        <div class="mask-grid">
        <div class="mask-card">
            <h3>test_image1.jpg</h3>
            <div class="segment-stats">
                <strong>Segmentation Details:</strong>
                <div style="margin-top: 10px;">
                    <div class="segment-item">
                        <span><strong>Total Segments:</strong></span>
                        <span>0</span>
                    </div>
                    <div class="segment-item">
                        <span><strong>Processing Time:</strong></span>
                        <span>2.50s</span>
                    </div>
                </div>
                <div style="margin-top: 15px;">
                    <strong>Segment Breakdown:</strong>
                    <div class="segment-item">
                        <span>Segment 1</span>
                        <span>45.2%</span>
                    </div>
                    <div class="segment-item">
                        <span>Segment 2</span>
                        <span>30.1%</span>
                    </div>
                    <div class="segment-item">
                        <span>Segment 3</span>
                        <span>24.7%</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="mask-card">
            <h3>test_image2.jpg</h3>
            <div class="segment-stats">
                <strong>Segmentation Details:</strong>
                <div style="margin-top: 10px;">
                    <div class="segment-item">
                        <span><strong>Total Segments:</strong></span>
                        <span>0</span>
                    </div>
                    <div class="segment-item">
                        <span><strong>Processing Time:</strong></span>
                        <span>3.10s</span>
                    </div>
                </div>
                <div style="margin-top: 15px;">
                    <strong>Segment Breakdown:</strong>
                    <div class="segment-item">
                        <span>Segment 1</span>
                        <span>55.3%</span>
                    </div>
                    <div class="segment-item">
                        <span>Segment 2</span>
                        <span>44.7%</span>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </div>
    
    <script>
        // Chart.js configuration and data
        const chartData = [{"image": "test_image1.jpg", "segment": "Segment 1", "percentage": 45.2, "pixel_count": 1000}, {"image": "test_image1.jpg", "segment": "Segment 2", "percentage": 30.1, "pixel_count": 800}, {"image": "test_image1.jpg", "segment": "Segment 3", "percentage": 24.7, "pixel_count": 600}, {"image": "test_image2.jpg", "segment": "Segment 1", "percentage": 55.3, "pixel_count": 1200}, {"image": "test_image2.jpg", "segment": "Segment 2", "percentage": 44.7, "pixel_count": 900}];
        
        // Prepare data for bar chart
        const images = [...new Set(chartData.map(d => d.image))];
        const segments = [...new Set(chartData.map(d => d.segment))];
        
        // Generate colors for segments
        const colors = [
            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
            '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
        ];
        
        // Bar Chart
        const barCtx = document.getElementById('barChart').getContext('2d');
        const barChart = new Chart(barCtx, {
            type: 'bar',
            data: {
                labels: images,
                datasets: segments.map((segment, index) => ({
                    label: segment,
                    data: images.map(image => {
                        const item = chartData.find(d => d.image === image && d.segment === segment);
                        return item ? item.percentage : 0;
                    }),
                    backgroundColor: colors[index % colors.length],
                    borderColor: colors[index % colors.length],
                    borderWidth: 1
                }))
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Segment Percentage Distribution by Image'
                    },
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Percentage (%)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Images'
                        }
                    }
                }
            }
        });
        
        // Scatter Plot
        const scatterCtx = document.getElementById('scatterChart').getContext('2d');
        const scatterChart = new Chart(scatterCtx, {
            type: 'scatter',
            data: {
                datasets: segments.map((segment, index) => ({
                    label: segment,
                    data: chartData
                        .filter(d => d.segment === segment)
                        .map((d, i) => ({
                            x: i + 1,
                            y: d.percentage
                        })),
                    backgroundColor: colors[index % colors.length],
                    borderColor: colors[index % colors.length],
                    pointRadius: 6
                }))
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Segment Percentage Distribution (Scatter View)'
                    },
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Percentage (%)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Image Index'
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>