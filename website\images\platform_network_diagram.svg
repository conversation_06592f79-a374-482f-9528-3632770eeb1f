<svg viewBox="0 0 1000 800" xmlns="http://www.w3.org/2000/svg">
  <style>
    .interface-node {
      cursor: pointer;
      transition: transform 0.3s ease;
    }
    .interface-node:hover {
      transform: scale(1.1);
    }
  </style>
  <defs>
    <linearGradient id="nodeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="centralGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <filter id="shadow">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
    </filter>
    
    <!-- Icon definitions -->
    <g id="brainIcon">
      <path d="M-8,-6 C-10,-8 -8,-10 -6,-8 C-4,-10 -2,-8 0,-6 C2,-8 4,-10 6,-8 C8,-10 10,-8 8,-6 C10,-4 8,-2 6,0 C8,2 10,4 8,6 C10,8 8,10 6,8 C4,10 2,8 0,6 C-2,8 -4,10 -6,8 C-8,10 -10,8 -8,6 C-10,4 -8,2 -6,0 C-8,-2 -10,-4 -8,-6 Z" fill="white" opacity="0.9"/>
    </g>
    
    <g id="segmentIcon">
      <rect x="-8" y="-6" width="16" height="3" fill="white" opacity="0.9" rx="1"/>
      <rect x="-8" y="-1" width="16" height="3" fill="white" opacity="0.9" rx="1"/>
      <rect x="-8" y="4" width="16" height="3" fill="white" opacity="0.9" rx="1"/>
    </g>
    
    <g id="targetIcon">
      <circle r="8" fill="none" stroke="white" stroke-width="1.5" opacity="0.9"/>
      <circle r="5" fill="none" stroke="white" stroke-width="1.5" opacity="0.9"/>
      <circle r="2" fill="white" opacity="0.9"/>
    </g>
    
    <g id="dotIcon">
      <circle r="2" cx="-4" cy="-4" fill="white" opacity="0.9"/>
      <circle r="2" cx="4" cy="-4" fill="white" opacity="0.9"/>
      <circle r="2" cx="-4" cy="4" fill="white" opacity="0.9"/>
      <circle r="2" cx="4" cy="4" fill="white" opacity="0.9"/>
      <circle r="1.5" cx="0" cy="0" fill="white" opacity="0.9"/>
    </g>
    
    <g id="grainIcon">
      <ellipse rx="6" ry="4" fill="white" opacity="0.9"/>
      <ellipse rx="3" ry="2" cx="2" cy="1" fill="#667eea" opacity="0.7"/>
    </g>
    
    <g id="batchIcon">
      <rect x="-6" y="-6" width="12" height="3" fill="white" opacity="0.9" rx="1"/>
      <rect x="-6" y="-1" width="12" height="3" fill="white" opacity="0.9" rx="1"/>
      <rect x="-6" y="4" width="12" height="3" fill="white" opacity="0.9" rx="1"/>
      <path d="M6,-3 L9,0 L6,3" stroke="white" stroke-width="1.5" fill="none" opacity="0.9"/>
    </g>
    
    <g id="labIcon">
      <rect x="-8" y="-6" width="16" height="12" fill="none" stroke="white" stroke-width="1.5" opacity="0.9" rx="2"/>
      <rect x="-6" y="-4" width="12" height="6" fill="white" opacity="0.3"/>
      <circle r="1" cx="-3" cy="-1" fill="white" opacity="0.9"/>
      <circle r="1" cx="3" cy="1" fill="white" opacity="0.9"/>
    </g>
    
    <g id="assistantIcon">
      <circle r="6" fill="white" opacity="0.9"/>
      <circle r="2" cx="-2" cy="-2" fill="#667eea"/>
      <circle r="2" cx="2" cy="-2" fill="#667eea"/>
      <path d="M-3,2 Q0,4 3,2" stroke="#667eea" stroke-width="1.5" fill="none"/>
    </g>
  </defs>
  
  <!-- Orbital Connection Rings -->
    <g transform="translate(500,400)">
      <circle r="280" fill="none" stroke="rgba(255,255,255,0.15)" stroke-width="2" stroke-dasharray="8,8">
        <animate attributeName="stroke-opacity" values="0.15;0.4;0.15" dur="8s" repeatCount="indefinite"/>
      </circle>
      <circle r="310" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1" stroke-dasharray="5,10">
        <animate attributeName="stroke-opacity" values="0.1;0.3;0.1" dur="12s" repeatCount="indefinite"/>
      </circle>
    </g>
  
  <!-- Central Hub - VisionLab AI -->
  <g transform="translate(500,400)">
    <circle r="120" fill="url(#centralGradient)" filter="url(#shadow)" stroke="#ffffff" stroke-width="3">
      <animate attributeName="r" values="120;125;120" dur="3s" repeatCount="indefinite"/>
    </circle>
    <use href="#brainIcon" transform="translate(0,-35) scale(2.5)"/>
    <text text-anchor="middle" y="15" fill="white" font-family="Inter, sans-serif" font-size="24" font-weight="600">VisionLab AI</text>
    <text text-anchor="middle" y="38" fill="white" font-family="Inter, sans-serif" font-size="20" opacity="0.9">Core Platform</text>
  </g>
  
  <!-- Interface Nodes with Orbital Animation -->
  
  <!-- Unsupervised Segmentation Interface -->
   <g transform="translate(500,400)">
     <g transform="rotate(0)">
       <animateTransform attributeName="transform" type="rotate" values="0 0 0;360 0 0" dur="30s" repeatCount="indefinite"/>
       <g transform="translate(0,-280)" class="interface-node" onclick="window.location.href='software.html#unsupervised-segmentation'">
          <circle r="90" fill="url(#nodeGradient)" filter="url(#shadow)" stroke="#ffffff" stroke-width="2">
            <animate attributeName="fill-opacity" values="0.8;1;0.8" dur="4s" repeatCount="indefinite"/>
          </circle>
          <use href="#segmentIcon" transform="translate(0,-25) scale(1.8)"/>
          <text text-anchor="middle" y="5" fill="white" font-family="Inter, sans-serif" font-size="16" font-weight="500">Unsupervised</text>
          <text text-anchor="middle" y="22" fill="white" font-family="Inter, sans-serif" font-size="16" font-weight="500">Segmentation</text>
          <text text-anchor="middle" y="38" fill="white" font-family="Inter, sans-serif" font-size="14" opacity="0.8">Interface</text>
        </g>
     </g>
   </g>
  
  <!-- Advanced Segmentation Interface -->
   <g transform="translate(500,400)">
     <g transform="rotate(45)">
       <animateTransform attributeName="transform" type="rotate" values="45 0 0;405 0 0" dur="30s" repeatCount="indefinite"/>
       <g transform="translate(0,-280)" class="interface-node" onclick="window.location.href='software.html#advanced-segmentation'">
          <circle r="90" fill="url(#nodeGradient)" filter="url(#shadow)" stroke="#ffffff" stroke-width="2">
            <animate attributeName="fill-opacity" values="0.8;1;0.8" dur="4.5s" repeatCount="indefinite"/>
          </circle>
          <use href="#targetIcon" transform="translate(0,-25) scale(1.8)"/>
          <text text-anchor="middle" y="5" fill="white" font-family="Inter, sans-serif" font-size="16" font-weight="500">Advanced</text>
          <text text-anchor="middle" y="22" fill="white" font-family="Inter, sans-serif" font-size="16" font-weight="500">Segmentation</text>
          <text text-anchor="middle" y="38" fill="white" font-family="Inter, sans-serif" font-size="14" opacity="0.8">Interface</text>
        </g>
     </g>
   </g>
  
  <!-- Trainable Segmentation Interface -->
   <g transform="translate(500,400)">
     <g transform="rotate(90)">
       <animateTransform attributeName="transform" type="rotate" values="90 0 0;450 0 0" dur="30s" repeatCount="indefinite"/>
       <g transform="translate(0,-280)" class="interface-node" onclick="window.location.href='software.html#trainable-segmentation'">
          <circle r="90" fill="url(#nodeGradient)" filter="url(#shadow)" stroke="#ffffff" stroke-width="2">
            <animate attributeName="fill-opacity" values="0.8;1;0.8" dur="3.5s" repeatCount="indefinite"/>
          </circle>
          <use href="#segmentIcon" transform="translate(0,-25) scale(1.8)"/>
          <text text-anchor="middle" y="5" fill="white" font-family="Inter, sans-serif" font-size="16" font-weight="500">Trainable</text>
          <text text-anchor="middle" y="22" fill="white" font-family="Inter, sans-serif" font-size="16" font-weight="500">Segmentation</text>
          <text text-anchor="middle" y="38" fill="white" font-family="Inter, sans-serif" font-size="14" opacity="0.8">Interface</text>
        </g>
     </g>
   </g>
  
  <!-- Point Counting Interface -->
   <g transform="translate(500,400)">
     <g transform="rotate(135)">
       <animateTransform attributeName="transform" type="rotate" values="135 0 0;495 0 0" dur="30s" repeatCount="indefinite"/>
       <g transform="translate(0,-280)" class="interface-node" onclick="window.location.href='software.html#point-counting'">
          <circle r="90" fill="url(#nodeGradient)" filter="url(#shadow)" stroke="#ffffff" stroke-width="2">
            <animate attributeName="fill-opacity" values="0.8;1;0.8" dur="5s" repeatCount="indefinite"/>
          </circle>
          <use href="#dotIcon" transform="translate(0,-25) scale(1.8)"/>
          <text text-anchor="middle" y="10" fill="white" font-family="Inter, sans-serif" font-size="16" font-weight="500">Point Counting</text>
          <text text-anchor="middle" y="28" fill="white" font-family="Inter, sans-serif" font-size="16" font-weight="500">Interface</text>
        </g>
     </g>
   </g>
  
  <!-- Grain Analysis Interface -->
   <g transform="translate(500,400)">
     <g transform="rotate(180)">
       <animateTransform attributeName="transform" type="rotate" values="180 0 0;540 0 0" dur="30s" repeatCount="indefinite"/>
       <g transform="translate(0,-280)" class="interface-node" onclick="window.location.href='software.html#grain-analysis'">
          <circle r="90" fill="url(#nodeGradient)" filter="url(#shadow)" stroke="#ffffff" stroke-width="2">
            <animate attributeName="fill-opacity" values="0.8;1;0.8" dur="4.2s" repeatCount="indefinite"/>
          </circle>
          <use href="#grainIcon" transform="translate(0,-25) scale(1.8)"/>
          <text text-anchor="middle" y="10" fill="white" font-family="Inter, sans-serif" font-size="16" font-weight="500">Grain Analysis</text>
          <text text-anchor="middle" y="28" fill="white" font-family="Inter, sans-serif" font-size="16" font-weight="500">Interface</text>
        </g>
     </g>
   </g>
  
  <!-- Batch Processing Interface -->
   <g transform="translate(500,400)">
     <g transform="rotate(225)">
       <animateTransform attributeName="transform" type="rotate" values="225 0 0;585 0 0" dur="30s" repeatCount="indefinite"/>
       <g transform="translate(0,-280)" class="interface-node" onclick="window.location.href='software.html#batch-processing'">
          <circle r="90" fill="url(#nodeGradient)" filter="url(#shadow)" stroke="#ffffff" stroke-width="2">
            <animate attributeName="fill-opacity" values="0.8;1;0.8" dur="3.8s" repeatCount="indefinite"/>
          </circle>
          <use href="#batchIcon" transform="translate(0,-25) scale(1.8)"/>
          <text text-anchor="middle" y="10" fill="white" font-family="Inter, sans-serif" font-size="16" font-weight="500">Batch Processing</text>
          <text text-anchor="middle" y="28" fill="white" font-family="Inter, sans-serif" font-size="16" font-weight="500">Interface</text>
        </g>
     </g>
   </g>
  
  <!-- Image Lab Interface -->
   <g transform="translate(500,400)">
     <g transform="rotate(270)">
       <animateTransform attributeName="transform" type="rotate" values="270 0 0;630 0 0" dur="30s" repeatCount="indefinite"/>
       <g transform="translate(0,-280)" class="interface-node" onclick="window.location.href='software.html#image-lab'">
          <circle r="90" fill="url(#nodeGradient)" filter="url(#shadow)" stroke="#ffffff" stroke-width="2">
            <animate attributeName="fill-opacity" values="0.8;1;0.8" dur="4.7s" repeatCount="indefinite"/>
          </circle>
          <use href="#labIcon" transform="translate(0,-25) scale(1.8)"/>
          <text text-anchor="middle" y="10" fill="white" font-family="Inter, sans-serif" font-size="16" font-weight="500">Image Lab</text>
          <text text-anchor="middle" y="28" fill="white" font-family="Inter, sans-serif" font-size="16" font-weight="500">Interface</text>
        </g>
     </g>
   </g>
  
  <!-- AI Assistant Interface -->
   <g transform="translate(500,400)">
     <g transform="rotate(315)">
       <animateTransform attributeName="transform" type="rotate" values="315 0 0;675 0 0" dur="30s" repeatCount="indefinite"/>
       <g transform="translate(0,-280)" class="interface-node" onclick="window.location.href='software.html#ai-assistant'">
          <circle r="90" fill="url(#nodeGradient)" filter="url(#shadow)" stroke="#ffffff" stroke-width="2">
            <animate attributeName="fill-opacity" values="0.8;1;0.8" dur="5.2s" repeatCount="indefinite"/>
          </circle>
          <use href="#assistantIcon" transform="translate(0,-25) scale(1.8)"/>
          <text text-anchor="middle" y="10" fill="white" font-family="Inter, sans-serif" font-size="16" font-weight="500">AI Assistant</text>
          <text text-anchor="middle" y="28" fill="white" font-family="Inter, sans-serif" font-size="16" font-weight="500">Interface</text>
        </g>
     </g>
   </g>
  
  <!-- Data Flow Indicators -->
  <g fill="#4facfe" opacity="0.7">
    <circle r="3" cx="350" cy="275">
      <animate attributeName="r" values="3;6;3" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;0.3;0.7" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle r="3" cx="650" cy="275">
      <animate attributeName="r" values="3;6;3" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;0.3;0.7" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle r="3" cx="675" cy="525">
      <animate attributeName="r" values="3;6;3" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;0.3;0.7" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle r="3" cx="325" cy="525">
      <animate attributeName="r" values="3;6;3" dur="2.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;0.3;0.7" dur="2.2s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Integration Indicators -->
  <g stroke="#00f2fe" stroke-width="1" fill="none" opacity="0.5">
    <circle r="120" cx="500" cy="400" stroke-dasharray="10,5">
      <animateTransform attributeName="transform" type="rotate" values="0 500 400;360 500 400" dur="20s" repeatCount="indefinite"/>
    </circle>
    <circle r="200" cx="500" cy="400" stroke-dasharray="15,10">
      <animateTransform attributeName="transform" type="rotate" values="360 500 400;0 500 400" dur="30s" repeatCount="indefinite"/>
    </circle>
  </g>
</svg>