# src/gui/handlers/ai_assistant_handlers.py

import os
import logging
import json
from PySide6.QtWidgets import QMessageBox, QTableWidgetItem, QGraphicsRectItem, QGraphicsSimpleTextItem, QApplication, QHeaderView
from PySide6.QtGui import QPen, QColor, QBrush, QFont
from PySide6.QtCore import Qt, Slot, QSettings, QDateTime
from PySide6.QtWidgets import QGraphicsPixmapItem


from src.utils.image_utils import convert_cvimage_to_qpixmap
from src.ai_assistant_components.src.gemini.gemini_worker import GeminiWorker
from src.ai_assistant_components.src.gemini.prompts import DEFAULT_PROMPTS
from src.ai_assistant_components.src.gemini.check_dependencies import check_gemini_dependencies, get_installation_instructions
from src.gui.dialogs.gemini_settings_dialog import GeminiSettingsDialog

logger = logging.getLogger(__name__)

# Define some colors for bounding boxes
BOX_COLORS = [
    QColor("red"), QColor("lime"), QColor("blue"), QColor("yellow"),
    QColor("cyan"), QColor("magenta"), QColor("orange"), QColor("purple")
]

class AIAssistantHandlers:
    """Class for handling AI Assistant page operations."""

    def __init__(self):
        # Initialize attributes
        self.ai_assistant_current_image_id = None
        self.ai_assistant_current_pixmap = None
        self.ai_assistant_bounding_boxes = []

        # Get API key from settings using the GeminiSettingsDialog method
        self.settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")
        self.ai_assistant_api_key = GeminiSettingsDialog.get_api_key()
        if self.ai_assistant_api_key and len(self.ai_assistant_api_key) > 10:
            logger.info(f"Loaded API key from settings: {self.ai_assistant_api_key[:5]}...{self.ai_assistant_api_key[-5:]}")
        else:
            logger.warning(f"API key not set or too short: {self.ai_assistant_api_key}")

        # Check for required dependencies
        self.missing_packages = check_gemini_dependencies()
        if self.missing_packages:
            logger.warning(f"Missing dependencies for Gemini API: {self.missing_packages}")
        else:
            logger.info("All Gemini API dependencies are installed")

        # Get model name from settings using the GeminiSettingsDialog method
        model_name = GeminiSettingsDialog.get_model_name()
        logger.info(f"Loaded model name from settings: {model_name}")

        # Initialize Gemini worker with the model from settings
        self.ai_assistant_gemini_worker = GeminiWorker(self.ai_assistant_api_key, model_name=model_name)
        logger.info(f"Initialized GeminiWorker with model: {self.ai_assistant_gemini_worker.model_name}")

        # Ensure prompt selector is initialized with default prompts
        if hasattr(self, 'ai_assistant_prompt_selector'):
            # Clear any existing items
            self.ai_assistant_prompt_selector.clear()
            # Add default prompts
            self.ai_assistant_prompt_selector.addItems(DEFAULT_PROMPTS.keys())
            logger.info(f"Added {len(DEFAULT_PROMPTS)} default prompts to selector")

            # Now load custom prompts
            self.reload_custom_prompts()
        else:
            logger.warning("Prompt selector not available during initialization")

        # Connect the signals
        logger.info("Connecting signals to the worker")
        try:
            self.ai_assistant_gemini_worker.result_ready.connect(self.handle_ai_assistant_text_result)
            logger.info("Connected result_ready signal")
            self.ai_assistant_gemini_worker.structured_data_ready.connect(self.handle_ai_assistant_structured_data)
            logger.info("Connected structured_data_ready signal")
            self.ai_assistant_gemini_worker.bounding_boxes_ready.connect(self.handle_ai_assistant_bounding_boxes)
            logger.info("Connected bounding_boxes_ready signal")
            self.ai_assistant_gemini_worker.error_occurred.connect(self.handle_ai_assistant_analysis_error)
            logger.info("Connected error_occurred signal")
            self.ai_assistant_gemini_worker.status_update.connect(self.update_ai_assistant_status)
            logger.info("Connected status_update signal")
        except Exception as e:
            logger.error(f"Error connecting signals: {e}")

        # Set up UI signal connections
        self.setup_ai_assistant_connections()
        logger.info("AI Assistant signal connections set up")

        # No loading indicator needed - we'll use the cursor instead

    def setup_ai_assistant_connections(self):
        """Sets up signal-slot connections for the AI Assistant page."""
        # Connect gallery signals
        self.ai_assistant_gallery.image_clicked.connect(self.on_ai_assistant_image_clicked)
        self.ai_assistant_gallery.remove_clicked.connect(self.on_ai_assistant_gallery_remove_clicked)
        
        # Clear gallery button
        if hasattr(self, 'clear_ai_assistant_gallery_button'):
            self.clear_ai_assistant_gallery_button.clicked.connect(self.clear_ai_assistant_gallery)

        # Connect UI control signals
        self.ai_assistant_prompt_selector.currentIndexChanged.connect(self.on_ai_assistant_prompt_template_selected)
        self.ai_assistant_analyze_button.clicked.connect(self.start_ai_assistant_analysis)
        self.ai_assistant_cancel_button.clicked.connect(self.cancel_ai_assistant_analysis)
        self.ai_assistant_settings_button.clicked.connect(self.show_gemini_settings_dialog)
        self.ai_assistant_structured_data_table.itemSelectionChanged.connect(self.highlight_ai_assistant_bbox_from_table)
        
        # Full view button connection is handled in app.py to ensure proper initialization
        # Connection removed from here to avoid duplicate connections

        # Connect Gemini worker signals
        self.ai_assistant_gemini_worker.result_ready.connect(self.handle_ai_assistant_text_result)
        self.ai_assistant_gemini_worker.structured_data_ready.connect(self.handle_ai_assistant_structured_data)
        self.ai_assistant_gemini_worker.bounding_boxes_ready.connect(self.handle_ai_assistant_bounding_boxes)
        self.ai_assistant_gemini_worker.error_occurred.connect(self.handle_ai_assistant_analysis_error)
        self.ai_assistant_gemini_worker.status_update.connect(self.update_ai_assistant_status)
        self.ai_assistant_gemini_worker.finished.connect(self.on_ai_assistant_analysis_finished)

        # Initialize prompt templates using the reload method from the UI class
        if hasattr(self, 'reload_custom_prompts'):
            self.reload_custom_prompts()
        else:
            # Fallback if the reload method is not available
            self.ai_assistant_prompt_selector.addItems(DEFAULT_PROMPTS.keys())

            # Load custom prompts
            try:
                from src.ai_assistant_components.src.gemini.custom_prompts import CustomPromptManager
                prompt_manager = CustomPromptManager()
                custom_prompts = prompt_manager.get_all_custom_prompts()
                if custom_prompts:
                    self.ai_assistant_prompt_selector.addItems(custom_prompts.keys())
                    logger.info(f"Loaded {len(custom_prompts)} custom prompts")
            except Exception as e:
                logger.error(f"Error loading custom prompts: {e}")

        self.on_ai_assistant_prompt_template_selected(0)  # Initialize with first template

    @Slot(int)
    def on_ai_assistant_image_clicked(self, index):
        """Handler for when an image is clicked in the AI Assistant gallery."""
        try:
            if index < 0 or index >= len(self.ai_assistant_gallery.images):
                return

            # Save current state if needed
            try:
                self.save_ai_assistant_state()
            except Exception as e:
                logger.error(f"Error saving AI Assistant state: {e}")

            # Get the selected image
            image = self.ai_assistant_gallery.images[index]
            image_filename = self.ai_assistant_gallery.filenames[index]
            image_full_path = self.ai_assistant_gallery.file_paths[index]

            # Set current image ID
            self.ai_assistant_current_image_id = image_filename

            # Display the image - ensure BGR to RGB conversion
            # The image from the gallery is already in RGB format (converted when loaded from file)
            self.ai_assistant_current_pixmap = convert_cvimage_to_qpixmap(image, already_rgb=True)

            # Check if image_item exists, recreate if needed
            if not hasattr(self, 'ai_assistant_image_item') or self.ai_assistant_image_item is None:
                logger.info("Creating new QGraphicsPixmapItem for AI Assistant image")
                self.ai_assistant_image_item = QGraphicsPixmapItem()
                self.ai_assistant_image_scene.addItem(self.ai_assistant_image_item)

            # Set the pixmap and update the view
            self.ai_assistant_image_item.setPixmap(self.ai_assistant_current_pixmap)
            self.ai_assistant_image_scene.setSceneRect(self.ai_assistant_current_pixmap.rect())
            self.ai_assistant_image_viewer.fitInView(self.ai_assistant_image_scene.sceneRect(), Qt.AspectRatioMode.KeepAspectRatio)

            # Clear previous results
            self.clear_ai_assistant_displays()

            # Load saved state and analysis results
            try:
                self.load_ai_assistant_state()
            except Exception as e:
                logger.error(f"Error loading AI Assistant state: {e}")

            try:
                self.load_ai_assistant_analysis()
            except Exception as e:
                logger.error(f"Error loading AI Assistant analysis: {e}")

            # Enable zoom buttons
            if hasattr(self, 'ai_assistant_zoom_in_button'):
                self.ai_assistant_zoom_in_button.setEnabled(True)
            if hasattr(self, 'ai_assistant_zoom_out_button'):
                self.ai_assistant_zoom_out_button.setEnabled(True)
            if hasattr(self, 'ai_assistant_fit_view_button'):
                self.ai_assistant_fit_view_button.setEnabled(True)

            # Update status
            self.update_ai_assistant_status(f"Loaded image: {image_filename}")
        except Exception as e:
            logger.error(f"Error in on_ai_assistant_image_clicked: {e}")

    @Slot(int)
    def on_ai_assistant_gallery_remove_clicked(self, index):
        """Handler for when a thumbnail X button is clicked in AI assistant gallery."""
        if not hasattr(self, 'ai_assistant_gallery') or not (0 <= index < len(self.ai_assistant_gallery.file_paths)):
            return
        
        image_path = self.ai_assistant_gallery.file_paths[index]
        filename = os.path.basename(image_path)
        
        # Confirm removal
        reply = QMessageBox.question(
            self, "Confirm Removal",
            f"Remove this image from the AI assistant gallery?\n{filename}",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # Manually remove from gallery UI without triggering the signal again
            if 0 <= index < len(self.ai_assistant_gallery.images):
                # Store the current selection state
                was_selected = (self.ai_assistant_gallery.selected_index == index)
                
                # Remove the thumbnail widget
                if index < len(self.ai_assistant_gallery.thumbnails):
                    thumbnail = self.ai_assistant_gallery.thumbnails.pop(index)
                    thumbnail.deleteLater()
                
                # Remove the image data
                self.ai_assistant_gallery.images.pop(index)
                self.ai_assistant_gallery.filenames.pop(index)
                self.ai_assistant_gallery.file_paths.pop(index)
                
                # Update the selected index
                if was_selected:
                    if len(self.ai_assistant_gallery.images) > 0:
                        new_index = min(index, len(self.ai_assistant_gallery.images) - 1)
                        self.ai_assistant_gallery.selected_index = new_index
                    else:
                        self.ai_assistant_gallery.selected_index = -1
                elif self.ai_assistant_gallery.selected_index > index:
                    self.ai_assistant_gallery.selected_index -= 1
                
                # Update all thumbnail indices
                for i, thumbnail in enumerate(self.ai_assistant_gallery.thumbnails):
                    thumbnail.index = i
                
                # Update thumbnail styling
                for i, thumbnail in enumerate(self.ai_assistant_gallery.thumbnails):
                    thumbnail.set_selected(i == self.ai_assistant_gallery.selected_index)
                
                # Update the count label
                self.ai_assistant_gallery.update_count_label()
                
                # Update image display/preview after removal
                if self.ai_assistant_gallery.selected_index >= 0 and len(self.ai_assistant_gallery.images) > 0:
                    # Load the newly selected image
                    self.on_ai_assistant_image_clicked(self.ai_assistant_gallery.selected_index)
                else:
                    # No images left, clear the display
                    self.ai_assistant_current_image_id = None
                    self.ai_assistant_current_pixmap = None
                    if hasattr(self, 'ai_assistant_image_view') and self.ai_assistant_image_view:
                        self.ai_assistant_image_view.clear()
            
            logger.info(f"Successfully removed image from AI assistant gallery: {filename}")

    @Slot(int)
    def on_ai_assistant_prompt_template_selected(self, index):
        """Updates the prompt input when a template is selected."""
        if index < 0 or index >= self.ai_assistant_prompt_selector.count():
            logger.warning(f"Invalid prompt template index: {index}")
            return

        template_name = self.ai_assistant_prompt_selector.currentText()
        logger.info(f"Selected prompt template: {template_name}")

        # Check if this is a default prompt
        if template_name in DEFAULT_PROMPTS:
            prompt_text = DEFAULT_PROMPTS.get(template_name, "")
            is_custom = (template_name == "Custom Prompt")

            self.ai_assistant_prompt_input.setReadOnly(not is_custom)
            if not is_custom:
                self.ai_assistant_prompt_input.setText(prompt_text)
                # Clear context when selecting a default prompt
                if hasattr(self, 'ai_assistant_context_input'):
                    self.ai_assistant_context_input.clear()
            else:
                # Don't clear custom prompt if user switches back to it
                if not self.ai_assistant_prompt_input.toPlainText() or self.ai_assistant_prompt_input.toPlainText() in DEFAULT_PROMPTS.values():
                    self.ai_assistant_prompt_input.setPlaceholderText("Enter your custom prompt here...")
        else:
            # This is a custom prompt from the CustomPromptManager
            try:
                from src.ai_assistant_components.src.gemini.custom_prompts import CustomPromptManager

                # Get the custom prompt
                prompt_manager = CustomPromptManager()
                prompt_data = prompt_manager.get_custom_prompt(template_name)

                logger.info(f"Loading custom prompt: {template_name}")
                logger.debug(f"Custom prompt data: {prompt_data}")

                if prompt_data:
                    # Set the prompt text
                    self.ai_assistant_prompt_input.setText(prompt_data.get('prompt', ''))
                    logger.info(f"Set prompt text: {prompt_data.get('prompt', '')[:50]}..." if len(prompt_data.get('prompt', '')) > 50 else f"Set prompt text: {prompt_data.get('prompt', '')}")

                    # Set the context if available
                    if hasattr(self, 'ai_assistant_context_input'):
                        context = prompt_data.get('context', '')
                        self.ai_assistant_context_input.setText(context)
                        logger.info(f"Set context: {context[:50]}..." if len(context) > 50 else f"Set context: {context}")

                    # Make the prompt input read-only for custom templates
                    self.ai_assistant_prompt_input.setReadOnly(True)
                    logger.info("Set prompt input to read-only")
                else:
                    logger.warning(f"Custom prompt not found: {template_name}")
            except Exception as e:
                logger.error(f"Error loading custom prompt: {e}")

    @staticmethod
    def start_ai_assistant_analysis(self_or_app):
        """Starts the Gemini analysis process."""
        logger.info("Starting AI Assistant analysis")

        # Check if API key is set
        if hasattr(self_or_app, 'ai_assistant_api_key'):
            if not self_or_app.ai_assistant_api_key or len(self_or_app.ai_assistant_api_key.strip()) == 0:
                logger.error("API key is not set")
                QMessageBox.critical(self_or_app, "Error", "API key is not set. Please set your Google Gemini API key in Settings.")
                return
            else:
                logger.info(f"Using API key: {self_or_app.ai_assistant_api_key[:5]}...{self_or_app.ai_assistant_api_key[-5:] if len(self_or_app.ai_assistant_api_key) > 10 else ''}")

        # if hasattr(self_or_app, 'ai_assistant_gemini_worker'):
        #     # Check if the worker is actually running (not just initialized)
        #     if self_or_app.ai_assistant_gemini_worker.isRunning() and hasattr(self_or_app, '_analysis_started') and self_or_app._analysis_started:
        #         logger.warning("Analysis already running")
        #         QMessageBox.information(self_or_app, "Analysis Running",
        #                               "An analysis is already in progress. Please use the Cancel button to stop it before starting a new one.")
        #         return
        #     elif self_or_app.ai_assistant_gemini_worker.isRunning():
        #         # Worker is running but we don't have the flag set, just reset it
        #         logger.info("Worker is running but no analysis flag, resetting worker")
        #         self_or_app.ai_assistant_gemini_worker.reset()
        # else:
        #     logger.error("Gemini worker not found")
        #     QMessageBox.critical(self_or_app, "Error", "AI Assistant not properly initialized.")
        #     return

        # Check for required dependencies
        if hasattr(self_or_app, 'missing_packages') and self_or_app.missing_packages:
            logger.error(f"Missing dependencies: {self_or_app.missing_packages}")
            instructions = get_installation_instructions(self_or_app.missing_packages)
            QMessageBox.critical(self_or_app, "Missing Dependencies",
                               f"Cannot start analysis. The following dependencies are missing:\n\n{instructions}")
            return

        # Re-check API key
        if hasattr(self_or_app, 'settings'):
            self_or_app.ai_assistant_api_key = self_or_app.settings.value("gemini/api_key", "")
            logger.info(f"API key present: {bool(self_or_app.ai_assistant_api_key)}")

            if not self_or_app.ai_assistant_api_key:
                logger.error("API key missing")
                QMessageBox.critical(self_or_app, "API Key Missing",
                                   "Cannot start analysis. Please set your Google Gemini API Key in Settings.")
                # Show the settings dialog
                AIAssistantHandlers.show_gemini_settings_dialog(self_or_app)
                return
        else:
            logger.error("Settings object not found")
            QMessageBox.critical(self_or_app, "Error", "Settings not properly initialized.")
            return

        if not hasattr(self_or_app, 'ai_assistant_current_image_id') or not self_or_app.ai_assistant_current_image_id or \
           not hasattr(self_or_app, 'ai_assistant_current_pixmap') or not self_or_app.ai_assistant_current_pixmap:
            logger.warning("No image selected")
            QMessageBox.warning(self_or_app, "No Image", "Please select a valid image first.")
            return

        # Get prompt and context from input
        prompt = ""
        context = ""

        # Get context if available
        if hasattr(self_or_app, 'ai_assistant_context_input'):
            context = self_or_app.ai_assistant_context_input.toPlainText().strip()
            if context:
                logger.info(f"Context: {context[:50]}..." if len(context) > 50 else f"Context: {context}")

        # Get prompt from input
        if hasattr(self_or_app, 'ai_assistant_prompt_input'):
            prompt_text = self_or_app.ai_assistant_prompt_input.toPlainText().strip()
            logger.info(f"Prompt: {prompt_text[:50]}..." if len(prompt_text) > 50 else f"Prompt: {prompt_text}")

            # Check if a template is selected
            template_selected = False
            selected_template = ""
            if hasattr(self_or_app, 'ai_assistant_prompt_selector'):
                selected_template = self_or_app.ai_assistant_prompt_selector.currentText()
                if selected_template and selected_template != "Custom Prompt":
                    template_selected = True
                    logger.info(f"Template selected: {selected_template}")

                    # If template is selected but prompt is empty, use the template text
                    if not prompt_text and selected_template in DEFAULT_PROMPTS:
                        prompt_text = DEFAULT_PROMPTS.get(selected_template, "")
                        logger.info(f"Using template text: {prompt_text[:50]}..." if len(prompt_text) > 50 else f"Using template text: {prompt_text}")
                    elif not prompt_text:
                        # Try to get custom prompt
                        try:
                            from src.ai_assistant_components.src.gemini.custom_prompts import CustomPromptManager
                            prompt_manager = CustomPromptManager()
                            prompt_data = prompt_manager.get_custom_prompt(selected_template)
                            if prompt_data and 'prompt' in prompt_data:
                                prompt_text = prompt_data['prompt']
                                logger.info(f"Using custom template text: {prompt_text[:50]}..." if len(prompt_text) > 50 else f"Using custom template text: {prompt_text}")
                        except Exception as e:
                            logger.error(f"Error loading custom prompt: {e}")

            # Only show warning if no prompt text and no template selected
            if not prompt_text and not template_selected:
                logger.warning("Empty prompt and no template selected")
                QMessageBox.warning(self_or_app, "Empty Prompt", "Please enter a prompt or select a template.")
                return

            # Combine context and prompt if context is provided
            if context:
                prompt = f"Context: {context}\n\nPrompt: {prompt_text}"
            else:
                prompt = prompt_text
        else:
            logger.error("Prompt input not found")
            QMessageBox.critical(self_or_app, "Error", "Prompt input not properly initialized.")
            return

        # Get image path
        if hasattr(self_or_app, 'ai_assistant_gallery'):
            image_index = self_or_app.ai_assistant_gallery.selected_index
            logger.info(f"Selected image index: {image_index}")

            if image_index < 0 or image_index >= len(self_or_app.ai_assistant_gallery.file_paths):
                logger.error(f"Invalid image index: {image_index}, gallery size: {len(self_or_app.ai_assistant_gallery.file_paths)}")
                QMessageBox.warning(self_or_app, "Invalid Image", "Cannot determine image path.")
                return

            image_path = self_or_app.ai_assistant_gallery.file_paths[image_index]
            logger.info(f"Image path: {image_path}")

            if not os.path.exists(image_path):
                logger.error(f"Image file not found: {image_path}")
                QMessageBox.warning(self_or_app, "File Not Found", f"Image file not found: {image_path}")
                return
        else:
            logger.error("Gallery not found")
            QMessageBox.critical(self_or_app, "Error", "Image gallery not properly initialized.")
            return

        # Get template name
        if hasattr(self_or_app, 'ai_assistant_prompt_selector'):
            template_name = self_or_app.ai_assistant_prompt_selector.currentText()

            # Determine if JSON output is expected
            request_json = "JSON" in template_name

            logger.info(f"Starting analysis for: {self_or_app.ai_assistant_current_image_id}")
            logger.info(f"Prompt template: {template_name}")
            logger.info(f"Request JSON: {request_json}")

            # Clear previous results
            if hasattr(self_or_app, 'clear_ai_assistant_displays'):
                self_or_app.clear_ai_assistant_displays()

            # Save current state
            try:
                if hasattr(self_or_app, 'save_ai_assistant_state'):
                    self_or_app.save_ai_assistant_state()
            except Exception as e:
                logger.error(f"Error saving state: {e}")

            # Update worker with current API key
            logger.info("Setting API key and task for worker")
            if hasattr(self_or_app, 'ai_assistant_gemini_worker'):
                self_or_app.ai_assistant_gemini_worker.set_api_key(self_or_app.ai_assistant_api_key)
                # Use a 60-second timeout for the API call (longer for newer models)
                timeout = 60
                # Use a longer timeout for Gemini 2.5 models
                if "gemini-2.5" in self_or_app.ai_assistant_gemini_worker.model_name:
                    timeout = 120  # 2 minutes for Gemini 2.5 models
                logger.info(f"Using timeout of {timeout} seconds for model {self_or_app.ai_assistant_gemini_worker.model_name}")
                self_or_app.ai_assistant_gemini_worker.set_task(image_path, prompt, request_json, timeout=timeout)

                # Update UI state
                logger.info("Updating UI state")
                if hasattr(self_or_app, 'ai_assistant_analyze_button'):
                    self_or_app.ai_assistant_analyze_button.setEnabled(False)
                if hasattr(self_or_app, 'ai_assistant_cancel_button'):
                    self_or_app.ai_assistant_cancel_button.setEnabled(True)

                # Set waiting cursor
                QApplication.setOverrideCursor(Qt.CursorShape.WaitCursor)

                # Start the worker thread
                logger.info(f"Starting worker thread with model: {self_or_app.ai_assistant_gemini_worker.model_name}")

                # Check if the worker has the necessary signals connected
                if hasattr(self_or_app.ai_assistant_gemini_worker, 'result_ready'):
                    logger.info("Worker has result_ready signal")
                    # Check if the signal is connected to the handler
                    try:
                        # Try to connect the signal to the handler
                        self_or_app.ai_assistant_gemini_worker.result_ready.connect(self_or_app.handle_ai_assistant_text_result)
                        logger.info("Connected result_ready signal to handle_ai_assistant_text_result")
                    except Exception as e:
                        logger.error(f"Error connecting result_ready signal: {e}")
                else:
                    logger.error("Worker does not have result_ready signal")

                # Set the analysis started flag
                self_or_app._analysis_started = True

                # Start the worker
                self_or_app.ai_assistant_gemini_worker.start()
                logger.info("Worker thread started")
            else:
                logger.error("Gemini worker not found")
                QMessageBox.critical(self_or_app, "Error", "AI Assistant not properly initialized.")
        else:
            logger.error("Prompt selector not found")
            QMessageBox.critical(self_or_app, "Error", "Prompt selector not properly initialized.")

    @staticmethod
    def cancel_ai_assistant_analysis(self_or_app):
        """Cancels the ongoing analysis."""
        if hasattr(self_or_app, 'ai_assistant_gemini_worker') and self_or_app.ai_assistant_gemini_worker.isRunning():
            self_or_app.ai_assistant_gemini_worker.cancel()

            # Reset the analysis started flag
            if hasattr(self_or_app, '_analysis_started'):
                self_or_app._analysis_started = False

            # Update UI state immediately
            if hasattr(self_or_app, 'ai_assistant_analyze_button'):
                self_or_app.ai_assistant_analyze_button.setEnabled(True)
            if hasattr(self_or_app, 'ai_assistant_cancel_button'):
                self_or_app.ai_assistant_cancel_button.setEnabled(False)

            # Restore normal cursor
            QApplication.restoreOverrideCursor()

            if hasattr(self_or_app, 'update_ai_assistant_status'):
                self_or_app.update_ai_assistant_status("Analysis cancelled.")
            else:
                logger.info("Analysis cancelled.")

    def clear_ai_assistant_displays(self):
        """Clears all result displays."""
        # Clear text results
        self.ai_assistant_text_results.clear()

        # Clear structured data table
        self.ai_assistant_structured_data_table.setRowCount(0)

        # Clear bounding boxes
        self.clear_ai_assistant_bounding_boxes()
        
        # Disable Full View button when displays are cleared
        if hasattr(self, 'ai_assistant_full_view_button'):
            self.ai_assistant_full_view_button.setEnabled(False)

        # Disable zoom buttons if no image is loaded
        if not hasattr(self, 'ai_assistant_current_pixmap') or self.ai_assistant_current_pixmap is None or self.ai_assistant_current_pixmap.isNull():
            if hasattr(self, 'ai_assistant_zoom_in_button'):
                self.ai_assistant_zoom_in_button.setEnabled(False)
            if hasattr(self, 'ai_assistant_zoom_out_button'):
                self.ai_assistant_zoom_out_button.setEnabled(False)
            if hasattr(self, 'ai_assistant_fit_view_button'):
                self.ai_assistant_fit_view_button.setEnabled(False)

    def clear_ai_assistant_bounding_boxes(self):
        """Removes all bounding boxes from the image viewer."""
        for box in self.ai_assistant_bounding_boxes:
            self.ai_assistant_image_scene.removeItem(box['rect'])
            self.ai_assistant_image_scene.removeItem(box['label'])
        self.ai_assistant_bounding_boxes = []

    @Slot(str)
    def handle_ai_assistant_text_result(self, text):
        """Handles the text result from the Gemini API."""
        logger.info(f"Received text result from Gemini API: {len(text)} characters")
        logger.info(f"Text preview: {text[:100]}..." if len(text) > 100 else f"Text: {text}")

        # Check if the text results widget exists
        if hasattr(self, 'ai_assistant_text_results'):
            logger.info("Setting text in ai_assistant_text_results widget")

            # Check if we're using the markdown widget
            if hasattr(self, 'markdown_text_edit') and self.ai_assistant_text_results == self.markdown_text_edit:
                logger.info("Using markdown rendering")
                self.markdown_text_edit.setMarkdown(text)
            else:
                # Fall back to plain text
                logger.info("Using plain text rendering")
                self.ai_assistant_text_results.setText(text)
        else:
            logger.error("ai_assistant_text_results widget not found")

        # Save the result
        if self.ai_assistant_current_image_id:
            self.save_ai_assistant_analysis_results()

        # Enable Full View button when results are available
        if hasattr(self, 'ai_assistant_full_view_button'):
            self.ai_assistant_full_view_button.setEnabled(bool(text.strip()))

        # Update UI state
        self.ai_assistant_analyze_button.setEnabled(True)
        self.ai_assistant_cancel_button.setEnabled(False)

        # Restore normal cursor (ensure it's restored even if multiple wait cursors were set)
        while QApplication.overrideCursor() is not None:
            QApplication.restoreOverrideCursor()

        # Update status
        self.update_ai_assistant_status("Analysis complete.")

    @Slot(list)
    def handle_ai_assistant_structured_data(self, data):
        """Handles structured data from the Gemini API.

        Dynamically creates table columns based on the JSON structure.
        """
        # Clear existing data
        self.ai_assistant_structured_data_table.setRowCount(0)

        # If no data, just return
        if not data:
            logger.info("No structured data received")
            return

        # Determine the columns dynamically based on the first item
        # (assuming all items have similar structure)
        first_item = data[0]
        logger.info(f"First item keys: {list(first_item.keys()) if isinstance(first_item, dict) else 'Not a dict'}")

        # Collect all unique keys from all items
        all_keys = set()
        for item in data:
            if isinstance(item, dict):
                # Add top-level keys
                all_keys.update(item.keys())
                # Add keys from attributes if it exists and is a dict
                if 'attributes' in item and isinstance(item['attributes'], dict):
                    for attr_key in item['attributes'].keys():
                        all_keys.add(f"attributes.{attr_key}")

        # Remove special keys that we handle differently
        if 'box_2d' in all_keys:
            all_keys.remove('box_2d')
        if 'attributes' in all_keys:
            all_keys.remove('attributes')

        # Ensure 'label' is the first column if it exists
        column_keys = list(all_keys)
        if 'label' in column_keys:
            column_keys.remove('label')
            column_keys.insert(0, 'label')

        # Set up the table columns
        self.ai_assistant_structured_data_table.setColumnCount(len(column_keys))
        self.ai_assistant_structured_data_table.setHorizontalHeaderLabels(column_keys)

        # Make the last column stretch
        self.ai_assistant_structured_data_table.horizontalHeader().setSectionResizeMode(
            len(column_keys) - 1, QHeaderView.ResizeMode.Stretch)

        # Populate table with new data
        for i, item in enumerate(data):
            if not isinstance(item, dict):
                logger.warning(f"Item {i} is not a dictionary, skipping")
                continue

            self.ai_assistant_structured_data_table.insertRow(i)

            # Fill in each column
            for col, key in enumerate(column_keys):
                value = ""

                # Handle nested attributes
                if '.' in key and key.startswith('attributes.'):
                    attr_key = key.split('.')[1]
                    if 'attributes' in item and isinstance(item['attributes'], dict):
                        value = item['attributes'].get(attr_key, '')
                else:
                    # Handle regular keys
                    value = item.get(key, '')

                # Convert value to string if needed
                if not isinstance(value, str):
                    value = str(value)

                # Create table item
                table_item = QTableWidgetItem(value)
                self.ai_assistant_structured_data_table.setItem(i, col, table_item)

                # Store the bounding box data in the first column item for compatibility
                if col == 0 and 'box_2d' in item:
                    table_item.setData(Qt.ItemDataRole.UserRole, item.get('box_2d', [0, 0, 0, 0]))

        # Save the result
        if self.ai_assistant_current_image_id:
            self.save_ai_assistant_analysis_results()

    @Slot(list)
    def handle_ai_assistant_bounding_boxes(self, boxes):
        """Handles bounding box data from the Gemini API."""
        # Clear existing boxes
        self.clear_ai_assistant_bounding_boxes()

        if not self.ai_assistant_current_pixmap:
            return

        # Check if image_item exists, recreate if needed
        if not hasattr(self, 'ai_assistant_image_item') or self.ai_assistant_image_item is None:
            logger.info("Creating new QGraphicsPixmapItem for AI Assistant image in handle_ai_assistant_bounding_boxes")
            self.ai_assistant_image_item = QGraphicsPixmapItem()
            self.ai_assistant_image_scene.addItem(self.ai_assistant_image_item)
            self.ai_assistant_image_item.setPixmap(self.ai_assistant_current_pixmap)

        # Get image dimensions - need to handle view scaling properly
        pixmap_width = self.ai_assistant_current_pixmap.width()
        pixmap_height = self.ai_assistant_current_pixmap.height()
        
        # Get scene rect and view transform
        scene_rect = self.ai_assistant_image_scene.sceneRect()
        view_transform = self.ai_assistant_image_viewer.transform()
        
        # Debug prints
        print(f"DEBUG: Pixmap dimensions: {pixmap_width}x{pixmap_height}")
        print(f"DEBUG: Scene rect: {scene_rect.width()}x{scene_rect.height()}")
        print(f"DEBUG: View transform: m11={view_transform.m11()}, m22={view_transform.m22()}")
        
        # Use pixmap dimensions as the base for coordinate calculations
        # The scene coordinates should match pixmap coordinates
        img_width = pixmap_width
        img_height = pixmap_height

        # Draw new boxes
        for i, box in enumerate(boxes):
            # Handle both old format (just coordinates) and new format (full objects)
            if isinstance(box, dict) and 'box_2d' in box:
                # New format: extract coordinates and label
                coords = box['box_2d']
                label_text = box.get('label', 'Object')
            else:
                # Old format: box is just coordinates
                coords = box
                label_text = "Object"
                if i < self.ai_assistant_structured_data_table.rowCount():
                    label_text = self.ai_assistant_structured_data_table.item(i, 0).text()

            # Unpack coordinates [ymin, xmin, ymax, xmax]
            ymin, xmin, ymax, xmax = coords
            
            print(f"DEBUG Box {i}: Original coords: ymin={ymin}, xmin={xmin}, ymax={ymax}, xmax={xmax}")
            
            # Detect coordinate format: normalized (0-1) vs absolute (0-999)
            max_coord = max(ymin, xmin, ymax, xmax)
            if max_coord <= 1.0:
                print(f"DEBUG Box {i}: Detected normalized coordinates (0-1 range)")
                # Coordinates are already normalized, convert directly to pixels
                ymin_pixel = ymin * img_height
                xmin_pixel = xmin * img_width
                ymax_pixel = ymax * img_height
                xmax_pixel = xmax * img_width
            else:
                print(f"DEBUG Box {i}: Detected absolute coordinates (0-999 range)")
                # Convert from 0-999 coordinate system to actual image pixel coordinates
                ymin_pixel = (ymin / 999.0) * img_height
                xmin_pixel = (xmin / 999.0) * img_width
                ymax_pixel = (ymax / 999.0) * img_height
                xmax_pixel = (xmax / 999.0) * img_width
            
            print(f"DEBUG Box {i}: After coordinate conversion: ymin_pixel={ymin_pixel}, xmin_pixel={xmin_pixel}, ymax_pixel={ymax_pixel}, xmax_pixel={xmax_pixel}")
            
            # Clamp coordinates to image bounds
            ymin_pixel = max(0, min(img_height - 1, ymin_pixel))
            xmin_pixel = max(0, min(img_width - 1, xmin_pixel))
            ymax_pixel = max(0, min(img_height - 1, ymax_pixel))
            xmax_pixel = max(0, min(img_width - 1, xmax_pixel))
            
            # Ensure min coordinates are less than max coordinates and minimum size
            if ymin_pixel >= ymax_pixel:
                ymax_pixel = min(img_height - 1, ymin_pixel + 5)  # Minimum 5 pixel height
            if xmin_pixel >= xmax_pixel:
                xmax_pixel = min(img_width - 1, xmin_pixel + 5)  # Minimum 5 pixel width

            print(f"DEBUG Box {i}: After clamping: ymin_pixel={ymin_pixel}, xmin_pixel={xmin_pixel}, ymax_pixel={ymax_pixel}, xmax_pixel={xmax_pixel}")

            # Use pixel coordinates directly
            x = xmin_pixel
            y = ymin_pixel
            width = xmax_pixel - xmin_pixel
            height = ymax_pixel - ymin_pixel
            
            print(f"DEBUG Box {i}: Final pixel coords: x={x}, y={y}, width={width}, height={height}")

            # Get color (cycle through available colors)
            color = BOX_COLORS[i % len(BOX_COLORS)]

            # Create rectangle item
            rect = QGraphicsRectItem(x, y, width, height)
            rect.setPen(QPen(color, 2))
            rect.setBrush(QBrush(QColor(color.red(), color.green(), color.blue(), 50)))
            self.ai_assistant_image_scene.addItem(rect)

            # Create label item
            label = QGraphicsSimpleTextItem(label_text)
            label.setBrush(QBrush(color))
            label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
            label.setPos(x, y - 20)  # Position above the box
            self.ai_assistant_image_scene.addItem(label)

            # Store references to graphics items
            self.ai_assistant_bounding_boxes.append({
                'rect': rect,
                'label': label,
                'data': box
            })

    @Slot(str)
    def handle_ai_assistant_analysis_error(self, error_message):
        """Handles errors from the Gemini API."""
        QMessageBox.critical(self, "Analysis Error", error_message)
        self.update_ai_assistant_status(f"Error: {error_message}")

        # Reset UI state
        self.ai_assistant_analyze_button.setEnabled(True)
        self.ai_assistant_cancel_button.setEnabled(False)

        # Restore normal cursor (ensure it's restored even if multiple wait cursors were set)
        while QApplication.overrideCursor() is not None:
            QApplication.restoreOverrideCursor()

        # Reset the analysis started flag
        self._analysis_started = False

    @Slot(str)
    def update_ai_assistant_status(self, message):
        """Updates the status label."""
        self.ai_assistant_status_label.setText(message)
        # Also update main status bar if available
        if hasattr(self, 'statusBar'):
            self.statusBar().showMessage(message, 3000)

    @Slot()
    def on_ai_assistant_analysis_finished(self):
        """Called when the worker thread finishes."""
        self.ai_assistant_analyze_button.setEnabled(True)
        self.ai_assistant_cancel_button.setEnabled(False)

        # Restore normal cursor (ensure it's restored even if multiple wait cursors were set)
        while QApplication.overrideCursor() is not None:
            QApplication.restoreOverrideCursor()

        self.update_ai_assistant_status("Analysis finished.")

        # Reset the analysis started flag
        self._analysis_started = False

        # Save the analysis results
        try:
            self.save_ai_assistant_analysis_results()
            logger.info("Analysis results saved automatically after completion")
        except Exception as e:
            logger.error(f"Error saving analysis results: {e}")

    @Slot()
    def highlight_ai_assistant_bbox_from_table(self):
        """Highlights the bounding box corresponding to the selected table row."""
        selected_rows = self.ai_assistant_structured_data_table.selectedItems()
        if not selected_rows:
            return

        # Reset all boxes to normal appearance
        for box in self.ai_assistant_bounding_boxes:
            box['rect'].setPen(QPen(box['rect'].pen().color(), 2))

        # Get the selected row
        row = selected_rows[0].row()
        if row < len(self.ai_assistant_bounding_boxes):
            # Highlight the selected box
            self.ai_assistant_bounding_boxes[row]['rect'].setPen(QPen(self.ai_assistant_bounding_boxes[row]['rect'].pen().color(), 4))

    def save_ai_assistant_state(self):
        """Saves the current state of the AI Assistant page."""
        if not self.ai_assistant_current_image_id:
            return

        # Create state dictionary
        state = {
            'last_prompt': self.ai_assistant_prompt_input.toPlainText(),
            'last_template': self.ai_assistant_prompt_selector.currentText(),
            'timestamp': str(QDateTime.currentDateTime().toString())
        }

        # Add context if available
        if hasattr(self, 'ai_assistant_context_input'):
            state['last_context'] = self.ai_assistant_context_input.toPlainText()

        # Save to project state
        if hasattr(self, 'current_project') and self.current_project:
            try:
                # Use the project's save_ai_assistant_state method
                image_id = os.path.basename(self.ai_assistant_current_image_id)
                self.current_project.save_ai_assistant_state(image_id, state)
                logger.info(f"AI Assistant state saved for {image_id} using project method")
            except Exception as e:
                logger.error(f"Error saving AI Assistant state using project method: {e}")

                # Fallback to direct file saving
                try:
                    # Get project directory
                    project_dir = os.path.dirname(self.current_project.project_file) if hasattr(self.current_project, 'project_file') else None
                    if not project_dir:
                        logger.error("Cannot determine project directory")
                        return

                    # Create state directory if it doesn't exist
                    state_dir = os.path.join(project_dir, 'state', 'ai_assistant')
                    os.makedirs(state_dir, exist_ok=True)

                    # Save state file
                    state_file = os.path.join(state_dir, f"{self.ai_assistant_current_image_id}.json")
                    with open(state_file, 'w') as f:
                        json.dump(state, f, indent=2)
                    logger.info(f"AI Assistant state saved for {self.ai_assistant_current_image_id} using direct file saving")
                except Exception as e2:
                    logger.error(f"Error saving AI Assistant state using direct file saving: {e2}")

    def load_ai_assistant_state(self):
        """Loads the saved state for the current image."""
        if not self.ai_assistant_current_image_id or not hasattr(self, 'current_project') or not self.current_project:
            return

        # Try to load state using the project method
        state = None
        try:
            # Use the project's load_ai_assistant_state method
            image_id = os.path.basename(self.ai_assistant_current_image_id)
            state = self.current_project.load_ai_assistant_state(image_id)
            if state:
                logger.info(f"AI Assistant state loaded for {image_id} using project method")
        except Exception as e:
            logger.error(f"Error loading AI Assistant state using project method: {e}")

        # Fallback to direct file loading if project method failed
        if not state:
            try:
                # Get project directory
                project_dir = os.path.dirname(self.current_project.project_file) if hasattr(self.current_project, 'project_file') else None
                if not project_dir:
                    logger.error("Cannot determine project directory")
                    return

                # Get state file path
                state_file = os.path.join(project_dir, 'state', 'ai_assistant', f"{self.ai_assistant_current_image_id}.json")
                if not os.path.exists(state_file):
                    return

                # Load state
                with open(state_file, 'r') as f:
                    state = json.load(f)
                logger.info(f"AI Assistant state loaded for {self.ai_assistant_current_image_id} using direct file loading")
            except Exception as e2:
                logger.error(f"Error loading AI Assistant state using direct file loading: {e2}")
                return

        # Apply state if we have it
        if state:
            try:
                # Apply state
                prompt_text = state.get('last_prompt', '')
                template_name = state.get('last_template', list(DEFAULT_PROMPTS.keys())[0])

                self.ai_assistant_prompt_input.setText(prompt_text)
                index = self.ai_assistant_prompt_selector.findText(template_name)
                if index != -1:
                    self.ai_assistant_prompt_selector.setCurrentIndex(index)

                # Load context if available
                if hasattr(self, 'ai_assistant_context_input') and 'last_context' in state:
                    self.ai_assistant_context_input.setText(state.get('last_context', ''))

                logger.info(f"Applied AI Assistant state for {self.ai_assistant_current_image_id}")
            except Exception as e:
                logger.error(f"Error applying AI Assistant state: {e}")

    def save_ai_assistant_analysis_results(self):
        """Saves the analysis results for the current image."""
        if not self.ai_assistant_current_image_id or not hasattr(self, 'current_project') or not self.current_project:
            return

        # Create results dictionary
        results = {
            'text_result': self.ai_assistant_text_results.toPlainText(),
            'structured_data': [],
            'timestamp': str(QDateTime.currentDateTime().toString())
        }

        # Get column headers
        column_count = self.ai_assistant_structured_data_table.columnCount()
        column_headers = []
        for col in range(column_count):
            header = self.ai_assistant_structured_data_table.horizontalHeaderItem(col).text()
            column_headers.append(header)

        logger.info(f"Saving structured data with columns: {column_headers}")

        # Add structured data
        for i in range(self.ai_assistant_structured_data_table.rowCount()):
            # Create a new item dictionary
            item = {}

            # Add all column values
            for col in range(column_count):
                header = column_headers[col]
                cell_item = self.ai_assistant_structured_data_table.item(i, col)
                value = cell_item.text() if cell_item else ""

                # Handle nested attributes
                if '.' in header and header.startswith('attributes.'):
                    attr_key = header.split('.')[1]
                    if 'attributes' not in item:
                        item['attributes'] = {}
                    item['attributes'][attr_key] = value
                else:
                    # Regular key
                    item[header] = value

            # Add bounding box if available
            if i < len(self.ai_assistant_bounding_boxes):
                item['box_2d'] = self.ai_assistant_bounding_boxes[i]['data']

            results['structured_data'].append(item)

        # Try to save using the project method
        try:
            # Use the project's save_ai_analysis_results method
            image_id = os.path.basename(self.ai_assistant_current_image_id)
            self.current_project.save_ai_analysis_results(image_id, results)
            logger.info(f"AI Assistant results saved for {image_id} using project method")
        except Exception as e:
            logger.error(f"Error saving AI Assistant results using project method: {e}")

            # Fallback to direct file saving
            try:
                # Get project directory
                project_dir = os.path.dirname(self.current_project.project_file) if hasattr(self.current_project, 'project_file') else None
                if not project_dir:
                    logger.error("Cannot determine project directory")
                    return

                # Create results directory if it doesn't exist
                # Note: We're saving to state/ai_assistant instead of results/ai_assistant to be consistent with other pages
                results_dir = os.path.join(project_dir, 'state', 'ai_assistant')
                os.makedirs(results_dir, exist_ok=True)

                # Save results file
                results_file = os.path.join(results_dir, f"{self.ai_assistant_current_image_id}_results.json")
                with open(results_file, 'w') as f:
                    json.dump(results, f, indent=2)
                logger.info(f"AI Assistant results saved for {self.ai_assistant_current_image_id} using direct file saving")
            except Exception as e2:
                logger.error(f"Error saving AI Assistant results using direct file saving: {e2}")

    def load_ai_assistant_analysis(self):
        """Loads saved analysis results for the current image."""
        if not self.ai_assistant_current_image_id or not hasattr(self, 'current_project') or not self.current_project:
            return

        # Check if image_item exists, recreate if needed
        if not hasattr(self, 'ai_assistant_image_item') or self.ai_assistant_image_item is None:
            if hasattr(self, 'ai_assistant_current_pixmap') and self.ai_assistant_current_pixmap is not None:
                logger.info("Creating new QGraphicsPixmapItem for AI Assistant image in load_ai_assistant_analysis")
                self.ai_assistant_image_item = QGraphicsPixmapItem()
                self.ai_assistant_image_scene.addItem(self.ai_assistant_image_item)
                self.ai_assistant_image_item.setPixmap(self.ai_assistant_current_pixmap)

        # Try to load results using the project method
        results = None
        try:
            # Use the project's load_ai_analysis_results method
            image_id = os.path.basename(self.ai_assistant_current_image_id)
            results = self.current_project.load_ai_analysis_results(image_id)
            if results:
                logger.info(f"AI Assistant results loaded for {image_id} using project method")
        except Exception as e:
            logger.error(f"Error loading AI Assistant results using project method: {e}")

        # Fallback to direct file loading if project method failed
        if not results:
            try:
                # Get project directory
                project_dir = os.path.dirname(self.current_project.project_file) if hasattr(self.current_project, 'project_file') else None
                if not project_dir:
                    logger.error("Cannot determine project directory")
                    return

                # Try first in the state directory (new location)
                results_file = os.path.join(project_dir, 'state', 'ai_assistant', f"{self.ai_assistant_current_image_id}_results.json")

                # If not found, try in the results directory (old location)
                if not os.path.exists(results_file):
                    results_file = os.path.join(project_dir, 'results', 'ai_assistant', f"{self.ai_assistant_current_image_id}.json")

                if not os.path.exists(results_file):
                    logger.info(f"No AI Assistant results found for {self.ai_assistant_current_image_id}")
                    return

                # Load results
                with open(results_file, 'r') as f:
                    results = json.load(f)
                logger.info(f"AI Assistant results loaded for {self.ai_assistant_current_image_id} using direct file loading")
            except Exception as e2:
                logger.error(f"Error loading AI Assistant results using direct file loading: {e2}")
                return

        # Apply results if we have them
        if results:
            try:
                # Apply results
                self.handle_ai_assistant_text_result(results.get('text_result', ''))
                structured_data = results.get('structured_data', [])
                self.handle_ai_assistant_structured_data(structured_data)

                # Extract and display bounding boxes
                boxes = []
                for item in structured_data:
                    if 'box_2d' in item:
                        boxes.append(item['box_2d'])
                self.handle_ai_assistant_bounding_boxes(boxes)

                logger.info(f"Applied AI Assistant results for {self.ai_assistant_current_image_id}")
                self.update_ai_assistant_status(f"Loaded saved analysis for {self.ai_assistant_current_image_id}")
            except Exception as e:
                logger.error(f"Error applying AI Assistant results: {e}")

    @staticmethod
    def show_gemini_settings_dialog(self_or_app):
        """Shows the Gemini API settings dialog."""
        dialog = GeminiSettingsDialog(self_or_app)
        if dialog.exec():
            # Update the API key and model
            if hasattr(self_or_app, 'settings'):
                # Get API key from settings using the GeminiSettingsDialog method
                self_or_app.ai_assistant_api_key = GeminiSettingsDialog.get_api_key()
                logger.info(f"Updated API key from settings: {self_or_app.ai_assistant_api_key[:5]}...{self_or_app.ai_assistant_api_key[-5:] if len(self_or_app.ai_assistant_api_key) > 10 else ''}")

                # Get model name from settings using the GeminiSettingsDialog method
                model_name = GeminiSettingsDialog.get_model_name()
                logger.info(f"Updated model name from settings: {model_name}")

                if hasattr(self_or_app, 'ai_assistant_gemini_worker'):
                    # Update API key
                    logger.info(f"Setting API key: {self_or_app.ai_assistant_api_key[:5]}...{self_or_app.ai_assistant_api_key[-5:] if len(self_or_app.ai_assistant_api_key) > 10 else ''}")
                    self_or_app.ai_assistant_gemini_worker.set_api_key(self_or_app.ai_assistant_api_key)

                    # Update model name
                    logger.info(f"Setting model name: {model_name}")
                    self_or_app.ai_assistant_gemini_worker.model_name = model_name
                    logger.info(f"Updated GeminiWorker model to: {self_or_app.ai_assistant_gemini_worker.model_name}")

                    # Create a new worker with the updated settings
                    logger.info("Creating a new worker with updated settings")
                    self_or_app.ai_assistant_gemini_worker = GeminiWorker(self_or_app.ai_assistant_api_key, model_name=model_name)
                    logger.info(f"New worker created with model: {self_or_app.ai_assistant_gemini_worker.model_name}")

                    # Connect the signals
                    logger.info("Connecting signals to the new worker")
                    try:
                        self_or_app.ai_assistant_gemini_worker.result_ready.connect(self_or_app.handle_ai_assistant_text_result)
                        logger.info("Connected result_ready signal")
                        self_or_app.ai_assistant_gemini_worker.structured_data_ready.connect(self_or_app.handle_ai_assistant_structured_data)
                        logger.info("Connected structured_data_ready signal")
                        self_or_app.ai_assistant_gemini_worker.bounding_boxes_ready.connect(self_or_app.handle_ai_assistant_bounding_boxes)
                        logger.info("Connected bounding_boxes_ready signal")
                        self_or_app.ai_assistant_gemini_worker.error_occurred.connect(self_or_app.handle_ai_assistant_analysis_error)
                        logger.info("Connected error_occurred signal")
                        self_or_app.ai_assistant_gemini_worker.status_update.connect(self_or_app.update_ai_assistant_status)
                        logger.info("Connected status_update signal")
                    except Exception as e:
                        logger.error(f"Error connecting signals: {e}")

            # Update status in the UI but don't show a message box
            if hasattr(self_or_app, 'ai_assistant_api_key') and self_or_app.ai_assistant_api_key:
                if hasattr(self_or_app, 'update_ai_assistant_status'):
                    self_or_app.update_ai_assistant_status(f"API key updated successfully. Using model: {model_name}")
                logger.info(f"API key updated successfully. Using model: {model_name}")
            else:
                if hasattr(self_or_app, 'update_ai_assistant_status'):
                    self_or_app.update_ai_assistant_status("API key not set. AI Assistant features will be disabled.")
                logger.warning("API key not set. AI Assistant features will be disabled.")

    def reload_custom_prompts(self):
        """Reload all custom prompts into the selector."""
        logger.info("Reloading custom prompts")
        try:
            from src.ai_assistant_components.src.gemini.custom_prompts import CustomPromptManager

            # Remember the current selection
            current_text = self.ai_assistant_prompt_selector.currentText()

            # Clear the selector
            self.ai_assistant_prompt_selector.clear()

            # Add default prompts
            self.ai_assistant_prompt_selector.addItems(DEFAULT_PROMPTS.keys())

            # Add custom prompts
            prompt_manager = CustomPromptManager()
            custom_prompts = prompt_manager.get_all_custom_prompts()
            if custom_prompts:
                self.ai_assistant_prompt_selector.addItems(custom_prompts.keys())
                logger.info(f"Loaded {len(custom_prompts)} custom prompts")

            # Restore the previous selection if possible
            index = self.ai_assistant_prompt_selector.findText(current_text)
            if index != -1:
                self.ai_assistant_prompt_selector.setCurrentIndex(index)

            logger.info("Custom prompts reloaded")
        except Exception as e:
            logger.error(f"Error reloading custom prompts: {e}")

    def handle_analysis_page_switch(self, analysis_type, image_paths, image_infos):
        """Handles switching to the AI Assistant page from the project hub.

        Note: This method is not used directly. The main app.py implementation is used instead.
        This is kept for compatibility and as a reference.

        Args:
            analysis_type: The type of analysis to perform (unused in this implementation)
            image_paths: List of image paths to load (unused in this implementation)
            image_infos: List of image info objects (unused in this implementation)
        """
        # This method is intentionally left empty as the main implementation is in app.py
        # to avoid conflicts between the two implementations.
        pass

    @staticmethod
    def update_ai_assistant_config(self_or_app):
        """Updates the AI Assistant configuration after settings have been changed.

        This method is called after the Gemini settings dialog is closed with OK.
        It updates the API key and model name in the worker.
        """
        logger.info("Updating AI Assistant configuration")
        try:
            from src.gui.dialogs.gemini_settings_dialog import GeminiSettingsDialog

            # Get API key from settings
            api_key = GeminiSettingsDialog.get_api_key()
            if api_key:
                logger.info(f"Retrieved API key: {api_key[:5]}...{api_key[-5:] if len(api_key) > 10 else ''}")
                self_or_app.ai_assistant_api_key = api_key
            else:
                logger.warning("No API key found in settings")

            # Get model name from settings
            model_name = GeminiSettingsDialog.get_model_name()
            logger.info(f"Retrieved model name: {model_name}")

            # Update worker if it exists
            if hasattr(self_or_app, 'ai_assistant_gemini_worker') and self_or_app.ai_assistant_gemini_worker:
                logger.info("Updating existing Gemini worker")
                self_or_app.ai_assistant_gemini_worker.set_api_key(api_key)
                self_or_app.ai_assistant_gemini_worker.model_name = model_name

                # Create a new worker with the updated settings
                from src.ai_assistant_components.src.gemini.gemini_worker import GeminiWorker
                logger.info("Creating a new worker with updated settings")
                self_or_app.ai_assistant_gemini_worker = GeminiWorker(api_key, model_name=model_name)

                # Connect the signals
                logger.info("Connecting signals to the new worker")
                try:
                    self_or_app.ai_assistant_gemini_worker.result_ready.connect(self_or_app.handle_ai_assistant_text_result)
                    self_or_app.ai_assistant_gemini_worker.structured_data_ready.connect(self_or_app.handle_ai_assistant_structured_data)
                    self_or_app.ai_assistant_gemini_worker.bounding_boxes_ready.connect(self_or_app.handle_ai_assistant_bounding_boxes)
                    self_or_app.ai_assistant_gemini_worker.error_occurred.connect(self_or_app.handle_ai_assistant_analysis_error)
                    self_or_app.ai_assistant_gemini_worker.status_update.connect(self_or_app.update_ai_assistant_status)
                except Exception as e:
                    logger.error(f"Error connecting signals: {e}")
            else:
                logger.info("No existing Gemini worker found, will be created when needed")

            # Update status in the UI without showing message boxes
            if api_key:
                if hasattr(self_or_app, 'update_ai_assistant_status'):
                    self_or_app.update_ai_assistant_status(f"AI Assistant configuration updated successfully. Using model: {model_name}")
                logger.info(f"Settings saved successfully. Using model: {model_name}")
            else:
                if hasattr(self_or_app, 'update_ai_assistant_status'):
                    self_or_app.update_ai_assistant_status("API key not set. AI Assistant features will be disabled.")
                logger.warning("The Gemini API Key is not set. AI Assistant features will be disabled.")

            return True
        except Exception as e:
            logger.exception(f"Error updating AI Assistant configuration: {e}")
            if hasattr(self_or_app, 'update_ai_assistant_status'):
                self_or_app.update_ai_assistant_status(f"Error updating configuration: {e}")
            return False

    def clear_ai_assistant_gallery(self):
        """Clear all images from the AI assistant gallery."""
        try:
            if hasattr(self, 'ai_assistant_gallery'):
                self.ai_assistant_gallery.clear()
                logger.info("AI assistant gallery cleared")
            else:
                logger.warning("AI assistant gallery not found")
            
            # Clear the AI assistant image viewer as well
            if hasattr(self, 'ai_assistant_image_scene') and self.ai_assistant_image_scene:
                # Remove the image item first to avoid C++ object deletion errors
                if hasattr(self, 'ai_assistant_image_item') and self.ai_assistant_image_item:
                    if self.ai_assistant_image_item.scene() == self.ai_assistant_image_scene:
                        self.ai_assistant_image_scene.removeItem(self.ai_assistant_image_item)
                    self.ai_assistant_image_item = None
                self.ai_assistant_image_scene.clear()
                logger.info("AI assistant image viewer cleared")
        except Exception as e:
            logger.error(f"Error clearing AI assistant gallery: {e}")

    def open_ai_assistant_full_view(self):
        """Opens the Full View dialog for AI assistant results."""
        try:
            from src.gui.dialogs.full_view_dialog import FullViewDialog
            
            # Get the text content from the results display
            if hasattr(self, 'ai_assistant_text_results'):
                content = self.ai_assistant_text_results.toPlainText()
                
                if content.strip():
                    # Create title with current image ID
                    title = f"AI Analysis Results - {self.ai_assistant_current_image_id}" if self.ai_assistant_current_image_id else "AI Analysis Results"
                    
                    # Create and show the dialog
                    dialog = FullViewDialog(content, title, self)
                    dialog.exec()
                else:
                    logger.warning("No AI analysis results to display")
            else:
                logger.error("AI assistant text results widget not found")
                
        except ImportError as e:
            logger.error(f"Failed to import FullViewDialog: {e}")
        except Exception as e:
            logger.error(f"Error opening Full View dialog: {e}")


