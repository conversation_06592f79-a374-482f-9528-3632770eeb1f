"""
Performance test for Image Lab save functionality.

This test verifies that the optimized image save workflow completes
in under 5 seconds and maintains data integrity.
"""

import os
import sys
import time
import tempfile
import shutil
import numpy as np
import cv2
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.core.grainsight_project import VisionLabProject
from src.core.project_data import ImageInfo


def create_test_image(width=1024, height=768):
    """Create a test image for performance testing."""
    # Create a simple test image with some patterns
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # Add some patterns to make it realistic
    for i in range(0, height, 50):
        cv2.line(image, (0, i), (width, i), (100, 100, 100), 2)
    for i in range(0, width, 50):
        cv2.line(image, (i, 0), (i, height), (150, 150, 150), 2)
    
    # Add some circles to simulate grain-like structures
    for i in range(10):
        center = (np.random.randint(50, width-50), np.random.randint(50, height-50))
        radius = np.random.randint(10, 30)
        color = (np.random.randint(50, 255), np.random.randint(50, 255), np.random.randint(50, 255))
        cv2.circle(image, center, radius, color, -1)
    
    return image


def test_image_save_performance():
    """Test the performance of the optimized image save workflow."""
    print("Testing Image Lab save performance optimization...")
    
    # Create temporary directory for test
    test_dir = tempfile.mkdtemp(prefix="visionlab_test_")
    project_file = os.path.join(test_dir, "test_project.vlp")
    
    try:
        # Create a test project
        print("Creating test project...")
        project = VisionLabProject()
        project.name = "Performance Test Project"
        project.description = "Test project for performance optimization"
        project.project_file = project_file
        
        # Create test image
        print("Creating test image...")
        test_image = create_test_image()
        
        # Save test image to temporary file
        temp_image_path = os.path.join(test_dir, "test_image.png")
        cv2.imwrite(temp_image_path, test_image)
        
        # Add initial image to project to simulate existing project
        print("Adding initial image to project...")
        initial_info = project.add_image(temp_image_path)
        assert initial_info is not None, "Failed to add initial image"
        
        # Save project initially
        print("Saving initial project...")
        success = project.save()
        assert success, "Failed to save initial project"
        
        # Now test the optimized save workflow
        print("Testing optimized image save workflow...")
        
        # Create a "processed" version of the image (simulate Image Lab editing)
        processed_image = test_image.copy()
        # Add some processing effects
        processed_image = cv2.GaussianBlur(processed_image, (5, 5), 0)
        processed_image = cv2.addWeighted(processed_image, 1.2, processed_image, 0, 10)
        
        # Convert to RGB (as Image Lab would have it)
        processed_image_rgb = cv2.cvtColor(processed_image, cv2.COLOR_BGR2RGB)
        
        # Simulate the ImageSaveWorker workflow
        start_time = time.time()
        
        # Step 1: Generate filename
        base_name = "test_image"
        edited_filename = f"{base_name}_edited_12345678.png"
        
        # Step 2: Convert RGB to BGR
        image_bgr = cv2.cvtColor(processed_image_rgb, cv2.COLOR_RGB2BGR)
        
        # Step 3: Save directly to project
        images_dir = os.path.join(project.temp_dir, "images")
        os.makedirs(images_dir, exist_ok=True)
        dest_path = os.path.join(images_dir, edited_filename)
        
        success = cv2.imwrite(dest_path, image_bgr)
        assert success, "Failed to write image file"
        
        # Create ImageInfo and add to project
        import uuid
        image_id = f"img_{uuid.uuid4().hex[:8]}_{os.path.basename(dest_path)}"
        rel_path = os.path.join("images", os.path.basename(dest_path))
        
        info = ImageInfo(
            id=image_id,
            filename=os.path.basename(dest_path),
            filepath=rel_path,
            metadata={}
        )
        project.images[image_id] = info
        
        # Step 4: Update manifest only (optimized operation)
        manifest_success = project.update_manifest_only()
        assert manifest_success, "Failed to update project manifest"
        
        end_time = time.time()
        save_duration = end_time - start_time
        
        print(f"Optimized save completed in {save_duration:.3f} seconds")
        
        # Verify the save was successful
        assert os.path.exists(dest_path), "Saved image file not found"
        assert image_id in project.images, "Image not added to project"
        assert project.images[image_id].filename == edited_filename, "Incorrect filename in project"
        
        # Verify manifest was updated
        manifest_path = os.path.join(project.temp_dir, "manifest.json")
        assert os.path.exists(manifest_path), "Manifest file not found"
        
        # Load and verify manifest content
        import json
        with open(manifest_path, 'r') as f:
            manifest = json.load(f)
        
        assert image_id in manifest['images'], "New image not in manifest"
        assert manifest['images'][image_id]['filename'] == edited_filename, "Incorrect filename in manifest"
        
        # Performance assertion
        assert save_duration < 5.0, f"Save took {save_duration:.3f} seconds, should be under 5 seconds"
        
        print(f"✓ Performance test PASSED: Save completed in {save_duration:.3f} seconds (target: < 5.0s)")
        print(f"✓ Data integrity verified: Image saved and manifest updated correctly")
        
        return True
        
    except Exception as e:
        print(f"✗ Performance test FAILED: {e}")
        return False
        
    finally:
        # Clean up
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)


def test_manifest_update_performance():
    """Test the performance of the manifest-only update."""
    print("\nTesting manifest update performance...")
    
    test_dir = tempfile.mkdtemp(prefix="visionlab_manifest_test_")
    project_file = os.path.join(test_dir, "manifest_test.vlp")
    
    try:
        # Create project with multiple images to simulate real scenario
        project = VisionLabProject()
        project.name = "Manifest Test Project"
        project.project_file = project_file
        
        # Add multiple images to simulate a larger project
        for i in range(10):
            image_id = f"img_{i:04d}"
            info = ImageInfo(
                id=image_id,
                filename=f"test_image_{i:04d}.png",
                filepath=f"images/test_image_{i:04d}.png",
                metadata={}
            )
            project.images[image_id] = info
        
        # Test manifest update performance
        start_time = time.time()
        success = project.update_manifest_only()
        end_time = time.time()
        
        update_duration = end_time - start_time
        
        assert success, "Manifest update failed"
        assert update_duration < 1.0, f"Manifest update took {update_duration:.3f} seconds, should be under 1 second"
        
        print(f"✓ Manifest update PASSED: Completed in {update_duration:.3f} seconds (target: < 1.0s)")
        
        return True
        
    except Exception as e:
        print(f"✗ Manifest update test FAILED: {e}")
        return False
        
    finally:
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)


if __name__ == "__main__":
    print("Image Lab Save Performance Test")
    print("=" * 50)
    
    # Run tests
    test1_passed = test_image_save_performance()
    test2_passed = test_manifest_update_performance()
    
    print("\n" + "=" * 50)
    if test1_passed and test2_passed:
        print("✓ ALL TESTS PASSED - Image save optimization is working correctly!")
        sys.exit(0)
    else:
        print("✗ SOME TESTS FAILED - Check the optimization implementation")
        sys.exit(1)
