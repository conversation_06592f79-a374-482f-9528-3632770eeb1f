# src/gui/project_hub_page.py
import os
import logging
from PySide6.QtWidgets import QWidget, QMessageBox
from PySide6.QtCore import Slot, Signal

from src.gui.ui.project_hub_page_ui import ProjectHubPageUI
from src.gui.handlers.project_hub_handler import ProjectHubHandler

logger = logging.getLogger(__name__)

class ProjectHubPage(QWidget, ProjectHubPageUI, ProjectHubHandler):
    """Project Hub page for managing projects and images."""

    # Define the signal for page switching
    switch_page_requested = Signal(str, list, list)  # analysis_type, image_paths, image_infos

    def __init__(self, parent=None):
        QWidget.__init__(self, parent)
        ProjectHubPageUI.__init__(self)
        ProjectHubHandler.__init__(self)

        # Set up the UI
        self.setup_ui(self)

        # Connect signals
        self._setup_project_hub_connections()

        # Initialize UI state
        self._update_hub_ui_state()

    def apply_theme(self, theme_name="dark", font_family=None, font_size=None, style_params=None):
        """Apply the current theme to the project hub page components.

        This method is called when the theme changes to update the button styles.

        Args:
            theme_name (str): The name of the theme to apply
            font_family (str, optional): The font family to use
            font_size (int, optional): The font size to use
            style_params (dict, optional): Custom style parameters to use
        """
        try:
            # Import theme-related modules
            from src.gui.styles.theme_aware_buttons import get_theme_name, get_theme_colors
            from src.gui.styles.theme_config import apply_theme

            # Get current theme colors
            theme_colors = get_theme_colors(theme_name)
            is_dark = "dark" in theme_name.lower()

            # Update button styles
            self._update_button_styles(theme_colors, is_dark)

            # Log theme change
            logger.info(f"Applied theme {theme_name} to Project Hub page")
        except Exception as e:
            logger.error(f"Error applying theme to Project Hub page: {e}")

    def _update_button_styles(self, theme_colors, is_dark):
        """Update the styles of all buttons in the Project Hub page.

        Args:
            theme_colors (dict): Dictionary of theme colors
            is_dark (bool): Whether the theme is dark or light
        """
        # Define header button style
        header_button_style = f"""
            QPushButton {{
                background-color: {theme_colors['button'].name()};
                color: {theme_colors['button-text'].name()};
                border: 0px solid {theme_colors['alternate-base'].name()};
                border-radius: 4px;
                padding: 5px 10px;
                text-align: center;
                min-height: 28px;
            }}
            QPushButton:hover {{
                background-color: {theme_colors['button'].lighter(110).name()};
                border-bottom: 2px solid {theme_colors['highlight'].name()};
            }}
            QPushButton:pressed {{
                background-color: {theme_colors['button'].darker(110).name()};
            }}
            QPushButton:disabled {{
                background-color: {theme_colors['button'].darker(110).name() if is_dark else theme_colors['button'].lighter(110).name()};
                color: {theme_colors['button-text'].darker(150).name() if is_dark else theme_colors['button-text'].lighter(150).name()};
            }}
        """

        # Apply header button style to project action buttons
        self.new_vlp_project_button.setStyleSheet(header_button_style)
        self.open_recent_button.setStyleSheet(header_button_style)
        self.open_vlp_project_button.setStyleSheet(header_button_style)

        # Define card-like button style for analysis buttons
        card_button_style = f"""
            QPushButton {{
                background-color: {theme_colors['button'].name()};
                color: {theme_colors['button-text'].name()};
                border: 1px solid {theme_colors['alternate-base'].name()};
                border-radius: 4px;
                padding: 3px;
                text-align: center;
                min-height: 30px;
                font-size: 11px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {theme_colors['button'].lighter(110).name()};
                border: 1px solid {theme_colors['highlight'].name()};
            }}
            QPushButton:pressed {{
                background-color: {theme_colors['button'].darker(110).name()};
            }}
            QPushButton:disabled {{
                background-color: {theme_colors['button'].darker(110).name() if is_dark else theme_colors['button'].lighter(110).name()};
                color: {theme_colors['button-text'].darker(150).name() if is_dark else theme_colors['button-text'].lighter(150).name()};
                border: 1px solid {theme_colors['alternate-base'].name()};
            }}
        """

        # Apply card button style to analysis buttons
        self.analyze_unsupervised_button.setStyleSheet(card_button_style)
        self.analyze_trainable_button.setStyleSheet(card_button_style)
        self.analyze_grain_size_button.setStyleSheet(card_button_style)
        self.analyze_porosity_button.setStyleSheet(card_button_style)
        self.analyze_ai_assistant_button.setStyleSheet(card_button_style)
        self.analyze_point_counting_button.setStyleSheet(card_button_style)

        # Define batch processing button style
        batch_button_style = f"""
            QPushButton {{
                background-color: {theme_colors['highlight'].darker(150).name() if is_dark else theme_colors['highlight'].lighter(150).name()};
                color: {theme_colors['highlight-text'].name()};
                border: 1px solid {theme_colors['highlight'].name()};
                border-radius: 4px;
                padding: 3px;
                text-align: center;
                min-height: 30px;
                font-size: 11px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {theme_colors['highlight'].darker(120).name() if is_dark else theme_colors['highlight'].lighter(120).name()};
                border: 1px solid {theme_colors['highlight'].lighter(120).name() if is_dark else theme_colors['highlight'].darker(120).name()};
            }}
            QPushButton:pressed {{
                background-color: {theme_colors['highlight'].darker(170).name() if is_dark else theme_colors['highlight'].lighter(170).name()};
            }}
            QPushButton:disabled {{
                background-color: {theme_colors['button'].darker(110).name() if is_dark else theme_colors['button'].lighter(110).name()};
                color: {theme_colors['button-text'].darker(150).name() if is_dark else theme_colors['button-text'].lighter(150).name()};
                border: 1px solid {theme_colors['alternate-base'].name()};
            }}
        """

        # Apply batch button style to batch processing button
        self.batch_process_button.setStyleSheet(batch_button_style)

        # Update batch label color
        self.batch_label.setStyleSheet(f"font-weight: bold; color: {theme_colors['highlight'].name()}; font-size: 10px;")

        # Update import images button style
        import_button_style = f"""
            QPushButton {{
                background-color: {theme_colors['button'].name()};
                color: {theme_colors['button-text'].name()};
                border: 0px solid {theme_colors['alternate-base'].name()};
                border-radius: 4px;
                padding: 5px 10px;
                text-align: center;
            }}
            QPushButton:hover {{
                background-color: {theme_colors['button'].lighter(110).name()};
                border-bottom: 2px solid {theme_colors['highlight'].name()};
            }}
            QPushButton:pressed {{
                background-color: {theme_colors['button'].darker(110).name()};
            }}
            QPushButton:disabled {{
                background-color: {theme_colors['button'].darker(110).name() if is_dark else theme_colors['button'].lighter(110).name()};
                color: {theme_colors['button-text'].darker(150).name() if is_dark else theme_colors['button-text'].lighter(150).name()};
            }}
        """
        self.import_images_button.setStyleSheet(import_button_style)

        # Update save metadata button style
        self.save_metadata_button.setStyleSheet(import_button_style)

        # Update group box styles
        group_box_style = f"""
            QGroupBox {{
                font-weight: bold;
                font-size: 14px;
                border: 1px solid {theme_colors['alternate-base'].name()};
                border-radius: 6px;
                margin-top: 12px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                subcontrol-position: top left;
                left: 10px;
                padding: 0 5px 0 5px;
                color: {theme_colors['highlight'].name()};
            }}
        """
        self.actions_groupbox.setStyleSheet(group_box_style)
        self.metadata_groupbox.setStyleSheet(group_box_style)
        self.image_preview_groupbox.setStyleSheet(group_box_style)

    def switch_to_analysis_page(self, image_infos, analysis_type):
        """Switch to the appropriate analysis page for the selected image(s).

        This method should be called by the main application when an analysis
        button is clicked in the Project Hub.

        Args:
            image_infos: List of ImageInfo objects for the selected images
            analysis_type: The type of analysis to perform
        """
        # Handle both single and multiple image selection
        if not isinstance(image_infos, list):
            # Convert single image_info to a list for consistent handling
            image_infos = [image_infos]

        # Get the absolute paths to all selected images
        image_paths = []
        valid_image_infos = []

        for image_info in image_infos:
            image_path = self.current_project.get_image_path(image_info.id)
            if image_path and os.path.exists(image_path):
                image_paths.append(image_path)
                valid_image_infos.append(image_info)
            else:
                logger.warning(f"Image file not found: {image_path}")

        if not image_paths:
            self._show_error_message("Error", "No valid image files found")
            return

        # Emit the signal with the required information
        self.switch_page_requested.emit(analysis_type, image_paths, valid_image_infos)

    def start_batch_processing(self):
        """Switch to the batch processing page for the selected images."""
        if not self.current_project:
            self._show_error_message("Error", "No project open.")
            return

        selected_infos = self.get_selected_image_infos()
        if not selected_infos:
            self._show_error_message("Selection Error", "No images selected.")
            return

        # Get the absolute paths to all selected images
        image_paths = []
        valid_image_infos = []
        
        for image_info in selected_infos:
            image_path = self.current_project.get_image_path(image_info.id)
            if image_path and os.path.exists(image_path):
                image_paths.append(image_path)
                valid_image_infos.append(image_info)
            else:
                logger.warning(f"Image file not found: {image_path}")
        
        if not image_paths:
            self._show_error_message("Error", "No valid image files found")
            return
        
        # Emit the signal to switch to batch processing page
        self.switch_page_requested.emit("batch_processing", image_paths, valid_image_infos)