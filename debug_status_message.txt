DEBUG: update_status_message called with: Selected label: Segment 1 (ID: 1)
DEBUG: Has statusBar: True
DEBUG: Has trainable_image_info_label: True
DEBUG: Updating statusBar with message
DEBUG: Updating trainable_image_info_label
DEBUG: Current label text: No image loaded
DEBUG: Setting new text (append): No image loaded
Selected label: Segment 1 (ID: 1)
DEBUG: update_status_message called with: Selected label: Segment 1 (ID: 1)
DEBUG: Has statusBar: True
DEBUG: Has trainable_image_info_label: True
DEBUG: Updating statusBar with message
DEBUG: Updating trainable_image_info_label
DEBUG: Current label text: No image loaded
Selected label: Segment 1 (ID: 1)
DEBUG: Setting new text (append): No image loaded
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
DEBUG: update_status_message called with: Selected label: Label 0 (ID: 0)
DEBUG: Has statusBar: True
DEBUG: Has trainable_image_info_label: True
DEBUG: Updating statusBar with message
DEBUG: Updating trainable_image_info_label
DEBUG: Current label text: No image loaded
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
DEBUG: Setting new text (append): No image loaded
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
DEBUG: update_status_message called with: Selected label: Label 0 (ID: 0)
DEBUG: Has statusBar: True
DEBUG: Has trainable_image_info_label: True
DEBUG: Updating statusBar with message
DEBUG: Updating trainable_image_info_label
DEBUG: Current label text: No image loaded
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
DEBUG: Setting new text (append): No image loaded
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
DEBUG: update_status_message called with: Selected label: Segment 1 (ID: 1)
DEBUG: Has statusBar: True
DEBUG: Has trainable_image_info_label: True
DEBUG: Updating statusBar with message
DEBUG: Updating trainable_image_info_label
DEBUG: Current label text: No image loaded
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
DEBUG: Setting new text (append): No image loaded
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
DEBUG: update_status_message called with: Selected label: Segment 1 (ID: 1)
DEBUG: Has statusBar: True
DEBUG: Has trainable_image_info_label: True
DEBUG: Updating statusBar with message
DEBUG: Updating trainable_image_info_label
DEBUG: Current label text: No image loaded
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
DEBUG: Setting new text (append): No image loaded
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
DEBUG: update_status_message called with: Selected label: Label 0 (ID: 0)
DEBUG: Has statusBar: True
DEBUG: Has trainable_image_info_label: True
DEBUG: Updating statusBar with message
DEBUG: Updating trainable_image_info_label
DEBUG: Current label text: No image loaded
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
DEBUG: Setting new text (append): No image loaded
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
DEBUG: update_status_message called with: Selected label: Label 0 (ID: 0)
DEBUG: Has statusBar: True
DEBUG: Has trainable_image_info_label: True
DEBUG: Updating statusBar with message
DEBUG: Updating trainable_image_info_label
DEBUG: Current label text: No image loaded
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
DEBUG: Setting new text (append): No image loaded
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
DEBUG: update_status_message called with: Selected label: Segment 1 (ID: 1)
DEBUG: Has statusBar: True
DEBUG: Has trainable_image_info_label: True
DEBUG: Updating statusBar with message
DEBUG: Updating trainable_image_info_label
DEBUG: Current label text: No image loaded
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
DEBUG: Setting new text (append): No image loaded
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
DEBUG: update_status_message called with: Selected label: Segment 1 (ID: 1)
DEBUG: Has statusBar: True
DEBUG: Has trainable_image_info_label: True
DEBUG: Updating statusBar with message
DEBUG: Updating trainable_image_info_label
DEBUG: Current label text: No image loaded
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
DEBUG: Setting new text (append): No image loaded
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
DEBUG: update_status_message called with: Selected label: Label 0 (ID: 0)
DEBUG: Has statusBar: True
DEBUG: Has trainable_image_info_label: True
DEBUG: Updating statusBar with message
DEBUG: Updating trainable_image_info_label
DEBUG: Current label text: No image loaded
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
DEBUG: Setting new text (append): No image loaded
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
DEBUG: update_status_message called with: Selected label: Label 0 (ID: 0)
DEBUG: Has statusBar: True
DEBUG: Has trainable_image_info_label: True
DEBUG: Updating statusBar with message
DEBUG: Updating trainable_image_info_label
DEBUG: Current label text: No image loaded
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
DEBUG: Setting new text (append): No image loaded
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
DEBUG: update_status_message called with: Selected label: Segment 1 (ID: 1)
DEBUG: Has statusBar: True
DEBUG: Has trainable_image_info_label: True
DEBUG: Updating statusBar with message
DEBUG: Updating trainable_image_info_label
DEBUG: Current label text: No image loaded
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
DEBUG: Setting new text (append): No image loaded
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
DEBUG: update_status_message called with: Selected label: Segment 1 (ID: 1)
DEBUG: Has statusBar: True
DEBUG: Has trainable_image_info_label: True
DEBUG: Updating statusBar with message
DEBUG: Updating trainable_image_info_label
DEBUG: Current label text: No image loaded
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
DEBUG: Setting new text (append): No image loaded
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
Selected label: Label 0 (ID: 0)
Selected label: Label 0 (ID: 0)
Selected label: Segment 1 (ID: 1)
Selected label: Segment 1 (ID: 1)
DEBUG: update_status_message called with: Drawing Mode: Active - Drawing 'Segment 1' with brush size 5
DEBUG: Has statusBar: True
DEBUG: Has trainable_image_info_label: True
DEBUG: Updating statusBar with message
DEBUG: Updating trainable_image_info_label
DEBUG: Current label text: Image: optimized_20-129_patch_000.jpg (1 of 3)
Size: 512x512
Channels: 3
DEBUG: Setting new text (with image info): Image: optimized_20-129_patch_000.jpg (1 of 3)
Size: 512x512
Drawing Mode: Active - Drawing 'Segment 1' with brush size 5
DEBUG: update_status_message called with: Drawing Mode: Inactive
DEBUG: Has statusBar: True
DEBUG: Has trainable_image_info_label: True
DEBUG: Updating statusBar with message
DEBUG: Updating trainable_image_info_label
DEBUG: Current label text: Image: optimized_20-129_patch_000.jpg (1 of 3)
Size: 512x512
Drawing Mode: Active - Drawing 'Segment 1' with brush size 5
DEBUG: Setting new text (with image info): Image: optimized_20-129_patch_000.jpg (1 of 3)
Size: 512x512
Drawing Mode: Inactive
DEBUG: update_status_message called with: Erasing Mode: Active - Erasing with brush size 5
DEBUG: Has statusBar: True
DEBUG: Has trainable_image_info_label: True
DEBUG: Updating statusBar with message
DEBUG: Updating trainable_image_info_label
DEBUG: Current label text: Image: optimized_20-129_patch_000.jpg (1 of 3)
Size: 512x512
Drawing Mode: Inactive
DEBUG: Setting new text (with image info): Image: optimized_20-129_patch_000.jpg (1 of 3)
Size: 512x512
Erasing Mode: Active - Erasing with brush size 5
DEBUG: update_status_message called with: Erasing Mode: Inactive
DEBUG: Has statusBar: True
DEBUG: Has trainable_image_info_label: True
DEBUG: Updating statusBar with message
DEBUG: Updating trainable_image_info_label
DEBUG: Current label text: Image: optimized_20-129_patch_000.jpg (1 of 3)
Size: 512x512
Erasing Mode: Active - Erasing with brush size 5
DEBUG: Setting new text (with image info): Image: optimized_20-129_patch_000.jpg (1 of 3)
Size: 512x512
Erasing Mode: Inactive
