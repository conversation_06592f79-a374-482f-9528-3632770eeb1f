"""
Batch progress dialog for the Image Lab page.

This module provides a progress dialog for batch operations in the Image Lab page.
"""

from PySide6.QtWidgets import QDialog, QVBoxLayout, QProgressBar, QLabel, QPushButton, QHBoxLayout
from PySide6.QtCore import Qt, Signal, Slot, QTimer

class BatchProgressDialog(QDialog):
    """Progress dialog for batch operations."""
    
    # Signal to update progress
    progress_updated = Signal(int, str)
    
    # Signal to indicate operation is complete
    operation_complete = Signal()
    
    def __init__(self, parent=None, total_items=0, title="Processing Images"):
        """Initialize the batch progress dialog.
        
        Args:
            parent: Parent widget
            total_items: Total number of items to process
            title: Dialog title
        """
        super().__init__(parent)
        
        # Set up dialog properties
        self.setWindowTitle(title)
        self.setMinimumSize(400, 150)
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint)
        self.setModal(True)
        
        # Initialize progress variables
        self.total_items = total_items
        self.current_item = 0
        self.canceled = False
        
        # Set up UI
        self._setup_ui()
        
        # Connect signals
        self.progress_updated.connect(self._update_progress)
        self.operation_complete.connect(self.accept)
        self.cancel_button.clicked.connect(self._cancel_operation)
    
    def _setup_ui(self):
        """Set up the dialog UI."""
        # Main layout
        layout = QVBoxLayout(self)
        
        # Status label
        self.status_label = QLabel("Preparing...")
        layout.addWidget(self.status_label)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, self.total_items)
        self.progress_bar.setValue(0)
        layout.addWidget(self.progress_bar)
        
        # Item label
        self.item_label = QLabel(f"Processing item 0 of {self.total_items}")
        layout.addWidget(self.item_label)
        
        # Cancel button
        button_layout = QHBoxLayout()
        self.cancel_button = QPushButton("Cancel")
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_button)
        layout.addLayout(button_layout)
    
    @Slot(int, str)
    def _update_progress(self, value, status_text):
        """Update the progress bar and status text.
        
        Args:
            value: Current progress value
            status_text: Status text to display
        """
        self.current_item = value
        self.progress_bar.setValue(value)
        self.status_label.setText(status_text)
        self.item_label.setText(f"Processing item {value} of {self.total_items}")
        
        # Process events to update the UI
        QTimer.singleShot(0, self._process_events)
    
    def _process_events(self):
        """Process events to update the UI."""
        from PySide6.QtWidgets import QApplication
        QApplication.processEvents()
    
    def _cancel_operation(self):
        """Cancel the current operation."""
        self.canceled = True
        self.reject()
    
    def is_canceled(self):
        """Check if the operation was canceled.
        
        Returns:
            True if canceled, False otherwise
        """
        return self.canceled
