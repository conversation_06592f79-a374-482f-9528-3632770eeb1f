# VisionLab Ai Test Build Instructions

This guide explains how to build a test executable for VisionLab Ai without code obfuscation, focusing on creating a basic runnable executable for testing purposes.

## Prerequisites

Before you begin, make sure you have Python 3.10 or higher installed.

## Building the Executable

### Option 1: Using the Batch File (Windows)

1. Simply double-click the `build_exe.bat` file
2. Wait for the build process to complete
3. The executable will be located at `dist\VisionLab_Ai\VisionLab_Ai.exe`

### Option 2: Using the Python Script

1. Open a command prompt or terminal
2. Navigate to the VisionLab_Ai directory
3. Run the build script:
   ```
   python build_petroseg_exe.py
   ```
4. Wait for the build process to complete
5. The executable will be located at `dist\VisionLab_Ai\VisionLab_Ai.exe`

## Command Line Options

The build script supports several options:

- `--skip-deps`: Skip installing dependencies (use this if you already have all dependencies installed)
- `--no-clean`: Do not clean build and dist directories (use this to speed up rebuilds)

Example:
```
python build_petroseg_exe.py --skip-deps --no-clean
```

## Required Model Files

The application requires certain model files to function properly. The build script will check for these files and create the necessary directories, but you need to download the files manually:

1. `weights/mobile_sam.pt` - MobileSAM model weights
   - Download from: https://github.com/ChaoningZhang/MobileSAM/tree/master/weights

2. `src/grainsight_components/models/FastSAM-x.pt` - FastSAM model
   - Download from: https://github.com/CASIA-IVA-Lab/FastSAM/releases

## Running the Executable

To run the executable:

1. Navigate to the `dist\VisionLab_Ai` directory
2. Run `VisionLab_Ai.exe`

Note: The console window will remain open during execution to show any error messages. This is intentional for testing purposes.

## Troubleshooting

### Missing Dependencies

If the build fails due to missing dependencies, try installing them manually:

```
pip install PySide6==6.6.1 numpy Pillow opencv-python torch torchvision scikit-image pyyaml ultralytics xgboost tensorflow
```

### PyInstaller Issues

If PyInstaller fails to build the executable, try:

1. Install PyInstaller manually:
   ```
   pip install pyinstaller
   ```

2. Run PyInstaller directly with the basic options:
   ```
   pyinstaller --name=VisionLab_Ai --add-data="weights;weights" --console main.py
   ```

### Model File Issues

If the application crashes due to missing model files:

1. Make sure you've downloaded the required model files
2. Place them in the correct locations as mentioned above

### XGBoost DLL Issues

If the application crashes with an error about missing XGBoost DLL:

1. Find the XGBoost DLL on your system (usually in your Python environment's `Lib/site-packages/xgboost/lib` directory)
2. Copy it manually to the following locations in the dist/VisionLab_Ai directory:
   - `dist/VisionLab_Ai/lib/xgboost.dll`
   - `dist/VisionLab_Ai/bin/xgboost.dll`
   - `dist/VisionLab_Ai/Library/bin/xgboost.dll`
