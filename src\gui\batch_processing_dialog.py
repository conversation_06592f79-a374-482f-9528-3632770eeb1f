# src/gui/batch_processing_dialog.py
import os
import logging
from typing import List, Dict, Any
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                             QComboBox, QProgressBar, QTextEdit, QGroupBox, QFormLayout,
                             QSpinBox, QDoubleSpinBox, QCheckBox, QDialogButtonBox)
from PySide6.QtCore import Qt, Slot

from src.core.project_data import ImageInfo
from src.core.batch_processor import BatchProcessor, BatchProcessingProgress

logger = logging.getLogger(__name__)

class BatchProcessingDialog(QDialog):
    """Dialog for configuring and running batch processing operations."""

    def __init__(self, parent=None, project=None, selected_images=None):
        super().__init__(parent)
        self.project = project
        self.selected_images = selected_images or []
        self.batch_processor = BatchProcessor(project) if project else None

        self.setWindowTitle("Batch Processing (Future Feature)")
        self.resize(600, 500)
        self.setup_ui()
        self.connect_signals()

    def setup_ui(self):
        """Set up the dialog UI."""
        self.main_layout = QVBoxLayout(self)

        # Header with selection info
        self.header_label = QLabel(f"Batch Processing for {len(self.selected_images)} images (Future Feature)")
        self.header_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        self.main_layout.addWidget(self.header_label)

        # Analysis type selection
        self.analysis_group = QGroupBox("Analysis Configuration (Future Feature)")
        self.analysis_layout = QFormLayout(self.analysis_group)

        self.analysis_type_combo = QComboBox()
        self.analysis_type_combo.addItems([
            "Unsupervised Segmentation",
            "Trainable Segmentation",
            "Grain Size Analysis",
            "Porosity Analysis"
        ])
        self.analysis_layout.addRow("Analysis Type:", self.analysis_type_combo)

        # Parameters section - will be dynamically updated based on analysis type
        self.parameters_group = QGroupBox("Parameters (Future Feature)")
        self.parameters_layout = QFormLayout(self.parameters_group)

        # Default parameters for unsupervised segmentation
        self.param_widgets = {}
        self._setup_unsupervised_parameters()

        self.main_layout.addWidget(self.analysis_group)
        self.main_layout.addWidget(self.parameters_group)

        # Progress section
        self.progress_group = QGroupBox("Progress (Future Feature)")
        self.progress_layout = QVBoxLayout(self.progress_group)

        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_layout.addWidget(self.progress_bar)

        self.status_label = QLabel("Ready to start processing")
        self.progress_layout.addWidget(self.status_label)

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMinimumHeight(150)
        self.progress_layout.addWidget(self.log_text)

        self.main_layout.addWidget(self.progress_group)

        # Buttons
        self.button_layout = QHBoxLayout()

        self.start_button = QPushButton("Start Processing (Future Feature)")
        self.stop_button = QPushButton("Stop (Future Feature)")
        self.stop_button.setEnabled(False)
        self.close_button = QPushButton("Close")

        self.button_layout.addWidget(self.start_button)
        self.button_layout.addWidget(self.stop_button)
        self.button_layout.addStretch()
        self.button_layout.addWidget(self.close_button)

        self.main_layout.addLayout(self.button_layout)

    def _setup_unsupervised_parameters(self):
        """Set up parameters for unsupervised segmentation."""
        # Clear existing parameters
        self._clear_parameters()

        # Add parameters for unsupervised segmentation
        self.param_widgets['method'] = QComboBox()
        self.param_widgets['method'].addItems(["KMeans", "SLIC", "Watershed"])
        self.parameters_layout.addRow("Segmentation Method:", self.param_widgets['method'])

        self.param_widgets['num_segments'] = QSpinBox()
        self.param_widgets['num_segments'].setRange(2, 20)
        self.param_widgets['num_segments'].setValue(5)
        self.parameters_layout.addRow("Number of Segments:", self.param_widgets['num_segments'])

        self.param_widgets['compactness'] = QDoubleSpinBox()
        self.param_widgets['compactness'].setRange(0.01, 100.0)
        self.param_widgets['compactness'].setValue(10.0)
        self.parameters_layout.addRow("Compactness:", self.param_widgets['compactness'])

    def _setup_trainable_parameters(self):
        """Set up parameters for trainable segmentation."""
        # Clear existing parameters
        self._clear_parameters()

        # Add parameters for trainable segmentation
        self.param_widgets['model'] = QComboBox()
        self.param_widgets['model'].addItems(["Random Forest", "SVM", "Neural Network"])
        self.parameters_layout.addRow("Model Type:", self.param_widgets['model'])

        self.param_widgets['epochs'] = QSpinBox()
        self.param_widgets['epochs'].setRange(10, 1000)
        self.param_widgets['epochs'].setValue(100)
        self.parameters_layout.addRow("Training Epochs:", self.param_widgets['epochs'])

        self.param_widgets['use_existing'] = QCheckBox("Use existing training data if available")
        self.param_widgets['use_existing'].setChecked(True)
        self.parameters_layout.addRow("", self.param_widgets['use_existing'])

    def _setup_grain_size_parameters(self):
        """Set up parameters for grain size analysis."""
        # Clear existing parameters
        self._clear_parameters()

        # Add parameters for grain size analysis
        self.param_widgets['min_size'] = QSpinBox()
        self.param_widgets['min_size'].setRange(1, 1000)
        self.param_widgets['min_size'].setValue(10)
        self.parameters_layout.addRow("Minimum Grain Size (px):", self.param_widgets['min_size'])

        self.param_widgets['max_size'] = QSpinBox()
        self.param_widgets['max_size'].setRange(10, 10000)
        self.param_widgets['max_size'].setValue(1000)
        self.parameters_layout.addRow("Maximum Grain Size (px):", self.param_widgets['max_size'])

        self.param_widgets['watershed'] = QCheckBox("Apply watershed separation")
        self.param_widgets['watershed'].setChecked(True)
        self.parameters_layout.addRow("", self.param_widgets['watershed'])

    def _setup_porosity_parameters(self):
        """Set up parameters for porosity analysis."""
        # Clear existing parameters
        self._clear_parameters()

        # Add parameters for porosity analysis
        self.param_widgets['threshold'] = QSpinBox()
        self.param_widgets['threshold'].setRange(0, 255)
        self.param_widgets['threshold'].setValue(128)
        self.parameters_layout.addRow("Threshold Value:", self.param_widgets['threshold'])

        self.param_widgets['auto_threshold'] = QCheckBox("Auto threshold")
        self.param_widgets['auto_threshold'].setChecked(True)
        self.parameters_layout.addRow("", self.param_widgets['auto_threshold'])

        self.param_widgets['min_pore_size'] = QSpinBox()
        self.param_widgets['min_pore_size'].setRange(1, 1000)
        self.param_widgets['min_pore_size'].setValue(5)
        self.parameters_layout.addRow("Minimum Pore Size (px):", self.param_widgets['min_pore_size'])

    def _clear_parameters(self):
        """Clear all parameter widgets."""
        # Remove all widgets from the layout
        while self.parameters_layout.rowCount() > 0:
            self.parameters_layout.removeRow(0)

        # Clear the parameter widgets dictionary
        self.param_widgets.clear()

    def connect_signals(self):
        """Connect UI signals to slots."""
        self.analysis_type_combo.currentIndexChanged.connect(self._on_analysis_type_changed)
        self.start_button.clicked.connect(self.start_processing)
        self.stop_button.clicked.connect(self.stop_processing)
        self.close_button.clicked.connect(self.close)

        # Connect batch processor signals
        if self.batch_processor:
            self.batch_processor.progress.progress_updated.connect(self._on_progress_updated)
            self.batch_processor.progress.status_updated.connect(self._on_status_updated)
            self.batch_processor.progress.processing_completed.connect(self._on_processing_completed)
            self.batch_processor.progress.image_completed.connect(self._on_image_completed)

    @Slot(int)
    def _on_analysis_type_changed(self, index):
        """Handle analysis type selection change."""
        analysis_type = self.analysis_type_combo.currentText()

        if "Unsupervised" in analysis_type:
            self._setup_unsupervised_parameters()
        elif "Trainable" in analysis_type:
            self._setup_trainable_parameters()
        elif "Grain Size" in analysis_type:
            self._setup_grain_size_parameters()
        elif "Porosity" in analysis_type:
            self._setup_porosity_parameters()

    def _get_parameters(self) -> Dict[str, Any]:
        """Get the current parameter values."""
        params = {}
        for name, widget in self.param_widgets.items():
            if isinstance(widget, QComboBox):
                params[name] = widget.currentText()
            elif isinstance(widget, (QSpinBox, QDoubleSpinBox)):
                params[name] = widget.value()
            elif isinstance(widget, QCheckBox):
                params[name] = widget.isChecked()
        return params

    def _get_analysis_type_key(self) -> str:
        """Convert the display analysis type to a key for the project."""
        analysis_type = self.analysis_type_combo.currentText()

        if "Unsupervised" in analysis_type:
            return "unsupervised_segmentation"
        elif "Trainable" in analysis_type:
            return "trainable_segmentation"
        elif "Grain Size" in analysis_type:
            return "grain_size_analysis"
        elif "Porosity" in analysis_type or "Image Lab" in analysis_type:
            return "image_lab"
        return "unknown_analysis"

    @Slot()
    def start_processing(self):
        """Start the batch processing operation."""
        if not self.batch_processor or not self.selected_images:
            self.log_message("Error: No project or images selected")
            return

        # Get the analysis type and parameters
        analysis_type = self._get_analysis_type_key()
        parameters = self._get_parameters()

        # Log the start of processing
        self.log_message(f"Starting batch processing of {len(self.selected_images)} images")
        self.log_message(f"Analysis type: {analysis_type}")
        self.log_message(f"Parameters: {parameters}")

        # Update UI state
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.analysis_type_combo.setEnabled(False)
        self.parameters_group.setEnabled(False)

        # Start processing
        success = self.batch_processor.process_images(
            image_infos=self.selected_images,
            analysis_type=analysis_type,
            parameters=parameters
        )

        if not success:
            self.log_message("Failed to start batch processing")
            self._reset_ui_state()

    @Slot()
    def stop_processing(self):
        """Stop the current batch processing operation."""
        if self.batch_processor:
            if self.batch_processor.stop_processing():
                self.log_message("Stopping batch processing...")
            else:
                self.log_message("No batch processing in progress")

    def _reset_ui_state(self):
        """Reset the UI state after processing completes or is stopped."""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.analysis_type_combo.setEnabled(True)
        self.parameters_group.setEnabled(True)

    @Slot(int, int)
    def _on_progress_updated(self, current, total):
        """Update the progress bar."""
        if total > 0:
            percent = int((current / total) * 100)
            self.progress_bar.setValue(percent)

    @Slot(str)
    def _on_status_updated(self, status):
        """Update the status label."""
        self.status_label.setText(status)
        self.log_message(status)

    @Slot(bool, str)
    def _on_processing_completed(self, success, message):
        """Handle completion of batch processing."""
        self.log_message(message)
        self._reset_ui_state()

        if success:
            self.status_label.setText("Processing completed successfully")
        else:
            self.status_label.setText("Processing completed with errors")

    @Slot(str, bool)
    def _on_image_completed(self, image_id, success):
        """Handle completion of processing for a single image."""
        # Find the image info for this ID
        image_info = next((img for img in self.selected_images if img.id == image_id), None)
        if image_info:
            status = "Success" if success else "Failed"
            self.log_message(f"Processed {image_info.filename}: {status}")

    def log_message(self, message):
        """Add a message to the log text area."""
        self.log_text.append(message)
        # Scroll to the bottom
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())