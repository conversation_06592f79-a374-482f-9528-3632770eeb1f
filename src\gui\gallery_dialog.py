from PySide6.QtWidgets import (QDialog, QVBoxLayout, QGridLayout, QLabel,
                              QPushButton, QScrollArea, QWidget, QFileDialog)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QPixmap
from src.utils.image_utils import resize_image, convert_cvimage_to_qpixmap
import cv2

class ImageThumbnail(QLabel):
    clicked = Signal(object)

    def __init__(self, image, name, parent=None):
        super().__init__(parent)
        self.image = image
        self.name = name
        self.setFixedSize(200, 220)
        self.setAlignment(Qt.AlignCenter)

        # Create layout for thumbnail and label
        layout = QVBoxLayout(self)

        # Image thumbnail
        img_label = QLabel()
        img_label.setFixedSize(180, 180)
        img_label.setAlignment(Qt.AlignCenter)
        thumbnail = resize_image(image.copy(), (170, 170))
        img_label.setPixmap(convert_cvimage_to_qpixmap(thumbnail))
        layout.addWidget(img_label)

        # Image name label
        name_label = QLabel(name)
        name_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(name_label)

        self.setStyleSheet("""
            ImageThumbnail {
                border: 2px solid #ccc;
                border-radius: 5px;
                margin: 5px;
                background: white;
            }
            ImageThumbnail:hover {
                border-color: #3498db;
            }
        """)

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.clicked.emit(self.image)

class GalleryDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Image Gallery")
        self.resize(800, 600)
        self.images = []

        # Main layout
        layout = QVBoxLayout(self)

        # Upload button removed - images are now only loaded from project hub

        # Scroll area for thumbnails
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setStyleSheet("QScrollArea { border: none; }")

        # Container for thumbnails
        self.gallery_widget = QWidget()
        self.gallery_layout = QGridLayout(self.gallery_widget)
        self.gallery_layout.setSpacing(10)
        scroll.setWidget(self.gallery_widget)
        layout.addWidget(scroll)

    # upload_images method removed - images are now only loaded from project hub

    def add_image(self, image, name):
        self.images.append(image)
        thumbnail = ImageThumbnail(image, name)
        thumbnail.clicked.connect(self.open_preprocessing)

        # Calculate position in grid
        count = len(self.images) - 1
        row = count // 3
        col = count % 3

        self.gallery_layout.addWidget(thumbnail, row, col)

    def open_preprocessing(self, image):
        from src.gui.preprocessing_dialog import PreprocessingDialog
        dialog = PreprocessingDialog(self.images, self)  # Pass all images
        # Find index of clicked image
        current_index = self.images.index(image)
        # Load current image
        dialog.current_index = current_index
        dialog.load_current_image()
        if dialog.exec():
            processed_images = dialog.get_result_images()
            # Update our stored images with the processed ones
            self.images = processed_images
            # Refresh all thumbnails
            self.refresh_thumbnails()

    def refresh_thumbnails(self):
        """Refreshes all thumbnails with current images."""
        # Clear existing thumbnails
        for i in reversed(range(self.gallery_layout.count())):
            self.gallery_layout.itemAt(i).widget().setParent(None)

        # Re-add all thumbnails
        for i, image in enumerate(self.images):
            name = f"Image {i+1}"  # You might want to keep track of original names
            thumbnail = ImageThumbnail(image, name)
            thumbnail.clicked.connect(self.open_preprocessing)
            row = i // 3
            col = i % 3
            self.gallery_layout.addWidget(thumbnail, row, col)
