# src/ai_assistant_components/src/gemini/custom_prompts.py

import os
import json
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

# Default location for custom prompts
DEFAULT_CUSTOM_PROMPTS_DIR = os.path.join(str(Path.home()), ".visionlab_ai", "custom_prompts")

class CustomPromptManager:
    """Manages custom prompt templates for the AI Assistant."""
    
    def __init__(self, custom_prompts_dir=None):
        """Initialize the custom prompt manager.
        
        Args:
            custom_prompts_dir: Directory to store custom prompts. If None, uses the default.
        """
        self.custom_prompts_dir = custom_prompts_dir or DEFAULT_CUSTOM_PROMPTS_DIR
        self.custom_prompts = {}
        self._ensure_dir_exists()
        self.load_custom_prompts()
    
    def _ensure_dir_exists(self):
        """Ensure the custom prompts directory exists."""
        os.makedirs(self.custom_prompts_dir, exist_ok=True)
    
    def load_custom_prompts(self):
        """Load custom prompts from the prompts directory."""
        self.custom_prompts = {}
        
        try:
            # Get all JSON files in the directory
            for filename in os.listdir(self.custom_prompts_dir):
                if filename.endswith('.json'):
                    file_path = os.path.join(self.custom_prompts_dir, filename)
                    try:
                        with open(file_path, 'r') as f:
                            prompt_data = json.load(f)
                            # Extract the prompt name from the filename (without extension)
                            prompt_name = os.path.splitext(filename)[0]
                            self.custom_prompts[prompt_name] = prompt_data.get('prompt', '')
                            logger.info(f"Loaded custom prompt: {prompt_name}")
                    except Exception as e:
                        logger.error(f"Error loading custom prompt {filename}: {e}")
        except Exception as e:
            logger.error(f"Error loading custom prompts: {e}")
        
        return self.custom_prompts
    
    def save_custom_prompt(self, name, prompt_text, context=None):
        """Save a custom prompt template.
        
        Args:
            name: Name of the prompt template
            prompt_text: The prompt text
            context: Optional context information
            
        Returns:
            bool: True if successful, False otherwise
        """
        self._ensure_dir_exists()
        
        # Create prompt data
        prompt_data = {
            'prompt': prompt_text,
            'context': context or ''
        }
        
        # Save to file
        file_path = os.path.join(self.custom_prompts_dir, f"{name}.json")
        try:
            with open(file_path, 'w') as f:
                json.dump(prompt_data, f, indent=2)
            
            # Update in-memory prompts
            self.custom_prompts[name] = prompt_text
            logger.info(f"Saved custom prompt: {name}")
            return True
        except Exception as e:
            logger.error(f"Error saving custom prompt {name}: {e}")
            return False
    
    def delete_custom_prompt(self, name):
        """Delete a custom prompt template.
        
        Args:
            name: Name of the prompt template to delete
            
        Returns:
            bool: True if successful, False otherwise
        """
        file_path = os.path.join(self.custom_prompts_dir, f"{name}.json")
        
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                
                # Remove from in-memory prompts
                if name in self.custom_prompts:
                    del self.custom_prompts[name]
                
                logger.info(f"Deleted custom prompt: {name}")
                return True
            except Exception as e:
                logger.error(f"Error deleting custom prompt {name}: {e}")
                return False
        else:
            logger.warning(f"Custom prompt not found: {name}")
            return False
    
    def get_custom_prompt(self, name):
        """Get a custom prompt template.
        
        Args:
            name: Name of the prompt template
            
        Returns:
            dict: Prompt data including prompt text and context
        """
        file_path = os.path.join(self.custom_prompts_dir, f"{name}.json")
        
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading custom prompt {name}: {e}")
                return None
        else:
            logger.warning(f"Custom prompt not found: {name}")
            return None
    
    def get_all_custom_prompts(self):
        """Get all custom prompt templates.
        
        Returns:
            dict: Dictionary of prompt name to prompt text
        """
        return self.custom_prompts
