# Welcome to the **Trainable Segmentation** page of VisionLab AI V4!

This tutorial will guide you through the process of creating custom segmentation models using manual annotations and machine learning.

## Table of Contents

[Overview](#overview)  
[Image Gallery](#image-gallery)  
[Annotation & Training Tabs](#annotation--training-tabs)  
[Segmentation Parameters](#segmentation-parameters)  
[Label Management & Drawing Tools](#label-management--drawing-tools)  
[SAM-Assisted Annotation](#sam-assisted-annotation)  
[Training & Segmentation Controls](#training--segmentation-controls)  
[Post-processing & Export](#post-processing--export)  
[Step-by-Step Workflow](#step-by-step-workflow)  
[Tips & Best Practices](#tips--best-practices)

## Overview

The Trainable Segmentation tool lets you:
- **Manually annotate** regions of interest using brushes, erasers, and smart tools.
- **Extract features** (intensity, edges, texture) at multiple scales for each pixel.
- **Train an XGBoost classifier** on your annotations to generate segmentation maps.
- **Visualize feature importance** and adjust parameters for best results.
- **Export results** for further analysis or machine learning workflows.

---

## Interface Walkthrough

Below is a mapping of the main UI elements (see screenshot) to their functions:

### 1. Image Gallery
- Browse, select, and manage the set of images for segmentation.
- Add or remove images as needed.

### 2. Annotation & Training Tabs
- **Annotate:** Draw labels for each class/region using brush, eraser, magic wand, or point prompt tools. Overlay labels on the original image.
- **Segmentation Result:** View the predicted segmentation map after training.
- **Side by Side Comparison:** Compare the original image and segmentation result interactively.
- **Feature Importance:** Visualize which features (intensity, edges, texture, and their scales) the model used for segmentation.

### 3. Segmentation Parameters
- **Number of Classes:** Set how many distinct regions or materials you want to segment.
- **Feature Extraction Parameters:**
  - **Intensity:** Use raw pixel intensity as a feature.
  - **Edges:** Use edge detection features (e.g., gradients, Sobel filters).
  - **Texture:** Use texture features (e.g., local variance, entropy).
  - **Sigma Range:** Set the minimum and maximum sigma (scale) for multi-scale feature extraction (default: 1–16).
  - **Number of Scales:** Choose how many scales (e.g., 10 or 15) to compute features across.
- **XGBoost Model Parameters:**
  - **Number of Trees (n_estimators):** Controls the number of boosting rounds (trees).
  - **Max Depth:** Maximum tree depth for base learners.
  - **Max Samples:** Maximum number of samples used for training (for large images).


### 4. Label Management & Drawing Tools
- **Select Label:** Choose which class/region you are annotating.
- **Manage Labels:** Add, rename, or remove classes.
- **Draw Label / Erase Label:** Use brush tools to mark regions.
- **Undo Last / Clear All:** Quickly fix mistakes.
- **Magic Wand / Point Prompts:** Semi-automatic selection tools for labeling complex shapes.
- **Brush Size:** Adjust the annotation brush width.

### 5. SAM-Assisted Annotation
- **Enable SAM:** Toggle Segment Anything Model (SAM) integration for enhanced annotation.
- **Magic Wand:** Draw a bounding box to auto-generate a mask with SAM.
- **Point Prompts:** Add positive (foreground) or negative (background) points to refine the mask.
- **Accept/Reject:** Approve or discard SAM-generated masks before adding to your annotation.

### 6. Training & Segmentation Controls
- **Start Training:** Train the XGBoost model using your current annotations and feature parameters.
- **Stop Training:** Interrupt training if needed.
- **Reload Previous Results:** Load results from earlier sessions.
- **Progress Bar:** Monitor training progress.

### 7. Post-processing & Export
- **Export Segmentation:** Save the predicted segmentation map.
- **Export to COCO Format:** Save results for machine learning workflows.
- **Export Annotations:** Download your manual labels for further use.

---

## Step-by-Step Workflow

1. **Add Images:** Load your images using the Image Gallery on the left.
2. **Define Labels:** Click 'Manage Labels' to add, rename, or remove classes.
3. **Annotate:** Select a label and use the brush, eraser, magic wand, or point prompts to mark regions.
4. **(Optional) Use SAM:** Enable SAM for smart annotation. Use the Magic Wand or +Point/-Point tools to generate/refine masks, then Accept or Reject as needed.
5. **Set Feature & Model Parameters:** In the right panel, select which features to use and adjust sigma range and XGBoost settings (trees, depth, samples).
6. **Train Classifier:** Click 'Train Classifier' to train the XGBoost model on your annotations.
7. **Review Results:** Switch to 'Segmentation Result' or 'Side by Side Comparison' to see predictions. Use 'Feature Importance' to see which features matter most.
8. **Refine & Retrain:** If needed, annotate more, tweak parameters, and retrain for better results.
9. **Export:** Save your segmentation results or annotations using the export options.

---

## Practical Tips
- **Label clearly:** Mark clean, representative regions for each class.
- **Use SAM for efficiency:** For complex shapes, let SAM generate a mask, then refine as needed.
- **Tune features:** If results are poor, try enabling/disabling features or adjusting sigma range.
- **Check feature importance:** Use this to understand what the model is learning and improve your labeling.
- **Save your work:** Use Save/Quick Save often to avoid losing progress.

---

For further help, see the in-app help or contact support.
