# Runtime hook to fix torch._numpy module
import sys
import types
import os

# Create the torch._numpy module if it doesn't exist
if 'torch' in sys.modules:
    torch = sys.modules['torch']
    
    # Create torch._numpy module
    torch_numpy = types.ModuleType('torch._numpy')
    sys.modules['torch._numpy'] = torch_numpy
    torch._numpy = torch_numpy
    
    # Create torch._numpy._ndarray module
    torch_numpy_ndarray = types.ModuleType('torch._numpy._ndarray')
    sys.modules['torch._numpy._ndarray'] = torch_numpy_ndarray
    torch_numpy._ndarray = torch_numpy_ndarray
    
    # Create ndarray class
    class ndarray:
        def __init__(self, tensor=None):
            self.tensor = tensor
        
        def __getattr__(self, name):
            # For any attribute access, just return a dummy function
            def dummy(*args, **kwargs):
                return args[0] if args else None
            return dummy
    
    # Add ndarray to _ndarray module
    torch_numpy_ndarray.ndarray = ndarray
    torch_numpy.ndarray = ndarray
    
    # Create torch._numpy._ufuncs module
    torch_numpy_ufuncs = types.ModuleType('torch._numpy._ufuncs')
    sys.modules['torch._numpy._ufuncs'] = torch_numpy_ufuncs
    torch_numpy._ufuncs = torch_numpy_ufuncs
    
    # Define all the binary ufunc names
    binary_ufunc_names = [
        "add", "subtract", "multiply", "divide", "logaddexp", "logaddexp2", 
        "true_divide", "floor_divide", "power", "remainder", "mod", "fmod", 
        "divmod", "gcd", "lcm", "maximum", "minimum", "fmax", "fmin", 
        "copysign", "nextafter", "ldexp", "hypot"
    ]
    
    # Add all binary ufuncs to the module
    for name in binary_ufunc_names:
        def make_dummy(name):
            def dummy(*args, **kwargs):
                return args[0] if args else None
            dummy.__name__ = name
            return dummy
        
        setattr(torch_numpy_ufuncs, name, make_dummy(name))
