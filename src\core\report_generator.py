# src/core/report_generator.py
import os
import json
import logging
import csv
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

import matplotlib.pyplot as plt
import numpy as np

from src.core.project_data import Project, ImageInfo

logger = logging.getLogger(__name__)

class ReportGenerator:
    """Class to generate reports from project analysis results."""

    def __init__(self, project: Project):
        self.project = project

    def generate_summary_report(self, output_dir: Optional[str] = None) -> str:
        """Generate a summary report for all analysis results in the project.

        Args:
            output_dir: Directory to save the report. If None, uses the project's directory.

        Returns:
            Path to the generated report file.
        """
        if output_dir is None:
            output_dir = os.path.join(self.project.project_dir, "reports")

        os.makedirs(output_dir, exist_ok=True)

        # Generate a timestamp for the report filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"project_summary_{timestamp}.csv"
        report_path = os.path.join(output_dir, report_filename)

        # Collect data for the report
        report_data = self._collect_summary_data()

        # Write the report to a CSV file
        with open(report_path, 'w', newline='') as csvfile:
            fieldnames = ['Image', 'Sample ID', 'Magnification',
                         'Analysis Types', 'Grain Count', 'Average Grain Size',
                         'Porosity (%)', 'Notes']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writeheader()
            for row in report_data:
                writer.writerow(row)

        logger.info(f"Generated summary report: {report_path}")
        return report_path

    def _collect_summary_data(self) -> List[Dict[str, Any]]:
        """Collect summary data for all images in the project."""
        report_data = []

        for image_id, image_info in self.project.images.items():
            row = {
                'Image': image_info.filename,
                'Sample ID': image_info.metadata.get('sample_id', ''),
                'Magnification': image_info.metadata.get('magnification', ''),
                'Analysis Types': ', '.join(image_info.analysis_results.keys()),
                'Grain Count': '',
                'Average Grain Size': '',
                'Porosity (%)': '',
                'Notes': image_info.metadata.get('notes', '')
            }

            # Extract data from analysis results if available
            if 'grain_size_analysis' in image_info.analysis_results:
                grain_data = self._extract_grain_analysis_data(image_id)
                if grain_data:
                    row['Grain Count'] = grain_data.get('grain_count', '')
                    row['Average Grain Size'] = grain_data.get('average_size', '')

            if 'image_lab' in image_info.analysis_results:
                porosity_data = self._extract_porosity_data(image_id)
                if porosity_data:
                    row['Porosity (%)'] = porosity_data.get('porosity_percent', '')

            report_data.append(row)

        return report_data

    def _extract_grain_analysis_data(self, image_id: str) -> Dict[str, Any]:
        """Extract grain analysis data for an image."""
        result_path = self.project.get_analysis_result(image_id, 'grain_size_analysis')
        if not result_path or not os.path.exists(result_path):
            return {}

        try:
            with open(result_path, 'r') as f:
                data = json.load(f)
            return data
        except Exception as e:
            logger.error(f"Error loading grain analysis data: {e}")
            return {}

    def _extract_porosity_data(self, image_id: str) -> Dict[str, Any]:
        """Extract porosity analysis data for an image."""
        result_path = self.project.get_analysis_result(image_id, 'image_lab')
        if not result_path or not os.path.exists(result_path):
            return {}

        try:
            with open(result_path, 'r') as f:
                data = json.load(f)
            return data
        except Exception as e:
            logger.error(f"Error loading porosity analysis data: {e}")
            return {}

    def generate_grain_size_distribution_plot(self, image_ids: List[str],
                                             output_dir: Optional[str] = None) -> Optional[str]:
        """Generate a grain size distribution plot for selected images.

        Args:
            image_ids: List of image IDs to include in the plot
            output_dir: Directory to save the plot. If None, uses the project's directory.

        Returns:
            Path to the generated plot file, or None if generation failed.
        """
        if output_dir is None:
            output_dir = os.path.join(self.project.project_dir, "reports")

        os.makedirs(output_dir, exist_ok=True)

        # Collect grain size data for each image
        grain_data = {}
        for image_id in image_ids:
            image_info = self.project.get_image_info(image_id)
            if not image_info:
                continue

            data = self._extract_grain_analysis_data(image_id)
            if data and 'size_distribution' in data:
                grain_data[image_info.filename] = data['size_distribution']

        if not grain_data:
            logger.warning("No grain size data found for the selected images")
            return None

        # Generate the plot
        plt.figure(figsize=(10, 6))

        for filename, distribution in grain_data.items():
            # Convert distribution to arrays
            sizes = [item['size'] for item in distribution]
            counts = [item['count'] for item in distribution]

            plt.plot(sizes, counts, label=filename)

        plt.xlabel('Grain Size (pixels)')
        plt.ylabel('Count')
        plt.title('Grain Size Distribution')
        plt.legend()
        plt.grid(True)

        # Save the plot
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        plot_filename = f"grain_size_distribution_{timestamp}.png"
        plot_path = os.path.join(output_dir, plot_filename)

        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"Generated grain size distribution plot: {plot_path}")
        return plot_path

    def generate_porosity_comparison_plot(self, image_ids: List[str],
                                         output_dir: Optional[str] = None) -> Optional[str]:
        """Generate a porosity comparison bar chart for selected images.

        Args:
            image_ids: List of image IDs to include in the plot
            output_dir: Directory to save the plot. If None, uses the project's directory.

        Returns:
            Path to the generated plot file, or None if generation failed.
        """
        if output_dir is None:
            output_dir = os.path.join(self.project.project_dir, "reports")

        os.makedirs(output_dir, exist_ok=True)

        # Collect porosity data for each image
        porosity_data = {}
        for image_id in image_ids:
            image_info = self.project.get_image_info(image_id)
            if not image_info:
                continue

            data = self._extract_porosity_data(image_id)
            if data and 'porosity_percent' in data:
                porosity_data[image_info.filename] = data['porosity_percent']

        if not porosity_data:
            logger.warning("No porosity data found for the selected images")
            return None

        # Generate the plot
        plt.figure(figsize=(10, 6))

        filenames = list(porosity_data.keys())
        values = list(porosity_data.values())

        plt.bar(filenames, values)
        plt.xlabel('Image')
        plt.ylabel('Porosity (%)')
        plt.title('Porosity Comparison')
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()
        plt.grid(True, axis='y')

        # Save the plot
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        plot_filename = f"porosity_comparison_{timestamp}.png"
        plot_path = os.path.join(output_dir, plot_filename)

        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"Generated porosity comparison plot: {plot_path}")
        return plot_path

    def export_results_to_csv(self, analysis_type: str,
                            image_ids: Optional[List[str]] = None,
                            output_dir: Optional[str] = None) -> Optional[str]:
        """Export analysis results to a CSV file.

        Args:
            analysis_type: Type of analysis to export
            image_ids: List of image IDs to include. If None, includes all images with this analysis.
            output_dir: Directory to save the CSV. If None, uses the project's directory.

        Returns:
            Path to the generated CSV file, or None if export failed.
        """
        if output_dir is None:
            output_dir = os.path.join(self.project.project_dir, "reports")

        os.makedirs(output_dir, exist_ok=True)

        # If no image IDs provided, find all images with this analysis type
        if image_ids is None:
            image_ids = []
            for img_id, img_info in self.project.images.items():
                if analysis_type in img_info.analysis_results:
                    image_ids.append(img_id)

        if not image_ids:
            logger.warning(f"No images found with analysis type: {analysis_type}")
            return None

        # Generate the CSV filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_filename = f"{analysis_type}_results_{timestamp}.csv"
        csv_path = os.path.join(output_dir, csv_filename)

        # Export based on analysis type
        if analysis_type == 'grain_size_analysis':
            success = self._export_grain_size_results(image_ids, csv_path)
        elif analysis_type == 'image_lab':
            success = self._export_porosity_results(image_ids, csv_path)
        else:
            logger.warning(f"Export not implemented for analysis type: {analysis_type}")
            return None

        if success:
            logger.info(f"Exported {analysis_type} results to: {csv_path}")
            return csv_path
        else:
            return None

    def _export_grain_size_results(self, image_ids: List[str], csv_path: str) -> bool:
        """Export grain size analysis results to CSV."""
        try:
            with open(csv_path, 'w', newline='') as csvfile:
                fieldnames = ['Image', 'Sample ID', 'Grain Count', 'Min Size',
                             'Max Size', 'Average Size', 'Median Size']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

                for image_id in image_ids:
                    image_info = self.project.get_image_info(image_id)
                    if not image_info:
                        continue

                    data = self._extract_grain_analysis_data(image_id)
                    if not data:
                        continue

                    row = {
                        'Image': image_info.filename,
                        'Sample ID': image_info.metadata.get('sample_id', ''),
                        'Grain Count': data.get('grain_count', ''),
                        'Min Size': data.get('min_size', ''),
                        'Max Size': data.get('max_size', ''),
                        'Average Size': data.get('average_size', ''),
                        'Median Size': data.get('median_size', '')
                    }
                    writer.writerow(row)

            return True
        except Exception as e:
            logger.exception(f"Error exporting grain size results: {e}")
            return False

    def _export_porosity_results(self, image_ids: List[str], csv_path: str) -> bool:
        """Export porosity analysis results to CSV."""
        try:
            with open(csv_path, 'w', newline='') as csvfile:
                fieldnames = ['Image', 'Sample ID', 'Porosity (%)',
                             'Total Area', 'Pore Area', 'Pore Count']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

                for image_id in image_ids:
                    image_info = self.project.get_image_info(image_id)
                    if not image_info:
                        continue

                    data = self._extract_porosity_data(image_id)
                    if not data:
                        continue

                    row = {
                        'Image': image_info.filename,
                        'Sample ID': image_info.metadata.get('sample_id', ''),
                        'Porosity (%)': data.get('porosity_percent', ''),
                        'Total Area': data.get('total_area', ''),
                        'Pore Area': data.get('pore_area', ''),
                        'Pore Count': data.get('pore_count', '')
                    }
                    writer.writerow(row)

            return True
        except Exception as e:
            logger.exception(f"Error exporting porosity results: {e}")
            return False