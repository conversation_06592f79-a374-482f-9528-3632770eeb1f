#!/usr/bin/env python3
"""
Vision Lab Waitlist API
Secure backend for handling waitlist submissions with proper validation and security measures.
"""

import os
import json
import hashlib
import secrets
import sqlite3
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from dataclasses import dataclass
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import smtplib
import re
from functools import wraps
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('waitlist_api.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class WaitlistEntry:
    """Data class for waitlist entries"""
    full_name: str
    email: str
    organization: Optional[str] = None
    use_case: Optional[str] = None
    experience: Optional[str] = None
    newsletter: bool = False
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    timestamp: Optional[datetime] = None
    verification_token: Optional[str] = None

class SecurityManager:
    """Handles security-related operations"""
    
    @staticmethod
    def generate_token() -> str:
        """Generate a secure random token"""
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def hash_email(email: str) -> str:
        """Hash email for privacy protection"""
        salt = os.environ.get('EMAIL_SALT', 'default_salt_change_in_production')
        return hashlib.sha256(f"{email}{salt}".encode()).hexdigest()
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """Validate email format"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def sanitize_input(text: str) -> str:
        """Sanitize user input"""
        if not text:
            return ""
        # Remove potentially dangerous characters
        sanitized = re.sub(r'[<>"\'\/\\]', '', text)
        return sanitized.strip()[:255]  # Limit length

class RateLimiter:
    """Simple rate limiting implementation"""
    
    def __init__(self):
        self.requests = {}
        self.cleanup_interval = 3600  # 1 hour
        self.last_cleanup = time.time()
    
    def is_allowed(self, ip_address: str, max_requests: int = 5, window: int = 3600) -> bool:
        """Check if request is allowed based on rate limiting"""
        current_time = time.time()
        
        # Cleanup old entries
        if current_time - self.last_cleanup > self.cleanup_interval:
            self._cleanup_old_entries(current_time - window)
            self.last_cleanup = current_time
        
        # Check current IP
        if ip_address not in self.requests:
            self.requests[ip_address] = []
        
        # Remove old requests for this IP
        self.requests[ip_address] = [
            req_time for req_time in self.requests[ip_address]
            if current_time - req_time < window
        ]
        
        # Check if under limit
        if len(self.requests[ip_address]) >= max_requests:
            return False
        
        # Add current request
        self.requests[ip_address].append(current_time)
        return True
    
    def _cleanup_old_entries(self, cutoff_time: float):
        """Remove old entries from rate limiter"""
        for ip in list(self.requests.keys()):
            self.requests[ip] = [
                req_time for req_time in self.requests[ip]
                if req_time > cutoff_time
            ]
            if not self.requests[ip]:
                del self.requests[ip]

class DatabaseManager:
    """Handles database operations"""
    
    def __init__(self, db_path: str = 'waitlist.db'):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize the database with required tables"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Create waitlist table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS waitlist (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email_hash TEXT UNIQUE NOT NULL,
                    full_name_encrypted TEXT NOT NULL,
                    organization_encrypted TEXT,
                    use_case TEXT,
                    experience TEXT,
                    newsletter BOOLEAN DEFAULT FALSE,
                    ip_address_hash TEXT,
                    user_agent_hash TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    verification_token TEXT UNIQUE,
                    verified BOOLEAN DEFAULT FALSE,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Create rate limiting table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS rate_limits (
                    ip_hash TEXT PRIMARY KEY,
                    request_count INTEGER DEFAULT 1,
                    window_start DATETIME DEFAULT CURRENT_TIMESTAMP,
                    last_request DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Create audit log table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS audit_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    action TEXT NOT NULL,
                    ip_hash TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    details TEXT
                )
            ''')
            
            conn.commit()
    
    def add_waitlist_entry(self, entry: WaitlistEntry) -> bool:
        """Add a new waitlist entry to the database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Hash sensitive data
                email_hash = SecurityManager.hash_email(entry.email)
                ip_hash = hashlib.sha256(entry.ip_address.encode()).hexdigest() if entry.ip_address else None
                ua_hash = hashlib.sha256(entry.user_agent.encode()).hexdigest() if entry.user_agent else None
                
                # Simple encryption for names (in production, use proper encryption)
                name_encrypted = self._simple_encrypt(entry.full_name)
                org_encrypted = self._simple_encrypt(entry.organization) if entry.organization else None
                
                cursor.execute('''
                    INSERT INTO waitlist (
                        email_hash, full_name_encrypted, organization_encrypted,
                        use_case, experience, newsletter, ip_address_hash,
                        user_agent_hash, verification_token
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    email_hash, name_encrypted, org_encrypted,
                    entry.use_case, entry.experience, entry.newsletter,
                    ip_hash, ua_hash, entry.verification_token
                ))
                
                conn.commit()
                
                # Log the action
                self._log_action('waitlist_signup', ip_hash, f'Email hash: {email_hash[:8]}...')
                
                return True
                
        except sqlite3.IntegrityError:
            logger.warning(f"Duplicate email attempt: {email_hash[:8]}...")
            return False
        except Exception as e:
            logger.error(f"Database error: {e}")
            return False
    
    def email_exists(self, email: str) -> bool:
        """Check if email already exists in waitlist"""
        email_hash = SecurityManager.hash_email(email)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT 1 FROM waitlist WHERE email_hash = ?', (email_hash,))
            return cursor.fetchone() is not None
    
    def get_waitlist_count(self) -> int:
        """Get total number of waitlist entries"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM waitlist')
            return cursor.fetchone()[0]
    
    def _simple_encrypt(self, text: str) -> str:
        """Simple encryption (use proper encryption in production)"""
        if not text:
            return ""
        # This is a placeholder - use proper encryption like Fernet in production
        key = os.environ.get('ENCRYPTION_KEY', 'default_key_change_in_production')
        return hashlib.sha256(f"{text}{key}".encode()).hexdigest()
    
    def _log_action(self, action: str, ip_hash: str, details: str = ""):
        """Log actions for audit purposes"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    'INSERT INTO audit_log (action, ip_hash, details) VALUES (?, ?, ?)',
                    (action, ip_hash, details)
                )
                conn.commit()
        except Exception as e:
            logger.error(f"Audit log error: {e}")

class EmailService:
    """Handles email notifications"""
    
    def __init__(self):
        self.smtp_server = os.environ.get('SMTP_SERVER', 'localhost')
        self.smtp_port = int(os.environ.get('SMTP_PORT', '587'))
        self.smtp_username = os.environ.get('SMTP_USERNAME', '')
        self.smtp_password = os.environ.get('SMTP_PASSWORD', '')
        self.from_email = os.environ.get('FROM_EMAIL', '<EMAIL>')
    
    def send_welcome_email(self, email: str, name: str, verification_token: str) -> bool:
        """Send welcome email to new waitlist member"""
        try:
            msg = MIMEMultipart('alternative')
            msg['Subject'] = 'Welcome to Vision Lab Beta Waitlist!'
            msg['From'] = self.from_email
            msg['To'] = email
            
            # Create HTML content
            html_content = self._create_welcome_email_html(name, verification_token)
            html_part = MIMEText(html_content, 'html')
            msg.attach(html_part)
            
            # Send email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                if self.smtp_username and self.smtp_password:
                    server.login(self.smtp_username, self.smtp_password)
                server.send_message(msg)
            
            logger.info(f"Welcome email sent to {email[:3]}...@{email.split('@')[1]}")
            return True
            
        except Exception as e:
            logger.error(f"Email sending error: {e}")
            return False
    
    def _create_welcome_email_html(self, name: str, verification_token: str) -> str:
        """Create HTML content for welcome email"""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Welcome to Vision Lab</title>
            <style>
                body {{ font-family: 'Inter', Arial, sans-serif; line-height: 1.6; color: #171726; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: linear-gradient(135deg, #446493, #51C8E5); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }}
                .content {{ background: white; padding: 30px; border: 1px solid #C4C6C6; }}
                .footer {{ background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; font-size: 14px; color: #C4C6C6; }}
                .btn {{ display: inline-block; background: #446493; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Welcome to Vision Lab!</h1>
                    <p>Thank you for joining our beta waitlist</p>
                </div>
                <div class="content">
                    <p>Hi {SecurityManager.sanitize_input(name)},</p>
                    <p>We're excited to have you on the Vision Lab beta waitlist! You're now among the first to know about our revolutionary computer vision platform.</p>
                    <p><strong>What's next?</strong></p>
                    <ul>
                        <li>We'll notify you as soon as beta access becomes available</li>
                        <li>You'll receive exclusive updates about our development progress</li>
                        <li>Priority access to training materials and documentation</li>
                    </ul>
                    <p>In the meantime, feel free to explore our website and learn more about Vision Lab's capabilities.</p>
                    <p>Best regards,<br>The Vision Lab Team</p>
                </div>
                <div class="footer">
                    <p>This email was sent to you because you signed up for the Vision Lab beta waitlist.</p>
                    <p>© 2024 Vision Lab. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        """

class WaitlistAPI:
    """Main API class for handling waitlist operations"""
    
    def __init__(self):
        self.db = DatabaseManager()
        self.rate_limiter = RateLimiter()
        self.email_service = EmailService()
        self.security = SecurityManager()
    
    def add_to_waitlist(self, data: Dict[str, Any], ip_address: str, user_agent: str) -> Dict[str, Any]:
        """Add user to waitlist with full validation and security checks"""
        
        # Rate limiting
        if not self.rate_limiter.is_allowed(ip_address):
            logger.warning(f"Rate limit exceeded for IP: {ip_address}")
            return {
                'success': False,
                'error': 'Too many requests. Please try again later.',
                'code': 'RATE_LIMIT_EXCEEDED'
            }
        
        # Validate required fields
        required_fields = ['fullName', 'email', 'privacy']
        for field in required_fields:
            if not data.get(field):
                return {
                    'success': False,
                    'error': f'Missing required field: {field}',
                    'code': 'VALIDATION_ERROR'
                }
        
        # Validate email
        email = data['email'].lower().strip()
        if not self.security.validate_email(email):
            return {
                'success': False,
                'error': 'Invalid email format',
                'code': 'INVALID_EMAIL'
            }
        
        # Check if email already exists
        if self.db.email_exists(email):
            return {
                'success': False,
                'error': 'Email already registered',
                'code': 'DUPLICATE_EMAIL'
            }
        
        # Sanitize inputs
        full_name = self.security.sanitize_input(data['fullName'])
        organization = self.security.sanitize_input(data.get('organization', ''))
        
        # Validate enum fields
        valid_use_cases = ['research', 'industrial', 'medical', 'geosciences', 'materials', 'biology', 'other', '']
        valid_experience = ['beginner', 'intermediate', 'advanced', 'expert', '']
        
        use_case = data.get('useCase', '') if data.get('useCase') in valid_use_cases else ''
        experience = data.get('experience', '') if data.get('experience') in valid_experience else ''
        
        # Create waitlist entry
        entry = WaitlistEntry(
            full_name=full_name,
            email=email,
            organization=organization,
            use_case=use_case,
            experience=experience,
            newsletter=bool(data.get('newsletter', False)),
            ip_address=ip_address,
            user_agent=user_agent,
            timestamp=datetime.now(),
            verification_token=self.security.generate_token()
        )
        
        # Add to database
        if self.db.add_waitlist_entry(entry):
            # Send welcome email (optional, don't fail if email fails)
            try:
                self.email_service.send_welcome_email(
                    email, full_name, entry.verification_token
                )
            except Exception as e:
                logger.error(f"Email sending failed: {e}")
            
            # Get current waitlist count
            count = self.db.get_waitlist_count()
            
            logger.info(f"New waitlist signup: {email[:3]}...@{email.split('@')[1]} (Total: {count})")
            
            return {
                'success': True,
                'message': 'Successfully added to waitlist',
                'waitlist_position': count,
                'verification_sent': True
            }
        else:
            return {
                'success': False,
                'error': 'Failed to add to waitlist',
                'code': 'DATABASE_ERROR'
            }
    
    def get_stats(self) -> Dict[str, Any]:
        """Get waitlist statistics (for admin use)"""
        return {
            'total_signups': self.db.get_waitlist_count(),
            'timestamp': datetime.now().isoformat()
        }

# Example usage and testing
if __name__ == '__main__':
    # Initialize API
    api = WaitlistAPI()
    
    # Test data
    test_data = {
        'fullName': 'John Doe',
        'email': '<EMAIL>',
        'organization': 'Test University',
        'useCase': 'research',
        'experience': 'intermediate',
        'newsletter': True,
        'privacy': True
    }
    
    # Test submission
    result = api.add_to_waitlist(test_data, '127.0.0.1', 'Test User Agent')
    print(json.dumps(result, indent=2))
    
    # Test stats
    stats = api.get_stats()
    print(json.dumps(stats, indent=2))