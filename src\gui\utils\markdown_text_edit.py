"""
Markdown Text Edit Widget for VisionLab Ai.
This module provides a QTextEdit subclass that can render markdown text.
"""

import re
from PySide6.QtWidgets import QTextEdit
from PySide6.QtGui import QTextCursor, QTextCharFormat, QFont, QColor, QTextBlockFormat
from PySide6.QtCore import Qt

class MarkdownTextEdit(QTextEdit):
    """A QTextEdit subclass that can render markdown text."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setReadOnly(True)
        self.setStyleSheet("font-family: monospace;")
        
        # Define formats for different markdown elements
        self._header_formats = {
            1: self._create_header_format(24, True),
            2: self._create_header_format(20, True),
            3: self._create_header_format(16, True),
            4: self._create_header_format(14, True),
            5: self._create_header_format(12, True),
            6: self._create_header_format(12, False),
        }
        
        self._bold_format = self._create_format(weight=QFont.Weight.Bold)
        self._italic_format = self._create_format(italic=True)
        self._code_format = self._create_format(family="Courier New", background=QColor("#f0f0f0"))
        self._link_format = self._create_format(underline=True, color=QColor("blue"))
        
        # Block formats
        self._block_format = QTextBlockFormat()
        self._block_format.setAlignment(Qt.AlignmentFlag.AlignLeft)
        
        self._code_block_format = QTextBlockFormat()
        self._code_block_format.setBackground(QColor("#f0f0f0"))
        self._code_block_format.setLeftMargin(20)
        self._code_block_format.setRightMargin(20)
        
        self._quote_block_format = QTextBlockFormat()
        self._quote_block_format.setLeftMargin(20)
        self._quote_block_format.setBackground(QColor("#f5f5f5"))
        self._quote_block_format.setProperty(QTextBlockFormat.BlockIndent, 1)

    def _create_header_format(self, size, bold=False):
        """Create a format for headers."""
        format = QTextCharFormat()
        font = QFont()
        font.setPointSize(size)
        if bold:
            font.setWeight(QFont.Weight.Bold)
        format.setFont(font)
        return format
    
    def _create_format(self, family=None, size=None, weight=None, italic=False, 
                      underline=False, color=None, background=None):
        """Create a text format with the specified properties."""
        format = QTextCharFormat()
        font = format.font()
        
        if family:
            font.setFamily(family)
        if size:
            font.setPointSize(size)
        if weight:
            font.setWeight(weight)
        font.setItalic(italic)
        font.setUnderline(underline)
        
        format.setFont(font)
        
        if color:
            format.setForeground(color)
        if background:
            format.setBackground(background)
            
        return format

    def setMarkdown(self, text):
        """Set the markdown text and render it."""
        self.clear()
        if not text:
            return
            
        cursor = self.textCursor()
        cursor.beginEditBlock()
        
        # Process the text line by line
        lines = text.split('\n')
        i = 0
        in_code_block = False
        code_block_content = []
        in_list = False
        
        while i < len(lines):
            line = lines[i]
            
            # Check for code blocks
            if line.strip().startswith('```'):
                in_code_block = not in_code_block
                in_list = False  # Reset list state
                
                # If we're ending a code block, render it
                if not in_code_block and code_block_content:
                    self._insert_code_block(cursor, code_block_content)
                    code_block_content = []
                
                i += 1
                continue
                
            # If we're in a code block, collect the content
            if in_code_block:
                code_block_content.append(line)
                i += 1
                continue
                
            # Headers
            header_match = re.match(r'^(#{1,6})\s+(.+)$', line)
            if header_match:
                level = len(header_match.group(1))
                text = header_match.group(2)
                self._insert_header(cursor, text, level)
                in_list = False  # Reset list state
                i += 1
                continue
                
            # Blockquotes
            if line.strip().startswith('>'):
                text = line.strip()[1:].strip()
                self._insert_blockquote(cursor, text)
                in_list = False  # Reset list state
                i += 1
                continue
                
            # Enhanced list detection - handle various bullet styles and indentation
            list_match = re.match(r'^(\s*)([-*+•]|\d+\.)\s+(.+)$', line)
            if list_match:
                indent = len(list_match.group(1))
                marker = list_match.group(2)
                text = list_match.group(3)
                self._insert_list_item(cursor, text, indent, marker)
                in_list = True
                i += 1
                continue
            
            # Also handle lines that start with just * (common in AI output)
            simple_bullet_match = re.match(r'^(\s*)\*\s+(.+)$', line)
            if simple_bullet_match:
                indent = len(simple_bullet_match.group(1))
                text = simple_bullet_match.group(2)
                self._insert_list_item(cursor, text, indent, '*')
                in_list = True
                i += 1
                continue
            
            # Handle continuation of list items (lines that start with spaces but no bullet)
            elif in_list and line.strip() and re.match(r'^\s+[^-*+•\d]', line):
                # This is likely a continuation of the previous list item
                stripped_line = line.strip()
                if stripped_line:
                    self._insert_list_continuation(cursor, stripped_line)
                i += 1
                continue
                
            # Empty line - break list context but still insert the line
            elif not line.strip():
                in_list = False
                # Only insert block if we're not already at a block start
                if not cursor.atBlockStart():
                    cursor.insertBlock()
                i += 1
                continue
                
            # Check for numbered sections (like "1. Overall Texture:")
            numbered_section_match = re.match(r'^(\d+\.\s+.+:)\s*$', line)
            if numbered_section_match:
                # Treat as a header-like element
                self._insert_numbered_section(cursor, numbered_section_match.group(1))
                in_list = False
                i += 1
                continue
            
            # Regular paragraph with inline formatting
            in_list = False  # Reset list state for regular paragraphs
            self._insert_paragraph(cursor, line)
            i += 1
            
        cursor.endEditBlock()
        self.setTextCursor(cursor)

    def _insert_header(self, cursor, text, level):
        """Insert a header with the appropriate format."""
        # Add some spacing before header if not at the beginning
        if cursor.position() > 0:
            cursor.insertBlock()
        cursor.insertBlock()
        cursor.setBlockFormat(self._block_format)
        cursor.setCharFormat(self._header_formats.get(level, self._header_formats[1]))
        cursor.insertText(text)
        cursor.setCharFormat(QTextCharFormat())  # Reset format
        cursor.insertBlock()  # Add an empty line after header

    def _insert_paragraph(self, cursor, text):
        """Insert a paragraph with inline formatting."""
        # Skip empty lines
        if not text.strip():
            return
            
        # Only insert a new block if we're not already at the start of a block
        if cursor.position() > 0 and not cursor.atBlockStart():
            cursor.insertBlock()
        elif cursor.position() == 0:
            # We're at the very beginning, just set the format
            pass
        else:
            cursor.insertBlock()
            
        cursor.setBlockFormat(self._block_format)
        
        # Use the shared inline formatting method
        self._insert_inline_formatted_text(cursor, text)

    def _insert_code_block(self, cursor, lines):
        """Insert a code block."""
        cursor.insertBlock()
        cursor.setBlockFormat(self._code_block_format)
        cursor.setCharFormat(self._code_format)
        
        for line in lines:
            cursor.insertText(line)
            cursor.insertBlock()
            cursor.setBlockFormat(self._code_block_format)
            
        cursor.setCharFormat(QTextCharFormat())
        cursor.setBlockFormat(self._block_format)

    def _insert_blockquote(self, cursor, text):
        """Insert a blockquote."""
        cursor.insertBlock()
        cursor.setBlockFormat(self._quote_block_format)
        self._insert_inline_formatted_text(cursor, text)
        cursor.setBlockFormat(self._block_format)
    
    def _insert_numbered_section(self, cursor, text):
        """Insert a numbered section header (like '1. Overall Texture:')."""
        cursor.insertBlock()
        cursor.setBlockFormat(self._block_format)
        
        # Create a format that's bold and slightly larger
        section_format = QTextCharFormat()
        font = section_format.font()
        font.setWeight(QFont.Weight.Bold)
        font.setPointSize(font.pointSize() + 2)
        section_format.setFont(font)
        
        cursor.setCharFormat(section_format)
        cursor.insertText(text)
        cursor.setCharFormat(QTextCharFormat())  # Reset format
        cursor.insertBlock()  # Add spacing after section header

    def _insert_list_item(self, cursor, text, indent, marker):
        """Insert a list item with proper formatting."""
        cursor.insertBlock()
        
        # Calculate indentation level (every 2 spaces = 1 level)
        indent_level = max(0, indent // 2)
        
        block_format = QTextBlockFormat()
        block_format.setLeftMargin(20 + (20 * indent_level))  # Base margin + indent
        block_format.setTextIndent(-15)  # Hanging indent for bullet
        cursor.setBlockFormat(block_format)
        
        # Normalize bullet markers
        if marker in ['*', '+', '•']:
            display_marker = '•'  # Use consistent bullet
        elif marker == '-':
            display_marker = '–'  # Use en-dash for better visibility
        elif re.match(r'\d+\.', marker):
            display_marker = marker  # Keep numbered lists as-is
        else:
            display_marker = '•'  # Default to bullet for unknown markers
        
        # Insert the marker with some spacing
        cursor.insertText(f"{display_marker}  ")
        
        # Insert the text with inline formatting, but don't create a new paragraph
        self._insert_inline_formatted_text(cursor, text)
    
    def _insert_list_continuation(self, cursor, text):
        """Insert continuation text for a list item."""
        cursor.insertBlock()
        
        # Use the same formatting as the previous block
        current_format = cursor.blockFormat()
        cursor.setBlockFormat(current_format)
        
        # Insert the continuation text with inline formatting
        self._insert_inline_formatted_text(cursor, text)
    
    def _insert_inline_formatted_text(self, cursor, text):
        """Insert text with inline formatting without creating a new paragraph."""
        # Reset character format
        cursor.setCharFormat(QTextCharFormat())
        
        # Process inline formatting
        i = 0
        while i < len(text):
            # Bold (**text**)
            if i + 1 < len(text) and text[i:i+2] == '**' and '**' in text[i+2:]:
                end = text.find('**', i+2)
                if end != -1:
                    cursor.setCharFormat(self._bold_format)
                    cursor.insertText(text[i+2:end])
                    cursor.setCharFormat(QTextCharFormat())
                    i = end + 2
                    continue
                
            # Italic (*text*) - but avoid conflict with bullet points
            if (text[i] == '*' and i + 1 < len(text) and text[i+1] != '*' and 
                '*' in text[i+1:] and not text[i:i+2] == '* '):
                end = text.find('*', i+1)
                if end != -1:
                    cursor.setCharFormat(self._italic_format)
                    cursor.insertText(text[i+1:end])
                    cursor.setCharFormat(QTextCharFormat())
                    i = end + 1
                    continue
                
            # Inline code (`text`)
            if text[i] == '`' and '`' in text[i+1:]:
                end = text.find('`', i+1)
                if end != -1:
                    cursor.setCharFormat(self._code_format)
                    cursor.insertText(text[i+1:end])
                    cursor.setCharFormat(QTextCharFormat())
                    i = end + 1
                    continue
                
            # Links [text](url)
            if text[i] == '[' and '](' in text[i:] and ')' in text[i:]:
                text_end = text.find('](', i)
                link_end = text.find(')', text_end)
                if text_end != -1 and link_end != -1:
                    link_text = text[i+1:text_end]
                    cursor.setCharFormat(self._link_format)
                    cursor.insertText(link_text)
                    cursor.setCharFormat(QTextCharFormat())
                    i = link_end + 1
                    continue
            
            # Regular text
            cursor.insertText(text[i])
            i += 1
