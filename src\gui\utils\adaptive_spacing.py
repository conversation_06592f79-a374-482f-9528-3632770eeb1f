"""Adaptive spacing utility for Vision Lab.

This module provides functionality to automatically adapt button spacing and padding
based on screen resolution and user preferences.
"""

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QRect
import logging

logger = logging.getLogger(__name__)

# Resolution thresholds (width x height)
RESOLUTION_THRESHOLDS = {
    "1080p (1920x1080)": (1920, 1080),
    "1440p (2560x1440)": (2560, 1440),
    "4K (3840x2160)": (3840, 2160),
    "Custom": None  # Will use current screen resolution
}

def get_screen_resolution():
    """Get the current screen resolution.
    
    Returns:
        tuple: (width, height) of the primary screen
    """
    try:
        app = QApplication.instance()
        if app:
            screen = app.primaryScreen()
            if screen:
                geometry = screen.geometry()
                return (geometry.width(), geometry.height())
    except Exception as e:
        logger.warning(f"Could not get screen resolution: {e}")
    
    # Fallback to common resolution
    return (1920, 1080)

def get_screen_dpi():
    """Get the current screen DPI.
    
    Returns:
        float: DPI of the primary screen
    """
    try:
        app = QApplication.instance()
        if app:
            screen = app.primaryScreen()
            if screen:
                return screen.logicalDotsPerInch()
    except Exception as e:
        logger.warning(f"Could not get screen DPI: {e}")
    
    # Fallback to standard DPI
    return 96.0

def calculate_resolution_scale_factor(current_resolution, threshold_resolution):
    """Calculate scale factor based on resolution difference.
    
    Args:
        current_resolution (tuple): Current screen resolution (width, height)
        threshold_resolution (tuple): Threshold resolution (width, height)
    
    Returns:
        float: Scale factor for spacing adjustments
    """
    if not threshold_resolution:
        return 1.0
    
    current_width, current_height = current_resolution
    threshold_width, threshold_height = threshold_resolution
    
    # Calculate scale based on diagonal resolution
    current_diagonal = (current_width ** 2 + current_height ** 2) ** 0.5
    threshold_diagonal = (threshold_width ** 2 + threshold_height ** 2) ** 0.5
    
    scale_factor = current_diagonal / threshold_diagonal
    
    # Clamp scale factor to reasonable bounds (0.5x to 3.0x)
    return max(0.5, min(3.0, scale_factor))

def calculate_dpi_scale_factor(current_dpi, base_dpi=96.0):
    """Calculate scale factor based on DPI difference.
    
    Args:
        current_dpi (float): Current screen DPI
        base_dpi (float): Base DPI for calculations (default: 96.0)
    
    Returns:
        float: Scale factor for high-DPI adjustments
    """
    scale_factor = current_dpi / base_dpi
    
    # Clamp scale factor to reasonable bounds (0.8x to 4.0x)
    return max(0.8, min(4.0, scale_factor))

def get_adaptive_spacing_params(settings_manager=None):
    """Get adaptive spacing parameters from settings.
    
    Args:
        settings_manager: Settings manager instance
    
    Returns:
        dict: Dictionary containing adaptive spacing parameters
    """
    default_params = {
        'enabled': True,
        'base_multiplier': 1.0,
        'high_dpi_scaling': 1.5,
        'resolution_threshold': '1440p (2560x1440)'
    }
    
    if not settings_manager:
        return default_params
    
    try:
        return {
            'enabled': settings_manager.get_value("app/adaptive_spacing_enabled", True),
            'base_multiplier': settings_manager.get_value("app/base_spacing_multiplier", 100) / 100.0,
            'high_dpi_scaling': settings_manager.get_value("app/high_dpi_scaling", 150) / 100.0,
            'resolution_threshold': settings_manager.get_value("app/resolution_threshold", "1440p (2560x1440)")
        }
    except Exception as e:
        logger.warning(f"Could not load adaptive spacing settings: {e}")
        return default_params

def calculate_adaptive_style_params(base_style_params, settings_manager=None):
    """Calculate adaptive style parameters based on screen resolution and settings.
    
    Args:
        base_style_params (dict): Base style parameters
        settings_manager: Settings manager instance
    
    Returns:
        dict: Updated style parameters with adaptive spacing
    """
    # Get adaptive spacing settings
    adaptive_params = get_adaptive_spacing_params(settings_manager)
    
    # If adaptive spacing is disabled, return original parameters
    if not adaptive_params['enabled']:
        return base_style_params.copy()
    
    # Get current screen information
    current_resolution = get_screen_resolution()
    current_dpi = get_screen_dpi()
    
    # Get threshold resolution
    threshold_name = adaptive_params['resolution_threshold']
    threshold_resolution = RESOLUTION_THRESHOLDS.get(threshold_name)
    
    if threshold_name == "Custom":
        threshold_resolution = current_resolution
    
    # Calculate scale factors
    resolution_scale = calculate_resolution_scale_factor(current_resolution, threshold_resolution)
    dpi_scale = calculate_dpi_scale_factor(current_dpi)
    
    # Apply high-DPI scaling only if DPI is significantly higher than standard
    if dpi_scale > 1.2:
        dpi_scale = min(dpi_scale, adaptive_params['high_dpi_scaling'])
    else:
        dpi_scale = 1.0
    
    # Calculate final scale factor
    base_multiplier = adaptive_params['base_multiplier']
    final_scale = base_multiplier * resolution_scale * dpi_scale
    
    # Clamp final scale to reasonable bounds
    final_scale = max(0.5, min(3.0, final_scale))
    
    # Create updated style parameters
    updated_params = base_style_params.copy()
    
    # Apply scaling to spacing-related parameters
    spacing_params = [
        'padding',
        'border-radius',
        'button-radius',
        'control-height',
        'slider-height',
        'slider-handle-size',
        'section-border-width'
    ]
    
    for param in spacing_params:
        if param in updated_params:
            original_value = updated_params[param]
            scaled_value = int(round(original_value * final_scale))
            
            # Ensure minimum values for usability
            if param == 'section-border-width':
                scaled_value = max(1, scaled_value)  # Minimum 1px border
            elif param in ['border-radius', 'button-radius']:
                scaled_value = max(2, scaled_value)  # Minimum 2px radius
            elif param == 'padding':
                scaled_value = max(2, scaled_value)  # Minimum 2px padding
            elif param == 'control-height':
                scaled_value = max(20, scaled_value)  # Minimum 20px height
            elif param == 'slider-height':
                scaled_value = max(2, scaled_value)  # Minimum 2px slider height
            elif param == 'slider-handle-size':
                scaled_value = max(8, scaled_value)  # Minimum 8px handle size
            
            updated_params[param] = scaled_value
    
    logger.info(f"Adaptive spacing applied: resolution={current_resolution}, "
                f"DPI={current_dpi:.1f}, scale={final_scale:.2f}")
    
    return updated_params

def get_current_screen_info():
    """Get current screen information for display purposes.
    
    Returns:
        str: Formatted string with current screen resolution and DPI
    """
    resolution = get_screen_resolution()
    dpi = get_screen_dpi()
    return f"{resolution[0]}x{resolution[1]} @ {dpi:.0f} DPI"