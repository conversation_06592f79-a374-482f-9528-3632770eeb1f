# main.py

import sys
import os
import logging
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtGui import QFont
from PySide6.QtCore import Qt

# --- GUI Imports ---
# Assuming the directory containing 'gui' and 'core' is on the Python path
# or you are running this script from the project root.
try:
    from gui.main_window import GrainAnalysisApp
    from gui.utils import find_system_font, load_font, resource_path # resource_path might be needed for icon in error msg
except ImportError as e:
     # Critical error: GUI components cannot be imported.
     # Try showing a basic message box if possible.
     print(f"FATAL ERROR: Could not import GUI components. Check project structure and PYTHONPATH.")
     print(f"Import Error: {e}")
     # Attempt to show a Qt message box as a last resort
     try:
          app = QApplication([]) # Minimal app needed for message box
          msgBox = QMessageBox()
          msgBox.setIcon(QMessageBox.Critical)
          msgBox.setWindowTitle("Startup Error")
          msgBox.setText(f"Failed to import necessary application components.\n\nPlease ensure the 'gui' and 'core' directories are correctly placed and accessible.\n\nError details: {e}")
          msgBox.setStandardButtons(QMessageBox.Ok)
          msgBox.exec()
     except Exception as me:
          print(f"Could not display error message box: {me}")
     sys.exit(1) # Exit with error code

# --- Logging Setup ---
# Configure logging early, before creating the main window
log_directory = "logs"
os.makedirs(log_directory, exist_ok=True)
log_file_path = os.path.join(log_directory, "grainsight_app.log")

logging.basicConfig(
    level=logging.INFO, # Set to DEBUG for more verbose output during development
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file_path, mode='w'), # Overwrite log each run
        logging.StreamHandler() # Output to console
    ]
)
logger = logging.getLogger(__name__)
logger.info("Application starting...")
logger.info(f"Log file location: {os.path.abspath(log_file_path)}")

# --- Main Execution Function ---
def main():
    # --- Application Setup ---
    # Set Application details for styling and identification
    QApplication.setApplicationName("GrainSight")
    QApplication.setOrganizationName("FaresAzzam") # Optional
    # Enable High DPI scaling BEFORE creating QApplication instance if possible
    # Note: For PySide6, these attributes are often set before QApplication creation.
    # If issues arise, try setting them via environment variables before running.
    try:
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        logger.debug("High DPI attributes set.")
    except Exception as e:
        logger.warning(f"Could not set High DPI attributes: {e}")

    app = QApplication(sys.argv)

    # --- Font Setup ---
    preferred_fonts = ['Arial', 'Helvetica', 'Verdana', 'Sans Serif']
    font_loaded = False
    for font_name in preferred_fonts:
        try:
            font_path = find_system_font(font_name)
            if font_path and load_font(font_path):
                # Set default application font - adjust size as needed
                default_font = QFont(font_name, 9)
                app.setFont(default_font)
                font_loaded = True
                logger.info(f"Using default application font: {font_name}")
                break
        except Exception as font_e:
             logger.error(f"Error setting up font '{font_name}': {font_e}")

    if not font_loaded:
         logger.warning("Could not load preferred fonts. Using system default.")
         # System default will be used automatically

    # --- Initialize and Show Window ---
    try:
        logger.info("Creating main application window...")
        window = GrainAnalysisApp()
        logger.info("Showing main application window...")
        window.show()
        logger.info("Starting Qt event loop.")
        sys.exit(app.exec()) # Start event loop

    except Exception as e:
         logger.critical(f"Unhandled exception during application startup or execution: {e}", exc_info=True)
         # Show error message box before exiting
         msgBox = QMessageBox()
         # Try setting icon - might fail if resource_path has issues here
         try: msgBox.setWindowIcon(QtGui.QIcon(resource_path("icons/grain_icon.png")))
         except: pass
         msgBox.setIcon(QMessageBox.Critical)
         msgBox.setWindowTitle("Fatal Application Error")
         msgBox.setText(f"A critical error occurred and GrainSight must close.\n\nError: {e}\n\nPlease check the log file for details:\n{os.path.abspath(log_file_path)}")
         msgBox.setStandardButtons(QMessageBox.Ok)
         msgBox.exec()
         sys.exit(1)

# --- Entry Point Check ---
if __name__ == "__main__":
    # Add the project root to sys.path to ensure 'core' and 'gui' are importable
    # This is often needed when running main.py directly from the root.
    project_root = os.path.dirname(os.path.abspath(__file__))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
        logger.debug(f"Added project root to sys.path: {project_root}")

    main()