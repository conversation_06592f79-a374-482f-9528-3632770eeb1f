# src/gui/ui/batch_processing_page_ui.py

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                             QGroupBox, QFormLayout, QComboBox, QSpinBox, QDoubleSpinBox,
                             QCheckBox, QProgressBar, QTextEdit, QSplitter, QListWidget,
                             QAbstractItemView, QFrame, QGridLayout, QScrollArea,
                             QTabWidget, QSlider, QSpacerItem, QSizePolicy)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont
from src.widgets.pixmap_view import QPixmapView
from src.widgets.collapsible_section import CollapsibleSection

class BatchProcessingPageUI:
    """UI definition for the Batch Processing page."""

    def setup_batch_processing_page(self):
        """Sets up the Batch Processing page."""
        # Set up the main layout for this widget
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Header section
        header_layout = QHBoxLayout()
        self.batch_title_label = QLabel("Batch Processing")
        self.batch_title_label.setStyleSheet("font-size: 18px; font-weight: bold;")
        header_layout.addWidget(self.batch_title_label)
        header_layout.addStretch()
        
        main_layout.addLayout(header_layout)

        # Main content splitter
        content_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(content_splitter)

        # Left panel - Image list and task selection
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(0, 0, 5, 0)
        
        # Selected images section
        images_group = QGroupBox("Selected Images")
        images_layout = QVBoxLayout(images_group)
        
        self.selected_images_label = QLabel("0 images selected")
        self.selected_images_label.setStyleSheet("font-weight: bold;")
        images_layout.addWidget(self.selected_images_label)
        
        self.selected_images_list = QListWidget()
        self.selected_images_list.setMaximumHeight(150)
        self.selected_images_list.setSelectionMode(QAbstractItemView.SingleSelection)
        images_layout.addWidget(self.selected_images_list)
        
        left_layout.addWidget(images_group)
        
        # Task selection section
        task_group = QGroupBox("Processing Task")
        task_layout = QVBoxLayout(task_group)
        
        self.task_combo = QComboBox()
        self.task_combo.addItems([
            "Grain Size Analysis",
            "Trainable Segmentation",
            "Advanced Segmentation"
        ])
        task_layout.addWidget(self.task_combo)
        
        left_layout.addWidget(task_group)
        
        # Parameters section (will be updated based on selected task)
        self.parameters_group = QGroupBox("Parameters")
        self.parameters_group.setMinimumHeight(400)  # Ensure adequate initial height
        self.parameters_group.setMaximumHeight(650)  # Set maximum height to enable scrolling
        self.parameters_layout = QVBoxLayout(self.parameters_group)
        
        # Create a scroll area for parameters
        self.parameters_scroll = QScrollArea()
        self.parameters_scroll.setWidgetResizable(True)
        self.parameters_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.parameters_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # Main parameters widget with single column layout
        self.parameters_widget = QWidget()
        self.parameters_main_layout = QVBoxLayout(self.parameters_widget)
        self.parameters_main_layout.setContentsMargins(10, 10, 10, 10)
        self.parameters_main_layout.setSpacing(15)
        
        self.parameters_scroll.setWidget(self.parameters_widget)
        self.parameters_layout.addWidget(self.parameters_scroll)
        
        left_layout.addWidget(self.parameters_group)
        
        # Control buttons
        control_layout = QHBoxLayout()
        self.start_batch_button = QPushButton("Start Batch Processing")
        self.start_batch_button.setMinimumHeight(35)
        self.start_batch_button.setStyleSheet("font-weight: bold;")
        self.stop_batch_button = QPushButton("Stop Processing")
        self.stop_batch_button.setMinimumHeight(35)
        self.stop_batch_button.setEnabled(False)
        
        control_layout.addWidget(self.start_batch_button)
        control_layout.addWidget(self.stop_batch_button)
        left_layout.addLayout(control_layout)
        
        left_layout.addStretch()
        content_splitter.addWidget(left_panel)

        # Right panel - Results and progress
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(5, 0, 0, 0)
        
        # Progress section
        progress_group = QGroupBox("Processing Progress")
        progress_layout = QVBoxLayout(progress_group)
        
        self.overall_progress_label = QLabel("Overall Progress: 0/0")
        progress_layout.addWidget(self.overall_progress_label)
        
        self.overall_progress_bar = QProgressBar()
        self.overall_progress_bar.setRange(0, 100)
        self.overall_progress_bar.setValue(0)
        progress_layout.addWidget(self.overall_progress_bar)
        
        self.current_image_label = QLabel("Current: None")
        progress_layout.addWidget(self.current_image_label)
        
        self.current_progress_bar = QProgressBar()
        self.current_progress_bar.setRange(0, 100)
        self.current_progress_bar.setValue(0)
        progress_layout.addWidget(self.current_progress_bar)
        
        right_layout.addWidget(progress_group)
        
        # Results section
        results_group = QGroupBox("Results")
        results_layout = QVBoxLayout(results_group)
        
        # Results tabs
        self.results_tabs = QTabWidget()
        
        # Grid view tab
        self.grid_tab = QWidget()
        grid_layout = QVBoxLayout(self.grid_tab)
        
        # Grid controls
        grid_controls = QHBoxLayout()
        grid_controls.addWidget(QLabel("Grid Size:"))
        self.grid_size_slider = QSlider(Qt.Horizontal)
        self.grid_size_slider.setRange(2, 6)
        self.grid_size_slider.setValue(3)
        self.grid_size_label = QLabel("3x3")
        grid_controls.addWidget(self.grid_size_slider)
        grid_controls.addWidget(self.grid_size_label)
        grid_controls.addStretch()
        
        self.export_results_button = QPushButton("Export Results")
        self.generate_report_button = QPushButton("Generate Report")
        grid_controls.addWidget(self.export_results_button)
        grid_controls.addWidget(self.generate_report_button)
        
        grid_layout.addLayout(grid_controls)
        
        # Remove visualization settings from here - they will be part of grain size analysis parameters
        
        # Results grid (will be populated dynamically)
        self.results_scroll = QScrollArea()
        self.results_scroll.setWidgetResizable(True)
        self.results_grid_widget = QWidget()
        self.results_grid_layout = QGridLayout(self.results_grid_widget)
        self.results_scroll.setWidget(self.results_grid_widget)
        grid_layout.addWidget(self.results_scroll)
        
        self.results_tabs.addTab(self.grid_tab, "Grid View")
        
        # Log tab
        self.log_tab = QWidget()
        log_layout = QVBoxLayout(self.log_tab)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        log_layout.addWidget(self.log_text)
        
        log_controls = QHBoxLayout()
        self.clear_log_button = QPushButton("Clear Log")
        self.save_log_button = QPushButton("Save Log")
        log_controls.addStretch()
        log_controls.addWidget(self.clear_log_button)
        log_controls.addWidget(self.save_log_button)
        log_layout.addLayout(log_controls)
        
        self.results_tabs.addTab(self.log_tab, "Processing Log")
        
        results_layout.addWidget(self.results_tabs)
        right_layout.addWidget(results_group)
        
        content_splitter.addWidget(right_panel)
        
        # Set splitter proportions
        content_splitter.setStretchFactor(0, 1)
        content_splitter.setStretchFactor(1, 2)
        
        # Initialize parameters for default task
        self._setup_grain_size_parameters()
        
    def _setup_grain_size_parameters(self):
        """Set up parameters for grain size analysis using collapsible sections."""
        # Clear existing parameters
        self._clear_parameters()
        
        # Create collapsible sections
        self._create_scale_factor_section()
        self._create_segmentation_section()
        self._create_patch_processing_section()
        self._create_artifact_detection_section()
        self._create_analysis_options_section()
        self._create_visualization_settings_section()
        
        # Add stretch to push sections to the top
        self.parameters_main_layout.addStretch()
        
    def _create_scale_factor_section(self):
        """Create the scale factor configuration collapsible section."""
        scale_section = CollapsibleSection("Scale Factor Configuration")
        scale_section.set_expanded(False)  # Collapsed by default
        
        self.uniform_scale_checkbox = QCheckBox("Use uniform scale factor")
        self.uniform_scale_checkbox.setChecked(True)
        scale_section.add_widget(self.uniform_scale_checkbox)
        
        scale_factor_layout = QHBoxLayout()
        scale_factor_layout.addWidget(QLabel("Scale Factor:"))
        self.scale_factor_spinbox = QDoubleSpinBox()
        self.scale_factor_spinbox.setRange(0.1, 10.0)
        self.scale_factor_spinbox.setSingleStep(0.1)
        self.scale_factor_spinbox.setValue(1.0)
        scale_factor_layout.addWidget(self.scale_factor_spinbox)
        scale_section.add_layout(scale_factor_layout)
        
        # Individual scale factor configuration buttons
        individual_scale_layout = QHBoxLayout()
        self.configure_individual_scale_button = QPushButton("Configure Individual Scale Factors")
        self.configure_individual_scale_button.setEnabled(False)  # Disabled when uniform scale is checked
        individual_scale_layout.addWidget(self.configure_individual_scale_button)
        
        self.load_scale_config_button = QPushButton("Load Scale Configuration")
        self.load_scale_config_button.setEnabled(False)  # Disabled when uniform scale is checked
        individual_scale_layout.addWidget(self.load_scale_config_button)
        
        scale_section.add_layout(individual_scale_layout)
        
        self.parameters_main_layout.addWidget(scale_section)
    
    def _create_segmentation_section(self):
        """Create the segmentation method collapsible section."""
        seg_section = CollapsibleSection("Segmentation Method")
        seg_section.set_expanded(False)  # Collapsed by default
        
        # Segmentation method
        method_layout = QHBoxLayout()
        method_layout.addWidget(QLabel("Method:"))
        self.segmentation_method_combo = QComboBox()
        self.segmentation_method_combo.addItems(["FastSAM", "MobileSAM"])
        method_layout.addWidget(self.segmentation_method_combo)
        seg_section.add_layout(method_layout)
        
        # SAM parameters
        points_layout = QHBoxLayout()
        points_layout.addWidget(QLabel("Points per Side:"))
        self.sam_points_per_side = QSpinBox()
        self.sam_points_per_side.setRange(8, 64)
        self.sam_points_per_side.setValue(32)
        points_layout.addWidget(self.sam_points_per_side)
        seg_section.add_layout(points_layout)
        
        iou_layout = QHBoxLayout()
        iou_layout.addWidget(QLabel("Prediction IoU Threshold:"))
        self.sam_pred_iou_thresh = QDoubleSpinBox()
        self.sam_pred_iou_thresh.setRange(0.1, 1.0)
        self.sam_pred_iou_thresh.setSingleStep(0.1)
        self.sam_pred_iou_thresh.setValue(0.88)
        iou_layout.addWidget(self.sam_pred_iou_thresh)
        seg_section.add_layout(iou_layout)
        
        stability_layout = QHBoxLayout()
        stability_layout.addWidget(QLabel("Stability Score Threshold:"))
        self.sam_stability_score_thresh = QDoubleSpinBox()
        self.sam_stability_score_thresh.setRange(0.1, 1.0)
        self.sam_stability_score_thresh.setSingleStep(0.1)
        self.sam_stability_score_thresh.setValue(0.95)
        stability_layout.addWidget(self.sam_stability_score_thresh)
        seg_section.add_layout(stability_layout)
        
        crop_layers_layout = QHBoxLayout()
        crop_layers_layout.addWidget(QLabel("Crop Layers:"))
        self.sam_crop_n_layers = QSpinBox()
        self.sam_crop_n_layers.setRange(0, 3)
        self.sam_crop_n_layers.setValue(0)
        crop_layers_layout.addWidget(self.sam_crop_n_layers)
        seg_section.add_layout(crop_layers_layout)
        
        downscale_layout = QHBoxLayout()
        downscale_layout.addWidget(QLabel("Crop Points Downscale:"))
        self.sam_crop_n_points_downscale_factor = QSpinBox()
        self.sam_crop_n_points_downscale_factor.setRange(1, 4)
        self.sam_crop_n_points_downscale_factor.setValue(1)
        downscale_layout.addWidget(self.sam_crop_n_points_downscale_factor)
        seg_section.add_layout(downscale_layout)
        
        min_area_layout = QHBoxLayout()
        min_area_layout.addWidget(QLabel("Min Mask Region Area:"))
        self.sam_min_mask_region_area = QSpinBox()
        self.sam_min_mask_region_area.setRange(0, 10000)
        self.sam_min_mask_region_area.setValue(0)
        min_area_layout.addWidget(self.sam_min_mask_region_area)
        seg_section.add_layout(min_area_layout)
        
        # Box NMS Threshold (required for MobileSAM)
        box_nms_layout = QHBoxLayout()
        box_nms_layout.addWidget(QLabel("Box NMS Threshold:"))
        self.sam_box_nms_thresh = QDoubleSpinBox()
        self.sam_box_nms_thresh.setRange(0.1, 1.0)
        self.sam_box_nms_thresh.setSingleStep(0.05)
        self.sam_box_nms_thresh.setValue(0.3)
        box_nms_layout.addWidget(self.sam_box_nms_thresh)
        seg_section.add_layout(box_nms_layout)
        
        # FastSAM-specific parameters
        input_size_layout = QHBoxLayout()
        input_size_layout.addWidget(QLabel("FastSAM Input Size:"))
        self.fastsam_input_size = QSpinBox()
        self.fastsam_input_size.setRange(512, 2048)
        self.fastsam_input_size.setValue(1024)
        self.fastsam_input_size.setSingleStep(64)
        input_size_layout.addWidget(self.fastsam_input_size)
        seg_section.add_layout(input_size_layout)
        
        conf_layout = QHBoxLayout()
        conf_layout.addWidget(QLabel("FastSAM Confidence Threshold:"))
        self.fastsam_conf_thresh = QDoubleSpinBox()
        self.fastsam_conf_thresh.setRange(0.1, 1.0)
        self.fastsam_conf_thresh.setSingleStep(0.1)
        self.fastsam_conf_thresh.setValue(0.4)
        conf_layout.addWidget(self.fastsam_conf_thresh)
        seg_section.add_layout(conf_layout)
        
        max_det_layout = QHBoxLayout()
        max_det_layout.addWidget(QLabel("FastSAM Max Detections:"))
        self.fastsam_max_det = QSpinBox()
        self.fastsam_max_det.setRange(10, 1000)
        self.fastsam_max_det.setValue(100)
        max_det_layout.addWidget(self.fastsam_max_det)
        seg_section.add_layout(max_det_layout)
        
        self.parameters_main_layout.addWidget(seg_section)
    
    def _create_patch_processing_section(self):
        """Create the patch processing collapsible section."""
        patch_section = CollapsibleSection("Patch Processing")
        patch_section.set_expanded(False)  # Collapsed by default
        
        self.enable_patch_processing_checkbox = QCheckBox("Enable patch processing")
        patch_section.add_widget(self.enable_patch_processing_checkbox)
        
        patch_size_layout = QHBoxLayout()
        patch_size_layout.addWidget(QLabel("Patch Size:"))
        self.patch_size_spinbox = QSpinBox()
        self.patch_size_spinbox.setRange(256, 2048)
        self.patch_size_spinbox.setValue(512)
        patch_size_layout.addWidget(self.patch_size_spinbox)
        patch_section.add_layout(patch_size_layout)
        
        overlap_layout = QHBoxLayout()
        overlap_layout.addWidget(QLabel("Overlap:"))
        self.overlap_spinbox = QSpinBox()
        self.overlap_spinbox.setRange(0, 50)
        self.overlap_spinbox.setValue(10)
        self.overlap_spinbox.setSuffix("%")
        overlap_layout.addWidget(self.overlap_spinbox)
        patch_section.add_layout(overlap_layout)
        
        self.parameters_main_layout.addWidget(patch_section)
    
    def _create_artifact_detection_section(self):
        """Create the intelligent artifact detection collapsible section."""
        artifact_section = CollapsibleSection("Intelligent Artifact Detection")
        artifact_section.set_expanded(False)  # Collapsed by default
        
        self.enable_artifact_detection_checkbox = QCheckBox("Enable intelligent artifact detection")
        self.enable_artifact_detection_checkbox.setToolTip("Automatically detect and remove border artifacts and patch boundaries (especially important for MobileSAM)")
        artifact_section.add_widget(self.enable_artifact_detection_checkbox)
        
        # Artifact sensitivity
        artifact_sens_layout = QHBoxLayout()
        artifact_sens_layout.addWidget(QLabel("Artifact Sensitivity:"))
        self.artifact_sensitivity_slider = QSlider(Qt.Horizontal)
        self.artifact_sensitivity_slider.setRange(1, 10)
        self.artifact_sensitivity_slider.setValue(5)  # Default for FastSAM, will be auto-adjusted for MobileSAM
        self.artifact_sensitivity_slider.setToolTip("Higher values = more aggressive artifact removal (auto-enhanced for MobileSAM)")
        artifact_sens_layout.addWidget(self.artifact_sensitivity_slider)
        self.artifact_sensitivity_label = QLabel("5")
        artifact_sens_layout.addWidget(self.artifact_sensitivity_label)
        artifact_section.add_layout(artifact_sens_layout)
        
        # Duplicate sensitivity
        duplicate_sens_layout = QHBoxLayout()
        duplicate_sens_layout.addWidget(QLabel("Duplicate Sensitivity:"))
        self.duplicate_sensitivity_slider = QSlider(Qt.Horizontal)
        self.duplicate_sensitivity_slider.setRange(1, 10)
        self.duplicate_sensitivity_slider.setValue(7)  # Default for FastSAM, will be auto-adjusted for MobileSAM
        self.duplicate_sensitivity_slider.setToolTip("Higher values = more aggressive duplicate removal across patch boundaries")
        duplicate_sens_layout.addWidget(self.duplicate_sensitivity_slider)
        self.duplicate_sensitivity_label = QLabel("7")
        duplicate_sens_layout.addWidget(self.duplicate_sensitivity_label)
        artifact_section.add_layout(duplicate_sens_layout)
        
        # Connect sliders to labels
        self.artifact_sensitivity_slider.valueChanged.connect(
            lambda v: self.artifact_sensitivity_label.setText(str(v))
        )
        self.duplicate_sensitivity_slider.valueChanged.connect(
            lambda v: self.duplicate_sensitivity_label.setText(str(v))
        )
        
        self.parameters_main_layout.addWidget(artifact_section)
    
    def _create_analysis_options_section(self):
        """Create the analysis options collapsible section."""
        analysis_section = CollapsibleSection("Analysis Options")
        analysis_section.set_expanded(False)  # Collapsed by default
        
        self.calculate_statistics_checkbox = QCheckBox("Calculate grain statistics")
        self.calculate_statistics_checkbox.setChecked(True)
        self.calculate_statistics_checkbox.setEnabled(False)  # Always enabled, so disable checkbox
        analysis_section.add_widget(self.calculate_statistics_checkbox)
        
        self.save_contours_checkbox = QCheckBox("Save analysis results")
        analysis_section.add_widget(self.save_contours_checkbox)
        
        self.parameters_main_layout.addWidget(analysis_section)
        
    def _create_visualization_settings_section(self):
        """Create the visualization settings collapsible section."""
        # Create a container widget for the entire visualization section
        vis_container = QWidget()
        vis_container_layout = QVBoxLayout(vis_container)
        vis_container_layout.setContentsMargins(0, 0, 0, 0)
        vis_container_layout.setSpacing(5)
        
        # Create the collapsible section for settings
        vis_section = CollapsibleSection("Visualization Settings")
        vis_section.set_expanded(False)  # Collapsed by default
        
        # Grain ID visibility
        self.show_grain_ids_checkbox = QCheckBox("Show Grain IDs")
        self.show_grain_ids_checkbox.setChecked(True)
        vis_section.add_widget(self.show_grain_ids_checkbox)
        
        # Grain ID font size
        font_size_layout = QHBoxLayout()
        font_size_layout.addWidget(QLabel("ID Font Size:"))
        self.grain_id_font_size_slider = QSlider(Qt.Horizontal)
        self.grain_id_font_size_slider.setRange(1, 16)
        self.grain_id_font_size_slider.setValue(10)
        self.grain_id_font_size_label = QLabel("10px")
        font_size_layout.addWidget(self.grain_id_font_size_slider)
        font_size_layout.addWidget(self.grain_id_font_size_label)
        vis_section.add_layout(font_size_layout)
        
        # Grain ID color
        id_color_layout = QHBoxLayout()
        id_color_layout.addWidget(QLabel("ID Color:"))
        self.grain_id_color_button = QPushButton()
        self.grain_id_color_button.setFixedSize(24, 24)
        self.grain_id_color_button.setStyleSheet("background-color: rgb(255, 255, 255); border: 1px solid black;")
        id_color_layout.addWidget(self.grain_id_color_button)
        id_color_layout.addStretch()
        vis_section.add_layout(id_color_layout)
        
        # Grain boundary visibility
        self.show_grain_boundaries_checkbox = QCheckBox("Show Grain Boundaries")
        self.show_grain_boundaries_checkbox.setChecked(True)
        vis_section.add_widget(self.show_grain_boundaries_checkbox)
        
        # Grain boundary thickness
        thickness_layout = QHBoxLayout()
        thickness_layout.addWidget(QLabel("Boundary Thickness:"))
        self.grain_boundary_thickness_slider = QSlider(Qt.Horizontal)
        self.grain_boundary_thickness_slider.setRange(1, 5)
        self.grain_boundary_thickness_slider.setValue(2)
        self.grain_boundary_thickness_label = QLabel("2px")
        thickness_layout.addWidget(self.grain_boundary_thickness_slider)
        thickness_layout.addWidget(self.grain_boundary_thickness_label)
        vis_section.add_layout(thickness_layout)
        
        # Add the collapsible section to the container
        vis_container_layout.addWidget(vis_section)
        
        # Apply button - outside the collapsible section so it's always visible
        self.apply_vis_settings_button = QPushButton("Apply Settings")
        self.apply_vis_settings_button.setStyleSheet("font-weight: bold; margin-top: 5px;")
        vis_container_layout.addWidget(self.apply_vis_settings_button)
        
        self.parameters_main_layout.addWidget(vis_container)
        
    def _setup_trainable_segmentation_parameters(self):
        """Set up parameters for trainable segmentation using collapsible sections."""
        # Clear existing parameters
        self._clear_parameters()
        
        # Create trainable segmentation section
        trainable_section = CollapsibleSection("Trainable Segmentation")
        trainable_section.set_expanded(False)  # Collapsed by default
        
        # Model selection
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("Trained Model:"))
        self.model_combo = QComboBox()
        self.model_combo.addItems(["Select a trained model..."])
        model_layout.addWidget(self.model_combo)
        
        # Load model button
        self.load_model_button = QPushButton("Load Model...")
        self.load_model_button.setToolTip("Browse and load a trained model from anywhere on your system")
        self.load_model_button.setMaximumWidth(100)
        model_layout.addWidget(self.load_model_button)
        
        trainable_section.add_layout(model_layout)
        
        # Note: Confidence and IoU thresholds are not needed for trainable segmentation
        
        # Batch size
        batch_layout = QHBoxLayout()
        batch_layout.addWidget(QLabel("Batch Size:"))
        self.batch_size = QSpinBox()
        self.batch_size.setRange(1, 16)
        self.batch_size.setValue(4)
        batch_layout.addWidget(self.batch_size)
        trainable_section.add_layout(batch_layout)
        
        self.parameters_main_layout.addWidget(trainable_section)
        self.parameters_main_layout.addStretch()
        
    def _setup_advanced_segmentation_parameters(self):
        """Set up parameters for advanced segmentation using collapsible sections."""
        # Clear existing parameters
        self._clear_parameters()
        
        # Create advanced segmentation section
        advanced_section = CollapsibleSection("Advanced Segmentation")
        advanced_section.set_expanded(False)  # Collapsed by default
        
        # Model selection
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("Model:"))
        self.advanced_model_combo = QComboBox()
        self.advanced_model_combo.addItems(["SAM", "FastSAM", "MobileSAM"])
        model_layout.addWidget(self.advanced_model_combo)
        advanced_section.add_layout(model_layout)
        
        # Prompt type
        prompt_layout = QHBoxLayout()
        prompt_layout.addWidget(QLabel("Prompt Type:"))
        self.prompt_type_combo = QComboBox()
        self.prompt_type_combo.addItems(["Automatic", "Grid Points", "Random Points"])
        prompt_layout.addWidget(self.prompt_type_combo)
        advanced_section.add_layout(prompt_layout)
        
        # Number of points (for point-based prompts)
        points_layout = QHBoxLayout()
        points_layout.addWidget(QLabel("Number of Points:"))
        self.num_points = QSpinBox()
        self.num_points.setRange(1, 100)
        self.num_points.setValue(10)
        points_layout.addWidget(self.num_points)
        advanced_section.add_layout(points_layout)
        
        self.parameters_main_layout.addWidget(advanced_section)
        self.parameters_main_layout.addStretch()
        
    def _clear_parameters(self):
        """Clear all parameter widgets from the main layout."""
        # Clear all widgets from the main parameters layout
        while self.parameters_main_layout.count():
            child = self.parameters_main_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
            elif child.spacerItem():
                # Remove spacer items as well
                pass