# Vision Lab Website Deployment Guide

This guide provides step-by-step instructions for deploying the Vision Lab website on various hosting platforms.

## 🚀 Quick Deploy Options

### Option 1: Netlify (Recommended)

#### Prerequisites
- Git repository (GitHub, GitLab, or Bitbucket)
- Netlify account (free tier available)

#### Steps

1. **Push to Git Repository**
   ```bash
   git init
   git add .
   git commit -m "Initial Vision Lab website"
   git remote add origin <your-repo-url>
   git push -u origin main
   ```

2. **Deploy on Netlify**
   - Go to [netlify.com](https://netlify.com)
   - Click "New site from Git"
   - Connect your repository
   - Configure build settings:
     - Build command: `echo 'No build required'`
     - Publish directory: `.` (root)
   - Click "Deploy site"

3. **Configure Environment Variables**
   - Go to Site settings → Environment variables
   - Add the following variables:
     ```
     EMAIL_SALT=your_secure_random_salt_here
     SMTP_SERVER=smtp.gmail.com
     SMTP_PORT=587
     SMTP_USERNAME=<EMAIL>
     SMTP_PASSWORD=your_app_password
     ```

4. **Enable Netlify Functions**
   - Functions are automatically detected from `netlify/functions/` directory
   - No additional configuration needed

5. **Custom Domain (Optional)**
   - Go to Site settings → Domain management
   - Add your custom domain
   - Configure DNS settings as instructed

#### Netlify Features Used
- ✅ Serverless Functions (Python)
- ✅ Form Handling
- ✅ HTTPS/SSL (automatic)
- ✅ CDN (global)
- ✅ Environment Variables
- ✅ Custom Headers & Redirects

---

### Option 2: Hostinger

#### Prerequisites
- Hostinger hosting account
- FTP/SFTP access or File Manager

#### Steps

1. **Upload Files**
   - Use File Manager or FTP client
   - Upload all files to `public_html` directory
   - Ensure `index.html` is in the root

2. **Setup Python Backend (if supported)**
   - Check if your Hostinger plan supports Python
   - Upload `server.py`, `api.py`, and `requirements_website.txt`
   - Install dependencies via SSH:
     ```bash
     pip install -r requirements_website.txt
     ```

3. **Configure Database**
   - Create MySQL/PostgreSQL database in cPanel
   - Update database connection in `api.py`
   - Run database initialization script

4. **Setup Environment Variables**
   - Create `.env` file with:
     ```
     SECRET_KEY=your_secret_key_here
     DATABASE_URL=your_database_url
     EMAIL_SALT=your_secure_salt
     SMTP_SERVER=smtp.hostinger.com
     SMTP_USERNAME=<EMAIL>
     SMTP_PASSWORD=your_email_password
     ```

5. **Configure Web Server**
   - Update `.htaccess` for URL rewriting
   - Configure Python WSGI if available

---

### Option 3: Static Hosting (GitHub Pages, Vercel)

#### For Static-Only Deployment

If you want to deploy without the Python backend:

1. **Modify JavaScript**
   - Update `script.js` to use external services:
     - Netlify Forms
     - Formspree
     - EmailJS
     - Google Forms

2. **Example with Netlify Forms**
   ```html
   <!-- Update form in index.html -->
   <form name="waitlist" method="POST" data-netlify="true" netlify-honeypot="bot-field">
     <input type="hidden" name="form-name" value="waitlist" />
     <!-- rest of form fields -->
   </form>
   ```

3. **Deploy to Platform**
   - **GitHub Pages**: Push to `gh-pages` branch
   - **Vercel**: Connect repository and deploy
   - **Surge.sh**: `npm install -g surge && surge`

---

## 🔧 Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `EMAIL_SALT` | Salt for email hashing | Yes |
| `SECRET_KEY` | Flask secret key | Yes (Flask) |
| `DATABASE_URL` | Database connection string | Yes (Flask) |
| `SMTP_SERVER` | Email server hostname | Optional |
| `SMTP_PORT` | Email server port | Optional |
| `SMTP_USERNAME` | Email username | Optional |
| `SMTP_PASSWORD` | Email password | Optional |

### Security Checklist

- [ ] Environment variables configured
- [ ] HTTPS enabled
- [ ] Security headers configured
- [ ] Rate limiting enabled
- [ ] Input validation implemented
- [ ] Email hashing enabled
- [ ] CORS properly configured
- [ ] No sensitive data in client-side code

---

## 🔍 Testing

### Local Testing

1. **Test Static Files**
   ```bash
   # Simple HTTP server
   python -m http.server 8000
   # Or with Node.js
   npx serve .
   ```

2. **Test Flask Backend**
   ```bash
   pip install -r requirements_website.txt
   python server.py
   ```

3. **Test Netlify Functions Locally**
   ```bash
   # Install Netlify CLI
   npm install -g netlify-cli
   
   # Run local development
   netlify dev
   ```

### Production Testing

- [ ] Homepage loads correctly
- [ ] All sections display properly
- [ ] Navigation works
- [ ] Waitlist form submits successfully
- [ ] Email validation works
- [ ] Rate limiting functions
- [ ] Mobile responsiveness
- [ ] Cross-browser compatibility

---

## 📊 Monitoring

### Netlify Analytics
- Built-in analytics available
- Monitor function invocations
- Track form submissions

### Custom Monitoring
- Add Google Analytics
- Monitor API endpoints
- Set up error tracking (Sentry)

### Health Checks
- Use `/api/health` endpoint
- Set up uptime monitoring
- Configure alerts

---

## 🔄 Updates

### Continuous Deployment

1. **Netlify**: Automatic deployment on git push
2. **Manual**: Upload files via FTP/File Manager
3. **CI/CD**: GitHub Actions, GitLab CI, etc.

### Version Control

```bash
# Update website
git add .
git commit -m "Update: description of changes"
git push origin main
```

---

## 🆘 Troubleshooting

### Common Issues

1. **Functions not working**
   - Check function logs in Netlify dashboard
   - Verify environment variables
   - Test functions locally

2. **Form submissions failing**
   - Check CORS configuration
   - Verify API endpoints
   - Test with browser developer tools

3. **Styling issues**
   - Clear browser cache
   - Check CSS file paths
   - Verify responsive design

4. **Email not sending**
   - Check SMTP configuration
   - Verify email credentials
   - Test email service separately

### Support

- Check deployment logs
- Review browser console errors
- Test API endpoints individually
- Verify all file paths are correct

---

## 📝 Notes

- The website is designed to be responsive and accessible
- All forms include proper validation and security measures
- The backend API is optional for static hosting
- Environment variables should never be committed to version control
- Regular backups of user data are recommended

For additional support, refer to the main README_WEBSITE.md file or contact the development team.