from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                                QRadioButton, QButtonGroup, QPushButton, QGroupBox)
from PySide6.QtCore import Qt, QSettings, Signal
from PySide6.QtWidgets import QTabWidget

class TabPositionSelector(QWidget):
    """Widget for selecting tab position with live preview."""
    
    position_changed = Signal(int)  # Emits QTabWidget position
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_current_setting()
        
    def setup_ui(self):
        """Sets up the tab position selector UI."""
        layout = QVBoxLayout(self)
        
        # Group box for tab position options
        position_group = QGroupBox("Tab Position")
        position_layout = QVBoxLayout(position_group)
        
        # Create radio buttons for each position
        self.position_group = QButtonGroup()
        
        self.top_radio = QRadioButton("Top (Default)")
        self.top_radio.setToolTip("Traditional top navigation tabs")
        self.position_group.addButton(self.top_radio, 0)  # Use integer IDs
        position_layout.addWidget(self.top_radio)
        
        layout.addWidget(position_group)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.apply_button = QPushButton("Apply")
        self.apply_button.clicked.connect(self.apply_position)
        button_layout.addWidget(self.apply_button)
        
        self.reset_button = QPushButton("Reset to Default")
        self.reset_button.clicked.connect(self.reset_to_default)
        button_layout.addWidget(self.reset_button)
        
        layout.addLayout(button_layout)
        
        # Connect signals
        self.position_group.buttonClicked.connect(self.on_position_changed)
        
    def load_current_setting(self):
        """Loads the current tab position setting."""
        try:
            settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")
            tab_position = settings.value("app/tab_position", "top").lower()
            
            # Map string values to radio buttons
            position_map = {
                "top": self.top_radio
            }
            
            radio_button = position_map.get(tab_position, self.top_radio)
            radio_button.setChecked(True)
            
        except Exception:
            # Default to top if there's an error
            self.top_radio.setChecked(True)
    
    def on_position_changed(self, button):
        """Called when a position radio button is clicked."""
        button_id = self.position_group.id(button)
        # Map button IDs to QTabWidget positions
        id_to_position = {
            0: QTabWidget.North   # Top
        }
        position = id_to_position.get(button_id, QTabWidget.North)
        self.position_changed.emit(position)
    
    def apply_position(self):
        """Applies the selected position and saves to settings."""
        checked_button = self.position_group.checkedButton()
        if checked_button:
            button_id = self.position_group.id(checked_button)
            
            # Map button IDs to QTabWidget positions and string values
            id_to_position = {
                0: (QTabWidget.North, "top")
            }
            
            position, position_str = id_to_position.get(button_id, (QTabWidget.North, "top"))
            
            # Save to settings
            settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")
            settings.setValue("app/tab_position", position_str)
            
            # Emit signal for immediate application
            self.position_changed.emit(position)
    
    def reset_to_default(self):
        """Resets to default top position."""
        self.top_radio.setChecked(True)
        
        # Save to settings
        settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")
        settings.setValue("app/tab_position", "top")
        
        # Emit signal
        self.position_changed.emit(QTabWidget.North)
    
    def get_current_position(self):
        """Returns the currently selected position."""
        checked_button = self.position_group.checkedButton()
        if checked_button:
            button_id = self.position_group.id(checked_button)
            # Map button IDs to QTabWidget positions
            id_to_position = {
                0: QTabWidget.North   # Top
            }
            return id_to_position.get(button_id, QTabWidget.North)
        return QTabWidget.North  # Default