#!/usr/bin/env python3
"""
VisionLab Project Repair Tool

This tool repairs corrupted VisionLab projects that have performance issues
due to large uncompressed annotation files.

Usage:
    python repair_project.py <project_file.vlp> [--backup] [--analyze-only]

Examples:
    python repair_project.py my_project.vlp --backup
    python repair_project.py my_project.vlp --analyze-only
"""

import sys
import os
import argparse
import logging
from pathlib import Path

# Add src to path to import project modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from utils.project_repair import ProjectRepairTool
except ImportError as e:
    print(f"Error importing repair tool: {e}")
    print("Make sure you're running this script from the VisionLab root directory.")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def format_size(size_mb: float) -> str:
    """Format file size in human-readable format."""
    if size_mb < 1:
        return f"{size_mb * 1024:.1f} KB"
    elif size_mb < 1024:
        return f"{size_mb:.1f} MB"
    else:
        return f"{size_mb / 1024:.1f} GB"


def main():
    parser = argparse.ArgumentParser(
        description="Repair corrupted VisionLab projects with large annotation files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s project.vlp --backup          # Repair with backup
  %(prog)s project.vlp --analyze-only    # Just analyze, don't repair
  %(prog)s project.vlp --no-backup       # Repair without backup (risky)
        """
    )
    
    parser.add_argument(
        'project_file',
        help='Path to the .vlp project file to repair'
    )
    
    parser.add_argument(
        '--backup',
        action='store_true',
        default=True,
        help='Create backup before repair (default: True)'
    )
    
    parser.add_argument(
        '--no-backup',
        action='store_true',
        help='Skip creating backup (not recommended)'
    )
    
    parser.add_argument(
        '--analyze-only',
        action='store_true',
        help='Only analyze the project, do not perform repair'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Validate project file
    if not os.path.exists(args.project_file):
        print(f"Error: Project file '{args.project_file}' not found.")
        sys.exit(1)
    
    if not args.project_file.lower().endswith('.vlp'):
        print(f"Error: '{args.project_file}' does not appear to be a VisionLab project file (.vlp).")
        sys.exit(1)
    
    # Determine backup setting
    create_backup = args.backup and not args.no_backup
    
    print("=" * 60)
    print("VisionLab Project Repair Tool")
    print("=" * 60)
    print(f"Project file: {args.project_file}")
    print(f"Backup: {'Yes' if create_backup else 'No'}")
    print()
    
    # Initialize repair tool
    repair_tool = ProjectRepairTool()
    
    # Analyze project
    print("Analyzing project...")
    analysis = repair_tool.analyze_project(args.project_file)
    
    if "error" in analysis:
        print(f"Error analyzing project: {analysis['error']}")
        sys.exit(1)
    
    # Display analysis results
    print(f"Project size: {format_size(analysis['total_size_mb'])}")
    print(f"Large annotation files: {len(analysis['large_files'])}")
    
    if analysis['large_files']:
        print("\nLarge annotation files found:")
        for i, file_info in enumerate(analysis['large_files'][:5], 1):
            filename = os.path.basename(file_info['filename'])
            size = format_size(file_info['size_mb'])
            compression_ratio = file_info.get('compression_ratio', 0)
            print(f"  {i}. {filename}: {size} (compression: {compression_ratio:.1%})")
        
        if len(analysis['large_files']) > 5:
            print(f"  ... and {len(analysis['large_files']) - 5} more files")
    
    if analysis['needs_repair']:
        estimated_savings = format_size(analysis['compression_savings'])
        print(f"\nEstimated space savings: {estimated_savings}")
        print("\n⚠️  This project has performance issues due to large uncompressed annotation files.")
        print("   Loading this project will be very slow without repair.")
    else:
        print("\n✅ Project appears to be in good condition.")
        print("   No repair needed.")
        return
    
    # Stop here if analyze-only
    if args.analyze_only:
        print("\nAnalysis complete. Use without --analyze-only to perform repair.")
        return
    
    # Confirm repair
    print("\n" + "=" * 60)
    print("REPAIR CONFIRMATION")
    print("=" * 60)
    
    if create_backup:
        backup_path = args.project_file + ".backup"
        print(f"A backup will be created at: {backup_path}")
    else:
        print("⚠️  NO BACKUP will be created. This operation cannot be undone!")
    
    print(f"Estimated time: 1-5 minutes (depending on file size)")
    print()
    
    try:
        response = input("Proceed with repair? (y/N): ").strip().lower()
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        sys.exit(0)
    
    if response != 'y':
        print("Repair cancelled.")
        return
    
    # Perform repair
    print("\nRepairing project...")
    print("This may take several minutes for large projects...")
    
    try:
        success, message = repair_tool.repair_project(args.project_file, create_backup)
        
        if success:
            print(f"\n✅ {message}")
            
            # Show final size
            final_size = os.path.getsize(args.project_file) / (1024 * 1024)
            original_size = analysis['total_size_mb']
            actual_savings = original_size - final_size
            
            print(f"\nResults:")
            print(f"  Original size: {format_size(original_size)}")
            print(f"  Final size: {format_size(final_size)}")
            print(f"  Space saved: {format_size(actual_savings)} ({actual_savings/original_size:.1%})")
            print(f"\n🚀 Project should now load much faster!")
            
        else:
            print(f"\n❌ Repair failed: {message}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\nRepair interrupted by user.")
        print("Project may be in an inconsistent state.")
        if create_backup:
            backup_path = args.project_file + ".backup"
            if os.path.exists(backup_path):
                print(f"You can restore from backup: {backup_path}")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error during repair: {e}")
        if create_backup:
            backup_path = args.project_file + ".backup"
            if os.path.exists(backup_path):
                print(f"You can restore from backup: {backup_path}")
        sys.exit(1)


if __name__ == "__main__":
    main()
