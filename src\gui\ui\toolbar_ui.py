# src/gui/ui/toolbar_ui.py

from PySide6.QtWidgets import QToolBar, QMenu
from PySide6.QtGui import QIcon, QAction
from PySide6.QtCore import QSize, QTimer

class ToolbarUI:
    """Class for creating and managing the application toolbar."""

    def _style_toolbar_button(self, action):
        """Apply custom styling to a toolbar button.

        Args:
            action (QAction): The action to style
        """
        # Wait for the action to be added to the toolbar and get its button
        def apply_style():
            if hasattr(action, 'parentWidget') and action.parentWidget():
                button = action.parentWidget()
                # Force style update
                button.style().unpolish(button)
                button.style().polish(button)
                button.update()
                return True
            return False

        # Try immediately
        if not apply_style():
            # If not successful, try again after a short delay
            QTimer.singleShot(100, apply_style)

    def setup_toolbar(self):
        """Sets up the main application toolbar with menus and actions."""
        # Create the main toolbar
        self.main_toolbar = QToolBar("Main Toolbar")
        self.main_toolbar.setIconSize(QSize(24, 24))
        self.main_toolbar.setMovable(False)
        self.main_toolbar.setContentsMargins(0, 0, 0, 0)

        # Load custom toolbar style from CSS file
        try:
            with open("src/gui/styles/custom_toolbar_style.css", "r") as f:
                self.main_toolbar.setStyleSheet(f.read())
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error loading custom toolbar style: {e}")
            # Fallback to inline style if file can't be loaded, using palette colors
            self.main_toolbar.setStyleSheet("""
                QToolBar {
                    background-color: palette(window);
                    spacing: 0px;
                    padding: 0px;
                    border: none;
                    border-bottom: 1px solid palette(mid);
                }
                QToolBar QToolButton {
                    background-color: palette(button);
                    color: palette(button-text);
                    border: none;
                    padding: 10px 18px;
                    font-size: 14px;
                    font-weight: 500;
                    margin: 0px 2px;
                    border-radius: 0px;
                }
                QToolBar QToolButton:hover {
                    background-color: palette(highlight);
                    color: palette(highlighted-text);
                }
                QToolBar QToolButton:pressed {
                    background-color: palette(dark);
                }
                /* Active tab styling */
                QToolBar QToolButton[active="true"] {
                    background-color: palette(highlight);
                    color: palette(highlighted-text);
                    font-weight: bold;
                    border-bottom: 3px solid palette(link);
                }
            """)

        self.addToolBar(self.main_toolbar)
        # Hide the toolbar since we're using the navigation bar instead
        self.main_toolbar.setVisible(False)

        # Create actions for direct access (without menus)
        # These actions will be used in connect_toolbar_actions
        # Order: Project Hub, Unsupervised Segmentation, Trainable Segmentation, Point Counting,
        # Grain Analysis, Advanced Segmentation, Image Lab, Settings, AI Assistant
        self.project_hub_action = QAction("Project Hub", self)
        self.unsupervised_segmentation_action = QAction("Unsupervised Segmentation", self)
        self.trainable_segmentation_action = QAction("Trainable Segmentation", self)
        self.point_counting_action = QAction("Point Counting", self)
        self.grain_analysis_action = QAction("Grain Analysis", self)
        self.advanced_segmentation_action = QAction("Advanced Segmentation", self)
        self.image_lab_action = QAction("Image Lab", self)
        self.settings_action = QAction("Settings", self)
        self.ai_assistant_action = QAction("AI Assistant", self)

        # Create other actions that might be needed
        self.new_project_action = QAction("New Project", self)
        self.open_project_action = QAction("Open Project", self)
        self.import_images_action = QAction("Import Images", self)
        self.save_project_action = QAction("Save Project", self)
        self.save_project_as_action = QAction("Save Project As...", self)
        self.export_results_action = QAction("Export Results", self)
        self.export_coco_action = QAction("Export COCO Annotations", self)
        self.reset_layout_action = QAction("Reset Layout", self)
        self.toggle_nav_action = QAction("Toggle Navigation Bar", self)
        self.documentation_action = QAction("Documentation", self)
        self.about_action = QAction("About VisionLab Ai", self)

    def connect_toolbar_actions(self):
        """Connects toolbar actions to their respective handlers."""
        # Connect tab switching actions in the desired order
        # 1. Project Hub
        project_hub_page_index = self.find_tab_index_by_name("Project Hub")
        if project_hub_page_index != -1:
            self.project_hub_action.triggered.connect(lambda: self.switch_page(project_hub_page_index))

        # 2. Unsupervised Segmentation
        process_page_index = self.find_tab_index_by_name("Unsupervised Segmentation")
        if process_page_index != -1:
            self.unsupervised_segmentation_action.triggered.connect(lambda: self.switch_page(process_page_index))

        # 3. Trainable Segmentation
        trainable_page_index = self.find_tab_index_by_name("Trainable Segmentation")
        if trainable_page_index != -1:
            self.trainable_segmentation_action.triggered.connect(lambda: self.switch_page(trainable_page_index))

        # 4. Point Counting
        point_counting_page_index = self.find_tab_index_by_name("Point Counting")
        if point_counting_page_index != -1:
            self.point_counting_action.triggered.connect(lambda: self.switch_page(point_counting_page_index))

        # 5. Grain Analysis
        grain_analysis_page_index = self.find_tab_index_by_name("Grain Analysis")
        if grain_analysis_page_index != -1:
            self.grain_analysis_action.triggered.connect(lambda: self.switch_page(grain_analysis_page_index))

        # 6. Advanced Segmentation
        advanced_segmentation_page_index = self.find_tab_index_by_name("Advanced Segmentation")
        if advanced_segmentation_page_index != -1:
            self.advanced_segmentation_action.triggered.connect(lambda: self.switch_page(advanced_segmentation_page_index))

        # 7. Image Lab
        analysis_page_index = self.find_tab_index_by_name("Image Lab")
        if analysis_page_index != -1:
            self.image_lab_action.triggered.connect(lambda: self.switch_page(analysis_page_index))

        # 8. Settings
        settings_page_index = self.find_tab_index_by_name("Settings")
        if settings_page_index != -1:
            self.settings_action.triggered.connect(lambda: self.switch_page(settings_page_index))

        # 9. AI Assistant
        ai_assistant_page_index = self.find_tab_index_by_name("AI Assistant")
        if ai_assistant_page_index != -1:
            self.ai_assistant_action.triggered.connect(lambda: self.switch_page(ai_assistant_page_index))

        # Connect other actions that might be needed
        if hasattr(self, 'create_new_project'):
            self.new_project_action.triggered.connect(self.create_new_project)

        if hasattr(self, 'open_project'):
            self.open_project_action.triggered.connect(self.open_project)

        if hasattr(self, 'import_images'):
            self.import_images_action.triggered.connect(self.import_images)

        if hasattr(self, 'save_project'):
            self.save_project_action.triggered.connect(self.save_project)

        if hasattr(self, 'save_project_as'):
            self.save_project_as_action.triggered.connect(self.save_project_as)

        if hasattr(self, 'export_results'):
            self.export_results_action.triggered.connect(self.export_results)

        if hasattr(self, 'export_coco'):
            self.export_coco_action.triggered.connect(self.export_coco)

        if hasattr(self, 'reset_layout'):
            self.reset_layout_action.triggered.connect(self.reset_layout)

        if hasattr(self, 'toggle_navigation_bar'):
            self.toggle_nav_action.triggered.connect(self.toggle_navigation_bar)

        if hasattr(self, 'show_documentation'):
            self.documentation_action.triggered.connect(self.show_documentation)

        if hasattr(self, 'show_about'):
            self.about_action.triggered.connect(self.show_about)
