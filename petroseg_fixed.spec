# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

block_cipher = None

# Define the base path
base_path = os.path.abspath('.')

# Collect all necessary data files
datas = []

# Add model weights and other resource files
weights_dir = os.path.join(base_path, 'weights')
if os.path.exists(weights_dir):
    datas.append((weights_dir, 'weights'))

# Add any other resource directories
resource_dirs = [
    ('src/gui/styles', 'src/gui/styles'),
    ('src/gui/icons', 'src/gui/icons'),
    ('src/grainsight_components/models', 'src/grainsight_components/models'),
    ('src/grainsight_components/weights', 'src/grainsight_components/weights'),
]

for src, dst in resource_dirs:
    src_path = os.path.join(base_path, src)
    if os.path.exists(src_path):
        datas.append((src_path, dst))

# Add mobile_sam module if it exists
try:
    import mobile_sam
    datas += collect_data_files('mobile_sam')
except ImportError:
    pass

# Collect all necessary hidden imports
hiddenimports = [
    'PySide6.QtCore',
    'PySide6.QtGui',
    'PySide6.QtWidgets',
    'numpy',
    'PIL',
    'cv2',
    'torch',
    'torchvision',
    'yaml',
    'ultralytics',
    'scipy.stats',
    'scipy.stats._distn_infrastructure',
    'xgboost',
]

# Add mobile_sam submodules if available
try:
    import mobile_sam
    hiddenimports += collect_submodules('mobile_sam')
except ImportError:
    pass

# Add scipy.stats submodules
try:
    import scipy.stats
    hiddenimports += collect_submodules('scipy.stats')
except ImportError:
    pass

a = Analysis(
    ['main.py'],
    pathex=[base_path],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=['hooks'],
    hooksconfig={},
    runtime_hooks=['hooks/rthooks/pyi_rth_scipy_stats.py'],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='VisionLab Ai',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,  # Set to True for testing to see any error messages
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='VisionLab Ai',
)
