# Replacement for torch._numpy/_ufuncs.py
import sys
import types

# Define all the binary ufunc names
binary_ufunc_names = [
    "add", "subtract", "multiply", "divide", "logaddexp", "logaddexp2", 
    "true_divide", "floor_divide", "power", "remainder", "mod", "fmod", 
    "divmod", "gcd", "lcm", "maximum", "minimum", "fmax", "fmin", 
    "copysign", "nextafter", "ldexp", "hypot"
]

# Add all binary ufuncs to the module
for name in binary_ufunc_names:
    def make_dummy(name):
        def dummy(*args, **kwargs):
            return args[0] if args else None
        dummy.__name__ = name
        return dummy
    
    globals()[name] = make_dummy(name)

# Export all the functions
__all__ = binary_ufunc_names
