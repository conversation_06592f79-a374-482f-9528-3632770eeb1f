# src/workers/segmentation_worker.py
import threading
import numpy as np
from PySide6.QtCore import QThread, Signal
from src.segmentation.segmentation_algorithms import perform_custom_segmentation


class SegmentationWorker(QThread):
    """Worker thread for performing image segmentation."""
    progress = Signal(int, np.ndarray)  # (epoch, image)
    finished = Signal(list)  # list of segmented images

    def __init__(self, image, params, parent=None):
        super().__init__(parent)
        self.image = image
        self.params = params
        self.stop_event = threading.Event()

    def run(self):
        """Runs the segmentation algorithm."""
        segmented_images = perform_custom_segmentation(
            self.image, self.params, self.stop_event, self.progress_callback
        )
        self.finished.emit(segmented_images)

    def progress_callback(self, epoch, image):
        """Callback for progress updates."""
        self.progress.emit(epoch, image)

    def stop(self):
        """Stops the segmentation process."""
        self.stop_event.set()