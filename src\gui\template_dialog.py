"""
Template management dialog for Image Lab.

This module provides a dialog for managing operation templates in the Image Lab page.
"""

import os
import logging
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QListWidget,
                              QPushButton, QLabel, QInputDialog, QMessageBox,
                              QDialogButtonBox)
from PySide6.QtCore import Qt

from src.image_lab.template_manager import TemplateManager

logger = logging.getLogger(__name__)

class TemplateDialog(QDialog):
    """Dialog for managing Image Lab operation templates."""

    def __init__(self, parent=None, template_manager=None):
        """Initialize the template dialog.

        Args:
            parent: Parent widget
            template_manager: Optional template manager to use (if None, creates a new one)
        """
        super().__init__(parent)

        self.setWindowTitle("Manage Templates")
        self.setMinimumSize(400, 300)

        # Use provided template manager or create a new one
        self.template_manager = template_manager or TemplateManager()

        # Set up UI
        self._setup_ui()

        # Load templates
        self._load_templates()

    def _setup_ui(self):
        """Set up the dialog UI."""
        # Main layout
        layout = QVBoxLayout(self)

        # Templates list
        self.templates_label = QLabel("Available Templates:")
        layout.addWidget(self.templates_label)

        self.templates_list = QListWidget()
        layout.addWidget(self.templates_list)

        # Buttons layout
        buttons_layout = QHBoxLayout()

        # Template action buttons
        self.rename_btn = QPushButton("Rename")
        self.delete_btn = QPushButton("Delete")

        buttons_layout.addWidget(self.rename_btn)
        buttons_layout.addWidget(self.delete_btn)

        # Add buttons layout to main layout
        layout.addLayout(buttons_layout)

        # Dialog buttons
        self.button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        layout.addWidget(self.button_box)

        # Connect signals
        self.rename_btn.clicked.connect(self._rename_template)
        self.delete_btn.clicked.connect(self._delete_template)
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        self.templates_list.itemSelectionChanged.connect(self._update_buttons)

        # Initial button state
        self._update_buttons()

    def _load_templates(self):
        """Load templates into the list widget."""
        # Clear the list
        self.templates_list.clear()

        # Get template names
        template_names = self.template_manager.get_template_names()

        # Add to list widget
        for name in template_names:
            self.templates_list.addItem(name)

        logger.debug(f"Loaded {len(template_names)} templates into dialog")

    def _update_buttons(self):
        """Update button states based on selection."""
        has_selection = len(self.templates_list.selectedItems()) > 0
        self.rename_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)

    def _rename_template(self):
        """Rename the selected template."""
        selected_items = self.templates_list.selectedItems()
        if not selected_items:
            return

        old_name = selected_items[0].text()

        # Get new name from user
        new_name, ok = QInputDialog.getText(
            self,
            "Rename Template",
            "New template name:",
            text=old_name
        )

        if ok and new_name and new_name != old_name:
            # Rename the template
            if self.template_manager.rename_template(old_name, new_name):
                # Reload templates
                self._load_templates()

                # Select the renamed template
                items = self.templates_list.findItems(new_name, Qt.MatchExactly)
                if items:
                    self.templates_list.setCurrentItem(items[0])

                logger.info(f"Renamed template from '{old_name}' to '{new_name}'")
            else:
                QMessageBox.warning(
                    self,
                    "Rename Failed",
                    f"Failed to rename template '{old_name}' to '{new_name}'."
                )

    def _delete_template(self):
        """Delete the selected template."""
        selected_items = self.templates_list.selectedItems()
        if not selected_items:
            return

        template_name = selected_items[0].text()

        # Confirm deletion
        reply = QMessageBox.question(
            self,
            "Confirm Deletion",
            f"Are you sure you want to delete the template '{template_name}'?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Delete the template
            if self.template_manager.delete_template(template_name):
                # Reload templates
                self._load_templates()
                logger.info(f"Deleted template: {template_name}")
            else:
                QMessageBox.warning(
                    self,
                    "Deletion Failed",
                    f"Failed to delete template '{template_name}'."
                )

    def get_selected_template(self):
        """Get the selected template name.

        Returns:
            Selected template name or None if no selection
        """
        selected_items = self.templates_list.selectedItems()
        if selected_items:
            return selected_items[0].text()
        return None
