# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[('src/gui/styles', 'src/gui/styles'), ('src/gui/icons', 'src/gui/icons'), ('C:\\Users\\<USER>\\Documents\\GitHub\\PetroSEG_V4\\venv\\Lib\\site-packages\\xgboost', 'xgboost')],
    hiddenimports=['PySide6.QtCore', 'PySide6.QtGui', 'PySide6.QtWidgets', 'numpy', 'PIL', 'cv2', 'yaml', 'xgboost', 'torch.onnx'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['tensorflow', 'tensorflow-plugins', 'keras', 'openvino', 'markdown', 'matplotlib.tests', 'scipy.tests', 'sklearn.tests', 'pandas.tests', 'numpy.tests'],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='VisionLab_Ai_Simple',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='VisionLab_Ai_Simple',
)
