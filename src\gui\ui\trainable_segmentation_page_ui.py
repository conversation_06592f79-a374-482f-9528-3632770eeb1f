# src/gui/ui/trainable_segmentation_page_ui.py

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
        QGroupBox, QScrollArea, QFrame, QSlider, QSpinBox, QDoubleSpinBox,
        QComboBox, QProgressBar, QFormLayout, QCheckBox, QTabWidget, QSplitter)
from PySide6.QtCore import Qt
import numpy as np

from src.widgets.scrollable_frame import ScrollableFrame
from src.widgets.pixmap_view import QPixmapView
from src.widgets.trainable_segmentation_gallery import TrainableSegmentationGallery
from src.widgets.class_percentages_widget import ClassPercentagesWidget

class TrainableSegmentationPageUI:
        """Class for creating and managing the trainable segmentation page UI."""

        def setup_trainable_segmentation_page(self):
            """Sets up the Trainable Segmentation page."""
            self.trainable_segmentation_page = QWidget()
            trainable_layout = QHBoxLayout(self.trainable_segmentation_page)
            self.stacked_widget.addTab(self.trainable_segmentation_page, "Trainable Segmentation")

            # Create main horizontal splitter for resizable panels
            self.trainable_main_splitter = QSplitter(Qt.Orientation.Horizontal)
            trainable_layout.addWidget(self.trainable_main_splitter)

            # Sidebar
            self.trainable_sidebar = ScrollableFrame()
            self.trainable_sidebar.setMinimumWidth(300)  # Minimum width
            self.trainable_sidebar.setMaximumWidth(600)  # Maximum width
            sidebar_layout = QVBoxLayout(self.trainable_sidebar.get_content_frame())
            self.trainable_main_splitter.addWidget(self.trainable_sidebar)
            self.setup_trainable_sidebar(sidebar_layout) # Add Controls to Sidebar

            # Image Display Area
            image_display_container = QWidget()
            image_display_layout = QHBoxLayout(image_display_container)
            image_display_layout.setContentsMargins(0, 0, 0, 0)
            self.trainable_main_splitter.addWidget(image_display_container)

            # Left side: Image display
            image_view_container = QWidget()
            image_view_layout = QVBoxLayout(image_view_container)
            image_view_layout.setContentsMargins(0, 0, 0, 0)
            image_display_layout.addWidget(image_view_container)

            # Original Image and Segmentation Result
            self.trainable_tab_widget = QTabWidget()
            image_view_layout.addWidget(self.trainable_tab_widget)

            # Use theme-aware styling instead of hardcoded styling
            # The tab styling will be handled by the theme system

            # Original Image Tab with mask overlay
            self.trainable_original_view = QPixmapView()
            self.trainable_tab_widget.addTab(self.trainable_original_view, "Annotate")

            # Segmented Image Tab
            self.trainable_result_view = QPixmapView()
            self.trainable_tab_widget.addTab(self.trainable_result_view, "Segmentation Result")

            # Side-by-side comparison view tab
            side_by_side_widget = QWidget()
            side_by_side_layout = QHBoxLayout(side_by_side_widget)
            side_by_side_layout.setContentsMargins(0, 0, 0, 0)

            # Create synchronized views for side-by-side comparison
            self.trainable_original_sync_view = QPixmapView()
            self.trainable_result_sync_view = QPixmapView()
            
            side_by_side_layout.addWidget(self.trainable_original_sync_view)
            side_by_side_layout.addWidget(self.trainable_result_sync_view)
            
            self.trainable_tab_widget.addTab(side_by_side_widget, "Side by Side Comparison")

            # Feature Importance Tab
            self.feature_importance_view = QPixmapView()
            self.trainable_tab_widget.addTab(self.feature_importance_view, "Feature Importance")

            # Right side: Controls panel
            controls_panel = QWidget()
            controls_panel.setMinimumWidth(250)  # Minimum width for controls panel
            controls_panel.setMaximumWidth(500)  # Maximum width for controls panel
            controls_layout = QVBoxLayout(controls_panel)
            image_display_layout.addWidget(controls_panel)
            
            # Set initial splitter sizes (left: 450px, center: remaining, right: 370px)
            self.trainable_main_splitter.setSizes([450, 600, 370])
            self.trainable_main_splitter.setStretchFactor(0, 0)  # Left sidebar doesn't stretch
            self.trainable_main_splitter.setStretchFactor(1, 1)  # Center area stretches
            self.trainable_main_splitter.setStretchFactor(2, 0)  # Right panel doesn't stretch much

            # Feature Selection Group
            feature_group = QGroupBox("Feature Selection")
            feature_layout = QVBoxLayout(feature_group)

            # Feature checkboxes
            self.intensity_checkbox = QCheckBox("Intensity Features")
            self.intensity_checkbox.setChecked(True)
            self.edges_checkbox = QCheckBox("Edge Features")
            self.texture_checkbox = QCheckBox("Texture Features")
            self.texture_checkbox.setChecked(True)
            
            feature_layout.addWidget(self.intensity_checkbox)
            feature_layout.addWidget(self.edges_checkbox)
            feature_layout.addWidget(self.texture_checkbox)

            # Sigma parameters
            sigma_form_layout = QFormLayout()
            self.sigma_min_spinbox = QSpinBox()
            self.sigma_min_spinbox.setRange(1, 10)
            self.sigma_min_spinbox.setValue(1)
            self.sigma_max_spinbox = QSpinBox()
            self.sigma_max_spinbox.setRange(2, 32)
            self.sigma_max_spinbox.setValue(16)

            sigma_form_layout.addRow("Sigma Min:", self.sigma_min_spinbox)
            sigma_form_layout.addRow("Sigma Max:", self.sigma_max_spinbox)
            feature_layout.addLayout(sigma_form_layout)

            controls_layout.addWidget(feature_group)

            # Classifier Settings Group
            classifier_group = QGroupBox("Classifier Settings")
            classifier_layout = QFormLayout(classifier_group)

            self.n_estimators_spinbox = QSpinBox()
            self.n_estimators_spinbox.setRange(10, 200)
            self.n_estimators_spinbox.setValue(50)

            self.max_depth_spinbox = QSpinBox()
            self.max_depth_spinbox.setRange(1, 50)
            self.max_depth_spinbox.setValue(10)

            self.max_samples_spinbox = QDoubleSpinBox()
            self.max_samples_spinbox.setRange(0.01, 1.0)
            self.max_samples_spinbox.setSingleStep(0.01)
            self.max_samples_spinbox.setValue(0.05)

            classifier_layout.addRow("Number of Trees:", self.n_estimators_spinbox)
            classifier_layout.addRow("Max Depth:", self.max_depth_spinbox)
            classifier_layout.addRow("Max Samples:", self.max_samples_spinbox)

            controls_layout.addWidget(classifier_group)

            # Training Controls
            self.train_button = QPushButton("Train Classifier")
            self.apply_to_current_button = QPushButton("Apply to Current Image")
            self.apply_to_new_button = QPushButton("Apply to New Image")
            self.save_classifier_button = QPushButton("Save Classifier")
            self.load_classifier_button = QPushButton("Load Classifier")

            # Create horizontal layouts for training controls
            training_controls_layout1 = QHBoxLayout()
            training_controls_layout1.addWidget(self.train_button)
            training_controls_layout1.addWidget(self.apply_to_current_button)
            controls_layout.addLayout(training_controls_layout1)

            training_controls_layout2 = QHBoxLayout()
            training_controls_layout2.addWidget(self.apply_to_new_button)
            training_controls_layout2.addWidget(self.save_classifier_button)
            training_controls_layout2.addWidget(self.load_classifier_button)
            controls_layout.addLayout(training_controls_layout2)

            # Progress bar
            self.trainable_progress = QProgressBar()
            self.trainable_progress.setMinimumHeight(20)  # Increased height for better visibility
            self.trainable_progress.setStyleSheet("QProgressBar { min-height: 20px; font-weight: bold; }")
            controls_layout.addWidget(self.trainable_progress)

            # Add class percentages widget for distribution visualization
            self.class_percentages_widget = ClassPercentagesWidget()
            controls_layout.addWidget(self.class_percentages_widget)

        def setup_trainable_sidebar(self, sidebar_layout):
            """Sets up the sidebar controls for the trainable segmentation page."""
            # Image Gallery Group
            gallery_group = QGroupBox("Image Gallery")
            gallery_layout = QVBoxLayout(gallery_group)
            gallery_layout.setContentsMargins(5, 5, 5, 5)  # Reduce margins
            gallery_layout.setSpacing(2)  # Reduce spacing between elements

            # Create the trainable segmentation gallery
            self.trainable_gallery = TrainableSegmentationGallery()
            gallery_layout.addWidget(self.trainable_gallery)

            # Clear Image Gallery button
            self.clear_trainable_gallery_button = QPushButton("Clear Image Gallery")
            self.clear_trainable_gallery_button.setStyleSheet("padding: 6px; margin-top: 5px;")
            gallery_layout.addWidget(self.clear_trainable_gallery_button)

            # Remove Selected Image button removed as requested

            # Add the gallery group to the sidebar
            sidebar_layout.addWidget(gallery_group)

            # Image Information Group
            info_group = QGroupBox("Image Information")
            info_layout = QVBoxLayout(info_group)

            self.trainable_image_info_label = QLabel("No image loaded")
            self.trainable_image_info_label.setWordWrap(True)
            info_layout.addWidget(self.trainable_image_info_label)

            sidebar_layout.addWidget(info_group)

            # Annotations Group
            annotations_group = QGroupBox("Annotations")
            annotations_layout = QVBoxLayout(annotations_group)

            # Save buttons row
            save_layout = QHBoxLayout()
            self.save_segmentation_results_button = QPushButton("Save Results")
            self.save_segmentation_results_button.setToolTip("Save current segmentation results to a file")
            save_layout.addWidget(self.save_segmentation_results_button)
            
            self.save_all_annotations_button = QPushButton("Save All Annotations")
            self.save_all_annotations_button.setToolTip("Save annotations for all images in a single operation")
            save_layout.addWidget(self.save_all_annotations_button)
            annotations_layout.addLayout(save_layout)

            # Load buttons row
            load_layout = QHBoxLayout()
            self.load_all_annotations_button = QPushButton("Load Annotations")
            self.load_all_annotations_button.setToolTip("Load annotations for all images from a previously saved file")
            load_layout.addWidget(self.load_all_annotations_button)

            self.load_exported_annotations_button = QPushButton("Load Exported")
            self.load_exported_annotations_button.setToolTip("Load annotations exported from the unsupervised segmentation page")
            load_layout.addWidget(self.load_exported_annotations_button)
            annotations_layout.addLayout(load_layout)

            # Add separator line
            separator_line = QFrame()
            separator_line.setFrameShape(QFrame.HLine)
            separator_line.setFrameShadow(QFrame.Sunken)
            annotations_layout.addWidget(separator_line)

            # Quick actions row
            quick_layout = QHBoxLayout()
            self.quick_save_annotations_button = QPushButton("Quick Save")
            self.quick_save_annotations_button.setToolTip("Quickly save all annotations to the project directory")
            quick_layout.addWidget(self.quick_save_annotations_button)

            self.quick_load_annotations_button = QPushButton("Quick Load")
            self.quick_load_annotations_button.setToolTip("Quickly load the last saved annotations from the project directory")
            quick_layout.addWidget(self.quick_load_annotations_button)
            annotations_layout.addLayout(quick_layout)

            sidebar_layout.addWidget(annotations_group)

            # Training Labels Group
            training_group = QGroupBox("Training Labels")
            training_layout = QVBoxLayout(training_group)

            # Label selection
            self.label_selection_combo = QComboBox()
            # The combo box will be populated in TrainableSegmentationHandlers.update_label_color_indicator
            training_layout.addWidget(QLabel("Select Label:"))
            training_layout.addWidget(self.label_selection_combo)

            # Label management button
            self.manage_labels_button = QPushButton("Manage Labels")
            self.manage_labels_button.setToolTip("Open label management dialog")
            training_layout.addWidget(self.manage_labels_button)

            # Drawing tools
            self.draw_button = QPushButton("Draw Label")
            self.erase_button = QPushButton("Erase Label")
            self.undo_last_button = QPushButton("Undo Last")
            self.clear_labels_button = QPushButton("Clear All Labels")
            self.sam_magic_wand_button = QPushButton("Magic Wand")
            self.sam_magic_wand_button.setCheckable(True)
            self.accept_sam_button = QPushButton("Accept")
            self.reject_sam_button = QPushButton("Reject")

            # Create horizontal layouts for drawing tools
            drawing_layout1 = QHBoxLayout()
            drawing_layout1.addWidget(self.draw_button)
            drawing_layout1.addWidget(self.erase_button)
            training_layout.addLayout(drawing_layout1)

            drawing_layout2 = QHBoxLayout()
            drawing_layout2.addWidget(self.undo_last_button)
            drawing_layout2.addWidget(self.clear_labels_button)
            training_layout.addLayout(drawing_layout2)

            drawing_layout3 = QHBoxLayout()
            drawing_layout3.addWidget(self.sam_magic_wand_button)
            training_layout.addLayout(drawing_layout3)

            # Add SAM point prompt buttons for consistency with handler
            self.sam_magic_wand_point_button = QPushButton("+ Point")
            self.sam_magic_wand_point_button.setCheckable(True)
            self.sam_magic_wand_point_button.setToolTip("Add positive (foreground) point prompt")

            self.sam_magic_wand_neg_point_button = QPushButton("- Point")
            self.sam_magic_wand_neg_point_button.setCheckable(True)
            self.sam_magic_wand_neg_point_button.setToolTip("Add negative (background) point prompt")

            drawing_layout3 = QHBoxLayout()
            drawing_layout3.addWidget(self.sam_magic_wand_point_button)
            drawing_layout3.addWidget(self.sam_magic_wand_neg_point_button)
            drawing_layout3.addWidget(self.accept_sam_button)
            drawing_layout3.addWidget(self.reject_sam_button)
            training_layout.addLayout(drawing_layout3)

            # Add brush size control
            brush_size_layout = QHBoxLayout()
            brush_size_layout.addWidget(QLabel("Brush Size:"))
            self.trainable_brush_size_slider = QSlider(Qt.Horizontal)
            self.trainable_brush_size_slider.setRange(1, 50)
            self.trainable_brush_size_slider.setValue(5)  # Default brush size
            self.trainable_brush_size_slider.setTickInterval(5)
            self.trainable_brush_size_slider.setTickPosition(QSlider.TicksBelow)
            brush_size_layout.addWidget(self.trainable_brush_size_slider)
            self.trainable_brush_size_label = QLabel("5")
            self.trainable_brush_size_label.setMinimumWidth(30)
            brush_size_layout.addWidget(self.trainable_brush_size_label)
            training_layout.addLayout(brush_size_layout)

            sidebar_layout.addWidget(training_group)

            # SAM Tools Group
            sam_tools_group = QGroupBox("SAM Tools")
            sam_tools_layout = QVBoxLayout(sam_tools_group)

            self.enable_sam_checkbox = QCheckBox("Enable SAM")
            self.enable_sam_checkbox.setToolTip("Enable or disable SAM functionality to improve performance.")
            self.enable_sam_checkbox.setChecked(True) # Enabled by default
            sam_tools_layout.addWidget(self.enable_sam_checkbox)

            sidebar_layout.addWidget(sam_tools_group)

            # Add stretch to push everything to the top
            sidebar_layout.addStretch()