# loading_indicator.py
# Simple animated loading indicator for the AI assistant
from PySide6.QtWidgets import QLabel
from PySide6.QtCore import Qt, QTimer

class LoadingIndicator(QLabel):
    """A simple loading indicator widget."""
    def __init__(self, text="", parent=None):
        super().__init__(parent)
        # Use window flags that ensure the indicator is visible but not intrusive
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self._is_loading = False
        self._animation_step = 0
        self._animation_chars = ["⟳", "⟲", "↻", "↺"] # Rotating arrows for animation
        self._timer = QTimer(self)
        self._timer.timeout.connect(self._update_animation)

        # Make the loading indicator more visible
        self.setStyleSheet("QLabel { color: white; background-color: rgba(0, 0, 0, 200); padding: 15px; border-radius: 8px; font-size: 32px; }")
        self.setText(self._animation_chars[0])
        self.setFixedSize(70, 70) # Larger fixed size to make it more visible

    def _update_animation(self):
        """Update the animation frame."""
        self._animation_step = (self._animation_step + 1) % len(self._animation_chars)
        self.setText(self._animation_chars[self._animation_step])

    def start_loading(self, text=None):
        # Start the animation timer
        self._timer.start(200) # Update every 200ms
        self.show()
        self._is_loading = True

    def stop_loading(self):
        self._timer.stop()
        self.hide()
        self._is_loading = False

    def set_text(self, text):
        # This method is kept for compatibility but doesn't do anything
        pass

    # Position in corner of parent instead of center
    def showEvent(self, event):
        if self.parentWidget():
            parent_rect = self.parentWidget().geometry()
            # Position in bottom-right corner with margin
            self.move(parent_rect.right() - self.width() - 20,
                     parent_rect.bottom() - self.height() - 20)
            # Ensure it's visible by raising it to the top
            self.raise_()
        super().showEvent(event)
