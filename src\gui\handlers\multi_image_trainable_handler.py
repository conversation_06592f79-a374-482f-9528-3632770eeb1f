import os
import numpy as np
import logging
from PySide6.QtWidgets import QMessageBox
from PySide6.QtGui import QPixmap
from src.utils.image_utils import convert_cvimage_to_qimage

# Configure logger
logger = logging.getLogger(__name__)

class MultiImageTrainableHandler:
    """Class for managing multiple images and their annotations for trainable segmentation."""

    def __init__(self):
        self.images = {}
        self.annotations = {}
        self.segmentation_results = {}  # New dictionary to store segmentation results
        self.current_image_path = None
        self.classifier = None
        # Initialize feature_params with default values
        self.feature_params = {
            'intensity': True,
            'edges': True,
            'texture': True,
            'sigma_min': 1,
            'sigma_max': 16,
            'num_sigma': 10
        }
        self.label_mapping = None

    def add_image(self, image_path, image, annotations=None):
        """Adds an image and its annotations to the collection."""
        self.images[image_path] = image
        if annotations is not None:
            self.annotations[image_path] = annotations
        else:
            self.annotations[image_path] = np.zeros(image.shape[:2], dtype=np.uint8)

    def remove_image(self, image_path):
        """Removes an image, its annotations, and segmentation results from the collection."""
        if image_path in self.images:
            del self.images[image_path]
        if image_path in self.annotations:
            del self.annotations[image_path]
        if image_path in self.segmentation_results:
            del self.segmentation_results[image_path]
            logger.debug(f"Removed segmentation result for {image_path}")

    def get_image(self, image_path):
        """Gets an image from the collection."""
        return self.images.get(image_path)

    def get_annotations(self, image_path):
        """Gets annotations for an image from the collection."""
        # Validate image_path
        if not image_path:
            logger.error(f"Cannot get annotations for empty image path")
            return None

        annotations = self.annotations.get(image_path)

        # Return a copy of the annotations to avoid race conditions
        if annotations is not None:
            # Create a fresh copy to ensure we're not returning a reference
            annotations_copy = annotations.copy()

            # Force the annotations to be a numpy array of type uint8
            if not isinstance(annotations_copy, np.ndarray):
                logger.debug(f"Converting annotations to numpy array for {image_path}")
                annotations_copy = np.array(annotations_copy, dtype=np.uint8)
            elif annotations_copy.dtype != np.uint8:
                logger.debug(f"Converting annotations dtype from {annotations_copy.dtype} to uint8 for {image_path}")
                annotations_copy = annotations_copy.astype(np.uint8)

            logger.debug(f"Returning annotations for {image_path} with shape {annotations_copy.shape}")
            return annotations_copy
        else:
            logger.debug(f"No annotations found for {image_path}")
            return None

    def set_annotations(self, image_path, annotations):
        """Sets annotations for an image in the collection."""
        # Validate image_path
        if not image_path:
            logger.error(f"Cannot set annotations for empty image path")
            return

        # Make sure we're working with a copy to avoid race conditions
        if annotations is not None:
            # Create a fresh copy to ensure we're not storing a reference
            annotations_copy = annotations.copy()

            # Force the annotations to be a numpy array of type uint8
            if not isinstance(annotations_copy, np.ndarray):
                logger.debug(f"Converting annotations to numpy array for {image_path}")
                annotations_copy = np.array(annotations_copy, dtype=np.uint8)
            elif annotations_copy.dtype != np.uint8:
                logger.debug(f"Converting annotations dtype from {annotations_copy.dtype} to uint8 for {image_path}")
                annotations_copy = annotations_copy.astype(np.uint8)
        else:
            logger.error(f"Cannot set None annotations for {image_path}")
            return

        # Validate that the image exists in our collection
        if image_path in self.images:
            # Check if dimensions match
            if annotations_copy.shape == self.images[image_path].shape[:2]:
                logger.debug(f"Setting annotations for {image_path} with shape {annotations_copy.shape}")
                self.annotations[image_path] = annotations_copy
            else:
                # Resize annotations to match image dimensions
                import cv2
                logger.warning(f"Annotation dimensions {annotations_copy.shape} do not match image dimensions {self.images[image_path].shape[:2]}. Resizing...")
                try:
                    # Use INTER_NEAREST to preserve label values
                    resized_annotations = cv2.resize(
                        annotations_copy,
                        (self.images[image_path].shape[1], self.images[image_path].shape[0]),
                        interpolation=cv2.INTER_NEAREST
                    )
                    self.annotations[image_path] = resized_annotations
                    logger.debug(f"Resized annotations to {resized_annotations.shape}")
                except Exception as e:
                    logger.error(f"Error resizing annotations: {e}")
                    # Create empty annotations matching the image size
                    self.annotations[image_path] = np.zeros(self.images[image_path].shape[:2], dtype=np.uint8)
                    logger.debug(f"Created empty annotations with shape {self.annotations[image_path].shape}")
        else:
            logger.warning(f"Image path {image_path} not found in collection. Cannot set annotations.")

    def get_all_images(self):
        """Returns all images in the collection."""
        return self.images

    def get_all_annotations(self):
        """Returns all annotations in the collection."""
        return self.annotations

    def clear(self):
        """Clears all images and annotations from the collection."""
        self.images.clear()
        self.annotations.clear()
        self.segmentation_results.clear()  # Clear segmentation results
        self.current_image_path = None
        self.classifier = None
        self.feature_params = {}
        self.label_mapping = None

    def export_all_annotations(self, file_path):
        """Exports all images and their annotations to a file."""
        try:
            # Prepare additional data to save
            export_data = {
                'images': self.images,
                'annotations': self.annotations,
                'image_paths': list(self.images.keys()),
                'segmentation_results': self.segmentation_results,
                'current_image_path': self.current_image_path
            }

            # Add label names if available
            if hasattr(self, 'label_names'):
                export_data['label_names'] = self.label_names
                print(f"DEBUG: Including label_names in export")

            # Add mask colors if available
            if hasattr(self, 'mask_colors'):
                export_data['mask_colors'] = self.mask_colors
                print(f"DEBUG: Including mask_colors in export")

            # Add classifier and related data if available
            if self.classifier is not None:
                export_data['has_classifier'] = True
                export_data['feature_params'] = self.feature_params
                if self.label_mapping is not None:
                    export_data['label_mapping'] = self.label_mapping

            # Save all data
            np.savez_compressed(file_path, **export_data)
            print(f"DEBUG: Successfully exported annotations with {len(self.annotations)} images")
            return True
        except Exception as e:
            print(f"ERROR: Failed to export annotations: {e}")
            import traceback
            traceback.print_exc()
            return False, str(e)

    def import_all_annotations(self, file_path):
        """Imports all images and their annotations from a file."""
        try:
            print(f"DEBUG: Loading annotations from {file_path}")
            data = np.load(file_path, allow_pickle=True)
            print(f"DEBUG: NPZ file keys: {list(data.keys())}")

            # Check for required keys
            if 'images' not in data or 'annotations' not in data or 'image_paths' not in data:
                return False, "Invalid annotation file. Missing required data."

            # Store current images that don't have annotations in the imported file
            current_images = self.images.copy()
            current_annotations = self.annotations.copy()

            # Store current segmentation results if any
            current_segmentation_results = {}
            if hasattr(self, 'segmentation_results'):
                current_segmentation_results = self.segmentation_results.copy()

            # Clear current data
            self.clear()

            # Load imported images and annotations
            imported_images = dict(data['images'].item())
            imported_annotations = dict(data['annotations'].item())

            # Load segmentation results if available
            if 'segmentation_results' in data:
                self.segmentation_results = dict(data['segmentation_results'].item())
                print(f"DEBUG: Loaded {len(self.segmentation_results)} segmentation results")

            # Load current image path if available
            if 'current_image_path' in data:
                self.current_image_path = str(data['current_image_path'])
                print(f"DEBUG: Set current_image_path to {self.current_image_path}")

            # Load label names if available
            if 'label_names' in data:
                self.label_names = dict(data['label_names'].item())
                print(f"DEBUG: Loaded label_names with {len(self.label_names)} entries")

            # Load mask colors if available
            if 'mask_colors' in data:
                self.mask_colors = data['mask_colors']
                print(f"DEBUG: Loaded mask_colors with shape {self.mask_colors.shape}")

            # Load classifier data if available
            if 'has_classifier' in data and data['has_classifier']:
                if 'feature_params' in data:
                    self.feature_params = dict(data['feature_params'].item())
                    print(f"DEBUG: Loaded feature_params")

                if 'label_mapping' in data:
                    self.label_mapping = dict(data['label_mapping'].item())
                    print(f"DEBUG: Loaded label_mapping")

            # Merge imported data with current data
            # First add all imported images and annotations
            self.images = imported_images
            self.annotations = imported_annotations

            # Then add back any images that weren't in the imported data
            for img_path, img in current_images.items():
                if img_path not in self.images:
                    self.images[img_path] = img
                    # If we had annotations for this image, restore them
                    if img_path in current_annotations:
                        self.annotations[img_path] = current_annotations[img_path]
                    # Otherwise create empty annotations
                    else:
                        self.annotations[img_path] = np.zeros(img.shape[:2], dtype=np.uint8)

                    # Restore segmentation results if available
                    if img_path in current_segmentation_results:
                        self.segmentation_results[img_path] = current_segmentation_results[img_path]

            print(f"DEBUG: Successfully imported {len(self.images)} images and {len(self.annotations)} annotations")
            return True
        except Exception as e:
            print(f"ERROR: Failed to import annotations: {e}")
            import traceback
            traceback.print_exc()
            return False, str(e)

    def set_classifier(self, classifier, feature_params, label_mapping=None, mask_colors=None):
        """Sets the classifier, its feature parameters, label mapping, and mask colors."""
        self.classifier = classifier
        print(f"DEBUG: Classifier set in multi_image_handler: {classifier is not None}")

        if feature_params is not None:
            self.feature_params = feature_params
            print(f"DEBUG: Feature parameters set in multi_image_handler: {feature_params}")
        else:
            print("DEBUG: No feature parameters provided, using existing ones")

        if label_mapping is not None:
            # Convert label_mapping keys from numpy types to Python native types
            converted_label_mapping = {}
            for k, v in label_mapping.items():
                # Convert numpy types to Python native types
                key = int(k) if hasattr(k, 'item') else k
                value = int(v) if hasattr(v, 'item') else v
                converted_label_mapping[key] = value

            print(f"DEBUG: Original label_mapping: {label_mapping}")
            print(f"DEBUG: Converted label_mapping: {converted_label_mapping}")
            self.label_mapping = converted_label_mapping
            print(f"DEBUG: Label mapping set in multi_image_handler: {self.label_mapping}")

        if mask_colors is not None:
            # Convert mask_colors to numpy array if it's a list
            if isinstance(mask_colors, list):
                import numpy as np
                mask_colors = np.array(mask_colors)
            self.mask_colors = mask_colors
            print(f"DEBUG: Mask colors set in multi_image_handler")

    def get_classifier(self):
        """Gets the current classifier, its feature parameters, label mapping, and mask colors."""
        mask_colors = getattr(self, 'mask_colors', None)
        print(f"DEBUG: get_classifier called, returning classifier: {self.classifier is not None}")
        print(f"DEBUG: feature_params: {self.feature_params}")
        print(f"DEBUG: label_mapping: {self.label_mapping}")
        print(f"DEBUG: mask_colors: {mask_colors is not None}")
        return self.classifier, self.feature_params, self.label_mapping, mask_colors

    def set_segmentation_result(self, image_path, result):
        """Sets the segmentation result for an image in the collection."""
        # Validate image_path
        if not image_path:
            print(f"ERROR: Cannot set segmentation result for empty image path")
            return

        # Make sure we're working with a copy to avoid race conditions
        if result is not None:
            # Create a fresh copy to ensure we're not storing a reference
            result_copy = result.copy()

            # Force the result to be a numpy array of type uint8
            if not isinstance(result_copy, np.ndarray):
                print(f"DEBUG: Converting segmentation result to numpy array for {image_path}")
                result_copy = np.array(result_copy, dtype=np.uint8)
            elif result_copy.dtype != np.uint8:
                print(f"DEBUG: Converting segmentation result dtype from {result_copy.dtype} to uint8 for {image_path}")
                result_copy = result_copy.astype(np.uint8)
        else:
            print(f"ERROR: Cannot set None segmentation result for {image_path}")
            return

        # Validate that the image exists in our collection
        if image_path in self.images:
            # Check if dimensions match
            if result_copy.shape == self.images[image_path].shape[:2]:
                print(f"DEBUG: Setting segmentation result for {image_path} with shape {result_copy.shape}")
                print(f"DEBUG: Unique values in result: {np.unique(result_copy)}")
                self.segmentation_results[image_path] = result_copy
                print(f"DEBUG: Saved segmentation result for {image_path}")
            else:
                # Resize result to match image dimensions
                import cv2
                print(f"WARNING: Result dimensions {result_copy.shape} do not match image dimensions {self.images[image_path].shape[:2]}. Resizing...")
                try:
                    # Use INTER_NEAREST to preserve label values
                    resized_result = cv2.resize(
                        result_copy,
                        (self.images[image_path].shape[1], self.images[image_path].shape[0]),
                        interpolation=cv2.INTER_NEAREST
                    )
                    self.segmentation_results[image_path] = resized_result
                    print(f"DEBUG: Resized segmentation result to {resized_result.shape}")
                    print(f"DEBUG: Unique values in resized result: {np.unique(resized_result)}")
                except Exception as e:
                    print(f"ERROR: Error resizing segmentation result: {e}")
                    import traceback
                    traceback.print_exc()
        else:
            print(f"WARNING: Image path {image_path} not found in collection. Cannot set segmentation result.")

    def get_segmentation_result(self, image_path):
        """Gets the segmentation result for an image from the collection."""
        # Validate image_path
        if not image_path:
            print(f"ERROR: Cannot get segmentation result for empty image path")
            return None

        if image_path in self.segmentation_results:
            result = self.segmentation_results.get(image_path)

            # Return a copy of the result to avoid race conditions
            if result is not None:
                # Create a fresh copy to ensure we're not returning a reference
                result_copy = result.copy()

                # Force the result to be a numpy array of type uint8
                if not isinstance(result_copy, np.ndarray):
                    print(f"DEBUG: Converting segmentation result to numpy array for {image_path}")
                    result_copy = np.array(result_copy, dtype=np.uint8)
                elif result_copy.dtype != np.uint8:
                    print(f"DEBUG: Converting segmentation result dtype from {result_copy.dtype} to uint8 for {image_path}")
                    result_copy = result_copy.astype(np.uint8)

                print(f"DEBUG: Retrieved segmentation result for {image_path} with shape {result_copy.shape}")
                print(f"DEBUG: Unique values in result: {np.unique(result_copy)}")
                return result_copy
            else:
                print(f"DEBUG: Retrieved None segmentation result for {image_path}")
                return None
        else:
            print(f"DEBUG: No segmentation result found for {image_path}")
            return None

    def get_all_segmentation_results(self):
        """Returns all segmentation results in the collection."""
        return self.segmentation_results