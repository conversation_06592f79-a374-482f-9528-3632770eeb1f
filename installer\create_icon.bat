@echo off
echo Creating application icon from PNG...

:: Check if ImageMagick is available
magick -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ImageMagick found, converting PNG to ICO...
    magick "resources\logo.png" -resize 32x32 "resources\app_icon.ico"
    if exist "resources\app_icon.ico" (
        echo Successfully created app_icon.ico
        goto :enable_icon
    )
)

:: Try with PowerShell method
echo Trying PowerShell conversion...
powershell -Command "& {Add-Type -AssemblyName System.Drawing; $img = [System.Drawing.Image]::FromFile('resources\logo.png'); $bitmap = New-Object System.Drawing.Bitmap(32, 32); $graphics = [System.Drawing.Graphics]::FromImage($bitmap); $graphics.DrawImage($img, 0, 0, 32, 32); $bitmap.Save('resources\app_icon.ico', [System.Drawing.Imaging.ImageFormat]::Icon); $graphics.Dispose(); $bitmap.Dispose(); $img.Dispose()}"

if exist "resources\app_icon.ico" (
    echo Successfully created app_icon.ico with PowerShell
    goto :enable_icon
)

:: If all methods fail, create installer without custom icon
echo Warning: Could not create ICO file. Installer will use default icon.
echo You can manually convert resources\logo.png to resources\app_icon.ico using:
echo - Online converter: https://convertio.co/png-ico/
echo - ImageMagick: magick logo.png -resize 32x32 app_icon.ico
echo - GIMP or other image editor
goto :end

:enable_icon
echo Enabling custom icon in installer script...
powershell -Command "(Get-Content 'VisionLab_Ai_Setup.iss') -replace '; SetupIconFile=resources\\app_icon.ico', 'SetupIconFile=resources\\app_icon.ico' | Set-Content 'VisionLab_Ai_Setup.iss'"
echo Custom icon enabled in installer.

:end
echo Done.
pause
