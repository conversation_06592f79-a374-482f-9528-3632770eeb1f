# src/gui/dialogs/close_confirmation_dialog.py

from PySide6.QtWidgets import Q<PERSON>ialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon
import logging

logger = logging.getLogger(__name__)

class CloseConfirmationDialog(QDialog):
    """Dialog to confirm application closure, preventing accidental closes."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the dialog UI."""
        self.setWindowTitle("Confirm Close")
        self.setModal(True)
        self.setFixedSize(350, 150)
        
        # Main layout
        layout = QVBoxLayout()
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Question icon and message
        message_layout = QHBoxLayout()
        
        # Question icon
        icon_label = QLabel()
        question_icon = self.style().standardIcon(self.style().StandardPixmap.SP_MessageBoxQuestion)
        icon_label.setPixmap(question_icon.pixmap(32, 32))
        icon_label.setAlignment(Qt.AlignmentFlag.AlignTop)
        message_layout.addWidget(icon_label)
        
        # Message text
        message_label = QLabel("Are you sure you want to close VisionLab?")
        message_label.setWordWrap(True)
        message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        message_layout.addWidget(message_label, 1)
        
        layout.addLayout(message_layout)
        
        # Spacer
        layout.addStretch()
        
        # Buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.setDefault(True)
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        self.close_button = QPushButton("Close Application")
        self.close_button.clicked.connect(self.accept)
        button_layout.addWidget(self.close_button)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)