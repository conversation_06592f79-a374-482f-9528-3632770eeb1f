# main.py
# Entry point for the AI Assistant application

import sys
import os
import logging
from PySide6.QtWidgets import QApplication

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add src directory to Python path if running main.py directly from project root
SRC_DIR = os.path.dirname(os.path.abspath(__file__))
if SRC_DIR not in sys.path:
    sys.path.insert(0, SRC_DIR)

try:
    from src.ui.main_window import MainWindow
except ImportError as e:
     logger.error(f"Failed to import MainWindow. Ensure src is in PYTHONPATH or run from project root. Error: {e}")
     sys.exit(1)


if __name__ == "__main__":
    logger.info("Starting VisionLab Ai V4 Application...")
    app = QApplication(sys.argv)

    # Set Application details for QSettings
    app.setOrganizationName("VisionLab Ai")
    app.setApplicationName("VisionLab_Ai_V4")


    main_win = MainWindow()
    main_win.show()
    logger.info("Main window displayed.")

    sys.exit(app.exec())