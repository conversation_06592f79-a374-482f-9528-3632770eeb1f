"""
Theme-aware button styles for the application.
This module provides functions to get button styles based on the current theme.
"""

from PySide6.QtWidgets import QApplication
from src.gui.styles.theme_config import get_palette, COLOR_SCHEMES

def get_theme_name():
    """Get the current theme name from the application settings."""
    app = QApplication.instance()
    if not app:
        return "default-dark"
    
    settings = app.property("settings")
    if settings:
        theme_base = settings.value("app/theme", "Dark Theme").lower().replace(" theme", "")
        color_scheme = settings.value("app/color_scheme", "Default").lower()
        return f"{color_scheme}-{theme_base}"
    else:
        return "default-dark"

def get_theme_colors(theme_name=None):
    """Get the color scheme for the current theme."""
    if theme_name is None:
        theme_name = get_theme_name()
    
    if theme_name not in COLOR_SCHEMES:
        theme_name = "default-dark" if "dark" in theme_name.lower() else "default-light"
    
    return COLOR_SCHEMES[theme_name]

def clear_button_styles():
    """Return an empty style to reset button styles."""
    return ""

def get_normal_button_style(theme_name=None):
    """Get the normal button style for the current theme."""
    colors = get_theme_colors(theme_name)
    return ""  # Return empty string to use theme default

def get_active_button_style(theme_name=None):
    """Get the active button style for the current theme."""
    colors = get_theme_colors(theme_name)
    return ""  # Return empty string to use theme default

def get_toggleable_button_style(theme_name=None):
    """Get the toggleable button style for the current theme."""
    colors = get_theme_colors(theme_name)
    return ""  # Return empty string to use theme default

def get_sam_tool_button_style(theme_name=None):
    """Get the SAM tool button style for the current theme."""
    colors = get_theme_colors(theme_name)
    return ""  # Return empty string to use theme default

def get_positive_point_button_style(theme_name=None):
    """Get the positive point button style for the current theme."""
    colors = get_theme_colors(theme_name)
    return ""  # Return empty string to use theme default

def get_negative_point_button_style(theme_name=None):
    """Get the negative point button style for the current theme."""
    colors = get_theme_colors(theme_name)
    return ""  # Return empty string to use theme default

def get_accept_button_style(theme_name=None):
    """Get the accept button style for the current theme."""
    colors = get_theme_colors(theme_name)
    return ""  # Return empty string to use theme default

def get_reject_button_style(theme_name=None):
    """Get the reject button style for the current theme."""
    colors = get_theme_colors(theme_name)
    return ""  # Return empty string to use theme default

def get_clear_button_style(theme_name=None):
    """Get the clear button style for the current theme."""
    colors = get_theme_colors(theme_name)
    return ""  # Return empty string to use theme default
