# VisionLab Ai Professional Windows Installer

This directory contains all the necessary files and scripts to create a professional Windows installer for VisionLab Ai (PetroSEG Vision Lab) v4.0.

## Overview

The installer is built using **Inno Setup 6**, a professional installer creation tool that produces high-quality Windows installers with modern features including:

- Professional installation wizard with custom branding
- Proper Program Files installation
- Start Menu and Desktop shortcuts
- Complete uninstaller functionality
- Registry integration
- License agreement display
- System requirements checking

## Prerequisites

### Required Software
1. **Inno Setup 6** - Download from: https://jrsoftware.org/isinfo.php
2. **PowerShell** (included with Windows)
3. **Built Application** - The PyInstaller-built application in `../dist/VisionLab_Ai_Simple/`

### System Requirements
- Windows 10 or later
- Administrator privileges for building the installer
- At least 1 GB free disk space for the build process

## Quick Start

### Option 1: Automated Setup (Recommended)
1. Open PowerShell as Administrator in the `installer` directory
2. Run: `.\prepare_resources.ps1`
3. Run: `.\build_installer.bat`

### Option 2: Manual Setup
1. Install Inno Setup 6
2. Prepare resource files (see Resource Files section)
3. Run the build script

## File Structure

```
installer/
├── VisionLab_Ai_Setup.iss      # Main Inno Setup script
├── build_installer.bat         # Windows batch build script
├── prepare_resources.ps1       # PowerShell resource preparation
├── README.md                   # This file
├── resources/                  # Installer resources
│   ├── app_icon.ico           # Application icon (32x32)
│   ├── wizard_image.bmp       # Large wizard image (164x314)
│   ├── wizard_small.bmp       # Small wizard image (55x55)
│   ├── license.txt            # Software license agreement
│   ├── readme.txt             # Installation information
│   └── logo.png               # Original logo file
└── output/                     # Generated installer output
    └── VisionLab_Ai_v4.0.0_Setup.exe
```

## Resource Files

### Icons and Images
- **app_icon.ico**: 32x32 pixel icon for the installer and shortcuts
- **wizard_image.bmp**: 164x314 pixel image displayed in the installer wizard
- **wizard_small.bmp**: 55x55 pixel image for the installer header

### Text Files
- **license.txt**: Software license agreement shown during installation
- **readme.txt**: Installation information and getting started guide

## Building the Installer

### Step 1: Prepare Resources
Run the PowerShell script to automatically prepare all necessary resources:

```powershell
.\prepare_resources.ps1
```

This script will:
- Convert the PNG logo to ICO format
- Create wizard images if they don't exist
- Verify all required files are present
- Check for Inno Setup installation

### Step 2: Build the Installer
Run the batch script to compile the installer:

```batch
.\build_installer.bat
```

This script will:
- Verify Inno Setup installation
- Check for the application distribution
- Compile the installer using Inno Setup
- Generate the final installer executable

### Step 3: Test the Installer
1. Navigate to the `output` directory
2. Run `VisionLab_Ai_v4.0.0_Setup.exe`
3. Test the complete installation process
4. Verify the application launches correctly
5. Test the uninstaller functionality

## Installer Features

### Installation Process
- Welcome screen with application information
- License agreement acceptance
- Installation directory selection (defaults to Program Files)
- Component selection (main application, shortcuts)
- Installation progress with file copying
- Completion screen with launch option

### Shortcuts Created
- **Start Menu**: VisionLab Ai program group with application and uninstaller shortcuts
- **Desktop**: Optional desktop shortcut (user selectable)
- **Quick Launch**: Optional quick launch shortcut (Windows 7 and earlier)

### Registry Entries
- Application registration for proper uninstall support
- Version information and publisher details
- Optional file associations (currently commented out)

### Uninstaller
- Complete removal of application files
- Registry cleanup
- Removal of created shortcuts
- Cleanup of runtime-generated files (logs, cache, temp)

## Customization

### Branding
To customize the installer appearance:

1. **Replace Icons**: Update `resources/app_icon.ico` with your custom icon
2. **Update Images**: Replace wizard images with professional graphics
3. **Modify Colors**: Edit the Inno Setup script to change color schemes
4. **Custom Text**: Update welcome messages and descriptions in the script

### Application Metadata
Update the following constants in `VisionLab_Ai_Setup.iss`:

```pascal
#define MyAppName "Your App Name"
#define MyAppVersion "X.X.X"
#define MyAppPublisher "Your Company"
#define MyAppURL "https://your-website.com"
```

### Installation Options
Modify the `[Tasks]` section to add or remove installation options:

```pascal
Name: "desktopicon"; Description: "Create desktop shortcut"
Name: "startmenu"; Description: "Create Start Menu shortcuts"
Name: "fileassoc"; Description: "Associate file types"
```

## Troubleshooting

### Common Issues

**Inno Setup Not Found**
- Ensure Inno Setup 6 is installed in the default location
- Update the path in `build_installer.bat` if installed elsewhere

**Application Not Found**
- Verify the PyInstaller build completed successfully
- Check that `../dist/VisionLab_Ai_Simple/` contains the application files

**Resource File Errors**
- Run `prepare_resources.ps1` to generate missing files
- Manually create or replace problematic resource files

**Permission Errors**
- Run PowerShell and Command Prompt as Administrator
- Ensure write permissions to the installer directory

### Build Logs
Check the Inno Setup compiler output for detailed error messages. Common issues include:
- Missing resource files
- Invalid file paths
- Syntax errors in the script

## Distribution

### Final Installer
The completed installer will be located at:
`installer/output/VisionLab_Ai_v4.0.0_Setup.exe`

### Distribution Checklist
- [ ] Test installation on clean Windows system
- [ ] Verify all features work correctly
- [ ] Test uninstallation process
- [ ] Check installer size and compression
- [ ] Validate digital signatures (if applicable)
- [ ] Create distribution documentation

### File Size Optimization
The installer uses LZMA2 ultra compression to minimize file size while maintaining fast extraction. Typical compression ratios are 60-80% for this type of application.

## Support

For issues with the installer creation process:
1. Check this README for common solutions
2. Review Inno Setup documentation: https://jrsoftware.org/ishelp/
3. Contact: <EMAIL>

## License

This installer configuration is part of the VisionLab Ai project and follows the same licensing terms as the main application.
