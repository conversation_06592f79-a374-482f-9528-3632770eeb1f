# src/widgets/recent_projects_menu.py
from PySide6.QtWidgets import QMenu, QWidget, QVBoxLayout, QLabel, QFrame
from PySide6.QtGui import QAction
from PySide6.QtCore import Signal, Qt
from PySide6.QtGui import QFont
import os
from src.utils.settings_manager import settings_manager

class RecentProjectsMenu(QMenu):
    """Custom menu for displaying recent projects."""
    
    project_selected = Signal(str)  # Emits the project path when selected
    clear_recent_requested = Signal()  # Emits when user wants to clear recent projects
    
    def __init__(self, parent=None):
        super().__init__("Open Recent", parent)
        self.setMinimumWidth(300)
        self.populate_menu()
    
    def populate_menu(self):
        """Populate the menu with recent projects."""
        self.clear()
        
        recent_projects = settings_manager.get_recent_projects()
        
        if not recent_projects:
            # No recent projects
            no_recent_action = QAction("No recent projects", self)
            no_recent_action.setEnabled(False)
            self.addAction(no_recent_action)
        else:
            # Add recent projects
            for i, project in enumerate(recent_projects):
                project_name = project.get('name', os.path.basename(project.get('path', '')))
                project_path = project.get('path', '')
                project_type = project.get('type', 'standard')
                
                # Create action text with project name and path
                action_text = f"{project_name}"
                if len(project_path) > 50:
                    # Truncate long paths
                    display_path = "..." + project_path[-47:]
                else:
                    display_path = project_path
                
                action = QAction(action_text, self)
                action.setToolTip(f"Path: {project_path}\nType: {project_type.title()}")
                action.setData(project_path)  # Store the full path
                action.triggered.connect(lambda checked=False, path=project_path: self.project_selected.emit(path))
                
                # Add keyboard shortcut for first few items
                if i < 9:
                    action.setShortcut(f"Ctrl+{i+1}")
                
                self.addAction(action)
            
            # Add separator and clear option
            self.addSeparator()
            
            clear_action = QAction("Clear Recent Projects", self)
            clear_action.triggered.connect(self.clear_recent_requested.emit)
            self.addAction(clear_action)
    
    def refresh(self):
        """Refresh the menu contents."""
        self.populate_menu()