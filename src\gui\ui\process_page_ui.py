# src/gui/ui/process_page_ui.py

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QGroupBox, QScrollArea, QFrame, QTabWidget, QSlider, QSpinBox, QDoubleSpinBox,
    QComboBox, QProgressBar, QFormLayout, QMessageBox, QSplitter, QCheckBox)
from PySide6.QtCore import Qt

from src.widgets.scrollable_frame import ScrollableFrame
from src.widgets.pixmap_view import QPixmapView
from src.widgets.segment_grid_widget import SegmentGridWidget
from src.gui.color_picker_dialog import ColorPickerDialog
from src.gui.export_annotations_dialog import ExportAnnotationsDialog

# Import ProcessPageGallery
from src.widgets.process_page_gallery import ProcessPageGallery

# Import PaletteComboBox
from src.widgets.palette_combo_box import PaletteComboBox

class ProcessPageUI:
    """Class for creating and managing the image processing page UI."""

    def show_export_annotations_dialog(self):
        """Shows the export annotations dialog."""
        print("DEBUG: show_export_annotations_dialog called")

        # Check if we have the necessary attributes
        if not hasattr(self, 'segmented_image') or self.segmented_image is None:
            QMessageBox.warning(self, "Warning", "No segmented image available. Please segment an image first.")
            return

        # Create and show the dialog
        dialog = ExportAnnotationsDialog(
            segmented_image=self.segmented_image,
            original_image=self.image if hasattr(self, 'image') else None,
            segment_names=self.segment_names if hasattr(self, 'segment_names') else {},
            image_path=self.image_full_path if hasattr(self, 'image_full_path') else None,
            parent=self
        )
        dialog.exec()

    def setup_process_page(self):
        """Sets up the Image Processing page."""
        self.process_page = QWidget()
        process_layout = QHBoxLayout(self.process_page)
        self.stacked_widget.addTab(self.process_page, "Unsupervised Segmentation")

        # Create main horizontal splitter for resizable left panel
        self.process_main_splitter = QSplitter(Qt.Orientation.Horizontal)
        process_layout.addWidget(self.process_main_splitter)

        # Sidebar
        self.sidebar = ScrollableFrame()
        self.sidebar.setMinimumWidth(250)  # Set minimum instead of fixed width
        self.sidebar.setMaximumWidth(500)  # Set maximum width
        sidebar_layout = QVBoxLayout(self.sidebar.get_content_frame())
        self.process_main_splitter.addWidget(self.sidebar)
        self.setup_sidebar(sidebar_layout) # Add Controls to Sidebar

        # Image Display Area (using QPixmapView)
        image_display_container = QWidget()
        image_display_layout = QHBoxLayout(image_display_container)
        image_display_layout.setContentsMargins(0, 0, 0, 0)
        self.process_main_splitter.addWidget(image_display_container)
        
        # Set initial splitter sizes (left panel: 350px, right area: remaining)
        self.process_main_splitter.setSizes([350, 800])
        self.process_main_splitter.setStretchFactor(0, 0)  # Left panel doesn't stretch
        self.process_main_splitter.setStretchFactor(1, 1)  # Right area stretches

        # Left side: Image display with splitter for segment grid
        left_container = QWidget()
        left_layout = QVBoxLayout(left_container)
        left_layout.setContentsMargins(0, 0, 0, 0)
        image_display_layout.addWidget(left_container)

        # Create a splitter to allow resizing between image and segment grid
        self.image_grid_splitter = QSplitter(Qt.Orientation.Vertical)
        left_layout.addWidget(self.image_grid_splitter)

        # Top part: Side-by-side image views
        image_view_container = QWidget()
        image_view_layout = QVBoxLayout(image_view_container)
        image_view_layout.setContentsMargins(0, 0, 0, 0)
        self.image_grid_splitter.addWidget(image_view_container)

        # Add header labels for the side-by-side views
        headers_container = QWidget()
        headers_container.setMaximumHeight(20)
        headers_layout = QHBoxLayout(headers_container)
        headers_layout.setContentsMargins(0, 0, 0, 0)
        headers_layout.setSpacing(0)
        
        original_label = QLabel("Original Image")
        original_label.setStyleSheet("font-weight: bold; font-size: 9pt; padding: 0px; margin: 0px;")
        original_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        headers_layout.addWidget(original_label)
        
        segmented_label = QLabel("Segmented Image")
        segmented_label.setStyleSheet("font-weight: bold; font-size: 9pt; padding: 0px; margin: 0px;")
        segmented_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        headers_layout.addWidget(segmented_label)
        
        image_view_layout.addWidget(headers_container)

        # Create horizontal splitter for side-by-side image views
        self.image_views_splitter = QSplitter(Qt.Orientation.Horizontal)
        image_view_layout.addWidget(self.image_views_splitter)

        # Original Image View (using QPixmapView)
        self.original_image_view = QPixmapView()
        self.image_views_splitter.addWidget(self.original_image_view)

        # Segmented Image View (using QPixmapView)
        self.segmented_image_view = QPixmapView()
        self.image_views_splitter.addWidget(self.segmented_image_view)
        
        # Set equal sizes for both views (50% each)
        self.image_views_splitter.setSizes([500, 500])
        
        # Connect signals for synchronized zooming and scrolling
        self._setup_synchronized_views()

        # Bottom part: Segment grid
        segment_grid_container = QWidget()
        segment_grid_layout = QVBoxLayout(segment_grid_container)
        segment_grid_layout.setContentsMargins(0, 0, 0, 0)
        self.image_grid_splitter.addWidget(segment_grid_container)

        # Add a header for the segment grid with label
        segment_grid_header = QWidget()
        segment_grid_header_layout = QHBoxLayout(segment_grid_header)
        segment_grid_header_layout.setContentsMargins(0, 0, 0, 0)

        # Add a label for the segment grid
        segment_grid_label = QLabel("Segment Grid View")
        segment_grid_label.setStyleSheet("font-weight: bold; font-size: 10pt;")
        segment_grid_header_layout.addWidget(segment_grid_label)

        segment_grid_layout.addWidget(segment_grid_header)

        # Add the segment grid widget
        self.segment_grid = SegmentGridWidget()
        segment_grid_layout.addWidget(self.segment_grid)

        # Set initial sizes for the splitter (70% image, 30% grid)
        self.image_grid_splitter.setSizes([700, 300])

        # Hide the segment grid container initially
        segment_grid_container.hide()

        # Right side: Controls panel
        controls_panel = QWidget()
        controls_panel.setFixedWidth(350)
        controls_layout = QVBoxLayout(controls_panel)
        image_display_layout.addWidget(controls_panel)

        # First group: Merge action button at the top
        merge_action_group = QGroupBox("Merge Action")
        merge_action_layout = QVBoxLayout(merge_action_group)

        # Create the merge button
        self.merge_button = QPushButton("Merge Selected Segments")
        self.merge_button.setMinimumHeight(25)  # Make button taller for better visibility
        merge_action_layout.addWidget(self.merge_button)

        # Add the merge action group to the main controls layout
        controls_layout.addWidget(merge_action_group)

        # Add a small spacing between the groups
        controls_layout.addSpacing(5)

        # Create a scrollable container for the Segments & Colors group box
        segments_scroll_container = QScrollArea()
        segments_scroll_container.setWidgetResizable(True)
        segments_scroll_container.setFixedHeight(290)  # Fixed height for the scrollable container
        segments_scroll_container.setFrameShape(QFrame.NoFrame)  # Remove the frame border

        # Create the Segments & Colors group box inside the scroll area
        segments_group = QGroupBox("Segments & Colors")
        segments_layout = QVBoxLayout(segments_group)
        segments_layout.setSpacing(5)  # Add spacing between elements

        # Add description label
        description_label = QLabel("Select segments to merge:")
        segments_layout.addWidget(description_label)

        # Create a frame for the segments with checkboxes
        self.merge_inner_frame = QFrame()
        self.merge_inner_layout = QVBoxLayout(self.merge_inner_frame)
        self.merge_inner_layout.setContentsMargins(5, 5, 5, 5)  # Add margins inside the frame

        # Add the inner frame to the segments layout
        segments_layout.addWidget(self.merge_inner_frame)

        # Set the group box as the widget for the scroll area
        segments_scroll_container.setWidget(segments_group)

        # Add the scroll container to the main controls layout
        controls_layout.addWidget(segments_scroll_container)

        # For backward compatibility, create these objects that won't be used
        # This prevents errors in code that expects these widgets to exist
        self.info_frame = QFrame()
        self.info_frame.setLayout(QVBoxLayout())
        self.merge_scroll_area = segments_scroll_container  # Redirect to the new scroll area

        # Post-processing Controls
        post_proc_group = QGroupBox("Post-processing")
        post_proc_layout = QVBoxLayout(post_proc_group)

        # Add color palette selection
        self.color_palette_combo = PaletteComboBox()
        self.color_palette_combo.addItems([
            "tab20", "viridis", "plasma", "magma", "inferno", "cividis",
            "Pastel1", "Set1", "Set2", "Set3", "Paired", "tab10", "tab20b", "tab20c",
            "Accent", "Dark2", "Paired", "Spectral", "Wistia", "YlOrRd", "YlOrBr",
            "YlGnBu", "YlGn", "RdYlGn", "RdYlBu", "RdGy", "RdBu", "PuRd", "PRGn",
            "PiYG", "OrRd", "GnBu", "BuPu", "BuGn", "BrBG"
        ])
        controls_layout.addWidget(QLabel("Color Palette:"))
        controls_layout.addWidget(self.color_palette_combo)

        # Add randomize colors button
        self.change_colors_button = QPushButton("Apply Color Palette")
        controls_layout.addWidget(self.change_colors_button)

        # Add pick colors button
        self.pick_colors_button = QPushButton("Pick Colors")
        post_proc_layout.addWidget(self.pick_colors_button)

        # Add save custom palette button
        self.save_palette_button = QPushButton("Save Custom Palette")
        post_proc_layout.addWidget(self.save_palette_button)

        # Add manage palettes button
        self.manage_palettes_button = QPushButton("Manage Palettes")
        post_proc_layout.addWidget(self.manage_palettes_button)

        # Add display segments button
        self.display_multi_segments_button = QPushButton("Select Segments to Display")
        self.display_multi_segments_button.setToolTip("Choose multiple segments to display in grid layout or combined in main preview")
        post_proc_layout.addWidget(self.display_multi_segments_button)

        # Add show full segmentation button
        self.show_full_segmentation_button = QPushButton("Show Full Segmentation")
        post_proc_layout.addWidget(self.show_full_segmentation_button)

        # Add export annotations button to post-processing section
        self.export_annotations_button = QPushButton("Export Segments as Annotations")
        self.export_annotations_button.clicked.connect(self.show_export_annotations_dialog)
        post_proc_layout.addWidget(self.export_annotations_button)

        controls_layout.addWidget(post_proc_group)

        controls_layout.addStretch()

    def setup_sidebar(self, sidebar_layout):
        """Sets up the sidebar controls for the processing page."""
        # Image Actions Section
        upload_group = QGroupBox("Image Actions")
        upload_layout = QVBoxLayout(upload_group)

        self.download_button = QPushButton("Save Segmented Image")
        upload_layout.addWidget(self.download_button)



        # Image Gallery Group
        gallery_group = QGroupBox("Image Gallery")
        gallery_layout = QVBoxLayout(gallery_group)
        gallery_layout.setContentsMargins(5, 5, 5, 5)  # Reduce margins
        gallery_layout.setSpacing(2)  # Reduce spacing between elements

        # Create the process page gallery
        self.process_gallery = ProcessPageGallery()
        gallery_layout.addWidget(self.process_gallery)

        # Clear Image Gallery button
        self.clear_process_gallery_button = QPushButton("Clear Image Gallery")
        self.clear_process_gallery_button.setStyleSheet("padding: 6px; margin-top: 5px;")
        gallery_layout.addWidget(self.clear_process_gallery_button)

        upload_layout.addWidget(gallery_group)

        sidebar_layout.addWidget(upload_group)

        # Segmentation Parameters
        params_group = QGroupBox("Segmentation Parameters")
        params_layout = QFormLayout(params_group)

        # Basic Parameters
        # Training Epochs
        self.train_epoch = QSpinBox()
        self.train_epoch.setRange(1, 1000)
        self.train_epoch.setValue(25)
        params_layout.addRow("Training Epochs:", self.train_epoch)

        # Model Dimensions
        self.mod_dim1 = QSpinBox()
        self.mod_dim1.setRange(1, 100)
        self.mod_dim1.setValue(25)
        params_layout.addRow("Model Dimension 1:", self.mod_dim1)

        self.mod_dim2 = QSpinBox()
        self.mod_dim2.setRange(1, 100)
        self.mod_dim2.setValue(25)
        params_layout.addRow("Model Dimension 2:", self.mod_dim2)

        # Label Number Range
        self.min_label_num = QSpinBox()
        self.min_label_num.setRange(1, 100)
        self.min_label_num.setValue(3)
        params_layout.addRow("Min Label Number:", self.min_label_num)

        self.max_label_num = QSpinBox()
        self.max_label_num.setRange(1, 250)
        self.max_label_num.setValue(25)
        params_layout.addRow("Max Label Number:", self.max_label_num)

        # Target Size
        self.target_size_width = QSpinBox()
        self.target_size_width.setRange(100, 2000)
        self.target_size_width.setValue(750)
        params_layout.addRow("Target Width:", self.target_size_width)

        self.target_size_height = QSpinBox()
        self.target_size_height.setRange(100, 2000)
        self.target_size_height.setValue(750)
        params_layout.addRow("Target Height:", self.target_size_height)

        # Segmentation Method
        self.segmentation_method = QComboBox()
        self.segmentation_method.addItems(["KMeans", "Felzenszwalb", "PCA"])
        params_layout.addRow("Method:", self.segmentation_method)

        # Basic Learning Rate
        self.learning_rate_spinbox = QDoubleSpinBox()
        self.learning_rate_spinbox.setRange(0.0001, 1.0)
        self.learning_rate_spinbox.setDecimals(4)
        self.learning_rate_spinbox.setSingleStep(0.0001)
        self.learning_rate_spinbox.setValue(0.001)
        params_layout.addRow("Learning Rate:", self.learning_rate_spinbox)

        # Advanced Options (Collapsed by default) - Inside Segmentation Parameters
        from src.widgets.collapsible_section import CollapsibleSection
        advanced_section = CollapsibleSection("Advanced Options")
        advanced_section.set_expanded(False)  # Collapsed by default
        
        # Advanced parameters layout
        advanced_layout = QFormLayout()
        
        # Adaptive Learning Rate checkbox
        self.adaptive_lr_checkbox = QCheckBox("Use Adaptive Learning Rate")
        self.adaptive_lr_checkbox.setChecked(True)  # Default to enabled
        advanced_layout.addRow("", self.adaptive_lr_checkbox)
        
        # Connect checkbox to enable/disable learning rate spinbox
        self.adaptive_lr_checkbox.toggled.connect(self.toggle_learning_rate_input)
        
        # Momentum parameter
        self.momentum_spinbox = QDoubleSpinBox()
        self.momentum_spinbox.setRange(0.0, 0.99)
        self.momentum_spinbox.setDecimals(3)
        self.momentum_spinbox.setSingleStep(0.01)
        self.momentum_spinbox.setValue(0.9)  # Default momentum value
        advanced_layout.addRow("Momentum:", self.momentum_spinbox)
        

        
        # Weight Decay (L2 regularization)
        self.weight_decay_spinbox = QDoubleSpinBox()
        self.weight_decay_spinbox.setRange(0.0, 0.01)
        self.weight_decay_spinbox.setDecimals(6)
        self.weight_decay_spinbox.setSingleStep(0.000001)
        self.weight_decay_spinbox.setValue(0.0001)
        self.weight_decay_spinbox.setToolTip("L2 regularization to prevent overfitting")
        advanced_layout.addRow("Weight Decay:", self.weight_decay_spinbox)
        
        # Gradient Clipping
        self.gradient_clipping_checkbox = QCheckBox("Enable Gradient Clipping")
        self.gradient_clipping_checkbox.setChecked(True)  # Default to enabled
        advanced_layout.addRow("", self.gradient_clipping_checkbox)
        
        self.gradient_clip_value_spinbox = QDoubleSpinBox()
        self.gradient_clip_value_spinbox.setRange(0.1, 10.0)
        self.gradient_clip_value_spinbox.setDecimals(1)
        self.gradient_clip_value_spinbox.setValue(1.0)
        self.gradient_clip_value_spinbox.setToolTip("Maximum gradient norm for clipping")
        advanced_layout.addRow("Gradient Clip Value:", self.gradient_clip_value_spinbox)
        
        # Connect gradient clipping checkbox
        self.gradient_clipping_checkbox.toggled.connect(self.toggle_gradient_clip_input)
        
        # Optimizer Selection
        self.optimizer_combo = QComboBox()
        self.optimizer_combo.addItems(["SGD", "Adam", "AdamW", "RMSprop"])
        self.optimizer_combo.setCurrentText("AdamW")  # Default to AdamW for better performance
        self.optimizer_combo.setToolTip("Choose optimization algorithm")
        advanced_layout.addRow("Optimizer:", self.optimizer_combo)
        
        # Connect optimizer selection to update UI
        self.optimizer_combo.currentTextChanged.connect(self.on_optimizer_changed)
        
        # Add the advanced layout to the collapsible section
        advanced_section.add_layout(advanced_layout)
        
        # Add the collapsible section to the params_layout (inside Segmentation Parameters)
        params_layout.addRow(advanced_section)

        sidebar_layout.addWidget(params_group)
        
        # Initialize UI state based on default values
        self.toggle_learning_rate_input(self.adaptive_lr_checkbox.isChecked())
        self.toggle_gradient_clip_input(self.gradient_clipping_checkbox.isChecked())
        self.on_optimizer_changed(self.optimizer_combo.currentText())

        # Segmentation Controls
        control_group = QGroupBox("Controls")
        control_layout = QVBoxLayout(control_group)

        self.segment_button = QPushButton("Start Segmentation")
        control_layout.addWidget(self.segment_button)

        self.stop_button = QPushButton("Stop Training")
        control_layout.addWidget(self.stop_button)

        # Add Reload Previous Results button
        self.reload_button = QPushButton("Reload Previous Results")
        self.reload_button.setEnabled(False)  # Disabled by default until we know there are results to reload
        control_layout.addWidget(self.reload_button)

        # Progress Bar
        self.progress = QProgressBar()
        self.progress.setMinimumHeight(20)  # Increased height for better visibility
        self.progress.setStyleSheet("QProgressBar { min-height: 20px; font-weight: bold; }")
        control_layout.addWidget(self.progress)

        # Epoch Slider
        slider_layout = QHBoxLayout()
        self.epoch_label = QLabel("Epoch: 0")
        slider_layout.addWidget(self.epoch_label)
        self.epoch_slider = QSlider(Qt.Horizontal)
        self.epoch_slider.setEnabled(False)
        slider_layout.addWidget(self.epoch_slider)
        control_layout.addLayout(slider_layout)

        sidebar_layout.addWidget(control_group)
        sidebar_layout.addStretch()

        # Image gallery is now part of the sidebar

    def _setup_synchronized_views(self):
        """Sets up synchronized zooming and scrolling between original and segmented image views."""
        # Connect zoom signals for synchronization
        self.original_image_view.zoom_changed.connect(self._sync_zoom_to_segmented)
        self.segmented_image_view.zoom_changed.connect(self._sync_zoom_to_original)
        
        # Connect scroll signals for synchronization
        self.original_image_view.scroll_changed.connect(self._sync_scroll_to_segmented)
        self.segmented_image_view.scroll_changed.connect(self._sync_scroll_to_original)
        
        # Flag to prevent infinite recursion during synchronization
        self._syncing = False
    
    def _sync_zoom_to_segmented(self, zoom_scale):
        """Synchronizes zoom from original view to segmented view."""
        if not self._syncing and self._can_sync_views():
            self._syncing = True
            try:
                self.segmented_image_view.zoom_scale = zoom_scale
                self.segmented_image_view.resize_to_image()
            except Exception as e:
                print(f"Warning: Failed to sync zoom to segmented view: {e}")
            finally:
                self._syncing = False
    
    def _sync_zoom_to_original(self, zoom_scale):
        """Synchronizes zoom from segmented view to original view."""
        if not self._syncing and self._can_sync_views():
            self._syncing = True
            try:
                self.original_image_view.zoom_scale = zoom_scale
                self.original_image_view.resize_to_image()
            except Exception as e:
                print(f"Warning: Failed to sync zoom to original view: {e}")
            finally:
                self._syncing = False
    
    def _sync_scroll_to_segmented(self, h_value, v_value):
        """Synchronizes scroll position from original view to segmented view."""
        if not self._syncing and self._can_sync_views():
            self._syncing = True
            try:
                self.segmented_image_view.setScrollPosition(h_value, v_value)
            except Exception as e:
                print(f"Warning: Failed to sync scroll to segmented view: {e}")
            finally:
                self._syncing = False
    
    def _sync_scroll_to_original(self, h_value, v_value):
        """Synchronizes scroll position from segmented view to original view."""
        if not self._syncing and self._can_sync_views():
            self._syncing = True
            try:
                self.original_image_view.setScrollPosition(h_value, v_value)
            except Exception as e:
                print(f"Warning: Failed to sync scroll to original view: {e}")
            finally:
                self._syncing = False
    
    def _can_sync_views(self):
        """Checks if views can be synchronized safely."""
        try:
            # Check if both views have valid pixmaps
            if not (hasattr(self.original_image_view, '_pixmap') and self.original_image_view._pixmap and
                    hasattr(self.segmented_image_view, '_pixmap') and self.segmented_image_view._pixmap):
                return False
            
            # Check if pixmaps have reasonable dimensions
            orig_size = self.original_image_view._pixmap.size()
            seg_size = self.segmented_image_view._pixmap.size()
            
            if orig_size.width() <= 0 or orig_size.height() <= 0 or seg_size.width() <= 0 or seg_size.height() <= 0:
                return False
            
            # Check if both images have the same dimensions
            # This prevents synchronization issues when loading pre-segmented images with different dimensions
            if orig_size.width() != seg_size.width() or orig_size.height() != seg_size.height():
                print(f"DEBUG: Cannot sync views - dimension mismatch: original({orig_size.width()}x{orig_size.height()}) vs segmented({seg_size.width()}x{seg_size.height()})")
                return False
                
            return True
        except Exception:
            return False
    
    def toggle_learning_rate_input(self, checked):
        """Toggle the learning rate input based on adaptive learning rate checkbox."""
        # When adaptive learning rate is checked, disable manual learning rate input
        # When unchecked, enable manual learning rate input
        self.learning_rate_spinbox.setEnabled(not checked)
        
        if checked:
            self.learning_rate_spinbox.setToolTip("Learning rate will be automatically adjusted during training")
        else:
            self.learning_rate_spinbox.setToolTip("Set the learning rate manually")
    

    
    def toggle_gradient_clip_input(self, checked):
        """Toggle the gradient clipping value input based on gradient clipping checkbox."""
        self.gradient_clip_value_spinbox.setEnabled(checked)
        
        if checked:
            self.gradient_clip_value_spinbox.setToolTip("Maximum gradient norm for clipping")
        else:
            self.gradient_clip_value_spinbox.setToolTip("Gradient clipping is disabled")
    
    def on_optimizer_changed(self, optimizer_name):
        """Handle optimizer selection changes and update UI accordingly."""
        # Enable/disable momentum based on optimizer
        if optimizer_name in ["Adam", "AdamW", "RMSprop"]:
            # These optimizers have built-in momentum-like mechanisms
            self.momentum_spinbox.setEnabled(False)
            self.momentum_spinbox.setToolTip(f"{optimizer_name} has built-in momentum mechanisms")
        else:
            # SGD can use momentum
            self.momentum_spinbox.setEnabled(True)
            self.momentum_spinbox.setToolTip("Set the momentum manually")
        
        # Update weight decay availability
        if optimizer_name == "AdamW":
            self.weight_decay_spinbox.setToolTip("AdamW includes decoupled weight decay")
        else:
            self.weight_decay_spinbox.setToolTip("L2 regularization to prevent overfitting")