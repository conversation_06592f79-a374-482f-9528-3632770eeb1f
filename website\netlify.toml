# Netlify configuration for Vision Lab website

[build]
  # Build command (if needed for preprocessing)
  command = "echo 'No build step required for static files'"
  
  # Directory to publish
  publish = "."
  
  # Functions directory (for serverless functions)
  functions = "netlify/functions"

[build.environment]
  # Environment variables for build
  NODE_VERSION = "18"
  PYTHON_VERSION = "3.9"

# Redirect rules
[[redirects]]
  # API endpoints to serverless functions
  from = "/api/waitlist"
  to = "/.netlify/functions/waitlist"
  status = 200
  
[[redirects]]
  from = "/api/stats"
  to = "/.netlify/functions/stats"
  status = 200
  
[[redirects]]
  from = "/api/health"
  to = "/.netlify/functions/health"
  status = 200

# Headers for security
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: https:; connect-src 'self' https:; frame-ancestors 'none';"

# Cache control
[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000"
    
[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000"
    
[[headers]]
  for = "*.html"
  [headers.values]
    Cache-Control = "public, max-age=3600"

# Form handling (alternative to custom API)
# Uncomment if you want to use Netlify Forms instead of custom API
# [[forms]]
#   name = "waitlist"
#   action = "/thank-you"
#   
# [forms.fields]
#   fullName = { required = true }
#   email = { required = true, type = "email" }
#   organization = { required = false }
#   useCase = { required = false }
#   experience = { required = false }
#   newsletter = { required = false, type = "checkbox" }
#   privacy = { required = true, type = "checkbox" }

# Edge functions (for advanced processing)
# [[edge_functions]]
#   function = "rate-limiter"
#   path = "/api/*"

# Plugin configuration
# [[plugins]]
#   package = "@netlify/plugin-sitemap"
#   
# [[plugins]]
#   package = "netlify-plugin-minify-html"
#   
# [[plugins]]
#   package = "netlify-plugin-inline-critical-css"

# Development settings
[dev]
  command = "python server.py"
  port = 5000
  publish = "."
  autoLaunch = true