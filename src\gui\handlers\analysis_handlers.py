# src/gui/handlers/analysis_handlers.py

import os
import numpy as np
import cv2
import logging
from PySide6.QtWidgets import QMessageBox, QDialog, QInputDialog, QLineEdit
from PySide6.QtGui import QPixmap
from PySide6.QtCore import Qt, Signal, Slot

from src.utils.image_utils import resize_image, convert_cvimage_to_qpixmap
from src.gui.crop_dialog import CropDialog
from src.widgets.collapsible_section import CollapsibleSection

logger = logging.getLogger(__name__)

class AnalysisHandlers:
    """Class for handling image analysis and enhancement functions."""

    def __init__(self):
        """Initialize the analysis handlers."""
        # Initialize image history stack for undo functionality
        self.image_history = []
        self.max_history_size = 10  # Maximum number of history states to keep

        # Initialize independent image variables for the Image Lab page
        self.analysis_image = None  # Current original image in the Image Lab page
        self.analysis_image_resized = None  # Resized version for display
        self.analysis_states = {}  # Store analysis states per image

    def _initialize_collapsible_sections(self):
        """Initialize the collapsible sections in the analysis page."""
        # Find all collapsible sections in the UI
        collapsible_sections = []
        for attr_name in dir(self):
            attr = getattr(self, attr_name)
            if isinstance(attr, CollapsibleSection):
                collapsible_sections.append(attr)

        # Set initial state (all sections collapsed by default)
        for section in collapsible_sections:
            section.set_expanded(False)

        logger.info(f"Initialized {len(collapsible_sections)} collapsible sections in analysis page")

    def reset_analysis_image(self):
        """Resets the analysis image to the original image."""
        # Check if we have images in the analysis gallery
        has_analysis_images = hasattr(self, 'analysis_gallery') and len(self.analysis_gallery.images) > 0

        # Only use the independent analysis_image if it exists and the analysis gallery has images
        if has_analysis_images and hasattr(self, 'analysis_image') and self.analysis_image is not None:
            source_image = self.analysis_image
        else:
            # Don't fall back to shared image - this would create the unwanted connection
            logger.warning("Cannot reset analysis image: No image loaded in Image Lab gallery.")
            return

        # Clear the history stack when resetting
        self.image_history = []
        if hasattr(self, 'undo_btn'):
            self.undo_btn.setEnabled(False)

        # Reset processed image to original
        self.processed_image = source_image.copy()

        # Reset sliders and spinbox with null checks
        if hasattr(self, 'brightness_slider'):
            self.brightness_slider.setValue(0)
        if hasattr(self, 'contrast_slider'):
            self.contrast_slider.setValue(0)
        if hasattr(self, 'saturation_slider'):
            self.saturation_slider.setValue(0)
        if hasattr(self, 'hue_slider'):
            self.hue_slider.setValue(0)
        if hasattr(self, 'sharpness_slider'):
            self.sharpness_slider.setValue(0)
        if hasattr(self, 'gamma_spinbox'):
            self.gamma_spinbox.setValue(1.0)
        if hasattr(self, 'gaussian_slider'):
            self.gaussian_slider.setValue(0)
        if hasattr(self, 'median_slider'):
            self.median_slider.setValue(0)

        # Reset value labels with null checks
        if hasattr(self, 'brightness_value'):
            self.brightness_value.setText("0")
        if hasattr(self, 'contrast_value'):
            self.contrast_value.setText("0")
        if hasattr(self, 'saturation_value'):
            self.saturation_value.setText("0")
        if hasattr(self, 'hue_value'):
            self.hue_value.setText("0")
        if hasattr(self, 'sharpness_value'):
            self.sharpness_value.setText("0")
        if hasattr(self, 'gaussian_value'):
            self.gaussian_value.setText("0")
        if hasattr(self, 'median_value'):
            self.median_value.setText("0")

        # Display images if the views exist
        if hasattr(self, 'common_size') and hasattr(self, 'processed_image'):
            processed_image_resized = resize_image(self.processed_image, self.common_size)
            pixmap = convert_cvimage_to_qpixmap(processed_image_resized, already_rgb=True)

            if hasattr(self, 'analysis_original_view'):
                self.analysis_original_view.setPixmap(pixmap.copy())
            if hasattr(self, 'process_image_view'):
                self.process_image_view.setPixmap(pixmap.copy())
            if hasattr(self, 'full_processed_view'):
                self.full_processed_view.setPixmap(pixmap.copy())

            # Update image info if the method exists
            if hasattr(self, 'update_image_info'):
                self.update_image_info(self.processed_image)

        logger.info("Analysis image reset to original.")

    def push_to_history(self):
        """Pushes the current processed image to the history stack."""
        if hasattr(self, 'processed_image') and self.processed_image is not None:
            # Make a deep copy of the current processed image
            self.image_history.append(self.processed_image.copy())

            # Limit the history size
            if len(self.image_history) > self.max_history_size:
                self.image_history.pop(0)  # Remove oldest item

            # Enable the undo button if it exists
            if hasattr(self, 'undo_btn'):
                self.undo_btn.setEnabled(True)

            logger.debug(f"Pushed image to history stack. Stack size: {len(self.image_history)}")

    def undo_last_operation(self):
        """Undoes the last image operation by restoring the previous state."""
        if not self.image_history:
            logger.warning("No history available to undo")
            return

        # Pop the last image from history
        previous_image = self.image_history.pop()

        # Restore the previous image
        self.processed_image = previous_image

        # Update the display
        if hasattr(self, 'common_size'):
            processed_image_resized = resize_image(self.processed_image, self.common_size)
            pixmap = convert_cvimage_to_qpixmap(processed_image_resized, already_rgb=True)

            if hasattr(self, 'process_image_view'):
                self.process_image_view.setPixmap(pixmap)
            if hasattr(self, 'full_processed_view'):
                self.full_processed_view.setPixmap(pixmap)

            # Update image info if available
            if hasattr(self, 'update_image_info'):
                self.update_image_info(self.processed_image)

        # Disable undo button if history is empty
        if not self.image_history and hasattr(self, 'undo_btn'):
            self.undo_btn.setEnabled(False)

        logger.info("Undid last operation")

    def update_analysis_preview(self):
        """Updates the analysis preview with current adjustments."""
        # Check if we have images in the analysis gallery
        has_analysis_images = hasattr(self, 'analysis_gallery') and len(self.analysis_gallery.images) > 0

        if not has_analysis_images:
            # Don't allow operations if no images are loaded in the Image Lab gallery
            logger.warning("Cannot update preview: No image loaded in Image Lab gallery.")
            return

        if self.processed_image is None:
            return

        # Save current state to history before making changes
        self.push_to_history()

        # Get adjustment values with null checks
        brightness = self.brightness_slider.value() if hasattr(self, 'brightness_slider') else 0
        contrast = self.contrast_slider.value() / 100.0 + 1.0 if hasattr(self, 'contrast_slider') else 1.0  # Convert to contrast factor
        gamma = self.gamma_spinbox.value() if hasattr(self, 'gamma_spinbox') else 1.0
        saturation = self.saturation_slider.value() if hasattr(self, 'saturation_slider') else 0
        hue = self.hue_slider.value() if hasattr(self, 'hue_slider') else 0
        sharpness = self.sharpness_slider.value() if hasattr(self, 'sharpness_slider') else 0
        gaussian_value = self.gaussian_slider.value() if hasattr(self, 'gaussian_slider') else 0
        median_value = self.median_slider.value() if hasattr(self, 'median_slider') else 0

        # Update value labels if they exist
        if hasattr(self, 'brightness_value'):
            self.brightness_value.setText(str(brightness))
        if hasattr(self, 'contrast_value'):
            self.contrast_value.setText(str(int((contrast - 1.0) * 100)))
        if hasattr(self, 'saturation_value'):
            self.saturation_value.setText(str(saturation))
        if hasattr(self, 'hue_value'):
            self.hue_value.setText(str(hue))
        if hasattr(self, 'sharpness_value'):
            self.sharpness_value.setText(str(sharpness))
        if hasattr(self, 'gaussian_value'):
            self.gaussian_value.setText(str(gaussian_value))
        if hasattr(self, 'median_value'):
            self.median_value.setText(str(median_value))

        # Apply the current adjustments to the image
        self._apply_current_adjustments()

    def update_threshold_label(self):
        """Updates the threshold value label."""
        self.threshold_value_label.setText(str(self.threshold_slider.value()))

    def update_adaptive_block_size_label(self):
        """Updates the adaptive block size label."""
        block_size = self.adaptive_block_size_slider.value() * 2 + 3  # Must be odd
        self.adaptive_block_size_label.setText(str(block_size))

    def update_adaptive_c_label(self):
        """Updates the adaptive C value label."""
        self.adaptive_c_label.setText(str(self.adaptive_c_slider.value()))

    def update_canny_low_threshold_label(self):
        """Updates the Canny low threshold label."""
        self.canny_low_threshold_label.setText(str(self.canny_low_threshold_slider.value()))

    def update_canny_high_threshold_label(self):
        """Updates the Canny high threshold label."""
        self.canny_high_threshold_label.setText(str(self.canny_high_threshold_slider.value()))

    def update_morph_kernel_size_label(self):
        """Updates the morphological kernel size label."""
        kernel_size = self.morph_kernel_size_slider.value() * 2 + 1  # Must be odd
        self.morph_kernel_size_label.setText(str(kernel_size))

    def update_morph_iterations_label(self):
        """Updates the morphological iterations label."""
        self.morph_iterations_label.setText(str(self.morph_iterations_slider.value()))

    def _apply_current_adjustments(self):
        """Helper method to apply current adjustments to the image."""
        if self.processed_image is None or not hasattr(self, 'image') or self.image is None:
            return

        # Get adjustment values with null checks
        brightness = self.brightness_slider.value() if hasattr(self, 'brightness_slider') else 0
        contrast = self.contrast_slider.value() / 100.0 + 1.0 if hasattr(self, 'contrast_slider') else 1.0
        gamma = self.gamma_spinbox.value() if hasattr(self, 'gamma_spinbox') else 1.0
        saturation = self.saturation_slider.value() if hasattr(self, 'saturation_slider') else 0
        hue = self.hue_slider.value() if hasattr(self, 'hue_slider') else 0
        sharpness = self.sharpness_slider.value() if hasattr(self, 'sharpness_slider') else 0
        gaussian_value = self.gaussian_slider.value() if hasattr(self, 'gaussian_slider') else 0
        median_value = self.median_slider.value() if hasattr(self, 'median_slider') else 0

        # Apply adjustments to the image
        adjusted_image = self.apply_adjustments(
            self.image,  # Use original image instead of processed_image
            brightness,
            contrast,
            gamma,
            saturation,
            hue,
            sharpness
        )

        # Apply noise reduction filters if values > 0
        if gaussian_value > 0:
            # Gaussian blur kernel size must be odd
            ksize = gaussian_value * 2 + 1
            adjusted_image = cv2.GaussianBlur(adjusted_image, (ksize, ksize), 0)

        if median_value > 0:
            # Median filter kernel size must be odd
            ksize = median_value * 2 + 1
            adjusted_image = cv2.medianBlur(adjusted_image, ksize)

        # Update the processed image with adjustments
        self.processed_image = adjusted_image.copy()

        # Resize and display
        adjusted_image_resized = resize_image(adjusted_image, self.common_size)
        pixmap = convert_cvimage_to_qpixmap(adjusted_image_resized, already_rgb=True)

        self.process_image_view.setPixmap(pixmap)
        self.full_processed_view.setPixmap(pixmap)

    def apply_adjustments(self, image, brightness, contrast, gamma, saturation=0, hue=0, sharpness=0):
        """Applies brightness, contrast, gamma, saturation, hue, and sharpness adjustments to an image."""
        # Convert to float32 for processing
        adjusted = image.astype(np.float32)

        # Apply brightness (scale -100 to 100)
        if brightness != 0:
            adjusted = adjusted + brightness
            adjusted = np.clip(adjusted, 0, 255)

        # Apply contrast (scale -100 to 100, converted to 0.0 to 2.0)
        if contrast != 1.0:
            mean = np.mean(adjusted)
            adjusted = (adjusted - mean) * contrast + mean
            adjusted = np.clip(adjusted, 0, 255)

        # Convert to uint8 for color space conversions
        adjusted_uint8 = adjusted.astype(np.uint8)

        # Apply saturation (scale -100 to 100)
        if saturation != 0:
            hsv = cv2.cvtColor(adjusted_uint8, cv2.COLOR_RGB2HSV)
            hsv = hsv.astype(np.float32)
            hsv[:,:,1] = hsv[:,:,1] * (1 + saturation/100.0)
            hsv[:,:,1] = np.clip(hsv[:,:,1], 0, 255)
            adjusted_uint8 = cv2.cvtColor(hsv.astype(np.uint8), cv2.COLOR_HSV2RGB)
            adjusted = adjusted_uint8.astype(np.float32)

        # Apply hue (scale -180 to 180)
        if hue != 0:
            hsv = cv2.cvtColor(adjusted_uint8, cv2.COLOR_RGB2HSV)
            # Convert to int32 to handle negative values, then apply modulo, then back to uint8
            h_channel = hsv[:,:,0].astype(np.int32)
            h_channel = (h_channel + hue) % 180
            hsv[:,:,0] = h_channel.astype(np.uint8)
            adjusted_uint8 = cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB)
            adjusted = adjusted_uint8.astype(np.float32)

        # Apply sharpness (scale 0 to 100)
        if sharpness > 0:
            blur = cv2.GaussianBlur(adjusted_uint8, (0, 0), 3)
            sharpness_factor = sharpness / 100.0
            adjusted = cv2.addWeighted(adjusted, 1.0 + sharpness_factor, blur.astype(np.float32), -sharpness_factor, 0)
            adjusted = np.clip(adjusted, 0, 255)

        # Apply gamma correction (scale 0.1 to 10.0)
        if gamma != 1.0:
            # Normalize to 0-1 range, add small epsilon to avoid division by zero
            normalized = (adjusted + 1e-10) / 255.0
            # Apply gamma correction
            adjusted = np.power(normalized, 1.0 / gamma) * 255.0
            adjusted = np.clip(adjusted, 0, 255)

        return adjusted.astype(np.uint8)

    def apply_histogram_equalization(self):
        """Applies histogram equalization to the image."""
        # Check if we have images in the analysis gallery
        has_analysis_images = hasattr(self, 'analysis_gallery') and len(self.analysis_gallery.images) > 0

        if not has_analysis_images:
            # Don't allow operations if no images are loaded in the Image Lab gallery
            QMessageBox.warning(self, "Warning", "No image loaded in Image Lab gallery.")
            return

        if self.processed_image is None:
            QMessageBox.warning(self, "Warning", "No image loaded.")
            return

        # Save current state to history before making changes
        self.push_to_history()

        # Convert to YUV color space
        img_yuv = cv2.cvtColor(self.processed_image, cv2.COLOR_RGB2YUV)

        # Apply histogram equalization to the Y channel
        img_yuv[:,:,0] = cv2.equalizeHist(img_yuv[:,:,0])

        # Convert back to RGB
        equalized = cv2.cvtColor(img_yuv, cv2.COLOR_YUV2RGB)

        # Update processed image
        self.processed_image = equalized

        # Display result
        processed_image_resized = resize_image(self.processed_image, self.common_size)
        pixmap = convert_cvimage_to_qpixmap(processed_image_resized, already_rgb=True)

        self.process_image_view.setPixmap(pixmap)
        self.full_processed_view.setPixmap(pixmap)

        # Update image info
        self.update_image_info(self.processed_image)

    def apply_auto_contrast(self):
        """Applies automatic contrast adjustment to the image."""
        # Check if we have images in the analysis gallery
        has_analysis_images = hasattr(self, 'analysis_gallery') and len(self.analysis_gallery.images) > 0

        if not has_analysis_images:
            # Don't allow operations if no images are loaded in the Image Lab gallery
            QMessageBox.warning(self, "Warning", "No image loaded in Image Lab gallery.")
            return

        if self.processed_image is None:
            QMessageBox.warning(self, "Warning", "No image loaded.")
            return

        # Save current state to history before making changes
        self.push_to_history()

        # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
        img_lab = cv2.cvtColor(self.processed_image, cv2.COLOR_RGB2LAB)
        l_channel, a_channel, b_channel = cv2.split(img_lab)

        # Apply CLAHE to L-channel
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        cl = clahe.apply(l_channel)

        # Merge channels back
        merged = cv2.merge((cl, a_channel, b_channel))

        # Convert back to RGB
        enhanced = cv2.cvtColor(merged, cv2.COLOR_LAB2RGB)

        # Update processed image
        self.processed_image = enhanced

        # Display result
        processed_image_resized = resize_image(self.processed_image, self.common_size)
        pixmap = convert_cvimage_to_qpixmap(processed_image_resized, already_rgb=True)

        self.process_image_view.setPixmap(pixmap)
        self.full_processed_view.setPixmap(pixmap)

        # Update image info
        self.update_image_info(self.processed_image)

    def open_crop_dialog(self):
        """Opens the crop dialog for interactive cropping."""
        # Check if we have images in the analysis gallery
        has_analysis_images = hasattr(self, 'analysis_gallery') and len(self.analysis_gallery.images) > 0

        if not has_analysis_images:
            # Don't allow operations if no images are loaded in the Image Lab gallery
            QMessageBox.warning(self, "Warning", "No image loaded in Image Lab gallery.")
            return

        if self.processed_image is None:
            QMessageBox.warning(self, "Warning", "No image loaded.")
            return

        # Save current state to history before making changes
        self.push_to_history()

        crop_dialog = CropDialog(self.processed_image, self)
        if crop_dialog.exec() == QDialog.Accepted:
            self.processed_image = crop_dialog.get_cropped_image()
            # Update processed image preview
            processed_image_resized = resize_image(self.processed_image, self.common_size)
            pixmap = convert_cvimage_to_qpixmap(processed_image_resized, already_rgb=True)
            self.process_image_view.setPixmap(pixmap)
            self.full_processed_view.setPixmap(pixmap) #Also update full view.
            self.process_image_view.resize_to_image()
            self.full_processed_view.resize_to_image()
            #No need to call reset here
            self.update_image_info(self.processed_image)  # Update info
            QMessageBox.information(self, "Success", "Image cropped successfully!")

    def apply_edits(self):
        """Applies the edited image to the main image and updates the project if applicable."""
        # Check if we have images in the analysis gallery
        has_analysis_images = hasattr(self, 'analysis_gallery') and len(self.analysis_gallery.images) > 0

        if not has_analysis_images:
            # Don't allow operations if no images are loaded in the Image Lab gallery
            QMessageBox.warning(self, "Warning", "No image loaded in Image Lab gallery.")
            return

        if self.processed_image is None:
            QMessageBox.warning(self, "Warning", "No image to apply.")
            return

        # Ask user for confirmation and action choice with clear options
        message_box = QMessageBox(self)
        message_box.setWindowTitle("Apply Edits to Project Hub")
        message_box.setText("How would you like to apply these edits to the Project Hub?")

        # Create custom buttons with clear labels
        replace_button = message_box.addButton("Replace Original Image", QMessageBox.ActionRole)
        add_button = message_box.addButton("Add as New Image", QMessageBox.ActionRole)
        cancel_button = message_box.addButton(QMessageBox.Cancel)

        # Set default button
        message_box.setDefaultButton(replace_button)

        # Show the dialog and get user choice
        message_box.exec()

        # Determine which button was clicked
        clicked_button = message_box.clickedButton()

        if clicked_button == cancel_button:
            return

        # Determine if we're replacing the original or adding to Project Hub
        replace_original = (clicked_button == replace_button)
        add_to_project_hub = (clicked_button == add_button)

        # Use the independent analysis_image variable if available
        if hasattr(self, 'analysis_image') and self.analysis_image is not None:
            source_image_var = 'analysis_image'
        else:
            source_image_var = 'image'

        if replace_original:
            # Debug information to help diagnose issues
            logger.info(f"Current project reference: {hasattr(self, 'current_project')}")
            if hasattr(self, 'current_project'):
                logger.info(f"Current project is None: {self.current_project is None}")
            logger.info(f"Current image info reference: {hasattr(self, 'current_image_info')}")
            if hasattr(self, 'current_image_info'):
                logger.info(f"Current image info is None: {self.current_image_info is None}")

            # Try to get the current project from various sources
            project = None

            # 1. Try to get from self.current_project
            if hasattr(self, 'current_project') and self.current_project:
                project = self.current_project
                logger.info("Using current_project from self")

            # 2. Try to get from parent (main app)
            if not project and hasattr(self, 'parent') and callable(self.parent):
                parent = self.parent()
                if hasattr(parent, 'project') and parent.project:
                    project = parent.project
                    # Set our own reference
                    self.current_project = project
                    logger.info("Using project from parent")

            # 3. Try to get from project_hub_page
            if not project and hasattr(self, 'project_hub_page') and hasattr(self.project_hub_page, 'current_project'):
                project = self.project_hub_page.current_project
                # Set our own reference
                self.current_project = project
                logger.info("Using project from project_hub_page")

            # 4. Try to get from project_hub_page_ref (set in app.py)
            if not project and hasattr(self, 'project_hub_page_ref') and hasattr(self.project_hub_page_ref, 'current_project'):
                project = self.project_hub_page_ref.current_project
                # Set our own reference
                self.current_project = project
                logger.info("Using project from project_hub_page_ref")

            # 5. Try to get from parent's project_hub_page
            if not project and hasattr(self, 'parent') and callable(self.parent):
                parent = self.parent()
                if hasattr(parent, 'project_hub_page') and hasattr(parent.project_hub_page, 'current_project'):
                    project = parent.project_hub_page.current_project
                    # Set our own reference
                    self.current_project = project
                    logger.info("Using project from parent's project_hub_page")

            # Check if we found a project
            if not project:
                QMessageBox.warning(self, "Warning", "No active project found. Please open a project in Project Hub first.")
                return

            # Now try to get the current image info
            image_info = None

            # 1. Try to get from self.current_image_info
            if hasattr(self, 'current_image_info') and self.current_image_info:
                image_info = self.current_image_info
                logger.info("Using current_image_info from self")

            # 2. Try to get from the current image path
            if not image_info and hasattr(self, 'image_full_path') and self.image_full_path:
                # Try to find the image info for this file path
                # Different project classes might have different ways to access image info
                try:
                    # Method 1: Try to access images dictionary directly (GrainSightProject)
                    if hasattr(project, 'images') and isinstance(project.images, dict):
                        for image_id, info in project.images.items():
                            project_image_path = project.get_image_path(image_id)
                            if project_image_path == self.image_full_path:
                                image_info = info
                                # Set our own reference
                                self.current_image_info = image_info
                                logger.info(f"Found image info for current path: {info.filename} (from images dict)")
                                break
                    # Method 2: Try to use get_all_image_infos method if available
                    elif hasattr(project, 'get_all_image_infos'):
                        for info in project.get_all_image_infos():
                            project_image_path = project.get_image_path(info.id)
                            if project_image_path == self.image_full_path:
                                image_info = info
                                # Set our own reference
                                self.current_image_info = image_info
                                logger.info(f"Found image info for current path: {info.filename} (from get_all_image_infos)")
                                break
                    # Method 3: Try to iterate through image IDs and get info for each
                    elif hasattr(project, 'get_image_ids') and hasattr(project, 'get_image_info'):
                        for image_id in project.get_image_ids():
                            info = project.get_image_info(image_id)
                            project_image_path = project.get_image_path(image_id)
                            if project_image_path == self.image_full_path:
                                image_info = info
                                # Set our own reference
                                self.current_image_info = image_info
                                logger.info(f"Found image info for current path: {info.filename} (from get_image_ids)")
                                break
                except Exception as e:
                    logger.error(f"Error finding image info for path {self.image_full_path}: {e}")

            # Check if we found image info
            if not image_info:
                # If we have an image in the gallery, create a temporary image info
                if hasattr(self, 'image_filename') and self.image_filename:
                    # Use the current image as a new image
                    logger.info(f"No image info found, treating as new image: {self.image_filename}")
                    return self.add_to_project_hub(project)
                else:
                    QMessageBox.warning(self, "Warning", "No image information found. Please select an image from the Project Hub.")
                    return

            # Set the current project and image info for use in the rest of the method
            self.current_project = project
            self.current_image_info = image_info

            # Convert RGB to BGR for OpenCV file saving
            image_bgr = cv2.cvtColor(self.processed_image, cv2.COLOR_RGB2BGR)

            # Try to update the image in the project using different methods
            update_success = False

            try:
                # Method 1: Try to use update_image method if available (Project class)
                if hasattr(project, 'update_image'):
                    update_success = project.update_image(self.current_image_info.id, image_bgr)
                    if update_success:
                        logger.info(f"Updated image in project using update_image method: {self.current_image_info.filename}")

                        # Save the project immediately after updating the image
                        try:
                            if hasattr(project, 'save'):
                                project.save()
                                logger.info(f"Project saved after update_image method: {self.current_image_info.filename}")
                        except Exception as e:
                            logger.error(f"Failed to save project after update_image method: {e}")

                # Method 2: Try to save the image directly to the file path (GrainSightProject class)
                elif not update_success:
                    # Get the image path
                    image_path = project.get_image_path(self.current_image_info.id)
                    if image_path and os.path.exists(image_path):
                        # Save the image directly to the file path
                        cv2.imwrite(image_path, image_bgr)
                        logger.info(f"Updated image in project by direct file write: {self.current_image_info.filename}")
                        update_success = True

                        # Save the project immediately after updating the image
                        try:
                            if hasattr(project, 'save'):
                                project.save()
                                logger.info(f"Project saved after direct file write update: {self.current_image_info.filename}")
                        except Exception as e:
                            logger.error(f"Failed to save project after direct file write update: {e}")
                    else:
                        logger.error(f"Failed to get valid image path for {self.current_image_info.id}")

                # Method 3: Try to remove and re-add the image (last resort)
                elif not update_success and hasattr(project, 'remove_image') and hasattr(project, 'add_image'):
                    # Create a temporary file
                    import tempfile
                    temp_dir = tempfile.gettempdir()
                    temp_path = os.path.join(temp_dir, self.current_image_info.filename)

                    # Save the image to a temporary file
                    cv2.imwrite(temp_path, image_bgr)

                    # Remove the old image and add the new one
                    if project.remove_image(self.current_image_info.id):
                        new_info = project.add_image(temp_path)
                        if new_info:
                            # Update the current image info
                            self.current_image_info = new_info
                            update_success = True
                            logger.info(f"Updated image in project by remove and re-add: {self.current_image_info.filename}")

                            # Save the project immediately after updating the image
                            try:
                                if hasattr(project, 'save'):
                                    project.save()
                                    logger.info(f"Project saved after remove-and-add image update: {self.current_image_info.filename}")
                            except Exception as e:
                                logger.error(f"Failed to save project after remove-and-add image update: {e}")

                        # Clean up the temporary file
                        try:
                            os.remove(temp_path)
                        except Exception as e:
                            logger.warning(f"Failed to remove temporary file: {e}")
            except Exception as e:
                logger.error(f"Error updating image in project: {e}")
                update_success = False

            # If any method was successful, update the UI
            if update_success:
                # Update the image in the Image Lab page
                # Always use the independent analysis_image variable
                if not hasattr(self, 'analysis_image'):
                    self.analysis_image = self.processed_image.copy()
                else:
                    self.analysis_image = self.processed_image.copy()

                # Update the resized version
                if not hasattr(self, 'analysis_image_resized'):
                    self.analysis_image_resized = resize_image(self.analysis_image, self.common_size)
                else:
                    self.analysis_image_resized = resize_image(self.analysis_image, self.common_size)

                # Update the Image Lab views
                try:
                    # Update the analysis original view
                    if hasattr(self, 'analysis_original_view'):
                        pixmap = convert_cvimage_to_qpixmap(self.analysis_image_resized)
                        self.analysis_original_view.setPixmap(pixmap)

                    # Update the process image view
                    if hasattr(self, 'process_image_view'):
                        self.process_image_view.setPixmap(pixmap)

                    # Update the full processed view
                    if hasattr(self, 'full_processed_view'):
                        self.full_processed_view.setPixmap(pixmap)
                except Exception as e:
                    logger.error(f"Error displaying updated image: {e}")

                # Update the original image in the analysis gallery
                if hasattr(self, 'analysis_gallery') and self.analysis_gallery.selected_index >= 0:
                    current_index = self.analysis_gallery.selected_index
                    # Update the thumbnail and image in the gallery
                    self.analysis_gallery.images[current_index] = self.processed_image.copy()
                    thumbnail = convert_cvimage_to_qpixmap(self.processed_image)
                    self.analysis_gallery.update_image(thumbnail, current_index)

                # Refresh the project hub display
                if hasattr(self, 'project_hub_page') and hasattr(self.project_hub_page, '_update_gallery_display'):
                    self.project_hub_page._update_gallery_display()

                # Save the project to ensure the updated image is persisted
                try:
                    if self.current_project and hasattr(self.current_project, 'save'):
                        self.current_project.save()
                        logger.info(f"Project saved after updating image: {self.current_image_info.filename}")
                except Exception as e:
                    logger.error(f"Failed to save project after updating image: {e}")

                # Important: Don't reload the image in other pages
                logger.info("Image updated in project but not reloaded in other pages to maintain independence")

                QMessageBox.information(self, "Success", f"Image '{self.current_image_info.filename}' has been replaced in the Project Hub.")
            else:
                logger.error(f"Failed to update image in project: {self.current_image_info.filename}")
                QMessageBox.warning(self, "Error", f"Failed to update image in project: {self.current_image_info.filename}")

        elif add_to_project_hub:
            # Debug information to help diagnose issues
            logger.info(f"Add to Project Hub - Current project reference: {hasattr(self, 'current_project')}")
            if hasattr(self, 'current_project'):
                logger.info(f"Add to Project Hub - Current project is None: {self.current_project is None}")
                if self.current_project:
                    logger.info(f"Add to Project Hub - Current project name: {self.current_project.name}")

            # Try to get the current project from various sources
            project = None

            # 1. Try to get from self.current_project
            if hasattr(self, 'current_project') and self.current_project:
                project = self.current_project
                logger.info("Using current_project from self")

            # 2. Try to get from parent (main app)
            if not project and hasattr(self, 'parent') and callable(self.parent):
                parent = self.parent()
                if hasattr(parent, 'project') and parent.project:
                    project = parent.project
                    # Set our own reference
                    self.current_project = project
                    logger.info("Using project from parent")

            # 3. Try to get from project_hub_page
            if not project and hasattr(self, 'project_hub_page') and hasattr(self.project_hub_page, 'current_project'):
                project = self.project_hub_page.current_project
                # Set our own reference
                self.current_project = project
                logger.info("Using project from project_hub_page")

            # Check if we found a project
            if not project:
                QMessageBox.warning(self, "Warning", "No active project found. Please open a project in Project Hub first.")
                return

            # Set the current project for use in the rest of the method
            self.current_project = project

            # Call the helper method to add the image to the project
            return self.add_to_project_hub(project)

    def add_to_project_hub(self, project=None):
        """Adds the current processed image to the project hub as a new image.

        Args:
            project: The project to add the image to. If None, uses self.current_project.

        Returns:
            bool: True if the image was added successfully, False otherwise.
        """
        # Use the provided project or try to find one
        if project is None:
            # Try to get the current project from various sources
            # 1. Try to get from self.current_project
            if hasattr(self, 'current_project') and self.current_project:
                project = self.current_project
                logger.info("Using current_project from self in add_to_project_hub")

            # 2. Try to get from parent (main app)
            if not project and hasattr(self, 'parent') and callable(self.parent):
                parent = self.parent()
                if hasattr(parent, 'project') and parent.project:
                    project = parent.project
                    # Set our own reference
                    self.current_project = project
                    logger.info("Using project from parent in add_to_project_hub")

            # 3. Try to get from project_hub_page
            if not project and hasattr(self, 'project_hub_page') and hasattr(self.project_hub_page, 'current_project'):
                project = self.project_hub_page.current_project
                # Set our own reference
                self.current_project = project
                logger.info("Using project from project_hub_page in add_to_project_hub")

            # 4. Try to get from project_hub_page_ref (set in app.py)
            if not project and hasattr(self, 'project_hub_page_ref') and hasattr(self.project_hub_page_ref, 'current_project'):
                project = self.project_hub_page_ref.current_project
                # Set our own reference
                self.current_project = project
                logger.info("Using project from project_hub_page_ref in add_to_project_hub")

            # 5. Try to get from parent's project_hub_page
            if not project and hasattr(self, 'parent') and callable(self.parent):
                parent = self.parent()
                if hasattr(parent, 'project_hub_page') and hasattr(parent.project_hub_page, 'current_project'):
                    project = parent.project_hub_page.current_project
                    # Set our own reference
                    self.current_project = project
                    logger.info("Using project from parent's project_hub_page in add_to_project_hub")

            # Check if we found a project
            if not project:
                QMessageBox.warning(self, "Warning", "No active project found. Please open a project in Project Hub first.")
                return False

        # Make sure we have a processed image
        if not hasattr(self, 'processed_image') or self.processed_image is None:
            QMessageBox.warning(self, "Warning", "No processed image to add.")
            return False

        # Make sure we have an image filename
        if not hasattr(self, 'image_filename') or not self.image_filename:
            # Try to generate a default filename
            if hasattr(self, 'current_image_info') and self.current_image_info:
                self.image_filename = self.current_image_info.filename
            else:
                self.image_filename = "image.png"

        # Generate a new filename with '_edited' suffix
        base_name, ext = os.path.splitext(self.image_filename)
        new_filename = f"{base_name}_edited{ext}"

        # Ask user to confirm or modify the filename
        new_filename, ok = QInputDialog.getText(
            self,
            "Add to Project Hub",
            "Enter filename for the new image:",
            QLineEdit.Normal,
            new_filename
        )

        if not ok or not new_filename:
            return False

        # Convert RGB to BGR for OpenCV file saving
        image_bgr = cv2.cvtColor(self.processed_image, cv2.COLOR_RGB2BGR)

        # Create a temporary file path
        import tempfile
        temp_dir = tempfile.gettempdir()
        temp_path = os.path.join(temp_dir, new_filename)

        # Save the image to a temporary file
        cv2.imwrite(temp_path, image_bgr)

        # Add the image to the project
        if os.path.exists(temp_path):
            new_info = project.add_image(temp_path)
            if new_info:
                logger.info(f"Added edited image to project: {new_info.filename}")

                # Refresh the project hub display
                if hasattr(self, 'project_hub_page') and hasattr(self.project_hub_page, '_update_gallery_display'):
                    self.project_hub_page._update_gallery_display()

                # Save the project to ensure the new image is persisted
                try:
                    if project and hasattr(project, 'save'):
                        project.save()
                        logger.info(f"Project saved after adding new image: {new_info.filename}")
                except Exception as e:
                    logger.error(f"Failed to save project after adding new image: {e}")

                # Important: Don't automatically load the new image in other pages
                # This ensures the changes only affect the Project Hub
                logger.info("New image added to project but not automatically loaded in other pages to maintain independence")

                # Clean up the temporary file
                try:
                    os.remove(temp_path)
                except Exception as e:
                    logger.warning(f"Failed to remove temporary file: {e}")

                QMessageBox.information(self, "Success", f"New image '{new_info.filename}' has been added to the Project Hub.")
                return True
            else:
                QMessageBox.warning(self, "Error", "Failed to add edited image to Project Hub.")
                return False
        else:
            QMessageBox.warning(self, "Error", "Failed to save temporary image file.")
            return False

    def open_crop_dialog(self):
        """Opens the image cropping dialog."""
        if self.processed_image is None:
            QMessageBox.warning(self, "Warning", "Please upload an image first.")
            return

        crop_dialog = CropDialog(self.processed_image, self)
        if crop_dialog.exec() == QDialog.Accepted:
            self.processed_image = crop_dialog.get_cropped_image()
            # Update processed image preview
            processed_image_resized = resize_image(self.processed_image, self.common_size)
            pixmap = convert_cvimage_to_qpixmap(processed_image_resized)
            self.process_image_view.setPixmap(pixmap)
            self.full_processed_view.setPixmap(pixmap) #Also update full view.
            self.process_image_view.resize_to_image()
            self.full_processed_view.resize_to_image()
            #No need to call reset here
            self.update_image_info(self.processed_image)  # Update info
            QMessageBox.information(self, "Success", "Image cropped successfully!")

    # Image Navigation Methods for Analysis Page
    def setup_analysis_navigation(self):
        """Sets up the image navigation controls for the analysis page."""
        # Connect gallery signals if available
        if hasattr(self, 'analysis_gallery'):
            # The connection is already made in the UI setup
            pass
        
        # Connect clear analysis gallery button
        if hasattr(self, 'clear_analysis_gallery_button'):
            self.clear_analysis_gallery_button.clicked.connect(self.clear_analysis_gallery)
        elif hasattr(self, 'prev_image_btn') and hasattr(self, 'next_image_btn'):
            # Legacy code for backward compatibility
            self.prev_image_btn.clicked.connect(self.previous_analysis_image)
            self.next_image_btn.clicked.connect(self.next_analysis_image)
            # Update image counter
            self.update_analysis_image_counter()

    def update_analysis_image_counter(self):
        """Updates the image counter label and navigation buttons for the analysis page.
        This method is kept for backward compatibility but is no longer needed with the gallery.
        The gallery handles its own image counting and display.
        """
        # Skip if using the new gallery
        if hasattr(self, 'analysis_gallery'):
            return

        # Legacy code for backward compatibility
        if hasattr(self, 'image_counter_label') and hasattr(self, 'prev_image_btn') and hasattr(self, 'next_image_btn'):
            if not hasattr(self, 'images') or not self.images:
                self.image_counter_label.setText("No images loaded")
                self.prev_image_btn.setEnabled(False)
                self.next_image_btn.setEnabled(False)
                return

            # Find current image index by comparing image paths instead of image data
            current_index = -1
            if hasattr(self, 'image_full_path') and hasattr(self, 'image_full_paths'):
                try:
                    current_index = self.image_full_paths.index(self.image_full_path)
                except ValueError:
                    current_index = -1

            if current_index >= 0:
                self.image_counter_label.setText(f"Image {current_index + 1} of {len(self.images)}")
                # Enable/disable navigation buttons based on position
                self.prev_image_btn.setEnabled(current_index > 0)
                self.next_image_btn.setEnabled(current_index < len(self.images) - 1)
            else:
                self.image_counter_label.setText(f"0 of {len(self.images)}")
                self.prev_image_btn.setEnabled(False)
                self.next_image_btn.setEnabled(False)

    def apply_noise_reduction(self):
        """Applies noise reduction filters to the image."""
        # Check if we have images in the analysis gallery
        has_analysis_images = hasattr(self, 'analysis_gallery') and len(self.analysis_gallery.images) > 0

        if not has_analysis_images:
            # Don't allow operations if no images are loaded in the Image Lab gallery
            QMessageBox.warning(self, "Warning", "No image loaded in Image Lab gallery.")
            return

        if self.processed_image is None:
            QMessageBox.warning(self, "Warning", "No image loaded.")
            return

        # Save current state to history before making changes
        self.push_to_history()

        # Get filter values
        gaussian_value = self.gaussian_slider.value()
        median_value = self.median_slider.value()

        # Update value labels
        self.gaussian_value.setText(str(gaussian_value))
        self.median_value.setText(str(median_value))

        # Apply filters
        filtered_image = self.processed_image.copy()

        # Apply Gaussian blur if value > 0
        if gaussian_value > 0:
            # Gaussian blur kernel size must be odd
            ksize = gaussian_value * 2 + 1
            filtered_image = cv2.GaussianBlur(filtered_image, (ksize, ksize), 0)

        # Apply median filter if value > 0
        if median_value > 0:
            # Median filter kernel size must be odd
            ksize = median_value * 2 + 1
            filtered_image = cv2.medianBlur(filtered_image, ksize)

        # Update processed image
        self.processed_image = filtered_image

        # Display result
        processed_image_resized = resize_image(self.processed_image, self.common_size)
        pixmap = convert_cvimage_to_qpixmap(processed_image_resized)

        self.process_image_view.setPixmap(pixmap)
        self.full_processed_view.setPixmap(pixmap)

    def apply_threshold(self):
        """Applies thresholding to the image based on selected method."""
        # Check if we have images in the analysis gallery
        has_analysis_images = hasattr(self, 'analysis_gallery') and len(self.analysis_gallery.images) > 0

        if not has_analysis_images:
            # Don't allow operations if no images are loaded in the Image Lab gallery
            QMessageBox.warning(self, "Warning", "No image loaded in Image Lab gallery.")
            return

        if self.processed_image is None:
            QMessageBox.warning(self, "Warning", "No image loaded.")
            return

        # Save current state to history before making changes
        self.push_to_history()

        # Get threshold values and method
        threshold_value = self.threshold_slider.value()
        threshold_method = self.threshold_method_combo.currentText()

        # Update value label
        self.threshold_value_label.setText(str(threshold_value))

        # Convert to grayscale if image is color
        if len(self.processed_image.shape) == 3:
            gray_image = cv2.cvtColor(self.processed_image, cv2.COLOR_RGB2GRAY)
        else:
            gray_image = self.processed_image.copy()

        # Apply different thresholding methods
        result = None

        if threshold_method == "Binary":
            _, result = cv2.threshold(gray_image, threshold_value, 255, cv2.THRESH_BINARY)
        elif threshold_method == "Binary Inverted":
            _, result = cv2.threshold(gray_image, threshold_value, 255, cv2.THRESH_BINARY_INV)
        elif threshold_method == "Truncate":
            _, result = cv2.threshold(gray_image, threshold_value, 255, cv2.THRESH_TRUNC)
        elif threshold_method == "To Zero":
            _, result = cv2.threshold(gray_image, threshold_value, 255, cv2.THRESH_TOZERO)
        elif threshold_method == "To Zero Inverted":
            _, result = cv2.threshold(gray_image, threshold_value, 255, cv2.THRESH_TOZERO_INV)
        elif threshold_method == "Otsu":
            _, result = cv2.threshold(gray_image, 0, 255, cv2.THRESH_BINARY+cv2.THRESH_OTSU)
        elif threshold_method == "Adaptive Mean":
            block_size = self.adaptive_block_size_slider.value() * 2 + 3  # Must be odd
            c_value = self.adaptive_c_slider.value()
            result = cv2.adaptiveThreshold(gray_image, 255, cv2.ADAPTIVE_THRESH_MEAN_C,
                                          cv2.THRESH_BINARY, block_size, c_value)
        elif threshold_method == "Adaptive Gaussian":
            block_size = self.adaptive_block_size_slider.value() * 2 + 3  # Must be odd
            c_value = self.adaptive_c_slider.value()
            result = cv2.adaptiveThreshold(gray_image, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                          cv2.THRESH_BINARY, block_size, c_value)

        # Convert back to RGB for display if needed
        if result is not None:
            if len(self.processed_image.shape) == 3:
                result = cv2.cvtColor(result, cv2.COLOR_GRAY2RGB)

            # Update processed image
            self.processed_image = result

            # Display result
            processed_image_resized = resize_image(self.processed_image, self.common_size)
            pixmap = convert_cvimage_to_qpixmap(processed_image_resized)

            self.process_image_view.setPixmap(pixmap)
            self.full_processed_view.setPixmap(pixmap)

    def apply_edge_detection(self):
        """Applies edge detection to the image."""
        # Check if we have images in the analysis gallery
        has_analysis_images = hasattr(self, 'analysis_gallery') and len(self.analysis_gallery.images) > 0

        if not has_analysis_images:
            # Don't allow operations if no images are loaded in the Image Lab gallery
            QMessageBox.warning(self, "Warning", "No image loaded in Image Lab gallery.")
            return

        if self.processed_image is None:
            QMessageBox.warning(self, "Warning", "No image loaded.")
            return

        # Save current state to history before making changes
        self.push_to_history()

        # Get edge detection method
        edge_method = self.edge_method_combo.currentText()

        # Convert to grayscale if image is color
        if len(self.processed_image.shape) == 3:
            gray_image = cv2.cvtColor(self.processed_image, cv2.COLOR_RGB2GRAY)
        else:
            gray_image = self.processed_image.copy()

        # Apply different edge detection methods
        result = None

        if edge_method == "Sobel":
            sobel_x = cv2.Sobel(gray_image, cv2.CV_64F, 1, 0, ksize=3)
            sobel_y = cv2.Sobel(gray_image, cv2.CV_64F, 0, 1, ksize=3)
            abs_sobel_x = cv2.convertScaleAbs(sobel_x)
            abs_sobel_y = cv2.convertScaleAbs(sobel_y)
            result = cv2.addWeighted(abs_sobel_x, 0.5, abs_sobel_y, 0.5, 0)
        elif edge_method == "Canny":
            low_threshold = self.canny_low_threshold_slider.value()
            high_threshold = self.canny_high_threshold_slider.value()
            result = cv2.Canny(gray_image, low_threshold, high_threshold)
        elif edge_method == "Laplacian":
            result = cv2.Laplacian(gray_image, cv2.CV_64F)
            result = cv2.convertScaleAbs(result)

        # Convert back to RGB for display if needed
        if result is not None:
            if len(self.processed_image.shape) == 3:
                result = cv2.cvtColor(result, cv2.COLOR_GRAY2RGB)

            # Update processed image
            self.processed_image = result

            # Display result
            processed_image_resized = resize_image(self.processed_image, self.common_size)
            pixmap = convert_cvimage_to_qpixmap(processed_image_resized)

            self.process_image_view.setPixmap(pixmap)
            self.full_processed_view.setPixmap(pixmap)

    def apply_morphological_operation(self):
        """Applies morphological operations to the image."""
        # Check if we have images in the analysis gallery
        has_analysis_images = hasattr(self, 'analysis_gallery') and len(self.analysis_gallery.images) > 0

        if not has_analysis_images:
            # Don't allow operations if no images are loaded in the Image Lab gallery
            QMessageBox.warning(self, "Warning", "No image loaded in Image Lab gallery.")
            return

        if self.processed_image is None:
            QMessageBox.warning(self, "Warning", "No image loaded.")
            return

        # Save current state to history before making changes
        self.push_to_history()

        # Get morphological operation parameters
        morph_op = self.morph_op_combo.currentText()
        kernel_size = self.morph_kernel_size_slider.value() * 2 + 1  # Must be odd
        iterations = self.morph_iterations_slider.value()

        # Create kernel
        kernel = np.ones((kernel_size, kernel_size), np.uint8)

        # Apply different morphological operations
        result = None

        if morph_op == "Erosion":
            result = cv2.erode(self.processed_image, kernel, iterations=iterations)
        elif morph_op == "Dilation":
            result = cv2.dilate(self.processed_image, kernel, iterations=iterations)
        elif morph_op == "Opening":
            result = cv2.morphologyEx(self.processed_image, cv2.MORPH_OPEN, kernel, iterations=iterations)
        elif morph_op == "Closing":
            result = cv2.morphologyEx(self.processed_image, cv2.MORPH_CLOSE, kernel, iterations=iterations)
        elif morph_op == "Gradient":
            result = cv2.morphologyEx(self.processed_image, cv2.MORPH_GRADIENT, kernel, iterations=iterations)
        elif morph_op == "Top Hat":
            result = cv2.morphologyEx(self.processed_image, cv2.MORPH_TOPHAT, kernel, iterations=iterations)
        elif morph_op == "Black Hat":
            result = cv2.morphologyEx(self.processed_image, cv2.MORPH_BLACKHAT, kernel, iterations=iterations)

        if result is not None:
            # Update processed image
            self.processed_image = result

            # Display result
            processed_image_resized = resize_image(self.processed_image, self.common_size)
            pixmap = convert_cvimage_to_qpixmap(processed_image_resized)

            self.process_image_view.setPixmap(pixmap)
            self.full_processed_view.setPixmap(pixmap)

    def set_current_project(self, project):
        """Sets the current project reference for the Image Lab page.

        Args:
            project: The project object to set as the current project.
        """
        self.current_project = project
        logger.info(f"Set current project for Image Lab page: {project.name if project else 'None'}")

    def previous_analysis_image(self):
        """Navigates to the previous image in the analysis page.
        This method is kept for backward compatibility but is no longer needed with the gallery.
        The gallery handles its own navigation.
        """
        # Use the gallery if available
        if hasattr(self, 'analysis_gallery') and self.analysis_gallery.selected_index > 0:
            # Save current processing state if needed
            self.save_analysis_state()

            # Select previous image in gallery
            self.analysis_gallery.select_image(self.analysis_gallery.selected_index - 1)
            self.on_analysis_gallery_image_clicked(self.analysis_gallery.selected_index)
            return

        # Legacy code for backward compatibility
        if not hasattr(self, 'images') or not self.images:
            return

        # Find current image index by comparing image paths
        current_index = -1
        if hasattr(self, 'image_full_path') and hasattr(self, 'image_full_paths'):
            try:
                current_index = self.image_full_paths.index(self.image_full_path)
            except ValueError:
                current_index = -1

        if current_index > 0:
            # Save current processing state if needed
            self.save_analysis_state()

            # Select previous image
            self.select_analysis_image(current_index - 1)

    def next_analysis_image(self):
        """Navigates to the next image in the analysis page.
        This method is kept for backward compatibility but is no longer needed with the gallery.
        The gallery handles its own navigation.
        """
        # Use the gallery if available
        if hasattr(self, 'analysis_gallery') and self.analysis_gallery.selected_index >= 0 and \
           self.analysis_gallery.selected_index < len(self.analysis_gallery.images) - 1:
            # Save current processing state if needed
            self.save_analysis_state()

            # Select next image in gallery
            self.analysis_gallery.select_image(self.analysis_gallery.selected_index + 1)
            self.on_analysis_gallery_image_clicked(self.analysis_gallery.selected_index)
            return

        # Legacy code for backward compatibility
        if not hasattr(self, 'images') or not self.images:
            return

        # Find current image index by comparing image paths
        current_index = -1
        if hasattr(self, 'image_full_path') and hasattr(self, 'image_full_paths'):
            try:
                current_index = self.image_full_paths.index(self.image_full_path)
            except ValueError:
                current_index = -1

        if current_index >= 0 and current_index < len(self.images) - 1:
            # Save current processing state if needed
            self.save_analysis_state()

            # Select next image
            self.select_analysis_image(current_index + 1)

    def select_analysis_image(self, index):
        """Selects and displays an image for analysis.
        This method is kept for backward compatibility but is now a wrapper around the gallery selection.
        """
        # Use the gallery if available
        if hasattr(self, 'analysis_gallery') and 0 <= index < len(self.analysis_gallery.images):
            # Select the image in the gallery
            self.analysis_gallery.select_image(index)
            # Trigger the gallery image clicked handler
            self.on_analysis_gallery_image_clicked(index)
            return

        # Legacy code for backward compatibility
        if 0 <= index < len(self.images):
            # Update current image properties
            self.image = self.images[index]
            self.image_filename = self.image_filenames[index]
            self.image_full_path = self.image_full_paths[index]
            self.original_image_size = self.image.shape[:2]

            # Load saved analysis state or initialize new one
            self.load_analysis_state()

            # Update the image counter
            self.update_analysis_image_counter()

    def save_analysis_state(self):
        """Saves the current analysis state for the current image."""
        if not hasattr(self, 'analysis_states'):
            self.analysis_states = {}

        if hasattr(self, 'image_full_path') and self.image_full_path:
            # Get current slider values
            brightness = self.brightness_slider.value() if hasattr(self, 'brightness_slider') else 0
            contrast = self.contrast_slider.value() if hasattr(self, 'contrast_slider') else 0
            gamma = self.gamma_spinbox.value() if hasattr(self, 'gamma_spinbox') else 1.0
            saturation = self.saturation_slider.value() if hasattr(self, 'saturation_slider') else 0
            hue = self.hue_slider.value() if hasattr(self, 'hue_slider') else 0
            sharpness = self.sharpness_slider.value() if hasattr(self, 'sharpness_slider') else 0
            gaussian_value = self.gaussian_slider.value() if hasattr(self, 'gaussian_slider') else 0
            median_value = self.median_slider.value() if hasattr(self, 'median_slider') else 0

            # Store current state
            self.analysis_states[self.image_full_path] = {
                'processed_image': self.processed_image.copy() if self.processed_image is not None else None,
                'brightness': brightness,
                'contrast': contrast,
                'gamma': gamma,
                'saturation': saturation,
                'hue': hue,
                'sharpness': sharpness,
                'gaussian_value': gaussian_value,
                'median_value': median_value
            }

    def load_analysis_state(self):
        """Loads the analysis state for the current image."""
        if not hasattr(self, 'analysis_states'):
            self.analysis_states = {}

        # Use the independent analysis_image if available, otherwise fall back to shared image
        source_image = self.analysis_image if hasattr(self, 'analysis_image') and self.analysis_image is not None else self.image

        # Initialize processed image from the appropriate source
        self.processed_image = source_image.copy()

        # Check if we have a saved state for this image
        if hasattr(self, 'image_full_path') and self.image_full_path in self.analysis_states:
            state = self.analysis_states[self.image_full_path]

            # Restore processed image if available
            if 'processed_image' in state and state['processed_image'] is not None:
                self.processed_image = state['processed_image'].copy()

            # Restore slider values if they exist
            if hasattr(self, 'brightness_slider') and 'brightness' in state:
                self.brightness_slider.setValue(state['brightness'])
                if hasattr(self, 'brightness_value'):
                    self.brightness_value.setText(str(state['brightness']))

            if hasattr(self, 'contrast_slider') and 'contrast' in state:
                self.contrast_slider.setValue(state['contrast'])
                if hasattr(self, 'contrast_value'):
                    self.contrast_value.setText(str(state['contrast']))

            if hasattr(self, 'gamma_spinbox') and 'gamma' in state:
                self.gamma_spinbox.setValue(state['gamma'])

            if hasattr(self, 'saturation_slider') and 'saturation' in state:
                self.saturation_slider.setValue(state['saturation'])
                if hasattr(self, 'saturation_value'):
                    self.saturation_value.setText(str(state['saturation']))

            if hasattr(self, 'hue_slider') and 'hue' in state:
                self.hue_slider.setValue(state['hue'])
                if hasattr(self, 'hue_value'):
                    self.hue_value.setText(str(state['hue']))

            if hasattr(self, 'sharpness_slider') and 'sharpness' in state:
                self.sharpness_slider.setValue(state['sharpness'])
                if hasattr(self, 'sharpness_value'):
                    self.sharpness_value.setText(str(state['sharpness']))

            if hasattr(self, 'gaussian_slider') and 'gaussian_value' in state:
                self.gaussian_slider.setValue(state['gaussian_value'])
                if hasattr(self, 'gaussian_value'):
                    self.gaussian_value.setText(str(state['gaussian_value']))

            if hasattr(self, 'median_slider') and 'median_value' in state:
                self.median_slider.setValue(state['median_value'])
                if hasattr(self, 'median_value'):
                    self.median_value.setText(str(state['median_value']))
        else:
            # Reset controls for new image if they exist
            if hasattr(self, 'brightness_slider'):
                self.brightness_slider.setValue(0)
                if hasattr(self, 'brightness_value'):
                    self.brightness_value.setText("0")

            if hasattr(self, 'contrast_slider'):
                self.contrast_slider.setValue(0)
                if hasattr(self, 'contrast_value'):
                    self.contrast_value.setText("0")

            if hasattr(self, 'gamma_spinbox'):
                self.gamma_spinbox.setValue(1.0)

            if hasattr(self, 'saturation_slider'):
                self.saturation_slider.setValue(0)
                if hasattr(self, 'saturation_value'):
                    self.saturation_value.setText("0")

            if hasattr(self, 'hue_slider'):
                self.hue_slider.setValue(0)
                if hasattr(self, 'hue_value'):
                    self.hue_value.setText("0")

            if hasattr(self, 'sharpness_slider'):
                self.sharpness_slider.setValue(0)
                if hasattr(self, 'sharpness_value'):
                    self.sharpness_value.setText("0")

            if hasattr(self, 'gaussian_slider'):
                self.gaussian_slider.setValue(0)
                if hasattr(self, 'gaussian_value'):
                    self.gaussian_value.setText("0")

            if hasattr(self, 'median_slider'):
                self.median_slider.setValue(0)
                if hasattr(self, 'median_value'):
                    self.median_value.setText("0")

        # Update displays
        if not hasattr(self, 'common_size') or not self.common_size:
            self.common_size = (750, 750)

        self.image_resized = resize_image(self.image, self.common_size)
        processed_image_resized = resize_image(self.processed_image, self.common_size)

        # Update original image view
        self.analysis_original_view.setPixmap(convert_cvimage_to_qpixmap(self.image_resized))

        # Update processed image views
        pixmap = convert_cvimage_to_qpixmap(processed_image_resized)
        self.process_image_view.setPixmap(pixmap)

    def clear_analysis_gallery(self):
        """Clears all images from the analysis gallery."""
        if hasattr(self, 'analysis_gallery'):
            self.analysis_gallery.clear_images()
            logger.info("Analysis gallery cleared successfully")
        
        # Clear the process image view as well
        if hasattr(self, 'process_image_view'):
            self.process_image_view.clear()
            logger.info("Analysis process image view cleared")
        self.full_processed_view.setPixmap(pixmap)

        # Update image info
        self.update_image_info(self.processed_image)