import sys
import os
import logging
import time
import warnings
from PySide6.QtWidgets import QApplication, QStyleFactory, QMessageBox
from PySide6.QtGui import QFont, QIcon
from PySide6.QtCore import QTimer, QSettings, qInstallMessageHandler, QtMsgType
from src.gui.splash_screen import VisionLabAiSplashScreen
from src.utils.startup_timer import StartupTimer

# Suppress all warnings to clean up startup output
warnings.filterwarnings("ignore")

# Suppress Qt warnings and debug messages
os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false;qt.gui.*=false;qt.widgets.*=false;qt.core.*=false'
# Suppress specific Qt warnings
os.environ['QT_ASSUME_STDERR_HAS_CONSOLE'] = '1'
os.environ['QT_LOGGING_TO_CONSOLE'] = '0'
# Additional Qt warning suppression
os.environ['QT_LOGGING_RULES'] += ';qt.qml.*=false;qt.quick.*=false'

# Custom Qt message handler to suppress warnings
def qt_message_handler(mode, context, message):
    """Custom Qt message handler to suppress unwanted warnings."""
    _ = context  # Suppress unused parameter warning
    # Suppress specific Qt warnings that appear during startup
    suppress_messages = [
        "Could not parse stylesheet",
        "QLayout: Attempting to add QLayout",
        "called with wrong argument types",
        "Supported signatures"
    ]

    # Only suppress warning and debug messages, allow critical errors
    if mode in (QtMsgType.QtWarningMsg, QtMsgType.QtDebugMsg):
        for suppress_msg in suppress_messages:
            if suppress_msg in message:
                return

    # For other messages, we can still suppress them during startup
    # but you might want to log critical errors
    pass

# Install the custom message handler
qInstallMessageHandler(qt_message_handler)

# Try to import the new theme system
try:
    from src.gui.styles.theme_config import apply_theme, FONT_SIZES, FONT_FAMILIES, COLOR_SCHEMES
    USE_NEW_THEME_SYSTEM = True
except ImportError:
    # Fall back to the old theme system
    from src.gui.styles.dark_theme import apply_modern_dark_theme
    USE_NEW_THEME_SYSTEM = False

# Configure logging to suppress warnings and debug messages
logging.basicConfig(level=logging.ERROR, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set logging level for specific modules to suppress warnings and debug output
logging.getLogger('src.gui.handlers').setLevel(logging.ERROR)
logging.getLogger('src.widgets').setLevel(logging.ERROR)
logging.getLogger('src.gui.ui').setLevel(logging.ERROR)
logging.getLogger('src.gui.handlers.point_counting_page_handler').setLevel(logging.ERROR)
logging.getLogger('src.gui.handlers.about_page_handler').setLevel(logging.ERROR)
logging.getLogger('src.gui.handlers.ai_assistant_handlers').setLevel(logging.ERROR)
logging.getLogger('src.gui.handlers.analysis_handlers').setLevel(logging.ERROR)
logging.getLogger('src.gui.ui.app_ui').setLevel(logging.ERROR)
logging.getLogger('src.gui.ui.base_ui').setLevel(logging.ERROR)

# Suppress PySide6/Qt warnings
logging.getLogger('PySide6').setLevel(logging.CRITICAL)
logging.getLogger('Qt').setLevel(logging.CRITICAL)

# VisionLabAiApp will be imported after splash screen is shown to reduce startup delay

# Function to perform initialization tasks
def initialize_app(splash, app, timer):
    # Track progress
    progress = 0

    # Process events to keep UI responsive
    app.processEvents()

    # Update splash screen
    splash.update_progress(progress, "Initializing application...")
    app.processEvents()  # Process events to keep UI responsive
    timer.checkpoint("Splash screen initialized")

    # Apply theme
    progress += 15
    splash.update_progress(progress, "Applying theme...")
    timer.start_component("Base style")
    app.setStyle(QStyleFactory.create("Fusion"))  # Use Fusion style as base
    timer.end_component("Base style")
    app.processEvents()  # Process events to keep UI responsive

    # Set application details for QSettings
    app.setOrganizationName("VisionLab Ai")
    app.setApplicationName("VisionLab_Ai_V4")
    timer.checkpoint("Application details set")

    # Load theme settings
    settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")

    # Set a modern font
    progress += 15
    splash.update_progress(progress, "Loading fonts...")
    timer.start_component("Font loading")

    # Get font settings from QSettings or use defaults
    if USE_NEW_THEME_SYSTEM:
        font_family = settings.value("app/font_family", "Segoe UI")
        font_size_name = settings.value("app/font_size", "normal")
        font_size = FONT_SIZES.get(font_size_name, 10)
    else:
        font_family = "Segoe UI"
        font_size = 9

    font = QFont(font_family, font_size)
    app.setFont(font)
    timer.end_component("Font loading")
    app.processEvents()  # Process events to keep UI responsive
    timer.checkpoint("Fonts loaded")

    # Apply theme
    progress += 15
    splash.update_progress(progress, "Applying stylesheet...")
    timer.start_component("Theme application")

    if USE_NEW_THEME_SYSTEM:
        # Get theme settings from QSettings or use defaults
        theme = settings.value("app/theme", "Dark Theme").lower().replace(" theme", "")
        color_scheme = settings.value("app/color_scheme", "Default").lower()
        theme_name = f"{color_scheme}-{theme}"

        try:
            # Apply theme to the application
            apply_theme(app, theme_name, font_family, font_size)
            logger.info(f"Applied theme: {theme_name} with font {font_family} size {font_size}")
        except Exception as e:
            logger.error(f"Error applying theme: {e}")
            # Fall back to the old theme system
            apply_modern_dark_theme(app)
            logger.info("Falling back to legacy dark theme")
    else:
        # Use the old theme system
        apply_modern_dark_theme(app)
        logger.info("Using legacy dark theme")

    timer.end_component("Theme application")
    timer.checkpoint("Theme applied")

    app.processEvents()  # Process events to keep UI responsive

    # Import the main application class when needed
    progress += 5
    splash.update_progress(progress, "Loading core modules...")
    timer.start_component("Application import")
    app.processEvents()  # Process events to keep UI responsive

    try:
        # Show progress during import
        splash.update_progress(progress + 2, "Loading UI components...")
        app.processEvents()

        from src.gui.app import VisionLabAiApp

        splash.update_progress(progress + 5, "Application modules loaded")
        app.processEvents()

        timer.end_component("Application import")
        timer.checkpoint("Application modules loaded")
    except Exception as e:
        timer.end_component("Application import")
        # Error importing application modules
        return None

    progress += 10

    # Prepare splash screen for window creation
    splash.prepare_for_window_creation()
    
    # Create the main window
    progress += 15
    splash.update_progress(progress, "Creating main window...")
    timer.start_component("Main window creation")
    try:
        window = VisionLabAiApp()
        timer.end_component("Main window creation")
        timer.checkpoint("Main window created")
        app.processEvents()  # Process events to keep UI responsive
    except Exception as e:
        timer.end_component("Main window creation")
        # Error creating main window (debug message removed)
        return None

    # Final preparations
    progress += 20
    splash.update_progress(progress, "Finalizing...")
    timer.start_component("Finalization")
    app.processEvents()  # Process events to keep UI responsive

    # Complete
    splash.update_progress(100, "Ready!")
    timer.end_component("Finalization")
    timer.checkpoint("Application ready")
    app.processEvents()  # Process events to keep UI responsive

    # Return the window
    return window

if __name__ == "__main__":
    # Initialize startup timer with a specific log file path
    startup_timer = StartupTimer("visionlab_ai_startup_timing.log")

    # Create the application
    startup_timer.start_component("QApplication creation")
    app = QApplication(sys.argv)
    startup_timer.end_component("QApplication creation")
    startup_timer.checkpoint("QApplication created")

    # Create and show the splash screen immediately for visual feedback
    startup_timer.start_component("Splash screen creation")
    splash = VisionLabAiSplashScreen()
    splash.show()
    app.processEvents()  # Ensure splash screen is rendered immediately
    startup_timer.end_component("Splash screen creation")
    startup_timer.checkpoint("Splash screen shown")

    # Show initial progress
    splash.update_progress(5, "Starting application...")
    app.processEvents()

    # Initialize the application in a timer to allow the splash screen to show
    window_container = [None]
    splash_container = [splash]  # Store splash screen in a container to ensure proper cleanup
    timer_container = [startup_timer]  # Store timer in a container to ensure proper access

    def init_and_show():
        try:
            # Initialize the application
            timer_container[0].start_component("Application initialization")
            window_container[0] = initialize_app(splash_container[0], app, timer_container[0])
            timer_container[0].end_component("Application initialization")

            # Check if initialization was successful
            if window_container[0] is None:
                print("Failed to initialize application")
                cleanup_splash()
                return

            # Show the main window
            timer_container[0].start_component("Window display")
            window_container[0].show()
            timer_container[0].end_component("Window display")
            timer_container[0].checkpoint("Main window displayed")

            # Process events to keep UI responsive
            app.processEvents()

            # Close the splash screen with fade effect only if it's still visible
            if splash_container[0] and splash_container[0].isVisible():
                QTimer.singleShot(500, lambda: splash_container[0].finish(window_container[0]))

            # Connect window close event to ensure splash screen is properly cleaned up
            app.aboutToQuit.connect(cleanup_splash)

            # Finish timing the startup process
            timer_container[0].finish()

        except Exception:
            # Error during initialization (debug message removed)
            cleanup_splash()

    def cleanup_splash():
        # Ensure splash screen is properly closed and cleaned up
        try:
            if splash_container[0]:
                # Stop video playback if using video
                if hasattr(splash_container[0], 'use_video_background') and splash_container[0].use_video_background:
                    if hasattr(splash_container[0], 'media_player'):
                        try:
                            splash_container[0].media_player.stop()
                        except Exception as e:
                            print(f"Error stopping media player: {e}")

                # Reset dragging state to prevent issues
                splash_container[0].dragging = False
                splash_container[0].drag_position = None

                # Hide the splash screen
                splash_container[0].hide()

                # Remove parent to allow proper cleanup
                splash_container[0].setParent(None)

                # Clear the reference
                splash_container[0] = None

                # Process events to ensure UI updates
                app.processEvents()
        except Exception as e:
            print(f"Error cleaning up splash screen: {e}")

    # Use a timer to start initialization after splash is shown
    # Minimal delay to allow splash screen to render
    startup_timer.checkpoint("Starting initialization timer")
    QTimer.singleShot(50, init_and_show)

    # Start the event loop
    sys.exit(app.exec())