
# Lazy Loading Optimization Patch for VisionLabAiApp
# Add this to the beginning of VisionLabAiApp.__init__ method

# Store heavy components as None initially
self._project_hub_page = None
self._image_lab_page = None
self._advanced_segmentation_page = None
self._batch_processing_page = None
self._ai_assistant_handlers_initialized = False
self._analysis_handlers_initialized = False

# Add these property methods to VisionLabAiApp class:

@property
def project_hub_page(self):
    """Lazy load project hub page."""
    if self._project_hub_page is None:
        from src.gui.project_hub_page import ProjectHubPage
        self._project_hub_page = ProjectHubPage(self)
        self._project_hub_page.switch_page_requested.connect(self.handle_analysis_page_switch)
    return self._project_hub_page

@property
def image_lab_page(self):
    """Lazy load image lab page."""
    if self._image_lab_page is None:
        from src.gui.image_lab_page import ImageLabPage
        self._image_lab_page = ImageLabPage(self)
    return self._image_lab_page

@property
def advanced_segmentation_page(self):
    """Lazy load advanced segmentation page."""
    if self._advanced_segmentation_page is None:
        from src.gui.advanced_segmentation_page import AdvancedSegmentationPage
        self._advanced_segmentation_page = AdvancedSegmentationPage(self)
    return self._advanced_segmentation_page

@property
def batch_processing_page(self):
    """Lazy load batch processing page."""
    if self._batch_processing_page is None:
        from src.gui.batch_processing_page import BatchProcessingPage
        self._batch_processing_page = BatchProcessingPage(self)
    return self._batch_processing_page

def initialize_ai_assistant_handlers(self):
    """Initialize AI assistant handlers when needed."""
    if not self._ai_assistant_handlers_initialized:
        AIAssistantHandlers.__init__(self)
        self._ai_assistant_handlers_initialized = True

def initialize_analysis_handlers(self):
    """Initialize analysis handlers when needed."""
    if not self._analysis_handlers_initialized:
        AnalysisHandlers.__init__(self)
        self._analysis_handlers_initialized = True
