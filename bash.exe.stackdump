Stack trace:
Frame         Function      Args
0007FFFF9DE0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8CE0) msys-2.0.dll+0x2118E
0007FFFF9DE0  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFA0B8) msys-2.0.dll+0x69BA
0007FFFF9DE0  0002100469F2 (00021028DF99, 0007FFFF9C98, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9DE0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9DE0  00021006A545 (0007FFFF9DF0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFA0C0  00021006B9A5 (0007FFFF9DF0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFEC6D60000 ntdll.dll
7FFEC66C0000 KERNEL32.DLL
7FFEC4540000 KERNELBASE.dll
7FFEC4C70000 USER32.dll
7FFEC40C0000 win32u.dll
000210040000 msys-2.0.dll
7FFEC5C10000 GDI32.dll
7FFEC4930000 gdi32full.dll
7FFEC3EB0000 msvcp_win.dll
7FFEC4270000 ucrtbase.dll
7FFEC5740000 advapi32.dll
7FFEC5C40000 msvcrt.dll
7FFEC5210000 sechost.dll
7FFEC4EB0000 RPCRT4.dll
7FFEC33C0000 CRYPTBASE.DLL
7FFEC3F60000 bcryptPrimitives.dll
7FFEC4E70000 IMM32.DLL
