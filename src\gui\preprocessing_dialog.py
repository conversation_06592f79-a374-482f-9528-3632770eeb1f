# src/gui/preprocessing_dialog.py

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QTabWidget, QWidget,
                               QGridLayout, QLabel, QPushButton, QDoubleSpinBox,
                               QSpinBox, QGroupBox, QHBoxLayout)
from PySide6.QtCore import Qt
from src.utils.image_utils import (resize_image, convert_cvimage_to_qpixmap,
                                  apply_preprocessing_advanced)
import cv2
import numpy as np


class PreprocessingDialog(QDialog):
    """Dialog for image preprocessing."""

    def __init__(self, image, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Image Preprocessing")
        self.resize(700, 600)
        self.original_image = image
        self.preview_image = image.copy()
        self.result_image = None # Store the result

        # Initialize variables first
        self.setup_variables()

        # Main layout
        layout = QVBoxLayout(self)
        layout.setSpacing(10)  # Add some spacing between widgets
        layout.setContentsMargins(10, 10, 10, 10)  # Add margins around the dialog

        # Image Information Group Box
        info_frame = QGroupBox("Image Information")
        info_layout = QGridLayout()
        info_frame.setLayout(info_layout)
        info_layout.setSpacing(2)
        info_layout.setContentsMargins(5, 5, 5, 5)
        
        # Labels for original image info with better styling
        orig_label = QLabel("Original Image:")
        orig_label.setStyleSheet("font-weight: bold; padding-right: 10px;")
        orig_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.original_dim_label = QLabel()
        self.original_dim_label.setStyleSheet("color: #000; font-family: monospace;")
        self.original_dim_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        info_layout.addWidget(orig_label, 0, 0)
        info_layout.addWidget(self.original_dim_label, 0, 1)
        
        # Labels for processed image info with better styling
        proc_label = QLabel("Processed Image:")
        proc_label.setStyleSheet("font-weight: bold; padding-right: 10px;")
        proc_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.processed_dim_label = QLabel()
        self.processed_dim_label.setStyleSheet("color: #000; font-family: monospace;")
        self.processed_dim_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        info_layout.addWidget(proc_label, 1, 0)
        info_layout.addWidget(self.processed_dim_label, 1, 1)
        
        # Set column stretch to make dimensions align properly
        info_layout.setColumnStretch(1, 1)
        
        # Update the image information immediately
        self.update_original_image_info()
        self.update_processed_image_info()
        
        # Add info frame to main layout with smaller fixed height
        info_frame.setFixedHeight(65)  # Reduced height
        layout.addWidget(info_frame)

        # Notebook for tabs
        self.notebook = QTabWidget(self)
        layout.addWidget(self.notebook)

        # Basic Adjustments Tab
        basic_tab = QWidget()
        self.notebook.addTab(basic_tab, "Basic Adjustments")
        self.basic_layout = QGridLayout(basic_tab)

        # Advanced Adjustments Tab
        advanced_tab = QWidget()
        self.notebook.addTab(advanced_tab, "Advanced Adjustments")
        self.advanced_layout = QGridLayout(advanced_tab)

        # Filters Tab
        filters_tab = QWidget()
        self.notebook.addTab(filters_tab, "Filters")
        self.filters_layout = QGridLayout(filters_tab)

        # Initialize preview container first
        self.setup_preview_container()
        
        # Variables initialization
        self.setup_variables()
        
        # Complete the UI setup
        self.setup_ui()

    def setup_preview_container(self):
        """Sets up the preview container and canvases."""
        # Preview Canvases for side-by-side comparison
        self.preview_container = QWidget()  # Make it an instance variable
        preview_layout = QHBoxLayout(self.preview_container)
        preview_layout.setContentsMargins(0, 0, 0, 0)
        
        # Original image preview
        self.original_canvas = QLabel()
        self.original_canvas.setFixedSize(350, 300)
        self.original_canvas.setAlignment(Qt.AlignCenter)
        original_preview = resize_image(self.original_image.copy(), (350, 300))
        self.original_canvas.setPixmap(convert_cvimage_to_qpixmap(original_preview))
        
        # Processed image preview
        self.preview_canvas = QLabel()
        self.preview_canvas.setFixedSize(350, 300)
        self.preview_canvas.setAlignment(Qt.AlignCenter)
        
        # Add labels and canvases to layout
        original_container = QVBoxLayout()
        original_container.setContentsMargins(0, 0, 0, 0)
        original_container.setSpacing(5)
        original_container.addWidget(QLabel("Original Image"))
        original_container.addWidget(self.original_canvas)
        
        preview_container_layout = QVBoxLayout()
        preview_container_layout.setContentsMargins(0, 0, 0, 0)
        preview_container_layout.setSpacing(5)
        preview_container_layout.addWidget(QLabel("Processed Preview"))
        preview_container_layout.addWidget(self.preview_canvas)
        
        preview_layout.addLayout(original_container)
        preview_layout.addLayout(preview_container_layout)
        
        # Preview Frame
        preview_frame = QGroupBox("Image Preview")
        preview_frame_layout = QVBoxLayout(preview_frame)
        preview_frame_layout.setSpacing(0)
        preview_frame_layout.setContentsMargins(5, 5, 5, 5)
        preview_frame_layout.addWidget(self.preview_container)  # Use the instance variable
        preview_frame.setStyleSheet("QGroupBox { padding-top: 10px; }")
        self.layout().addWidget(preview_frame)
        
        self.update_all_preview()

    def setup_variables(self):
        """Initialize all the adjustment variables."""
        self.brightness_var = QDoubleSpinBox()
        self.brightness_var.setRange(0.1, 3.0)
        self.brightness_var.setSingleStep(0.1)
        self.brightness_var.setValue(1.0)

        self.contrast_var = QDoubleSpinBox()
        self.contrast_var.setRange(0.1, 3.0)
        self.contrast_var.setSingleStep(0.1)
        self.contrast_var.setValue(1.0)

        self.saturation_var = QDoubleSpinBox()
        self.saturation_var.setRange(0.0, 2.0)
        self.saturation_var.setSingleStep(0.1)
        self.saturation_var.setValue(1.0)

        self.gamma_var = QDoubleSpinBox()
        self.gamma_var.setRange(0.1, 2.0)
        self.gamma_var.setSingleStep(0.1)
        self.gamma_var.setValue(1.0)

        self.sharpness_var = QDoubleSpinBox()
        self.sharpness_var.setRange(0.0, 2.0)
        self.sharpness_var.setSingleStep(0.1)
        self.sharpness_var.setValue(0.0)

        self.blur_var = QSpinBox()
        self.blur_var.setRange(0, 10)
        self.blur_var.setValue(0)

    def setup_ui(self):
        """Sets up the UI elements."""
        # Basic Controls
        self.basic_layout.addWidget(QLabel("Brightness"), 0, 0)
        self.basic_layout.addWidget(self.brightness_var, 0, 1)
        self.brightness_var.valueChanged.connect(self.update_all_preview)

        self.basic_layout.addWidget(QLabel("Contrast"), 1, 0)
        self.basic_layout.addWidget(self.contrast_var, 1, 1)
        self.contrast_var.valueChanged.connect(self.update_all_preview)

        self.basic_layout.addWidget(QLabel("Saturation"), 2, 0)
        self.basic_layout.addWidget(self.saturation_var, 2, 1)
        self.saturation_var.valueChanged.connect(self.update_all_preview)

        # Presets
        presets_frame = QGroupBox("Presets")
        presets_layout = QHBoxLayout(presets_frame)
        self.basic_layout.addWidget(presets_frame, 3, 0, 1, 2)

        presets_layout.addWidget(
            QPushButton("Default", clicked=lambda: self.apply_preset("default")))
        presets_layout.addWidget(
            QPushButton("Enhance", clicked=lambda: self.apply_preset("enhance")))
        presets_layout.addWidget(
            QPushButton("Vintage", clicked=lambda: self.apply_preset("vintage")))
        presets_layout.addWidget(
            QPushButton("B&W", clicked=lambda: self.apply_preset("bw")))

        # Advanced Controls
        self.advanced_layout.addWidget(QLabel("Gamma"), 0, 0)
        self.advanced_layout.addWidget(self.gamma_var, 0, 1)
        self.gamma_var.valueChanged.connect(self.update_all_preview)

        self.advanced_layout.addWidget(QLabel("Sharpness"), 1, 0)
        self.advanced_layout.addWidget(self.sharpness_var, 1, 1)
        self.sharpness_var.valueChanged.connect(self.update_all_preview)

        # Filters Controls
        self.filters_layout.addWidget(QLabel("Blur"), 0, 0)
        self.filters_layout.addWidget(self.blur_var, 0, 1)
        self.blur_var.valueChanged.connect(self.update_all_preview)

        # Action Buttons
        action_buttons_layout = QHBoxLayout()
        self.layout().addLayout(action_buttons_layout)
        
        reset_to_original_button = QPushButton("Reset to Original")
        reset_to_original_button.clicked.connect(self.reset_to_original)
        action_buttons_layout.addWidget(reset_to_original_button)
        
        reset_button = QPushButton("Reset All Parameters")
        reset_button.clicked.connect(lambda: self.apply_preset("default"))
        action_buttons_layout.addWidget(reset_button)

        self.apply_button = QPushButton("Apply")
        self.apply_button.clicked.connect(self.apply_changes)
        action_buttons_layout.addWidget(self.apply_button)

        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(self.reject)
        action_buttons_layout.addWidget(cancel_button)

    def update_original_image_info(self):
        """Updates the original image information display."""
        height, width = self.original_image.shape[:2]
        self.original_dim_label.setText(f"{width} × {height} pixels")

    def update_processed_image_info(self):
        """Updates the processed image information display."""
        if self.preview_image is not None:
            height, width = self.preview_image.shape[:2]
            self.processed_dim_label.setText(f"{width} × {height} pixels")

    def update_all_preview(self):
        """Updates the preview image with all current settings."""
        self.preview_image = apply_preprocessing_advanced(
            self.original_image.copy(),
            self.brightness_var.value(),
            self.contrast_var.value(),
            self.saturation_var.value(),
            self.gamma_var.value(),
            self.sharpness_var.value(),
            self.blur_var.value()
        )
        preview = resize_image(self.preview_image, (350, 300))
        pixmap = convert_cvimage_to_qpixmap(preview)
        self.preview_canvas.setPixmap(pixmap)
        self.update_processed_image_info()  # Update the processed image dimensions

    def apply_preset(self, preset):
        """Applies a preset configuration."""
        if preset == "default":
            self.brightness_var.setValue(1.0)
            self.contrast_var.setValue(1.0)
            self.saturation_var.setValue(1.0)
            self.gamma_var.setValue(1.0)
            self.sharpness_var.setValue(0.0)
            self.blur_var.setValue(0)
        elif preset == "enhance":
            self.brightness_var.setValue(1.1)
            self.contrast_var.setValue(1.2)
            self.saturation_var.setValue(1.2)
            self.gamma_var.setValue(1.0)
            self.sharpness_var.setValue(0.5)
            self.blur_var.setValue(0)
        elif preset == "vintage":
            self.brightness_var.setValue(0.9)
            self.contrast_var.setValue(1.1)
            self.saturation_var.setValue(0.7)
            self.gamma_var.setValue(1.1)
            self.sharpness_var.setValue(0.0)
            self.blur_var.setValue(1)
        elif preset == "bw":
            self.brightness_var.setValue(1.0)
            self.contrast_var.setValue(1.5)
            self.saturation_var.setValue(0.0)
            self.gamma_var.setValue(1.0)
            self.sharpness_var.setValue(0.3)
            self.blur_var.setValue(0)
        self.update_all_preview()

    def reset_to_original(self):
        """Resets the image to the original state and updates the preview."""
        self.apply_preset("default")
        self.update_all_preview()
        
    def apply_changes(self):
        """Applies the preprocessing and closes the dialog."""
        self.result_image = self.preview_image  # Store preprocessed result
        self.accept()  # Close and accept

    def get_result_image(self):
        """Returns the resulting image after preprocessing."""
        return self.result_image