from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QLineEdit, QFormLayout, QMessageBox, QScrollArea, QWidget)
from PySide6.QtGui import QColor
from PySide6.QtCore import Qt, Signal

class CustomPaletteDialog(QDialog):
    """A dialog for saving custom color palettes."""

    def __init__(self, segment_colors, segment_names, parent=None):
        """Initialize the custom palette dialog.

        Args:
            segment_colors (dict): Dictionary mapping segment RGB tuples to their colors
            segment_names (dict): Dictionary mapping segment RGB tuples to their names
            parent: Parent widget
        """
        super().__init__(parent)
        self.setWindowTitle("Save Custom Palette")
        self.setModal(True)
        self.resize(500, 400)

        self.segment_colors = segment_colors.copy()
        self.segment_names = segment_names.copy()
        
        self.setup_ui()

    def setup_ui(self):
        """Set up the dialog UI."""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(10)

        # Palette name input
        form_layout = QFormLayout()
        self.palette_name_input = QLineEdit()
        self.palette_name_input.setPlaceholderText("e.g., sandstone_palette, carbonate_palette")
        form_layout.addRow("Palette Name:", self.palette_name_input)
        main_layout.addLayout(form_layout)

        # Description label
        description_label = QLabel(
            "This will save the current segment colors and names as a custom palette.\n"
            "The palette will be available in the palette selection dropdown."
        )
        description_label.setWordWrap(True)
        main_layout.addWidget(description_label)

        # Preview of colors and names
        preview_label = QLabel("Palette Preview:")
        preview_label.setStyleSheet("font-weight: bold;")
        main_layout.addWidget(preview_label)

        # Scroll area for preview
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setMinimumHeight(200)
        
        # Container for preview items
        container = QWidget()
        container_layout = QVBoxLayout(container)
        container_layout.setSpacing(5)

        # Add preview of each segment color and name
        sorted_colors = sorted(self.segment_colors.keys(), 
                              key=lambda x: self.segment_names.get(x, ""))
        
        for color in sorted_colors:
            segment_name = self.segment_names.get(color, "Unnamed Segment")
            
            # Create a horizontal layout for this color
            color_layout = QHBoxLayout()
            
            # Color box
            color_box = QLabel()
            color_box.setFixedSize(20, 20)
            rgb_color = self.segment_colors[color]
            if isinstance(rgb_color, tuple):
                color_box.setStyleSheet(
                    f"background-color: rgb({rgb_color[0]}, {rgb_color[1]}, {rgb_color[2]}); "
                    f"border: 1px solid black;"
                )
            else:  # Handle case where color might be a QColor or string
                qcolor = QColor(rgb_color)
                color_box.setStyleSheet(
                    f"background-color: {qcolor.name()}; border: 1px solid black;"
                )
            
            color_layout.addWidget(color_box)
            
            # Name label
            name_label = QLabel(segment_name)
            color_layout.addWidget(name_label)
            color_layout.addStretch()
            
            container_layout.addLayout(color_layout)
        
        container_layout.addStretch()
        scroll.setWidget(container)
        main_layout.addWidget(scroll)

        # Buttons
        button_layout = QHBoxLayout()
        save_button = QPushButton("Save Palette")
        cancel_button = QPushButton("Cancel")
        
        save_button.clicked.connect(self.accept)
        cancel_button.clicked.connect(self.reject)
        
        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)
        main_layout.addLayout(button_layout)

    def get_palette_name(self):
        """Get the palette name entered by the user."""
        return self.palette_name_input.text().strip()

    def validate(self):
        """Validate the dialog inputs."""
        palette_name = self.get_palette_name()
        if not palette_name:
            QMessageBox.warning(self, "Warning", "Please enter a palette name.")
            return False
        
        # Check for invalid characters in palette name
        import re
        if not re.match(r'^[a-zA-Z0-9_]+$', palette_name):
            QMessageBox.warning(self, "Warning", 
                               "Palette name can only contain letters, numbers, and underscores.")
            return False
        
        return True
    
    def accept(self):
        """Override accept to validate inputs."""
        if self.validate():
            super().accept()
