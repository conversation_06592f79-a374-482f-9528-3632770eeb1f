"""
Wrapper module for MobileSAM that delays importing the actual module until it's needed.
This helps avoid model registry warnings during application startup.
"""

import logging
import os
import sys
import importlib.util

logger = logging.getLogger(__name__)

# Flag to track if MobileSAM is available
MOBILE_SAM_AVAILABLE = None  # None means we haven't checked yet

# Placeholder for imported modules
_sam_model_registry = None
_SamPredictor = None
_SamAutomaticMaskGenerator = None

def is_available():
    """Check if MobileSAM is available without importing it."""
    global MOBILE_SAM_AVAILABLE
    if MOBILE_SAM_AVAILABLE is not None:
        return MOBILE_SAM_AVAILABLE
    
    try:
        # Check if mobile_sam module exists without importing it
        spec = importlib.util.find_spec("mobile_sam")
        MOBILE_SAM_AVAILABLE = spec is not None
        return MOBILE_SAM_AVAILABLE
    except ImportError:
        MOBILE_SAM_AVAILABLE = False
        return False

def get_sam_model_registry():
    """Get the sam_model_registry from mobile_sam.build_sam, importing it only when needed."""
    global _sam_model_registry
    if _sam_model_registry is not None:
        return _sam_model_registry
    
    if not is_available():
        logger.error("MobileSAM is not available")
        return None
    
    try:
        # Import only when needed
        from mobile_sam.build_sam import sam_model_registry
        _sam_model_registry = sam_model_registry
        return _sam_model_registry
    except ImportError as e:
        logger.error(f"Failed to import sam_model_registry: {e}")
        return None

def get_sam_predictor():
    """Get the SamPredictor class from mobile_sam.predictor, importing it only when needed."""
    global _SamPredictor
    if _SamPredictor is not None:
        return _SamPredictor
    
    if not is_available():
        logger.error("MobileSAM is not available")
        return None
    
    try:
        # Import only when needed
        from mobile_sam.predictor import SamPredictor
        _SamPredictor = SamPredictor
        return _SamPredictor
    except ImportError as e:
        logger.error(f"Failed to import SamPredictor: {e}")
        return None

def get_sam_automatic_mask_generator():
    """Get the SamAutomaticMaskGenerator class from mobile_sam.automatic_mask_generator, importing it only when needed."""
    global _SamAutomaticMaskGenerator
    if _SamAutomaticMaskGenerator is not None:
        return _SamAutomaticMaskGenerator
    
    if not is_available():
        logger.error("MobileSAM is not available")
        return None
    
    try:
        # Import only when needed
        from mobile_sam.automatic_mask_generator import SamAutomaticMaskGenerator
        _SamAutomaticMaskGenerator = SamAutomaticMaskGenerator
        return _SamAutomaticMaskGenerator
    except ImportError as e:
        logger.error(f"Failed to import SamAutomaticMaskGenerator: {e}")
        return None
