import os
import sys
import cv2
import numpy as np
import torch
import pandas as pd
import matplotlib.pyplot as plt
from scipy import stats
from skimage.measure import regionprops, label
import math
import matplotlib.pyplot as plt
from pycocotools import mask as mask_util
from PIL import Image
import time
import json
import pandas as pd

# Adjust Python path to include the 'src' directory and project root
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(project_root)
# Add 'src' to sys.path to allow imports like 'from segmentation...'
# and potentially to find a local 'detectron2' copy if it's in 'src/detectron2'
sys.path.append(os.path.join(project_root, 'src'))

# --- Configuration ---
FASTER_RCNN_MODEL_PATH = r"D:\Github\Nouveau dossier (5)\run faster cnn\model_final.pth"
MASK_RCNN_MODEL_PATH = r"D:\Github\Nouveau dossier (5)\run mask cnn\model_final.pth"
# MobileSAM model path will be handled by SamSegmentationHandler.
# It typically downloads or uses a checkpoint like 'mobile_sam.pt' or 'weights/mobile_sam.pt'.
# Ensure this path is correct or SamSegmentationHandler can find/download the model.
MOBILE_SAM_CHECKPOINT_PATH = os.path.join(project_root, "weights", "mobile_sam.pt")

VAL_DATA_DIR = r"D:\Github\detectron_2_fixed\detectron2\sandstone detectron 2.v2i.coco-segmentation\val"
VAL_ANNOTATIONS_FILE = os.path.join(VAL_DATA_DIR, "annotations.json")

OUTPUT_DIR = os.path.join(project_root, "evaluation_results_presentation") # Changed name to avoid conflict if user runs this multiple times
os.makedirs(OUTPUT_DIR, exist_ok=True)

# Model configurations
NUM_CLASSES = 5  # 4 minerals + 1 background
CLASS_NAMES = {
    0: "Background",
    1: "Feldspar",
    2: "Glauconite",
    3: "Opaque",
    4: "Quartz"
}
CONFIDENCE_THRESHOLD = 0.5
DEVICE = "cuda" if torch.cuda.is_available() else "cpu"

print(f"Using device: {DEVICE}")

# --- Imports for Detectron2, COCO, and SAM ---
try:
    from detectron2.config import get_cfg
    from detectron2.engine import DefaultPredictor
    from detectron2.utils.visualizer import Visualizer, ColorMode
    from detectron2.data import MetadataCatalog
    from detectron2.model_zoo import model_zoo
    from pycocotools.coco import COCO
    from pycocotools import mask as coco_mask_utils # Renamed to avoid conflict with local mask variables
except ImportError as e:
    print(f"Error importing Detectron2 or COCO tools: {e}")
    print("Attempting to use local Detectron2 copy...")
    # This handles the case where detectron2 is a local folder (e.g., in src/detectron2)
    # The sys.path.append(os.path.join(project_root, 'src')) should help here.
    try:
        # If detectron2 is in 'src/detectron2', this structure might be needed:
        # sys.path.insert(0, os.path.join(project_root, 'src')) # To find the 'detectron2' package inside 'src'
        from detectron2.config import get_cfg
        from detectron2.engine import DefaultPredictor
        from detectron2.utils.visualizer import Visualizer, ColorMode
        from detectron2.data import MetadataCatalog
        from detectron2.model_zoo import model_zoo
        from pycocotools.coco import COCO
        from pycocotools import mask as coco_mask_utils
        print("Successfully imported local Detectron2 components.")
    except ImportError as e_local:
        print(f"Failed to import local Detectron2 components: {e_local}")
        print("Please ensure Detectron2 and pycocotools are installed and accessible, or placed correctly as a local copy.")
        sys.exit(1)

try:
    from segmentation.sam_segmentation_handler import SAMSegmentationHandler # Corrected class name
except ImportError as e:
    print(f"Failed to import SamSegmentationHandler: {e}")
    print("Ensure 'sam_segmentation_handler.py' is in 'src/segmentation/' and 'src' is in PYTHONPATH.")
    sys.exit(1)

# --- Helper Functions ---

def load_detectron2_model(model_path, model_type, num_classes, threshold, device):
    """Loads a Detectron2 model (Faster R-CNN or Mask R-CNN)."""
    cfg = get_cfg()
    if model_type == "Faster R-CNN":
        config_file = "COCO-Detection/faster_rcnn_R_50_FPN_3x.yaml"
        cfg.merge_from_file(model_zoo.get_config_file(config_file))
        cfg.MODEL.MASK_ON = False
    elif model_type == "Mask R-CNN":
        config_file = "COCO-InstanceSegmentation/mask_rcnn_R_50_FPN_3x.yaml"
        cfg.merge_from_file(model_zoo.get_config_file(config_file))
        cfg.MODEL.MASK_ON = True
    else:
        raise ValueError("model_type must be 'Faster R-CNN' or 'Mask R-CNN'")

    cfg.MODEL.WEIGHTS = model_path
    cfg.MODEL.ROI_HEADS.NUM_CLASSES = num_classes
    cfg.MODEL.ROI_HEADS.SCORE_THRESH_TEST = threshold
    cfg.MODEL.DEVICE = device
    
    try:
        predictor = DefaultPredictor(cfg)
    except Exception as e:
        print(f"Error creating DefaultPredictor for {model_type} from {model_path}: {e}")
        print("This might be due to a mismatch between the model checkpoint and the configuration (e.g., number of classes).")
        print(f"Ensure NUM_CLASSES ({num_classes}) matches the trained model.")
        return None, None

    # Create a generic metadata object for visualization if not available from training config
    # This is important for the Visualizer to know class names.
    # If your models were trained with specific class names, this should reflect them.
    metadata_name = f"{model_type.replace(' ', '_').lower()}_eval_metadata"
    metadata = MetadataCatalog.get(metadata_name) # Get or create the metadata object

    # Ensure thing_classes is set if it's not already populated.
    if not hasattr(metadata, "thing_classes") or not metadata.thing_classes:
        metadata.set(thing_classes=[f"class_{i}" for i in range(num_classes)])
    
    # metadata is already the correct object, no need to get it again.
    
    return predictor, metadata

def initialize_sam_handler(checkpoint_path, device):
    """Initializes the MobileSAM handler."""
    print(f"Initializing SAM Handler with checkpoint: {checkpoint_path}")
    sam_handler = SAMSegmentationHandler(model_checkpoint=checkpoint_path, device=device)
    if not sam_handler.setup_mobilesam(): # This method loads the model
        print("Failed to initialize MobileSAM handler. Check checkpoint path and SAM setup.")
        return None
    print("MobileSAM handler initialized successfully.")
    return sam_handler

def load_coco_annotations(ann_file):
    """Loads COCO annotations."""
    if not os.path.exists(ann_file):
        print(f"Annotation file not found: {ann_file}")
        return None, []
    coco = COCO(ann_file)
    img_ids = coco.getImgIds()
    print(f"Loaded {len(img_ids)} images from COCO annotations.")
    return coco, img_ids

def get_ground_truth_masks(coco, img_id, img_info):
    """Retrieves ground truth masks and their class IDs for a given image ID."""
    ann_ids = coco.getAnnIds(imgIds=img_id)
    anns = coco.loadAnns(ann_ids)
    masks_with_classes = []
    for ann in anns:
        if 'segmentation' in ann and 'category_id' in ann:
            rle = coco_mask_utils.frPyObjects(ann['segmentation'], img_info['height'], img_info['width'])
            mask = coco_mask_utils.decode(rle)
            if mask.ndim > 2: # Handle multiple polygons for one instance
                mask = np.sum(mask, axis=2) > 0
            # Assuming category_id from COCO is 1-indexed, convert to 0-indexed if NUM_CLASSES is used for 0-based indexing
            # Or, ensure consistency. For now, let's keep it as is and adjust later if needed.
            class_id = ann['category_id'] # This might need adjustment based on how COCO dataset was created (0 or 1 indexed)
            masks_with_classes.append({'mask': mask.astype(np.uint8), 'class_id': class_id, 'area': np.sum(mask)})
    return masks_with_classes

def calculate_iou(gt_mask, pred_mask):
    """Calculates Intersection over Union (IoU) for a single pair of masks."""
    intersection = np.logical_and(gt_mask, pred_mask)
    union = np.logical_or(gt_mask, pred_mask)
    iou_score = np.sum(intersection) / np.sum(union) if np.sum(union) > 0 else 0.0
    return iou_score

def calculate_mean_iou_for_image(pred_masks_list, gt_masks_list):
    """Calculates mean IoU for a set of predicted masks against ground truth masks for an image."""
    if not pred_masks_list or not gt_masks_list:
        return 0.0

    total_iou = 0
    num_pred_masks_matched = 0

    # Ensure gt_masks are boolean
    gt_masks_list = [gt.astype(bool) for gt in gt_masks_list]

    for pred_mask in pred_masks_list:
        pred_mask = pred_mask.astype(bool) # Ensure pred_mask is boolean
        if pred_mask.ndim == 3 and pred_mask.shape[0] == 1: # Handle masks like (1, H, W)
            pred_mask = pred_mask.squeeze(0)
        elif pred_mask.ndim != 2:
            print(f"Skipping pred_mask with unexpected shape: {pred_mask.shape}")
            continue
            
        best_iou_for_pred = 0
        for gt_mask in gt_masks_list:
            if pred_mask.shape != gt_mask.shape:
                # Attempt to resize or pad if necessary, or skip
                # For simplicity, skipping if shapes don't match directly after conversion
                # This might happen if SAM produces masks of slightly different sizes than GT
                # A more robust solution would involve ensuring consistent mask dimensions earlier
                print(f"Shape mismatch: Pred {pred_mask.shape}, GT {gt_mask.shape}. Skipping this GT mask for IoU.")
                continue
            iou = calculate_iou(gt_mask, pred_mask)
            if iou > best_iou_for_pred:
                best_iou_for_pred = iou
        
        if best_iou_for_pred > 0: # Consider a match if IoU > 0, or use a threshold e.g. 0.5
            total_iou += best_iou_for_pred
            num_pred_masks_matched += 1

    mean_iou = total_iou / num_pred_masks_matched if num_pred_masks_matched > 0 else 0.0
    return mean_iou

def visualize_comparison(image_path, gt_masks, mrcnn_preds, frcnn_sam_preds, mrcnn_metadata, frcnn_metadata, output_path):
    """Visualizes the comparison and saves it to a file.
    
    Args:
        image_path: Path to the input image
        gt_masks: List of ground truth masks (can be either numpy arrays or dicts with 'mask' key)
        mrcnn_preds: Mask R-CNN predictions
        frcnn_sam_preds: Faster R-CNN + SAM predictions
        mrcnn_metadata: Metadata for Mask R-CNN visualization
        frcnn_metadata: Metadata for Faster R-CNN visualization
        output_path: Path to save the output visualization
    """
    original_image = cv2.imread(image_path)
    if original_image is None:
        print(f"Failed to load image: {image_path}")
        return
        
    original_image_rgb = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)

    fig, axs = plt.subplots(1, 3, figsize=(20, 7))
    fig.suptitle(f"Comparison for: {os.path.basename(image_path)}", fontsize=16)

    # 1. Ground Truth
    axs[0].imshow(original_image_rgb)
    axs[0].set_title("Ground Truth")
    axs[0].axis('off')
    
    if gt_masks and len(gt_masks) > 0:
        for mask_item in gt_masks:
            # Handle both dictionary and direct mask formats
            mask = mask_item['mask'] if isinstance(mask_item, dict) and 'mask' in mask_item else mask_item
            if mask is None or not isinstance(mask, np.ndarray):
                continue
                
            color = np.random.rand(3,)*0.7 + 0.3  # Random bright color
            masked_img = np.zeros_like(original_image_rgb)
            mask_bool = mask.astype(bool)  # Ensure mask is boolean
            
            for c in range(3):
                masked_img[:,:,c] = np.where(mask_bool, 
                                           original_image_rgb[:,:,c] * 0.5 + color[c] * 127, 
                                           original_image_rgb[:,:,c])
            # Apply the mask overlay
            axs[0].imshow(np.where(mask_bool[..., None], masked_img, original_image_rgb), alpha=0.6)

    # 2. Mask R-CNN Output
    vis_mrcnn = Visualizer(original_image_rgb.copy(), metadata=mrcnn_metadata, scale=0.8, instance_mode=ColorMode.IMAGE_BW)
    if mrcnn_preds and 'instances' in mrcnn_preds:
        out_mrcnn = vis_mrcnn.draw_instance_predictions(mrcnn_preds["instances"].to("cpu"))
        axs[1].imshow(out_mrcnn.get_image())
    else:
        axs[1].imshow(original_image_rgb) # Show original if no predictions
    axs[1].set_title("Mask R-CNN")
    axs[1].axis('off')

    # 3. Faster R-CNN + MobileSAM Output
    axs[2].imshow(original_image_rgb)
    axs[2].set_title("Faster R-CNN + MobileSAM")
    axs[2].axis('off')
    if frcnn_sam_preds and 'masks' in frcnn_sam_preds:
        for mask_sam in frcnn_sam_preds['masks']:
            color = np.random.rand(3,)*0.7 + 0.3 # Random bright color for SAM masks
            masked_img_sam = np.zeros_like(original_image_rgb)
            for c in range(3):
                masked_img_sam[:,:,c] = np.where(mask_sam == 1, original_image_rgb[:,:,c] * 0.5 + color[c] * 127, original_image_rgb[:,:,c])
            axs[2].imshow(np.where(mask_sam[...,None], masked_img_sam, original_image_rgb), alpha=0.6)
    
    plt.tight_layout(rect=[0, 0, 1, 0.96]) # Adjust layout to make space for suptitle
    plt.savefig(output_path)
    plt.close(fig)
    print(f"Saved visualization to {output_path}")

# --- Main Evaluation Logic ---
def main():
    print("Starting model evaluation script...")

    # 1. Load Models
    print("Loading Mask R-CNN model...")
    mask_rcnn_predictor, mrcnn_metadata = load_detectron2_model(MASK_RCNN_MODEL_PATH, "Mask R-CNN", NUM_CLASSES, CONFIDENCE_THRESHOLD, DEVICE)
    if not mask_rcnn_predictor:
        print("Failed to load Mask R-CNN model. Exiting.")
        return

    print("Loading Faster R-CNN model...")
    faster_rcnn_predictor, frcnn_metadata = load_detectron2_model(FASTER_RCNN_MODEL_PATH, "Faster R-CNN", NUM_CLASSES, CONFIDENCE_THRESHOLD, DEVICE)
    if not faster_rcnn_predictor:
        print("Failed to load Faster R-CNN model. Exiting.")
        return

    print("Initializing MobileSAM...")
    sam_handler = initialize_sam_handler(MOBILE_SAM_CHECKPOINT_PATH, DEVICE)
    if not sam_handler:
        print("Failed to initialize MobileSAM. Exiting.")
        return

    # 2. Load Validation Data and Annotations
    print(f"Loading COCO annotations from: {VAL_ANNOTATIONS_FILE}")
    coco, img_ids = load_coco_annotations(VAL_ANNOTATIONS_FILE)
    if not coco:
        print("Failed to load COCO annotations. Exiting.")
        return

    # Limit number of images to process for quick testing, remove or increase for full run
    num_images_to_process = min(5, len(img_ids)) # TODO: Increase for full evaluation
    print(f"Processing {num_images_to_process} images out of {len(img_ids)} total.")

    # For per-class area calculation
    # Initialize dictionaries to store total area and count for each class for each method
    # Assuming class IDs from COCO are 1-based. NUM_CLASSES is the total number of classes.
    gt_class_areas = {i: {'total_area': 0, 'count': 0, 'image_count': 0} for i in range(1, NUM_CLASSES + 1)}
    mrcnn_class_areas = {i: {'total_area': 0, 'count': 0, 'image_count': 0} for i in range(1, NUM_CLASSES + 1)}
    frcnn_sam_class_areas = {i: {'total_area': 0, 'count': 0, 'image_count': 0} for i in range(1, NUM_CLASSES + 1)}
    # Storing all areas per class per image for detailed stats if needed later
    gt_all_class_areas_per_image = {i: [] for i in range(1, NUM_CLASSES + 1)}
    mrcnn_all_class_areas_per_image = {i: [] for i in range(1, NUM_CLASSES + 1)}
    frcnn_sam_all_class_areas_per_image = {i: [] for i in range(1, NUM_CLASSES + 1)}

    # Initialize lists to store IoU scores for each method
    all_mrcnn_iou_scores = []
    all_frcnn_sam_iou_scores = []
    evaluation_data = []  # To store per-image metrics
    df_results = None  # Will store the final DataFrame for Excel export
    
    # Initialize dictionaries to store areas per class
    gt_areas_per_class = {i: [] for i in range(NUM_CLASSES)}
    mrcnn_areas_per_class = {i: [] for i in range(NUM_CLASSES)}
    frcnn_sam_areas_per_class = {i: [] for i in range(NUM_CLASSES)}
    total_gt_area_all_images = 0
    total_mrcnn_area_all_images = 0
    total_frcnn_sam_area_all_images = 0
    for i in range(num_images_to_process):
        img_id = img_ids[i]
        img_info = coco.loadImgs(img_id)[0]
        image_filename = img_info['file_name']
        image_path = os.path.join(VAL_DATA_DIR, "images", image_filename) # Added 'images' subdirectory

        if not os.path.exists(image_path):
            print(f"Image file not found: {image_path}. Skipping.")
            continue
        
        print(f"\nProcessing image ({i+1}/{num_images_to_process}): {image_filename}")
        image = cv2.imread(image_path)
        if image is None:
            print(f"Failed to read image: {image_path}. Skipping.")
            continue

        # Get Ground Truth Masks (now returns list of dicts with 'mask', 'class_id', 'area')
        gt_masks_with_classes = get_ground_truth_masks(coco, img_id, img_info)
        current_image_gt_total_area = 0
        gt_masks_for_iou = [] # For IoU calculation
        # Track which classes are present in this image for GT
        gt_classes_in_image = set()

        if not gt_masks_with_classes:
            print(f"No ground truth masks found for {image_filename}. Skipping IoU calculation for this image.")
        else:
            for item in gt_masks_with_classes:
                mask = item['mask']
                class_id = item['class_id']
                area = item['area']
                
                gt_masks_for_iou.append(mask.astype(bool)) # For IoU
                current_image_gt_total_area += area

                # Ensure class_id is within the expected range (1 to NUM_CLASSES)
                # COCO category_ids are typically 1-indexed.
                if 1 <= class_id <= NUM_CLASSES:
                    gt_class_areas[class_id]['total_area'] += area
                    gt_class_areas[class_id]['count'] += 1
                    gt_all_class_areas_per_image[class_id].append(area)
                    gt_classes_in_image.add(class_id)
                else:
                    print(f"Warning: GT class_id {class_id} for image {image_filename} is outside expected range 1-{NUM_CLASSES}.")
            # Increment image_count for each class present in this GT image
            for cid in gt_classes_in_image:
                 gt_class_areas[cid]['image_count'] +=1
        total_gt_area_all_images += current_image_gt_total_area


        # --- Method 1: Mask R-CNN --- 
        print("Running Mask R-CNN inference...")
        mrcnn_iou_for_excel = 0.0
        mrcnn_time_for_excel = 0.0
        current_image_mrcnn_total_area = 0
        mrcnn_pred_masks_np_list_for_iou = [] # For IoU calculation
        # Track which classes are present in this image for Mask R-CNN
        mrcnn_classes_in_image = set()

        start_time = time.time()
        mrcnn_outputs = mask_rcnn_predictor(image) # BGR image
        mrcnn_time = time.time() - start_time
        print(f"Mask R-CNN inference time: {mrcnn_time:.3f}s")
        mrcnn_time_for_excel = mrcnn_time

        if 'instances' in mrcnn_outputs and len(mrcnn_outputs['instances']) > 0:
            mrcnn_instances = mrcnn_outputs['instances'].to('cpu')
            mrcnn_pred_masks_tensor = mrcnn_instances.pred_masks
            mrcnn_pred_classes_np = mrcnn_instances.pred_classes.numpy() # 0-indexed

            for idx, mask_tensor in enumerate(mrcnn_pred_masks_tensor):
                mask_np = mask_tensor.numpy()
                area = np.sum(mask_np)
                class_id = mrcnn_pred_classes_np[idx] + 1 # Convert 0-indexed to 1-indexed

                current_image_mrcnn_total_area += area
                mrcnn_pred_masks_np_list_for_iou.append(mask_np.astype(bool)) # For IoU

                if 1 <= class_id <= NUM_CLASSES:
                    mrcnn_class_areas[class_id]['total_area'] += area
                    mrcnn_class_areas[class_id]['count'] += 1
                    mrcnn_all_class_areas_per_image[class_id].append(area)
                    mrcnn_classes_in_image.add(class_id)
                else:
                    print(f"Warning: Mask R-CNN class_id {class_id} (0-indexed: {mrcnn_pred_classes_np[idx]}) for image {image_filename} is outside expected range 1-{NUM_CLASSES}.")
            
            # Increment image_count for each class predicted by Mask R-CNN in this image
            for cid in mrcnn_classes_in_image:
                mrcnn_class_areas[cid]['image_count'] += 1

            if gt_masks_for_iou: # Only calculate IoU if GT exists
                mrcnn_img_iou = calculate_mean_iou_for_image(mrcnn_pred_masks_np_list_for_iou, gt_masks_for_iou)
                mrcnn_iou_for_excel = mrcnn_img_iou
                all_mrcnn_iou_scores.append(mrcnn_img_iou)
                print(f"Mask R-CNN Mean IoU for {image_filename}: {mrcnn_img_iou:.4f}")
            else:
                print(f"Skipping Mask R-CNN IoU for {image_filename} due to no ground truth.")
                mrcnn_iou_for_excel = None # Or some indicator for no GT
        else:
            print(f"No instances found by Mask R-CNN for {image_filename}. IoU: 0")
            if gt_masks_for_iou: # Still append 0 if GT exists but no preds
                 all_mrcnn_iou_scores.append(0.0)
            mrcnn_iou_for_excel = 0.0
        total_mrcnn_area_all_images += current_image_mrcnn_total_area


        # --- Method 2: Faster R-CNN + MobileSAM ---
        print("Running Faster R-CNN inference...")
        frcnn_sam_iou_for_excel = 0.0 # Default value
        frcnn_time_for_excel = 0.0 # Default value
        sam_refinement_time_for_excel = 0.0 # Default value
        start_time = time.time()
        frcnn_outputs = faster_rcnn_predictor(image) # BGR image
        frcnn_time = time.time() - start_time
        print(f"Faster R-CNN inference time: {frcnn_time:.3f}s")

        frcnn_sam_results = {'masks': [], 'boxes': [], 'scores': []}
        if 'instances' in frcnn_outputs:
            pred_boxes_tensor = frcnn_outputs["instances"].pred_boxes.tensor
            pred_scores_tensor = frcnn_outputs["instances"].scores
            
            if pred_boxes_tensor.numel() > 0:
                print(f"Faster R-CNN detected {len(pred_boxes_tensor)} boxes. Refining with MobileSAM...")
                # Set image for SAM (expects RGB)
                image_rgb_for_sam = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                if not sam_handler.set_image(image_rgb_for_sam):
                    print("Failed to set image for SAM. Skipping SAM refinement for this image.")
                else:
                    sam_refinement_total_time = 0.0
                    for box_idx in range(len(pred_boxes_tensor)):
                        box = pred_boxes_tensor[box_idx].cpu().numpy()
                        score = pred_scores_tensor[box_idx].cpu().item()
                        
                        sam_start_time = time.time()
                        # SAM expects box in [x1, y1, x2, y2] format
                        # The predict_from_box method in SamSegmentationHandler handles numpy array conversion
                        sam_masks, sam_scores, _ = sam_handler.predict_from_box(box)
                        sam_refinement_total_time += (time.time() - sam_start_time)

                        if sam_masks is not None and len(sam_masks) > 0:
                            best_mask_idx = np.argmax(sam_scores) # SAM returns multiple masks per prompt
                            refined_mask = sam_masks[best_mask_idx]
                            frcnn_sam_results['masks'].append(refined_mask)
                            frcnn_sam_results['boxes'].append(box)
                            frcnn_sam_results['scores'].append(score) # Keep original Faster R-CNN score for the box
                        else:
                            print(f"SAM did not return a mask for box {box_idx}.")
                    print(f"MobileSAM refinement time for {len(pred_boxes_tensor)} boxes: {sam_refinement_total_time:.3f}s")
                    sam_refinement_time_for_excel = sam_refinement_total_time
            else:
                print("Faster R-CNN detected no boxes.")

        # Calculate total mask areas and areas per class
        gt_total_area = 0
        mrcnn_total_area = 0
        frcnn_sam_total_area = 0
        
        # Calculate areas for ground truth
        if gt_masks_with_classes:
            gt_total_area = np.sum([np.sum(mask['mask']) for mask in gt_masks_with_classes])
            # Get class IDs for ground truth masks
            gt_class_ids = [mask['class_id'] for mask in gt_masks_with_classes]
            for mask, class_id in zip(gt_masks_with_classes, gt_class_ids):
                area = mask['area']
                gt_areas_per_class[class_id].append(area)
        
        # Calculate areas for Mask R-CNN predictions
        if mrcnn_outputs and 'instances' in mrcnn_outputs and mrcnn_outputs['instances'].has('pred_masks'):
            mrcnn_masks_np = mrcnn_outputs['instances'].pred_masks.cpu().numpy()
            mrcnn_total_area = np.sum([np.sum(mask) for mask in mrcnn_masks_np])
            if mrcnn_outputs['instances'].has('pred_classes'):
                mrcnn_class_ids = mrcnn_outputs['instances'].pred_classes.cpu().numpy()
                for mask, class_id in zip(mrcnn_masks_np, mrcnn_class_ids):
                    area = np.sum(mask)
                    mrcnn_areas_per_class[class_id].append(area)
        
        # Calculate areas for Faster R-CNN + SAM predictions
        if frcnn_sam_results and frcnn_sam_results['masks']:
            frcnn_sam_total_area = np.sum([np.sum(mask) for mask in frcnn_sam_results['masks']])
            # Get class predictions for Faster R-CNN + SAM
            if 'instances' in frcnn_outputs and len(frcnn_outputs['instances']) > 0:
                frcnn_pred_classes = frcnn_outputs['instances'].pred_classes.cpu().numpy()
                for mask_idx, class_id in enumerate(frcnn_pred_classes):
                    if mask_idx < len(frcnn_sam_results['masks']):
                        area = np.sum(frcnn_sam_results['masks'][mask_idx])
                        frcnn_sam_areas_per_class[class_id].append(area)

        # Calculate IoU for Faster R-CNN + MobileSAM
        if frcnn_sam_results['masks'] and gt_masks_for_iou:
            # frcnn_sam_results['masks'] is already a list of numpy arrays
            frcnn_sam_img_iou = calculate_mean_iou_for_image(frcnn_sam_results['masks'], gt_masks_for_iou)
            frcnn_sam_iou_for_excel = frcnn_sam_img_iou
            all_frcnn_sam_iou_scores.append(frcnn_sam_img_iou)
            print(f"Faster R-CNN + MobileSAM Mean IoU for {image_filename}: {frcnn_sam_img_iou:.4f}")
        elif not gt_masks_for_iou:
            print(f"Skipping Faster R-CNN + SAM IoU for {image_filename} due to no ground truth.")
            frcnn_sam_iou_for_excel = None # Or some indicator for no GT
        else:
            print(f"No masks produced by Faster R-CNN + MobileSAM for {image_filename}. IoU: 0")
            all_frcnn_sam_iou_scores.append(0.0) # Append 0 if no predictions
            frcnn_sam_iou_for_excel = 0.0
        frcnn_time_for_excel = frcnn_time

        # Calculate per-class areas for this image
        image_class_areas = {}
        for class_id in range(NUM_CLASSES):
            # Ground Truth
            gt_areas = [m['area'] for m in gt_masks_with_classes 
                      if m and 'class_id' in m and m['class_id'] == class_id]
            gt_avg = np.mean(gt_areas) if gt_areas else 0
            
            # Mask R-CNN
            mrcnn_areas = [np.sum(mask) for mask, cls_id in zip(
                mrcnn_instances.pred_masks.cpu().numpy() if 'instances' in mrcnn_outputs else [],
                mrcnn_instances.pred_classes.cpu().numpy() if 'instances' in mrcnn_outputs else []
            ) if cls_id == class_id] if 'instances' in mrcnn_outputs and len(mrcnn_outputs['instances']) > 0 else []
            mrcnn_avg = np.mean(mrcnn_areas) if mrcnn_areas else 0
            
            # Faster R-CNN + SAM
            frcnn_sam_areas = []
            if frcnn_sam_results and 'masks' in frcnn_sam_results and 'instances' in frcnn_outputs:
                pred_classes = frcnn_outputs['instances'].pred_classes.cpu().numpy()
                frcnn_sam_areas = [np.sum(mask) for mask, cls_id in zip(frcnn_sam_results['masks'], pred_classes) 
                                  if cls_id == class_id and mask is not None]
            frcnn_sam_avg = np.mean(frcnn_sam_areas) if frcnn_sam_areas else 0
            
            image_class_areas[f'Class_{class_id}_GT_Area'] = gt_avg
            image_class_areas[f'Class_{class_id}_MRCNN_Area'] = mrcnn_avg
            image_class_areas[f'Class_{class_id}_FRCNN_SAM_Area'] = frcnn_sam_avg
        
        # Store data for this image
        evaluation_data.append({
            'Image Filename': image_filename,
            'Mask R-CNN IoU': mrcnn_iou_for_excel,
            'Mask R-CNN Inference Time (s)': mrcnn_time_for_excel,
            'Faster R-CNN + SAM IoU': frcnn_sam_iou_for_excel,
            'Faster R-CNN Inference Time (s)': frcnn_time_for_excel,
            'MobileSAM Refinement Time (s)': sam_refinement_time_for_excel,
            'GT Total Area': gt_total_area,
            'Mask R-CNN Total Area': mrcnn_total_area,
            'Faster R-CNN + SAM Total Area': frcnn_sam_total_area,
            **image_class_areas
        })
        
        # Print per-image class areas
        print(f"\nAverage Area per Class for {image_filename}:")
        print("Class\tGT Avg Area\tMask R-CNN Avg Area\tFaster R-CNN + SAM Avg Area")
        for class_id in range(NUM_CLASSES):
            print(f"{class_id}\t"
                  f"{image_class_areas[f'Class_{class_id}_GT_Area']:.1f}\t\t"
                  f"{image_class_areas[f'Class_{class_id}_MRCNN_Area']:.1f}\t\t"
                  f"{image_class_areas[f'Class_{class_id}_FRCNN_SAM_Area']:.1f}")

        
        # 4. Visualize and Save Results
        output_image_path = os.path.join(OUTPUT_DIR, f"comparison_{os.path.splitext(image_filename)[0]}.png")
        visualize_comparison(image_path, gt_masks_with_classes, mrcnn_outputs, frcnn_sam_results, 
                           mrcnn_metadata, frcnn_metadata, output_image_path)
    
    # Calculate and print global area distribution by class
    print("\nGlobal Area Distribution by Class:")
    print("Class\tName\t\tGT %\tMask R-CNN %\tFaster R-CNN + SAM %")
    print("-" * 70)
    
    # Initialize dictionaries to store total areas
    total_gt_areas = {class_id: 0 for class_id in range(NUM_CLASSES)}
    total_mrcnn_areas = {class_id: 0 for class_id in range(NUM_CLASSES)}
    total_frcnn_sam_areas = {class_id: 0 for class_id in range(NUM_CLASSES)}
    
    # Calculate total areas for each class across all images
    for class_id in range(NUM_CLASSES):
        if gt_areas_per_class[class_id]:
            total_gt_areas[class_id] = np.sum(gt_areas_per_class[class_id])
        if mrcnn_areas_per_class[class_id]:
            total_mrcnn_areas[class_id] = np.sum(mrcnn_areas_per_class[class_id])
        if frcnn_sam_areas_per_class[class_id]:
            total_frcnn_sam_areas[class_id] = np.sum(frcnn_sam_areas_per_class[class_id])
    
    # Calculate total areas across all classes
    total_gt = sum(total_gt_areas.values())
    total_mrcnn = sum(total_mrcnn_areas.values())
    total_frcnn_sam = sum(total_frcnn_sam_areas.values())
    
    class_areas = []
    
    for class_id in range(NUM_CLASSES):
        class_name = CLASS_NAMES.get(class_id, f"Class_{class_id}")
        
        # Calculate percentages
        gt_pct = (total_gt_areas[class_id] / total_gt * 100) if total_gt > 0 else 0
        mrcnn_pct = (total_mrcnn_areas[class_id] / total_mrcnn * 100) if total_mrcnn > 0 else 0
        frcnn_sam_pct = (total_frcnn_sam_areas[class_id] / total_frcnn_sam * 100) if total_frcnn_sam > 0 else 0
        
        print(f"{class_id}\t{class_name:12}\t{gt_pct:.1f}%\t{mrcnn_pct:6.1f}%\t{frcnn_sam_pct:10.1f}%")
        
        class_areas.append({
            'Class': class_id,
            'Class_Name': class_name,
            'GT_Area_Total': total_gt_areas[class_id],
            'GT_Area_Percent': gt_pct,
            'MaskRCNN_Area_Total': total_mrcnn_areas[class_id],
            'MaskRCNN_Area_Percent': mrcnn_pct,
            'FasterRCNN_SAM_Area_Total': total_frcnn_sam_areas[class_id],
            'FasterRCNN_SAM_Area_Percent': frcnn_sam_pct
        })
    
    def calculate_grain_sizes(mask_data, is_coco=False, pixel_size_um=1.0, debug=False):
        """Calculate grain sizes from mask data.
        
        Args:
            mask_data: Can be a binary mask, list of masks, or COCO annotation
            is_coco: Whether the input is in COCO format
            pixel_size_um: Size of one pixel in micrometers
            debug: If True, print debug information
            
        Returns:
            List of grain diameters in micrometers
        """
        # Initialize an empty list to store grain sizes
        grain_sizes = []
        
        # Only process if we have valid mask data
        if mask_data is None:
            if debug:
                print("  Warning: mask_data is None")
            return grain_sizes
            
        if debug:
            print(f"  Processing mask_data of type: {type(mask_data)}")
            if hasattr(mask_data, 'shape'):
                print(f"  Shape: {mask_data.shape}")
            elif isinstance(mask_data, dict):
                print(f"  Keys: {mask_data.keys()}")
                
        try:
            # Handle different input formats
            if is_coco and isinstance(mask_data, dict) and 'segmentation' in mask_data:
                if debug:
                    print("  Processing COCO format annotation")
                # COCO format annotation
                if isinstance(mask_data['segmentation'], dict):  # RLE format
                    if debug:
                        print("    RLE format detected")
                    try:
                        mask = mask_util.decode(mask_data['segmentation'])
                        if mask.size > 0:
                            mask = mask.astype(bool)
                            if debug:
                                print(f"    Decoded RLE mask shape: {mask.shape}")
                    except Exception as e:
                        print(f"    Error decoding RLE: {e}")
                        return grain_sizes
                else:  # Polygon format
                    if debug:
                        print("    Polygon format detected")
                    try:
                        h = mask_data.get('height', mask_data.get('size', [1024])[0])
                        w = mask_data.get('width', mask_data.get('size', [1024, 1024])[1])
                        mask = np.zeros((h, w), dtype=np.uint8)
                        for seg in mask_data['segmentation']:
                            poly = np.array(seg).reshape(-1, 2).astype(np.int32)
                            cv2.fillPoly(mask, [poly], 1)
                        mask = mask.astype(bool)
                        if debug:
                            print(f"    Created polygon mask shape: {mask.shape}")
                    except Exception as e:
                        print(f"    Error creating polygon mask: {e}")
                        return grain_sizes
                
                # Process the mask if we have valid data
                if 'mask' in locals() and isinstance(mask, np.ndarray):
                    try:
                        labeled = label(mask)
                        num_features = np.max(labeled)  # Get number of features from labeled image
                        if debug:
                            print(f"    Found {num_features} features in mask")
                            
                        if num_features > 0:
                            props = regionprops(labeled)
                            for prop in props:
                                if prop.area > 0:
                                    eq_diameter = 2 * math.sqrt(prop.area / math.pi) * pixel_size_um
                                    grain_sizes.append(eq_diameter)
                                    if debug and len(grain_sizes) <= 5:  # Only print first few for brevity
                                        print(f"    Grain size: {eq_diameter:.2f} µm (area: {prop.area} px²)")
                    except Exception as e:
                        print(f"    Error processing mask: {e}")
                        
            elif isinstance(mask_data, np.ndarray):  # Direct binary mask
                if debug:
                    print("  Processing numpy array mask")
                try:
                    if mask_data.ndim > 2:  # Convert to 2D if needed
                        mask = mask_data.any(axis=2) if mask_data.shape[2] > 1 else mask_data[:, :, 0]
                    else:
                        mask = mask_data
                    
                    mask = mask.astype(bool)
                    labeled = label(mask)
                    num_features = np.max(labeled)  # Get number of features from labeled image
                    if debug:
                        print(f"    Found {num_features} features in numpy mask")
                        
                    if num_features > 0:
                        props = regionprops(labeled)
                        for prop in props:
                            if prop.area > 0:
                                eq_diameter = 2 * math.sqrt(prop.area / math.pi) * pixel_size_um
                                grain_sizes.append(eq_diameter)
                except Exception as e:
                    print(f"    Error processing numpy array: {e}")
                    
            elif isinstance(mask_data, list):  # List of masks or annotations
                if debug:
                    print(f"  Processing list of {len(mask_data)} items")
                for i, item in enumerate(mask_data):
                    if debug and i < 3:  # Only debug first few items
                        print(f"    Processing list item {i} of type {type(item)}")
                    sizes = calculate_grain_sizes(
                        item, is_coco, pixel_size_um, debug=(debug and i < 3))
                    if sizes:  # Only extend if we got valid sizes
                        grain_sizes.extend(sizes)
            
            if debug:
                print(f"  Total grain sizes found: {len(grain_sizes)}")
                
            return grain_sizes
                
        except Exception as e:
            print(f"Error in calculate_grain_sizes: {e}")
            import traceback
            traceback.print_exc()
            return grain_sizes  # Return whatever we have so far

    # Analyze grain size distribution
    print("\nAnalyzing grain size distribution...")
    
    # Initialize grain size dictionaries for each class
    gt_grain_sizes = {class_id: [] for class_id in CLASS_NAMES}
    mrcnn_grain_sizes = {class_id: [] for class_id in CLASS_NAMES}
    frcnn_sam_grain_sizes = {class_id: [] for class_id in CLASS_NAMES}
    
    # Add a helper function to safely extend lists
    def safe_extend(target_dict, key, values):
        if key in target_dict and values:
            target_dict[key].extend(values)
    
    # Pixel size in micrometers (adjust this based on your microscope calibration)
    PIXEL_SIZE_UM = 1.0  # Default to 1.0 um/pixel, adjust as needed
    
    # Process each image's masks
    for i, img_data in enumerate(evaluation_data):
        img_name = img_data['Image Filename']
        img_id = None
        
        # Find the image ID in the COCO dataset
        for coco_img_id, coco_img in coco.imgs.items():
            if coco_img['file_name'] in img_name:
                img_id = coco_img_id
                break
                
        if img_id is None:
            print(f"Warning: Could not find {img_name} in COCO dataset")
            continue
            
        # Only enable debug for first image to avoid too much output
        debug = (i == 0)
        
        print(f"\n{'='*80}\nProcessing grain sizes for {img_name} (Image {i+1}/{len(evaluation_data)})\n{'='*80}")
        
        # Get masks for this image
        gt_masks = []
        mrcnn_masks = []
        frcnn_sam_masks = []
        
        # Get ground truth masks from COCO
        if img_id in coco.imgToAnns:
            gt_masks = coco.imgToAnns[img_id]
            if debug:
                print(f"\nGround Truth Annotations:")
                for j, ann in enumerate(gt_masks):
                    if j < 3:  # Only show first few for brevity
                        print(f"  Annotation {j}: {list(ann.keys())}")
                    elif j == 3:
                        print(f"  ... and {len(gt_masks)-3} more")
        
        # Get Mask R-CNN masks if available
        if 'instances' in mrcnn_outputs and mrcnn_outputs['instances'].has('pred_masks'):
            mrcnn_masks = [m.squeeze().cpu().numpy() 
                         for m in mrcnn_outputs['instances'].pred_masks]
            if debug and mrcnn_masks:
                print(f"\nMask R-CNN Masks: {len(mrcnn_masks)} masks")
                print(f"  First mask shape: {mrcnn_masks[0].shape}")
        
        # Get Faster R-CNN + SAM masks if available
        if frcnn_sam_results and 'masks' in frcnn_sam_results:
            frcnn_sam_masks = [m.squeeze() for m in frcnn_sam_results['masks'] 
                             if m is not None and m.size > 0]
            if debug and frcnn_sam_masks:
                print(f"\nFaster R-CNN + SAM Masks: {len(frcnn_sam_masks)} masks")
                print(f"  First mask shape: {frcnn_sam_masks[0].shape}")
        
        print(f"\nFound: {len(gt_masks)} GT masks, {len(mrcnn_masks)} Mask R-CNN masks, {len(frcnn_sam_masks)} FRCNN-SAM masks")
        
        # Calculate grain sizes for each type
        print("\nCalculating grain sizes...")
        
        try:
            # Process ground truth masks (COCO format)
            gt_sizes = {cat_id: [] for cat_id in CLASS_NAMES}
            if gt_masks:
                print(f"Processing {len(gt_masks)} GT masks...")
                for j, ann in enumerate(gt_masks):
                    if 'segmentation' in ann and ann['segmentation']:
                        if debug and j < 3:  # Only debug first few masks
                            print(f"  Processing GT mask {j}...")
                        sizes = calculate_grain_sizes(
                            ann, 
                            is_coco=True, 
                            pixel_size_um=PIXEL_SIZE_UM,
                            debug=(debug and j < 3)  # Only debug first few masks
                        ) or []  # Ensure we have a list, even if None is returned
                        cat_id = ann.get('category_id', 0)  # Default to 0 if not present
                        safe_extend(gt_sizes, cat_id, sizes)
                
                print(f"  Found {sum(len(sizes) for sizes in gt_sizes.values())} GT grains")
            
            # Process Mask R-CNN masks (numpy arrays)
            mrcnn_sizes = {cat_id: [] for cat_id in CLASS_NAMES}
            if mrcnn_masks and 'instances' in mrcnn_outputs and mrcnn_outputs['instances'].has('pred_classes'):
                print(f"\nProcessing {len(mrcnn_masks)} Mask R-CNN masks...")
                pred_classes = mrcnn_outputs['instances'].pred_classes.cpu().numpy()
                
                for j, (mask, cat_id) in enumerate(zip(mrcnn_masks, pred_classes)):
                    if debug and j < 3:  # Only debug first few masks
                        print(f"  Processing Mask R-CNN mask {j} (class: {CLASS_NAMES.get(int(cat_id), cat_id)})...")
                    sizes = calculate_grain_sizes(
                        mask, 
                        is_coco=False, 
                        pixel_size_um=PIXEL_SIZE_UM,
                        debug=(debug and j < 3)
                    ) or []  # Ensure we have a list, even if None is returned
                    safe_extend(mrcnn_sizes, int(cat_id), sizes)
                
                # Print summary of grains found per class
                print("  Mask R-CNN grains per class:")
                for cat_id, sizes in mrcnn_sizes.items():
                    if sizes:
                        print(f"    {CLASS_NAMES.get(int(cat_id), cat_id)}: {len(sizes)} grains")
            
            # Process Faster R-CNN + SAM masks (numpy arrays)
            frcnn_sam_sizes = {cat_id: [] for cat_id in CLASS_NAMES}
            if frcnn_sam_masks and frcnn_outputs and 'instances' in frcnn_outputs and frcnn_outputs['instances'].has('pred_classes'):
                print(f"\nProcessing {len(frcnn_sam_masks)} FRCNN-SAM masks...")
                pred_classes = frcnn_outputs['instances'].pred_classes.cpu().numpy()
                
                # Ensure we have the same number of masks and class predictions
                num_masks = min(len(frcnn_sam_masks), len(pred_classes))
                
                for j in range(num_masks):
                    mask = frcnn_sam_masks[j]
                    cat_id = int(pred_classes[j])
                    
                    if debug and j < 3:  # Only debug first few masks
                        print(f"  Processing FRCNN-SAM mask {j} (class: {CLASS_NAMES.get(cat_id, cat_id)})...")
                    
                    sizes = calculate_grain_sizes(
                        mask, 
                        is_coco=False, 
                        pixel_size_um=PIXEL_SIZE_UM,
                        debug=(debug and j < 3)
                    ) or []  # Ensure we have a list, even if None is returned
                    safe_extend(frcnn_sam_sizes, cat_id, sizes)
                
                # Print summary of grains found per class
                print("  FRCNN-SAM grains per class:")
                for cat_id, sizes in frcnn_sam_sizes.items():
                    if sizes:
                        print(f"    {CLASS_NAMES.get(int(cat_id), cat_id)}: {len(sizes)} grains")
            
            # Add to global lists
            for cat_id in CLASS_NAMES:
                safe_extend(gt_grain_sizes, cat_id, gt_sizes.get(cat_id, []))
                safe_extend(mrcnn_grain_sizes, cat_id, mrcnn_sizes.get(cat_id, []))
                safe_extend(frcnn_sam_grain_sizes, cat_id, frcnn_sam_sizes.get(cat_id, []))
            
            # Print total grains processed for this image
            total_gt = sum(len(sizes) for sizes in gt_sizes.values())
            total_mrcnn = sum(len(sizes) for sizes in mrcnn_sizes.values())
            total_frcnn = sum(len(sizes) for sizes in frcnn_sam_sizes.values())
            print(f"\nProcessed: {total_gt} GT grains, {total_mrcnn} Mask R-CNN grains, {total_frcnn} FRCNN-SAM grains")
            
        except Exception as e:
            print(f"\nError processing image {img_name}: {e}")
            import traceback
            traceback.print_exc()
    
    # Create cumulative distribution plots
    plt.figure(figsize=(15, 10))
    
    # Sort the data for CDF
    def plot_cdf(data, label, color):
        if not data:
            return
        sorted_data = np.sort(data)
        y_vals = np.arange(len(sorted_data)) / float(len(sorted_data) - 1) * 100  # Convert to percentage
        plt.plot(sorted_data, y_vals, label=label, color=color, linewidth=2)
    
    # Create a figure for combined CDF
    plt.figure(figsize=(15, 10))
    
    # Plot combined CDF
    for class_id, class_name in CLASS_NAMES.items():
        if class_id == 0:  # Skip background
            continue
            
        # Create a new figure for each class
        plt.figure(figsize=(12, 8))
        
        # Plot CDFs for this class
        if gt_grain_sizes[class_id]:
            plot_cdf(gt_grain_sizes[class_id], f'GT - {class_name}', 'green')
        if mrcnn_grain_sizes[class_id]:
            plot_cdf(mrcnn_grain_sizes[class_id], f'MRCNN - {class_name}', 'blue')
        if frcnn_sam_grain_sizes[class_id]:
            plot_cdf(frcnn_sam_grain_sizes[class_id], f'FRCNN-SAM - {class_name}', 'red')
        
        plt.xlabel('Grain Size (µm)')
        plt.ylabel('Cumulative Frequency (%)')
        plt.title(f'Cumulative Grain Size Distribution - {class_name}')
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)
        
        # Save individual class plot
        class_plot_path = os.path.join(OUTPUT_DIR, f'grain_size_distribution_{class_name.lower()}.png')
        plt.savefig(class_plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"Grain size distribution plot for {class_name} saved to: {class_plot_path}")
    
    # Create a combined plot for all classes
    plt.figure(figsize=(15, 10))
    for class_id, class_name in CLASS_NAMES.items():
        if class_id == 0:  # Skip background
            continue
        if gt_grain_sizes[class_id]:
            plot_cdf(gt_grain_sizes[class_id], f'GT - {class_name}', plt.cm.tab10(class_id-1))
    
    plt.xlabel('Grain Size (µm)')
    plt.ylabel('Cumulative Frequency (%)')
    plt.title('Cumulative Grain Size Distribution - Ground Truth (All Classes)')
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # Save combined plot
    combined_plot_path = os.path.join(OUTPUT_DIR, 'grain_size_distribution_all_classes.png')
    plt.savefig(combined_plot_path, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"Combined grain size distribution plot saved to: {combined_plot_path}")
    
    plt.xlabel('Grain Size (µm)')
    plt.ylabel('Cumulative Frequency (%)')
    plt.title('Cumulative Grain Size Distribution')
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # Save the plot
    grain_size_plot_path = os.path.join(OUTPUT_DIR, 'grain_size_distribution.png')
    plt.savefig(grain_size_plot_path, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"Grain size distribution plot saved to: {grain_size_plot_path}")
    
    # Calculate and print grain size statistics
    def print_grain_stats(sizes_dict, name):
        print(f"\n{'='*50}")
        print(f"{name} Grain Size Statistics")
        print("-"*50)
        
        for class_id, sizes in sizes_dict.items():
            if not sizes:
                continue
                
            class_name = CLASS_NAMES.get(class_id, f'Class {class_id}')
            sizes = np.array(sizes)
            
            print(f"\n{class_name}:")
            print(f"  Count: {len(sizes)}")
            print(f"  Mean: {np.mean(sizes):.2f} µm")
            print(f"  Median: {np.median(sizes):.2f} µm")
            print(f"  Std Dev: {np.std(sizes):.2f} µm")
            print(f"  Min: {np.min(sizes):.2f} µm")
            print(f"  Max: {np.max(sizes):.2f} µm")
            print(f"  10th percentile: {np.percentile(sizes, 10):.2f} µm")
            print(f"  90th percentile: {np.percentile(sizes, 90):.2f} µm")
    
    # Print statistics for each method
    print_grain_stats(gt_grain_sizes, 'Ground Truth')
    print_grain_stats(mrcnn_grain_sizes, 'Mask R-CNN')
    print_grain_stats(frcnn_sam_grain_sizes, 'Faster R-CNN + SAM')
    
    # Save grain size data to Excel
    grain_size_data = {
        'Ground_Truth': pd.Series(gt_grain_sizes),
        'Mask_RCNN': pd.Series(mrcnn_grain_sizes),
        'FasterRCNN_SAM': pd.Series(frcnn_sam_grain_sizes)
    }
    df_grain_sizes = pd.DataFrame(dict([(k, pd.Series(v)) for k, v in grain_size_data.items()]))
    
    # Save evaluation data to Excel
    if evaluation_data:
        # Save per-image metrics
        df_results = pd.DataFrame(evaluation_data)
        
        # Save per-class areas to a separate sheet
        df_class_areas = pd.DataFrame(class_areas)
        
        excel_path = os.path.join(OUTPUT_DIR, "evaluation_metrics.xlsx")
        
        try:
            # Try to close the Excel file if it's open
            try:
                import win32com.client
                excel = win32com.client.Dispatch('Excel.Application')
                for wb in excel.Workbooks:
                    if os.path.normpath(wb.FullName).lower() == os.path.normpath(excel_path).lower():
                        wb.Close(SaveChanges=False)
                excel.Quit()
            except Exception as e:
                print(f"Note: Could not close Excel file (might not be open): {e}")
            
            # Now save the data
            try:
                writer = pd.ExcelWriter(excel_path, engine='openpyxl')
                df_results.to_excel(writer, sheet_name='Per Image Metrics', index=False)
                df_class_areas.to_excel(writer, sheet_name='Per Class Areas', index=False)
                df_grain_sizes.to_excel(writer, sheet_name='Grain Sizes', index=False)
                writer.close()
                print(f"\nEvaluation metrics saved to: {excel_path}")
            except Exception as e:
                print(f"\nError saving evaluation metrics to Excel: {e}")
                print("Make sure the Excel file is not open in another program.")
                if 'writer' in locals():
                    writer.close()
        except Exception as e:
            print(f"\nError preparing to save Excel file: {e}")
    else:
        print("No evaluation data collected to save to Excel.")
    
    # Calculate and print overall average IoUs
    avg_mrcnn_iou = np.mean(all_mrcnn_iou_scores) if all_mrcnn_iou_scores else 0
    avg_frcnn_sam_iou = np.mean(all_frcnn_sam_iou_scores) if all_frcnn_sam_iou_scores else 0
    
    print(f"\nOverall Average Mask R-CNN Mean IoU: {avg_mrcnn_iou:.4f}")
    print(f"Overall Average Faster R-CNN + MobileSAM Mean IoU: {avg_frcnn_sam_iou:.4f}")

    # Generate and save IoU comparison plot
    if all_mrcnn_iou_scores or all_frcnn_sam_iou_scores: # Proceed if there's any data to plot
        model_names = ['Mask R-CNN', 'Faster R-CNN + MobileSAM']
        iou_values = [avg_mrcnn_iou, avg_frcnn_sam_iou]

        plt.figure(figsize=(8, 6))
        bars = plt.bar(model_names, iou_values, color=['skyblue', 'lightcoral'])
        plt.ylabel('Mean Intersection over Union (IoU)')
        plt.title('Model Accuracy Comparison (Mean IoU)')
        plt.ylim(0, 1) # IoU is between 0 and 1

        # Add IoU values on top of bars
        for bar in bars:
            yval = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2.0, yval + 0.01, f'{yval:.3f}', ha='center', va='bottom')

        plot_path = os.path.join(OUTPUT_DIR, 'iou_comparison_plot.png')
        plt.savefig(plot_path)
        plt.close()
        print(f"IoU comparison plot saved to: {plot_path}")
    else:
        print("No IoU scores recorded, skipping IoU plot generation.")

    # Generate and save total area comparison plot
    if evaluation_data and df_results is not None and not df_results.empty:
        avg_gt_area = df_results['GT Total Area'].mean() if 'GT Total Area' in df_results else 0
        avg_mrcnn_area = df_results['Mask R-CNN Total Area'].mean() if 'Mask R-CNN Total Area' in df_results else 0
        avg_frcnn_sam_area = df_results['Faster R-CNN + SAM Total Area'].mean() if 'Faster R-CNN + SAM Total Area' in df_results else 0

        area_model_names = ['Ground Truth', 'Mask R-CNN', 'Faster R-CNN + SAM']
        area_values = [avg_gt_area, avg_mrcnn_area, avg_frcnn_sam_area]

        plt.figure(figsize=(10, 7))
        area_bars = plt.bar(area_model_names, area_values, color=['green', 'skyblue', 'lightcoral'])
        plt.ylabel('Average Total Mask Area (pixels)')
        plt.title('Model Comparison: Average Total Mask Area')
        
        # Add area values on top of bars
        for bar in area_bars:
            yval = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2.0, yval + 0.01 * max(area_values, default=1), f'{yval:.0f}', ha='center', va='bottom')

        area_plot_path = os.path.join(OUTPUT_DIR, 'average_area_comparison_plot.png')
        plt.tight_layout()
        plt.savefig(area_plot_path)
        plt.close()
        print(f"Average area comparison plot saved to: {area_plot_path}")
    else:
        print("No area data recorded or DataFrame is empty, skipping area plot generation.")

if __name__ == "__main__":
    main()