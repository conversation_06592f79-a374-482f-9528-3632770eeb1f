# src/gui/handlers/about_page_handler.py

import logging
import os
from pathlib import Path

# Optional markdown import - not actually used in this file
try:
    import markdown
except ImportError:
    markdown = None
from PySide6.QtWidgets import QMessageBox
from PySide6.QtCore import QUrl, QCoreApplication
from PySide6.QtGui import QDesktopServices
from src.gui.styles.theme_aware_buttons import get_theme_colors

logger = logging.getLogger(__name__)

def get_tutorial_colors(theme_name=None):
    """Get properly formatted colors for tutorial HTML content."""
    from src.gui.styles.theme_aware_buttons import get_theme_colors
    
    # Get current theme colors using the proper theme system
    theme_colors = get_theme_colors(theme_name)
    
    # Create comprehensive color mapping for tutorials
    colors = {
        # Basic colors
        'text': theme_colors['text'].name(),
        'window': theme_colors['window'].name(),
        'base': theme_colors['base'].name(),
        'accent': theme_colors['accent'].name(),
        'highlight': theme_colors['highlight'].name(),
        'button': theme_colors['button'].name(),
        'button_text': theme_colors['button-text'].name(),
        
        # Tutorial-specific colors
        'text_primary': theme_colors['text'].name(),
        'text_secondary': theme_colors['text'].name() if theme_colors['text'].lightness() > 128 else '#7f8c8d',
        'text_header': theme_colors['text'].name(),
        'bg_primary': theme_colors['window'].name(),
        'bg_secondary': theme_colors['alternate-base'].name(),
        'bg_code': theme_colors['base'].name(),
        'text_code': theme_colors['text'].name(),
        'bg_inline_code': theme_colors['alternate-base'].name(),
        'text_inline_code': theme_colors['accent'].name(),
        'accent_secondary': theme_colors['highlight'].name(),
        'bg_info': theme_colors['base'].name(),
        'bg_success': theme_colors['base'].name(),
        'bg_warning': theme_colors['base'].name(),
        'accent_success': theme_colors['accent'].name(),
        'text_on_accent': theme_colors['highlight-text'].name()
    }
    
    # Adjust colors for better contrast based on theme
    is_dark = theme_colors['window'].lightness() < 128
    
    if is_dark:
        # Dark theme adjustments
        colors['text_secondary'] = '#95a5a6'
        colors['bg_info'] = '#2c3e50'
        colors['bg_success'] = '#27ae60'
        colors['bg_warning'] = '#f39c12'
    else:
        # Light theme adjustments
        colors['text_secondary'] = '#7f8c8d'
        colors['bg_info'] = '#e8f4fd'
        colors['bg_success'] = '#d5f4e6'
        colors['bg_warning'] = '#fff3cd'
    
    return colors

class AboutPageHandler:
    """Handler for About page functionality."""
    
    def __init__(self, app):
        """Initialize the About page handler.
        
        Args:
            app: The main application instance
        """
        self.app = app
        
        # Initialize tutorial functions dictionary
        self.tutorial_functions = {
            "Unsupervised Segmentation": self._show_unsupervised_seg_tutorial,
            "Advanced Segmentation": self._show_advanced_segmentation_tutorial,
            "Trainable Segmentation": self._show_trainable_seg_tutorial,
            "Point Counting": self._show_point_counting_tutorial,
            "Grain Analysis": self._show_grain_analysis_tutorial,
            "Batch Processing": self._show_batch_processing_tutorial,
            "Image Lab": self._show_image_lab_tutorial,
            "AI Assistant": self._show_ai_assistant_tutorial
        }

        # Connect tutorial buttons
        if hasattr(self.app, 'unsupervised_segmentation_tut_btn'):
            self.app.unsupervised_segmentation_tut_btn.clicked.connect(lambda: self.show_tutorial_content("Unsupervised Segmentation"))
        if hasattr(self.app, 'advanced_segmentation_tut_btn'):
            self.app.advanced_segmentation_tut_btn.clicked.connect(lambda: self.show_tutorial_content("Advanced Segmentation"))
        if hasattr(self.app, 'trainable_segmentation_tut_btn'):
            self.app.trainable_segmentation_tut_btn.clicked.connect(lambda: self.show_tutorial_content("Trainable Segmentation"))
        if hasattr(self.app, 'point_counting_tut_btn'):
            self.app.point_counting_tut_btn.clicked.connect(lambda: self.show_tutorial_content("Point Counting"))
        if hasattr(self.app, 'grain_analysis_tut_btn'):
            self.app.grain_analysis_tut_btn.clicked.connect(lambda: self.show_tutorial_content("Grain Analysis"))
        if hasattr(self.app, 'batch_processing_tut_btn'):
            self.app.batch_processing_tut_btn.clicked.connect(lambda: self.show_tutorial_content("Batch Processing"))
        if hasattr(self.app, 'image_lab_tut_btn'):
            self.app.image_lab_tut_btn.clicked.connect(lambda: self.show_tutorial_content("Image Lab"))
        if hasattr(self.app, 'ai_assistant_tut_btn'):
            self.app.ai_assistant_tut_btn.clicked.connect(lambda: self.show_tutorial_content("AI Assistant"))

        
        self.setup_about_page_connections()
        
    def setup_about_page_connections(self):
        """Connect About page signals to their handlers."""
        try:
            # Connect tutorial buttons if they exist
            if hasattr(self.app, 'quick_start_btn'):
                self.app.quick_start_btn.clicked.connect(self.show_quick_start_guide)
                
            if hasattr(self.app, 'video_tutorials_btn'):
                self.app.video_tutorials_btn.clicked.connect(self.show_video_tutorials)
                
            if hasattr(self.app, 'help_btn'):
                self.app.help_btn.clicked.connect(self.show_help_dialog)
                
            # Connect new tutorial system buttons
            if hasattr(self.app, 'unsupervised_segmentation_tut_btn'):
                self.app.unsupervised_segmentation_tut_btn.clicked.connect(lambda: self.show_tutorial_content("Unsupervised Segmentation"))
                
            if hasattr(self.app, 'trainable_segmentation_tut_btn'):
                self.app.trainable_segmentation_tut_btn.clicked.connect(lambda: self.show_tutorial_content("Trainable Segmentation"))
                
            if hasattr(self.app, 'point_counting_tut_btn'):
                self.app.point_counting_tut_btn.clicked.connect(lambda: self.show_tutorial_content("Point Counting"))
                
            # Set default tutorial content
            if hasattr(self.app, 'tutorial_display'):
                self.app.tutorial_display.setHtml(self._get_welcome_tutorial_content())
                
            logger.info("About page connections established successfully")
            
        except Exception as e:
            logger.error(f"Error setting up About page connections: {e}")
            
    def show_tutorial_content(self, tutorial_name):
        """Display tutorial content in the tutorial display area."""
        try:
            if hasattr(self.app, 'tutorial_display'):
                if tutorial_name in self.tutorial_functions:
                    content = self.tutorial_functions[tutorial_name]()
                    self.app.tutorial_display.setHtml(content)
                else:
                    content = self._show_generic_tutorial(tutorial_name)
                    self.app.tutorial_display.setHtml(content)
            else:
                logger.warning("Tutorial display widget not found")
        except Exception as e:
            logger.error(f"Error displaying tutorial content for {tutorial_name}: {e}")
            
    def _get_welcome_tutorial_content(self):
        """Get welcome content for the tutorial display."""
        from src.gui.styles.theme_aware_buttons import get_theme_name
        theme_name = get_theme_name()
        colors = get_tutorial_colors(theme_name)
        bg_primary_color = colors['bg_primary']
        text_primary_color = colors['text_primary']
        accent_color = colors['accent']
        accent_secondary_color = colors['accent_secondary']
        bg_info_color = colors['bg_info']
        bg_secondary_color = colors['bg_secondary']
        bg_success = colors['bg_success']
        bg_warning = colors.get('bg_warning', colors['bg_info'])
        accent_color_2 = colors.get('accent_secondary', colors['accent'])
        

        return f"""
        <style>
            @keyframes fadeIn {{ from {{ opacity: 0; }} to {{ opacity: 1; }} }}
        </style>
        <div style='font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; animation: fadeIn 0.5s ease-in-out; color: {text_primary_color};'>
            <h2 style='color: {bg_primary_color}; border-bottom: 2px solid {accent_color}; padding-bottom: 10px;'>📚 Welcome to VisionLab AI Tutorials</h2>
            
                <p style='font-size: 14px; line-height: 1.6; color: {text_primary_color} !important; background-color: {bg_primary_color}; margin: 15px 0; padding: 10px; border-radius: 5px;'>Select a tutorial from the left panel to get detailed guidance on using VisionLab AI's powerful features.</p>
            <div style='background: {accent_secondary_color}; padding: 15px; border-radius: 8px; margin: 20px 0;'>
                <h3 style='color: {text_primary_color}; margin-top: 0;'>🎯 Available Guides:</h3>
                <ul style='font-size: 14px;'>
                    <li><strong>Unsupervised Segmentation:</strong> Learn hybrid machine learning approach for automatic image segmentation</li>
                    <li><strong>Trainable Segmentation:</strong> Master XGBoost-powered custom segmentation with interactive labeling</li>
                    <li><strong>Point Counting:</strong> Understand modal analysis and percentage calculations</li>
                    <li><strong>Grain Analysis:</strong> Explore morphometric analysis of geological grains</li>
                    <li><strong>Batch Processing:</strong> Efficiently process multiple images</li>
                    <li><strong>Image Lab:</strong> Discover advanced image processing and enhancement tools</li>
                    <li><strong>AI Assistant:</strong> Leverage AI for personalized guidance and support</li>
                </ul>
            </div>
            
            <div style='background: {bg_success}; padding: 15px; border-radius: 8px; border-left: 4px solid {accent_color};'>
                <p style='margin: 0; color: {accent_color}; font-weight: bold;'>💡 Tip:</p>
                <p style='margin: 5px 0 0 0; color: {text_primary_color};'>Each tutorial includes step-by-step instructions, parameter explanations, and practical tips for best results.</p>
            </div>
        </div>
        """
            
    def show_quick_start_guide(self):
        """Show the quick start guide dialog."""
        try:
            msg = QMessageBox(self.app)
            msg.setWindowTitle("Quick Start Guide")
            msg.setIcon(QMessageBox.Information)
            
            quick_start_text = """
🚀 VisionLab AI Quick Start Guide

1. CREATE A PROJECT
   • Go to Project Hub
   • Click "New Project" or "Open Project"
   • Set up your project workspace

2. IMPORT IMAGES
   • Use the "Upload" button in any analysis module
   • Supported formats: JPG, PNG, TIFF, BMP
   • Organize images in your project folder

3. CHOOSE ANALYSIS TYPE
   • Unsupervised Segmentation: Automatic mineral identification
   • Trainable Segmentation: Custom AI model training
   • Point Counting: Modal analysis and percentages
   • Grain Analysis: Size and shape statistics

4. PROCESS YOUR DATA
   • Follow the module-specific workflows
   • Use AI Assistant for guidance
   • Export results when complete

5. BATCH PROCESSING
   • Process multiple samples efficiently
   • Set up automated workflows
   • Generate comprehensive reports

Tip: Start with the AI Assistant for personalized guidance!
            """
            
            msg.setText(quick_start_text)
            msg.setStandardButtons(QMessageBox.Ok)
            msg.exec()
            
            logger.info("Quick start guide displayed")
            
        except Exception as e:
            logger.error(f"Error showing quick start guide: {e}")
            
    def show_video_tutorials(self):
        """Show video tutorials (placeholder for future implementation)."""
        try:
            msg = QMessageBox(self.app)
            msg.setWindowTitle("Video Tutorials")
            msg.setIcon(QMessageBox.Information)
            msg.setText(
                "🎥 Video Tutorials\n\n"
                "Interactive video tutorials are coming soon!\n\n"
                "In the meantime, you can:\n"
                "• Use the Quick Start Guide\n"
                "• Explore the AI Assistant\n"
                "• Check our documentation\n\n"
                "Stay tuned for comprehensive video walkthroughs!"
            )
            msg.setStandardButtons(QMessageBox.Ok)
            msg.exec()
            
            logger.info("Video tutorials info displayed")
            
        except Exception as e:
            logger.error(f"Error showing video tutorials info: {e}")
            
    def show_help_dialog(self):
        """Show the help dialog with support options."""
        try:
            msg = QMessageBox(self.app)
            msg.setWindowTitle("Get Help")
            msg.setIcon(QMessageBox.Question)
            
            help_text = """
❓ Need Help with VisionLab AI?

HERE'S HOW TO GET SUPPORT:

🤖 AI Assistant
   • Click on the "AI Assistant" tab
   • Ask questions in natural language
   • Get real-time guidance and tips

📚 Documentation
   • Check the built-in tutorials
   • Review module-specific guides
   • Browse the quick start guide

🔗 Online Resources
   • Visit our GitHub repository
   • Check the project wiki
   • Browse community discussions

📧 Direct Support
   • Contact the development team
   • Report bugs or issues
   • Request new features

COMMON SOLUTIONS:
   • Restart the application
   • Check image file formats
   • Verify project permissions
   • Update to the latest version
            """
            
            msg.setText(help_text)
            
            # Add custom buttons
            ai_assistant_btn = msg.addButton("Open AI Assistant", QMessageBox.ActionRole)
            github_btn = msg.addButton("Visit GitHub", QMessageBox.ActionRole)
            ok_btn = msg.addButton(QMessageBox.Ok)
            
            result = msg.exec()
            
            # Handle button clicks
            if msg.clickedButton() == ai_assistant_btn:
                self.switch_to_ai_assistant()
            elif msg.clickedButton() == github_btn:
                QDesktopServices.openUrl(QUrl("https://github.com/your-repo"))
                
            logger.info("Help dialog displayed")
            
        except Exception as e:
            logger.error(f"Error showing help dialog: {e}")
            
    def switch_to_ai_assistant(self):
        """Switch to the AI Assistant tab."""
        try:
            # Find the AI Assistant tab index
            for i in range(self.app.stacked_widget.count()):
                if self.app.stacked_widget.tabText(i) == "AI Assistant":
                    self.app.stacked_widget.setCurrentIndex(i)
                    logger.debug("Switched to AI Assistant tab")
                    return
                    
            # Silently handle missing AI Assistant tab - no warning needed
            logger.debug("AI Assistant tab not found")
            
        except Exception as e:
            logger.debug(f"Error switching to AI Assistant: {e}")
            
    def show_tutorial_for_page(self, page_name):
        """Show a tutorial specific to a page.
        
        Args:
            page_name (str): Name of the page to show tutorial for
        """
        tutorials = {
            "Unsupervised Segmentation": self._show_unsupervised_seg_tutorial,
            "Trainable Segmentation": self._show_trainable_seg_tutorial,
            "Point Counting": self._show_point_counting_tutorial,
            "Grain Analysis": self._show_grain_analysis_tutorial,
            "Batch Processing": self._show_batch_processing_tutorial,
            "Image Lab": self._show_image_lab_tutorial,
            "AI Assistant": self._show_ai_assistant_tutorial
        }
        
        if page_name in tutorials:
            tutorials[page_name]()
        else:
            self._show_generic_tutorial(page_name)
            
    def _load_tutorial_markdown(self, tutorial_name):
        """Load tutorial content from markdown file.
        
        Args:
            tutorial_name (str): Name of the tutorial markdown file (without extension)
            
        Returns:
            str: HTML content of the tutorial
        """
        try:
            # Get the application directory
            app_dir = Path(QCoreApplication.applicationDirPath())
            
            # Look for tutorials in both development and packaged locations
            possible_paths = [
                app_dir.parent / 'tutorials' / f'{tutorial_name}.md',
                app_dir / 'tutorials' / f'{tutorial_name}.md',
                Path(__file__).parent.parent.parent / 'tutorials' / f'{tutorial_name}.md'
            ]
            
            for path in possible_paths:
                if path.exists():
                    with open(path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Convert markdown-style formatting to HTML for better presentation
                    formatted_content = self._format_markdown_to_html(content)
                    
                    colors = get_theme_colors()
                    bg_primary_color = colors.get('bg_primary', '#fafafa')
                    text_color = colors.get('text', '#333')

                    html = f"""
                    <style>
                        @keyframes fadeIn {{ from {{ opacity: 0; }} to {{ opacity: 1; }} }}
                    </style>
                    <div style='
                        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
                        max-width: 1000px;
                        margin: 0 auto;
                        padding: 30px;
                        background-color: {bg_primary_color};
                        border-radius: 12px;
                        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                        line-height: 1.7;
                        color: {text_color};
                        animation: fadeIn 0.5s ease-in-out;
                    '>
                        {formatted_content}
                    </div>
                    """
                    return html
            
            logger.warning(f"Tutorial file {tutorial_name}.md not found in any location")
            return f"<p>Tutorial content not found for {tutorial_name}. Please check the installation.</p>"
            
        except Exception as e:
            logger.error(f"Error loading tutorial {tutorial_name}: {e}")
            return f"<p>Error loading tutorial content: {str(e)}</p>"
    
    def _format_markdown_to_html(self, content):
        """Convert basic markdown formatting to HTML for better presentation.
        
        Args:
            content (str): Raw markdown content
            
        Returns:
            str: HTML formatted content
        """
        import re
        
        # Escape HTML characters first
        content = content.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
        
        theme_name = self.app.theme_manager.current_theme_name
        colors = get_tutorial_colors(theme_name)
        header_color = colors['text_header']
        accent_color = colors['accent']

        # Convert headers with hover effects
        text_secondary_color = colors['text_secondary']
        bg_code_color = colors['bg_code']
        text_code_color = colors['text_code']
        bg_inline_code_color = colors['bg_inline_code']
        text_inline_code_color = colors['text_inline_code']
        accent_secondary_color = colors['accent_secondary']

        content = re.sub(r'^# (.+)$', '<h1 style="color: {}; border-bottom: 3px solid {}; padding-bottom: 10px; margin-top: 30px; margin-bottom: 20px; font-size: 28px; transition: color 0.3s;" onmouseover="this.style.color=\'{}\';" onmouseout="this.style.color=\'{}\';">\1</h1>'.format(header_color, accent_color, accent_color, header_color), content, flags=re.MULTILINE)
        content = re.sub(r'^## (.+)$', '<h2 style="color: {}; border-bottom: 2px solid {}; padding-bottom: 8px; margin-top: 25px; margin-bottom: 15px; font-size: 24px; transition: color 0.3s;" onmouseover="this.style.color=\'{}\';" onmouseout="this.style.color=\'{}\';">\1</h2>'.format(header_color, border_color, accent_color, header_color), content, flags=re.MULTILINE)
        content = re.sub(r'^### (.+)$', '<h3 style="color: {}; margin-top: 20px; margin-bottom: 12px; font-size: 20px; transition: color 0.3s;" onmouseover="this.style.color=\'{}\';" onmouseout="this.style.color=\'{}\';">\1</h3>'.format(header_color, accent_color, header_color), content, flags=re.MULTILINE)
        
        # Convert bold text
        content = re.sub(r'\*\*(.+?)\*\*', f'<strong style="color: {header_color}; font-weight: 600;">\1</strong>', content)
        
        # Convert italic text
        content = re.sub(r'\*(.+?)\*', f'<em style="color: {text_secondary_color}; font-style: italic;">\1</em>', content)
        
        # Convert code blocks
        content = re.sub(r'```([\s\S]*?)```', f'<pre style="background-color: {bg_code_color}; color: {text_code_color}; padding: 15px; border-radius: 5px; overflow-x: auto; margin: 15px 0; font-family: \'Consolas\', \'Monaco\', monospace; font-size: 14px; line-height: 1.4;"><code>\1</code></pre>', content)
        
        # Convert inline code
        content = re.sub(r'`(.+?)`', f'<code style="background-color: {bg_inline_code_color}; color: {text_inline_code_color}; padding: 2px 6px; border-radius: 3px; font-family: \'Consolas\', \'Monaco\', monospace; font-size: 13px;">\1</code>', content)
        
        # Convert horizontal rules
        content = re.sub(r'^---$', f'<hr style="border: none; height: 2px; background: linear-gradient(to right, {accent_color}, {accent_secondary_color}); margin: 30px 0; border-radius: 1px;">', content, flags=re.MULTILINE)
        
        # Handle table of contents placeholders (remove &2 patterns)
        content = re.sub(r'^\s*&\d+\s*$', '', content, flags=re.MULTILINE)
        
        # Convert numbered lists first
        content = re.sub(r'^(\d+)\. (.+)$', r'<li class="numbered-item" style="margin: 8px 0; padding-left: 5px;">\2</li>', content, flags=re.MULTILINE)
        
        # Convert bullet points
        content = re.sub(r'^- (.+)$', r'<li class="bullet-item" style="margin: 8px 0; padding-left: 5px;">\1</li>', content, flags=re.MULTILINE)
        
        # Wrap consecutive numbered list items in ol tags
        numbered_class = ' class="numbered-item"'
        content = re.sub(r'(<li class="numbered-item"[^>]*>.*?</li>\s*)+', lambda m: f'<ol style="margin: 15px 0; padding-left: 25px; list-style-type: decimal;">{m.group(0).replace(numbered_class, "")}</ol>', content, flags=re.DOTALL)
        
        # Wrap consecutive bullet list items in ul tags
        bullet_class = ' class="bullet-item"'
        content = re.sub(r'(<li class="bullet-item"[^>]*>.*?</li>\s*)+', lambda m: f'<ul style="margin: 15px 0; padding-left: 25px; list-style-type: disc;">{m.group(0).replace(bullet_class, "")}</ul>', content, flags=re.DOTALL)
        
        # Convert paragraphs (double newlines)
        paragraphs = content.split('\n\n')
        formatted_paragraphs = []
        
        for para in paragraphs:
            para = para.strip()
            if para and not para.startswith('<'):
                para = f'<p style="margin: 15px 0; text-align: justify;">{para}</p>'
            formatted_paragraphs.append(para)
        
        return '\n\n'.join(formatted_paragraphs)
    
    def _show_unsupervised_seg_tutorial(self):
        """Return Unsupervised Segmentation tutorial content as HTML."""
        from src.gui.styles.theme_aware_buttons import get_theme_name
        theme_name = get_theme_name()
        colors = get_tutorial_colors(theme_name)
        bg_primary_color = colors['bg_primary']
        text_primary_color = colors['text_primary']
        accent_color = colors['accent']
        accent_secondary_color = colors['accent_secondary']
        bg_info_color = colors['bg_info']
        bg_secondary_color = colors['bg_secondary']
        bg_success = colors['bg_success']
        bg_warning = colors.get('bg_warning', colors['bg_info'])
        accent_color_2 = colors.get('accent_secondary', colors['accent'])
        
        return f"""
        <style>
            @keyframes fadeIn {{ from {{ opacity: 0; }} to {{ opacity: 1; }} }}
            .tutorial-container {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                padding: 25px;
                line-height: 1.7;
                background: transparent;
                color: {text_primary_color};
                border-radius: 12px;
                max-width: 900px;
                margin: 0 auto;
                animation: fadeIn 0.5s ease-in-out;
                
                /* Define CSS variables with theme colors */
                --bg-primary: {bg_primary_color};
                --bg-secondary: {bg_secondary_color};
                --bg-info: {bg_info_color};
                --bg-success: {bg_success};
                --bg-warning: {bg_warning};
                --bg-card: {bg_secondary_color};
                --bg-code: {colors['bg_code']};
                --bg-key: {bg_secondary_color};
                --text-primary: {text_primary_color};
                --text-secondary: {colors['text_secondary']};
                --text-key: {text_primary_color};
                --border-color: {colors['text_secondary']};
                --border-light: {bg_secondary_color};
                --border-key: {colors['text_secondary']};
                --accent-color: {accent_color};
            }}
            .tutorial-header {{
                text-align: center;
                margin-bottom: 35px;
                padding: 25px;
                background: linear-gradient(135deg, {accent_color} 0%, {accent_secondary_color} 100%);
                border-radius: 15px;
                color: white;
                box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            }}
            }}
        .tutorial-title {{
                margin: 0;
                font-size: 2.8em;
                font-weight: 700;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                letter-spacing: -0.5px;
            }}
            .tutorial-subtitle {{
                margin: 15px 0 0 0;
                font-size: 1.3em;
                opacity: 0.95;
                font-weight: 300;
            }}
            .overview-section {{
                background: transparent;
                padding: 25px;
                border-radius: 12px;
                margin: 25px 0;
                border-left: 6px solid {accent_color};
                box-shadow: none;
            }}
            .step-section {{
                background: transparent;
                padding: 20px;
                border-radius: 10px;
                margin: 25px 0;
                border-left: 5px solid {accent_color};
                box-shadow: 0 3px 12px rgba(0,0,0,0.08);
                transition: transform 0.2s ease, box-shadow 0.2s ease;
            }}
            .step-section:hover {{
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(0,0,0,0.12);
            }}
            .step-title {{
                color: {text_primary_color};
                margin-top: 0;
                font-size: 1.4em;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 10px;
            }}
            .feature-list {{
                list-style: none;
                padding-left: 0;
            }}
            .feature-list li {{
                margin: 8px 0;
                padding-left: 25px;
                position: relative;
            }}
            .feature-list li:before {{
                content: '▶';
                position: absolute;
                left: 0;
                color: {accent_color};
                font-weight: bold;
            }}
            .workflow-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 15px;
                margin: 20px 0;
            }}
            .workflow-step {{
                background: transparent;
                padding: 15px;
                border-radius: 8px;
                border-left: 4px solid {accent_color};
                box-shadow: none;
            }}
            .workflow-number {{
                color: {accent_color};
                font-weight: bold;
                font-size: 1.2em;
            }}
        </style>
        <div class='tutorial-container'>
            <div class='tutorial-header'>
                <h1 class='tutorial-title'>🔬 Unsupervised Segmentation</h1>
                <p class='tutorial-subtitle'>Hybrid Machine Learning Approach for Automatic Image Segmentation</p>
            </div>
            
            <div class='overview-section'>
                <h2 style='color: {accent_color}; margin-top: 0; font-size: 1.6em;'>🧠 How It Works</h2>
                <p style='font-size: 1.15em; margin-bottom: 15px;'>
                    The Unsupervised Segmentation page uses a <strong>hybrid machine learning approach</strong> to segment your images without requiring any manual labels. This method combines traditional clustering with a lightweight Convolutional Neural Network (CNN) trained on-the-fly.
                </p>
                <div class='workflow-grid'>
                    <div class='workflow-step'>
                        <div class='workflow-number'>1. Initial Segmentation</div>
                        <p>Image is segmented into superpixels using classic methods (KMeans, PCA+KMeans, or Felzenszwalb) to create pseudo-labels.</p>
                    </div>
                    <div class='workflow-step'>
                        <div class='workflow-number'>2. CNN Refinement</div>
                        <p>A small CNN is trained using superpixels as targets, learning to refine and smooth the segmentation for better results.</p>
                    </div>
                </div>
            </div>
            
            <div class='step-section'>
                <h3 class='step-title'>📁 Image Gallery Management</h3>
                <ul class='feature-list'>
                    <li><strong>Add Images:</strong> Use the add button to include new images in your gallery</li>
                    <li><strong>Remove Images:</strong> Select and remove unwanted images from the gallery</li>
                    <li><strong>Preview:</strong> Click on any image to preview it before segmentation</li>
                    <li><strong>Clear All:</strong> Remove all images at once for a fresh start</li>
                    <li><strong>Batch Processing:</strong> Load multiple images for efficient batch segmentation</li>
                </ul>
            </div>
            
            <div class='step-section'>
                <h3 class='step-title'>⚙️ Segmentation Parameters</h3>
                <p><strong>Basic Parameters:</strong></p>
                <ul class='feature-list'>
                    <li><strong>Method:</strong> Choose algorithm for initial over-segmentation (KMeans, Felzenszwalb, PCA+KMeans)</li>
                    <li><strong>Training Epochs:</strong> Number of epochs to train the CNN refinement model</li>
                    <li><strong>Model Dimensions:</strong> Set width (channels) for CNN's internal layers</li>
                    <li><strong>Min/Max Label Number:</strong> Define range for number of segments created</li>
                    <li><strong>Target Width/Height:</strong> Resize input image for segmentation processing</li>
                    <li><strong>Learning Rate:</strong> Initial learning rate for CNN training</li>
                </ul>
                <p><strong>Advanced Options:</strong></p>
                <ul class='feature-list'>
                    <li><strong>Optimizer:</strong> Select optimization algorithm (SGD, Adam, AdamW, RMSprop)</li>
                    <li><strong>Adaptive Learning Rate:</strong> Automatically adjust learning rate during training</li>
                    <li><strong>Momentum:</strong> Set momentum factor for SGD optimizer</li>
                    <li><strong>Weight Decay:</strong> Apply L2 regularization to prevent overfitting</li>
                    <li><strong>Gradient Clipping:</strong> Prevent training instability with gradient norm limits</li>
                </ul>
            </div>
            
            <div class='step-section'>
                <h3 class='step-title'>👁️ Viewing Results</h3>
                <ul class='feature-list'>
                    <li><strong>Original Image:</strong> Shows the raw input image for reference</li>
                    <li><strong>Segmented Image:</strong> Displays segmentation result with distinct colors</li>
                    <li><strong>Segment Grid View:</strong> Individual segments or overlays for detailed inspection</li>
                </ul>
            </div>
            
            <div class='step-section'>
                <h3 class='step-title'>🎨 Segments & Colors</h3>
                <ul class='feature-list'>
                    <li>Review and manage detected segments with color swatches</li>
                    <li>View percentage coverage for each segment</li>
                    <li>Select segments to merge or highlight for analysis</li>
                </ul>
            </div>
            
            <div class='step-section'>
                <h3 class='step-title'>🔗 Merge Action</h3>
                <ul class='feature-list'>
                    <li>Select multiple segments from the list</li>
                    <li>Click <strong>Merge Selected Segments</strong> to combine them</li>
                    <li>Useful for grouping similar regions or correcting over-segmentation</li>
                </ul>
            </div>
            
            <div class='step-section'>
                <h3 class='step-title'>🎨 Color Palette & Post-processing</h3>
                <ul class='feature-list'>
                    <li><strong>Choose Palettes:</strong> Select from predefined color schemes (e.g., tab20)</li>
                    <li><strong>Pick Colors:</strong> Manually assign colors to specific segments</li>
                    <li><strong>Save Custom Palette:</strong> Save your current color choices for reuse</li>
                    <li><strong>Manage Palettes:</strong> Edit or switch between saved color palettes</li>
                    <li><strong>Select Segments to Display:</strong> Show/hide specific segments</li>
                    <li><strong>Show Full Segmentation:</strong> View complete segmentation overlay</li>
                </ul>
            </div>
            
            <div class='step-section'>
                <h3 class='step-title'>💾 Export & Save Options</h3>
                <ul class='feature-list'>
                    <li><strong>Save Segmented Image:</strong> Export current segmented result as image file</li>
                    <li><strong>Export to COCO Format:</strong> Save in COCO annotation format for ML tasks</li>
                    <li><strong>Export Segments as Annotations:</strong> Generate annotation files for further analysis</li>
                </ul>
            </div>
            
            <div class='overview-section'>
                <h2 style='color: {bg_success}; margin-top: 0; font-size: 1.4em;'>💡 Tips for Best Results</h2>
                <ul class='feature-list'>
                    <li>Start with default parameters and adjust based on your image characteristics</li>
                    <li>Higher training epochs may improve results but increase processing time</li>
                    <li>Use advanced options for fine-tuning on challenging datasets</li>
                    <li>Experiment with different initial segmentation methods for your image type</li>
                </ul>
            </div>
        </div>
        """
        
    def _show_advanced_segmentation_tutorial(self):
        """Return Advanced Segmentation tutorial content as HTML."""
        from src.gui.styles.theme_aware_buttons import get_theme_name
        theme_name = get_theme_name()
        colors = get_tutorial_colors(theme_name)
        bg_primary_color = colors['bg_primary']
        text_primary_color = colors['text_primary']
        accent_color = colors['accent']
        accent_secondary_color = colors['accent_secondary']
        bg_info_color = colors['bg_info']
        accent_color_2 = colors['accent']
        bg_secondary_color = colors['bg_secondary']
        bg_success = colors['bg_success']
        bg_warning = colors.get('bg_warning', colors['bg_info'])

        # FIX: Escaped all literal '{' and '}' characters in the CSS with '{{' and '}}'
        return f"""
        <style>
            @keyframes fadeIn {{ from {{ opacity: 0; }} to {{ opacity: 1; }} }}
            .tutorial-container {{
                --bg-primary: {bg_primary_color};
                --bg-secondary: {bg_secondary_color};
                --bg-info: {bg_info_color};
                --bg-success: {bg_success};
                --bg-warning: {bg_warning};
                --bg-card: {bg_primary_color};
                --bg-code: {bg_secondary_color};
                --bg-key: {bg_secondary_color};
                --text-primary: {text_primary_color};
                --text-secondary: {text_primary_color};
                --text-key: {text_primary_color};
                --accent-color: {accent_color};
                --accent-secondary: {accent_secondary_color};
                --border-color: {accent_color};
                --border-light: {accent_secondary_color};
                --border-key: {accent_color};
                
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                padding: 25px;
                line-height: 1.7;
                background: transparent;
                color: var(--text-primary);
                border-radius: 12px;
                max-width: 900px;
                margin: 0 auto;
                animation: fadeIn 0.5s ease-in-out;
            }}
            .tutorial-header {{
                text-align: center;
                margin-bottom: 35px;
                padding: 25px;
                background: linear-gradient(135deg, {accent_color} 0%, {accent_secondary_color} 100%);
                border-radius: 15px;
                color: white;
                box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            }}
            .tutorial-title {{
                margin: 0;
                font-size: 2.8em;
                font-weight: 700;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                letter-spacing: -0.5px;
            }}
            .tutorial-subtitle {{
                margin: 15px 0 0 0;
                font-size: 1.3em;
                opacity: 0.95;
                font-weight: 300;
            }}
            .overview-section {{
                background: transparent;
                padding: 25px;
                border-radius: 12px;
                margin: 25px 0;
                border-left: 6px solid {accent_color_2};
                box-shadow: none;
            }}
            .step-section {{
                background: transparent;
                padding: 20px;
                border-radius: 10px;
                margin: 25px 0;
                border-left: 5px solid {accent_color_2};
                box-shadow: none;
                transition: transform 0.2s ease;
            }}
            .step-section:hover {{
                transform: translateY(-2px);
            }}
            .step-title {{
                color: var(--text-primary);
                margin-top: 0;
                font-size: 1.4em;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 10px;
            }}
            .step-content {{
                color: var(--text-secondary);
                margin-bottom: 15px;
            }}
            .feature-list {{
                list-style: none;
                padding-left: 0;
            }}
            .feature-list li {{
                margin: 8px 0;
                padding-left: 25px;
                position: relative;
            }}
            .feature-list li:before {{
                content: '▶';
                position: absolute;
                left: 0;
                color: var(--accent-color);
                font-weight: bold;
            }}
            .tips-section {{
                background: var(--bg-success);
                padding: 20px;
                border-radius: 10px;
                border-left: 5px solid var(--accent-color);
                margin: 25px 0;
                box-shadow: 0 3px 12px rgba(0,0,0,0.1);
            }}
        </style>
        <div class='tutorial-container'>
            <div class='tutorial-header'>
                <h1 class='tutorial-title'>🎯 Advanced Segmentation</h1>
                <p class='tutorial-subtitle'>Professional Annotation & Deep Learning Model Training</p>
            </div>
            
            <div class='overview-section'>
                <h2 style='color: {accent_color}; margin-top: 0; font-size: 1.6em;'>📋 What is Advanced Segmentation?</h2>
                <p style='font-size: 1.15em; margin-bottom: 15px;'>
                    The Advanced Segmentation page is a comprehensive annotation and model training environment designed for creating precise object annotations and training custom deep learning models for segmentation tasks.
                </p>
                <p style='font-size: 1.05em;'>
                    <strong>Applications:</strong> Object detection, instance segmentation, custom model training, dataset preparation, and automated inference workflows.
                </p>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_color};'>
                <h3 class='step-title'>📁 Step 1: Loading Images</h3>
                <div class='step-content'>
                    <p>Start by loading images into the Advanced Segmentation workspace:</p>
                    <ul class='feature-list'>
                        <li>Images are automatically loaded from the Project Hub when you navigate to this page</li>
                        <li>The <strong>Image Gallery</strong> on the left shows all available images as thumbnails</li>
                        <li>Click on any thumbnail to select it for annotation</li>
                        <li>Use the "Clear Image Gallery" button to remove all images if needed</li>
                        <li>Supported formats: JPG, PNG, TIFF, BMP</li>
                    </ul>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_secondary_color};'>
                <h3 class='step-title'>🖥️ Step 2: Understanding the Interface</h3>
                <div class='step-content'>
                    <p>The Advanced Segmentation page is organized into three main panels:</p>
                    <ul class='feature-list'>
                        <li><strong>Left Panel:</strong> Image Gallery, Annotation Tools, Class Management, and Annotations List</li>
                        <li><strong>Center Panel:</strong> Tabbed image views with "Annotate" and "Segmentation Results" tabs</li>
                        <li><strong>Right Panel:</strong> Dataset Preparation and Model Training controls</li>
                    </ul>
                    <p style='margin-top: 15px;'>The interface supports synchronized zooming and panning between different views for efficient annotation workflow.</p>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_color_2};'>
                <h3 class='step-title'>🛠️ Step 3: Annotation Tools</h3>
                <div class='step-content'>
                    <p>The <strong>Annotation Tools</strong> section provides various drawing and selection tools:</p>
                    <ul class='feature-list'>
                        <li><strong>Select Tool:</strong> Default tool for selecting and editing existing annotations</li>
                        <li><strong>Polygon Tool:</strong> Draw precise polygon shapes around objects</li>
                        <li><strong>Rectangle Tool:</strong> Create rectangular bounding boxes</li>
                        <li><strong>+ Point Tool:</strong> Add positive (foreground) point prompts for SAM</li>
                        <li><strong>- Point Tool:</strong> Add negative (background) point prompts for SAM</li>
                        <li><strong>Brush Tool:</strong> Paint annotations with adjustable brush size</li>
                        <li><strong>Erase Tool:</strong> Remove parts of annotations with brush</li>
                        <li><strong>Magic Wand:</strong> Intelligent selection based on color similarity</li>
                    </ul>
                    <div style='background: var(--bg-info, {bg_info_color}); padding: 15px; border-radius: 8px; margin: 15px 0;'>
                        <p style='margin: 0; color: {accent_color}; font-weight: 600;'>💡 Tool Tips:</p>
                        <p style='margin: 5px 0 0 0; color: {accent_color};'>Use keyboard shortcuts: S (Select), P (Polygon), R (Rectangle), F (+ Point), B (- Point), M (Magic Wand)</p>
                    </div>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_secondary_color};'>
                <h3 class='step-title'>🎨 Step 4: SAM Integration & Controls</h3>
                <div class='step-content'>
                    <p>Advanced Segmentation integrates with Segment Anything Model (SAM) for intelligent annotation:</p>
                    <ul class='feature-list'>
                        <li><strong>Point Prompts:</strong> Use + and - point tools to guide SAM predictions</li>
                        <li><strong>Accept Button:</strong> Confirm and save the current SAM prediction</li>
                        <li><strong>Reject Button:</strong> Discard the current SAM prediction</li>
                        <li><strong>Reset Points:</strong> Clear all point prompts and start over</li>
                        <li><strong>Brush Size Slider:</strong> Adjust brush size for manual painting and erasing</li>
                    </ul>
                    <p style='margin-top: 15px;'>SAM provides real-time feedback as you add point prompts, making annotation faster and more accurate.</p>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_color};'>
                <h3 class='step-title'>🏷️ Step 5: Class Management</h3>
                <div class='step-content'>
                    <p>Organize your annotations with the <strong>Class Management</strong> system:</p>
                    <ul class='feature-list'>
                        <li><strong>Current Class Selector:</strong> Choose which class to assign to new annotations</li>
                        <li><strong>Default Classes:</strong> Class 1, Class 2, Class 3 are provided by default</li>
                        <li><strong>Manage Classes Button:</strong> Add, remove, rename, and customize class colors</li>
                        <li><strong>Class Colors:</strong> Each class has a unique color for visual distinction</li>
                        <li>All new annotations are automatically assigned to the currently selected class</li>
                    </ul>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_secondary_color};'>
                <h3 class='step-title'>📝 Step 6: Managing Annotations</h3>
                <div class='step-content'>
                    <p>The <strong>Annotations</strong> list provides comprehensive annotation management:</p>
                    <ul class='feature-list'>
                        <li><strong>Annotations List:</strong> View all annotations for the current image</li>
                        <li><strong>Multi-Selection:</strong> Select multiple annotations using Ctrl+Click</li>
                        <li><strong>Edit Button:</strong> Modify selected annotations</li>
                        <li><strong>Remove Button:</strong> Delete selected annotations</li>
                        <li><strong>Clear All:</strong> Remove all annotations from the current image</li>
                        <li><strong>Save/Load Annotations:</strong> Export and import annotation files</li>
                        <li><strong>Quick Save/Load:</strong> Rapid save/load to default project location</li>
                    </ul>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_color_2};'>
                <h3 class='step-title'>📊 Step 7: Dataset Preparation</h3>
                <div class='step-content'>
                    <p>Prepare your annotated data for model training:</p>
                    <ul class='feature-list'>
                        <li><strong>Prepare & Export Dataset:</strong> Comprehensive dataset preparation with splitting and augmentation</li>
                        <li><strong>Data Splitting:</strong> Automatically split data into training, validation, and test sets</li>
                        <li><strong>Data Augmentation:</strong> Apply transformations to increase dataset size</li>
                        <li><strong>Export Formats:</strong> COCO JSON, YOLO, Pascal VOC, and custom formats</li>
                        <li><strong>Import Dataset:</strong> Load existing datasets for continued annotation</li>
                    </ul>
                    <div style='background: var(--bg-success, {bg_success}); padding: 15px; border-radius: 8px; margin: 15px 0;'>
                        <p style='margin: 0; color: {accent_color}; font-weight: 600;'>🚀 Best Practice:</p>
                        <p style='margin: 5px 0 0 0; color: {accent_color};'>Aim for at least 100-200 annotations per class for effective model training.</p>
                    </div>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_color};'>
                <h3 class='step-title'>🤖 Step 8: Model Training & Inference</h3>
                <div class='step-content'>
                    <p>Train and deploy custom deep learning models:</p>
                    <ul class='feature-list'>
                        <li><strong>Model Trainer:</strong> Train Faster R-CNN models on your annotated dataset</li>
                        <li><strong>Training Configuration:</strong> Adjust hyperparameters, epochs, and learning rates</li>
                        <li><strong>Model Inference:</strong> Run trained models on new images for automatic segmentation</li>
                        <li><strong>Model Evaluation:</strong> Assess model performance with validation metrics</li>
                        <li><strong>Refine with SAM:</strong> Use SAM to refine model predictions for higher accuracy</li>
                    </ul>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_secondary_color};'>
                <h3 class='step-title'>🔄 Step 9: Workflow Integration</h3>
                <div class='step-content'>
                    <p>Advanced Segmentation integrates seamlessly with other VisionLab AI modules:</p>
                    <ul class='feature-list'>
                        <li><strong>Project Hub Integration:</strong> Images and annotations are automatically saved to your project</li>
                        <li><strong>Batch Processing:</strong> Apply trained models to large image datasets</li>
                        <li><strong>Export to Analysis:</strong> Send segmented results to Grain Analysis for detailed measurements</li>
                        <li><strong>State Persistence:</strong> All work is automatically saved and restored between sessions</li>
                    </ul>
                    <p style='margin-top: 15px; font-style: italic;'>This comprehensive workflow enables end-to-end custom segmentation solutions tailored to your specific needs.</p>
                </div>
            </div>
        </div>
        """
        
    def _show_trainable_seg_tutorial(self):
        """Return Trainable Segmentation tutorial content as HTML."""
        from src.gui.styles.theme_aware_buttons import get_theme_name
        theme_name = get_theme_name()
        colors = get_tutorial_colors(theme_name)
        bg_primary_color = colors['bg_primary']
        text_primary_color = colors['text_primary']
        accent_color = colors['accent']
        accent_secondary_color = colors['accent_secondary']
        bg_info_color = colors['bg_info']
        bg_secondary_color = colors['bg_secondary']
        bg_success = colors['bg_success']
        bg_warning = colors.get('bg_warning', colors['bg_info'])
        accent_color_2 = colors.get('accent_secondary', colors['accent'])
        
        return f"""
        <style>
            @keyframes fadeIn {{ from {{ opacity: 0; }} to {{ opacity: 1; }} }}
            .tutorial-container {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                padding: 25px;
                line-height: 1.7;
                background: transparent;
                color: {text_primary_color};
                border-radius: 12px;
                max-width: 900px;
                margin: 0 auto;
                animation: fadeIn 0.5s ease-in-out;
                
                /* Define CSS variables with theme colors */
                --bg-primary: {bg_primary_color};
                --bg-secondary: {bg_secondary_color};
                --bg-info: {bg_info_color};
                --bg-success: {bg_success};
                --bg-warning: {bg_warning};
                --bg-card: {bg_secondary_color};
                --bg-code: {colors['bg_code']};
                --bg-key: {bg_secondary_color};
                --text-primary: {text_primary_color};
                --text-secondary: {colors['text_secondary']};
                --text-key: {text_primary_color};
                --border-color: {colors['text_secondary']};
                --border-light: {bg_secondary_color};
                --border-key: {colors['text_secondary']};
                --accent-color: {accent_color};
            }}
            .tutorial-header {{
                text-align: center;
                margin-bottom: 35px;
                padding: 25px;
                background: linear-gradient(135deg, {accent_color} 0%, {accent_secondary_color} 100%);
                border-radius: 15px;
                color: white;
                box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            }}
            .tutorial-title {{
                margin: 0;
                font-size: 2.8em;
                font-weight: 700;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                letter-spacing: -0.5px;
            }}
            .tutorial-subtitle {{
                margin: 15px 0 0 0;
                font-size: 1.3em;
                opacity: 0.95;
                font-weight: 300;
            }}
            .overview-section {{
                background: transparent;
                padding: 25px;
                border-radius: 12px;
                margin: 25px 0;
                border-left: 6px solid {accent_color};
                box-shadow: none;
            }}
            .step-section {{
                background: transparent;
                padding: 20px;
                border-radius: 10px;
                margin: 25px 0;
                border-left: 5px solid {accent_color};
                box-shadow: none;
                transition: transform 0.2s ease;
            }}
            .step-section:hover {{
                transform: translateY(-2px);
            }}
            .step-title {{
                color: {text_primary_color};
                margin-top: 0;
                font-size: 1.4em;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 10px;
            }}
            .feature-list {{
                list-style: none;
                padding-left: 0;
            }}
            .feature-list li {{
                margin: 8px 0;
                padding-left: 25px;
                position: relative;
            }}
            .feature-list li:before {{
                content: '▶';
                position: absolute;
                left: 0;
                color: {accent_color};
                font-weight: bold;
            }}
            .workflow-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 15px;
                margin: 20px 0;
            }}
            .workflow-step {{
                background: transparent;
                padding: 15px;
                border-radius: 8px;
                border-left: 4px solid {accent_color};
                box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            }}
            .workflow-number {{
                color: {accent_color};
                font-weight: bold;
                font-size: 1.2em;
                margin-bottom: 5px;
            }}
            .numbered-list {{
                counter-reset: step-counter;
                list-style: none;
                padding-left: 0;
            }}
            .numbered-list li {{
                counter-increment: step-counter;
                margin: 12px 0;
                padding-left: 35px;
                position: relative;
            }}
            .numbered-list li:before {{
                content: counter(step-counter);
                position: absolute;
                left: 0;
                top: 0;
                width: 25px;
                height: 25px;
                background-color: {accent_color};
                color: white;
                border-radius: 50%;
                text-align: center;
                line-height: 25px;
                font-weight: bold;
            }}
        </style>
        <div class='tutorial-container'>
            <div class='tutorial-header'>
                <h1 class='tutorial-title'>🧠 Trainable Segmentation</h1>
                <p class='tutorial-subtitle'>Create Custom Segmentation Models with Manual Annotations</p>
            </div>
            
            <div class='overview-section'>
                <h2 style='color: {accent_color}; margin-top: 0; font-size: 1.6em;'>🔍 Overview</h2>
                <p style='font-size: 1.15em; margin-bottom: 15px;'>
                    The Trainable Segmentation tool lets you create custom segmentation models by manually annotating regions of interest and training a machine learning classifier.
                </p>
                <ul class='feature-list'>
                    <li><strong>Manually annotate</strong> regions of interest using brushes, erasers, and smart tools</li>
                    <li><strong>Extract features</strong> (intensity, edges, texture) at multiple scales for each pixel</li>
                    <li><strong>Train an XGBoost classifier</strong> on your annotations to generate segmentation maps</li>
                    <li><strong>Visualize feature importance</strong> and adjust parameters for best results</li>
                    <li><strong>Export results</strong> for further analysis or machine learning workflows</li>
                </ul>
            </div>
            
            <div class='step-section'>
                <h3 class='step-title'>📁 Image Gallery</h3>
                <ul class='feature-list'>
                    <li>Browse, select, and manage the set of images for segmentation</li>
                    <li>Add or remove images as needed for your project</li>
                    <li>Click on thumbnails to select the active image for annotation</li>
                </ul>
            </div>
            
            <div class='step-section'>
                <h3 class='step-title'>📋 Annotation & Training Tabs</h3>
                <ul class='feature-list'>
                    <li><strong>Annotate:</strong> Draw labels for each class/region using brush, eraser, magic wand, or point prompt tools</li>
                    <li><strong>Segmentation Result:</strong> View the predicted segmentation map after training</li>
                    <li><strong>Side by Side Comparison:</strong> Compare the original image and segmentation result interactively</li>
                    <li><strong>Feature Importance:</strong> Visualize which features the model used for segmentation</li>
                </ul>
            </div>
            
            <div class='step-section'>
                <h3 class='step-title'>⚙️ Segmentation Parameters</h3>
                <p><strong>Feature Extraction Parameters:</strong></p>
                <ul class='feature-list'>
                    <li><strong>Number of Classes:</strong> Set how many distinct regions or materials you want to segment</li>
                    <li><strong>Intensity:</strong> Use raw pixel intensity as a feature</li>
                    <li><strong>Edges:</strong> Use edge detection features (gradients, Sobel filters)</li>
                    <li><strong>Texture:</strong> Use texture features (local variance, entropy)</li>
                    <li><strong>Sigma Range:</strong> Set the minimum and maximum sigma (scale) for multi-scale feature extraction</li>
                    <li><strong>Number of Scales:</strong> Choose how many scales to compute features across</li>
                </ul>
                <p><strong>XGBoost Model Parameters:</strong></p>
                <ul class='feature-list'>
                    <li><strong>Number of Trees:</strong> Controls the number of boosting rounds (trees)</li>
                    <li><strong>Max Depth:</strong> Maximum tree depth for base learners</li>
                    <li><strong>Max Samples:</strong> Maximum number of samples used for training (for large images)</li>
                </ul>
            </div>
            
            <div class='step-section'>
                <h3 class='step-title'>🖌️ Label Management & Drawing Tools</h3>
                <ul class='feature-list'>
                    <li><strong>Select Label:</strong> Choose which class/region you are annotating</li>
                    <li><strong>Manage Labels:</strong> Add, rename, or remove classes</li>
                    <li><strong>Draw Label / Erase Label:</strong> Use brush tools to mark regions</li>
                    <li><strong>Undo Last / Clear All:</strong> Quickly fix mistakes</li>
                    <li><strong>Brush Size:</strong> Adjust the annotation brush width</li>
                </ul>
            </div>
            
            <div class='step-section'>
                <h3 class='step-title'>🤖 SAM-Assisted Annotation</h3>
                <ul class='feature-list'>
                    <li><strong>Enable SAM:</strong> Toggle Segment Anything Model integration for enhanced annotation</li>
                    <li><strong>Magic Wand:</strong> Draw a bounding box to auto-generate a mask with SAM</li>
                    <li><strong>Point Prompts:</strong> Add positive (foreground) or negative (background) points to refine the mask</li>
                    <li><strong>Accept/Reject:</strong> Approve or discard SAM-generated masks before adding to your annotation</li>
                </ul>
            </div>
            
            <div class='step-section'>
                <h3 class='step-title'>▶️ Training & Segmentation Controls</h3>
                <ul class='feature-list'>
                    <li><strong>Start Training:</strong> Train the XGBoost model using your current annotations and feature parameters</li>
                    <li><strong>Stop Training:</strong> Interrupt training if needed</li>
                    <li><strong>Reload Previous Results:</strong> Load results from earlier sessions</li>
                    <li><strong>Progress Bar:</strong> Monitor training progress</li>
                </ul>
            </div>
            
            <div class='step-section'>
                <h3 class='step-title'>💾 Post-processing & Export</h3>
                <ul class='feature-list'>
                    <li><strong>Export Segmentation:</strong> Save the predicted segmentation map</li>
                    <li><strong>Export to COCO Format:</strong> Save results for machine learning workflows</li>
                    <li><strong>Export Annotations:</strong> Download your manual labels for further use</li>
                </ul>
            </div>
            
            <div class='overview-section'>
                <h2 style='color: {accent_color}; margin-top: 0; font-size: 1.6em;'>🔄 Step-by-Step Workflow</h2>
                <ol class='numbered-list'>
                    <li><strong>Add Images:</strong> Load your images using the Image Gallery on the left</li>
                    <li><strong>Define Labels:</strong> Click 'Manage Labels' to add, rename, or remove classes</li>
                    <li><strong>Annotate:</strong> Select a label and use the brush, eraser, magic wand, or point prompts to mark regions</li>
                    <li><strong>Use SAM (Optional):</strong> Enable SAM for smart annotation with Magic Wand or Point tools</li>
                    <li><strong>Set Parameters:</strong> Select which features to use and adjust sigma range and XGBoost settings</li>
                    <li><strong>Train Classifier:</strong> Click 'Train Classifier' to train the XGBoost model on your annotations</li>
                    <li><strong>Review Results:</strong> Switch to 'Segmentation Result' or 'Side by Side Comparison' to see predictions</li>
                    <li><strong>Refine & Retrain:</strong> If needed, annotate more, tweak parameters, and retrain for better results</li>
                    <li><strong>Export:</strong> Save your segmentation results or annotations using the export options</li>
                </ol>
            </div>
            
            <div class='step-section'>
                <h3 class='step-title'>💡 Practical Tips</h3>
                <ul class='feature-list'>
                    <li><strong>Label clearly:</strong> Mark clean, representative regions for each class</li>
                    <li><strong>Use SAM for efficiency:</strong> For complex shapes, let SAM generate a mask, then refine as needed</li>
                    <li><strong>Tune features:</strong> If results are poor, try enabling/disabling features or adjusting sigma range</li>
                    <li><strong>Check feature importance:</strong> Use this to understand what the model is learning</li>
                    <li><strong>Save your work:</strong> Use Save/Quick Save often to avoid losing progress</li>
                </ul>
            </div>
        </div>
        """
        
    def _show_point_counting_tutorial(self):
        """Return Point Counting tutorial content as HTML."""
        from src.gui.styles.theme_aware_buttons import get_theme_name
        theme_name = get_theme_name()
        colors = get_tutorial_colors(theme_name)
        bg_primary_color = colors['bg_primary']
        text_primary_color = colors['text_primary']
        accent_color = colors['accent']
        accent_secondary_color = colors['accent_secondary']
        bg_info_color = colors['bg_info']
        accent_color_2 = colors['accent']
        bg_secondary_color = colors['bg_secondary']
        bg_success = colors['bg_success']
        bg_warning = colors.get('bg_warning', colors['bg_info'])

        # FIX: Escaped all literal '{' and '}' characters in the CSS with '{{' and '}}'
        return f"""
        <style>
            @keyframes fadeIn {{ from {{ opacity: 0; }} to {{ opacity: 1; }} }}
            .tutorial-container {{
                --bg-primary: {bg_primary_color};
                --bg-secondary: {bg_secondary_color};
                --bg-info: {bg_info_color};
                --bg-success: {bg_success};
                --bg-warning: {bg_warning};
                --bg-card: {bg_primary_color};
                --bg-code: {bg_secondary_color};
                --bg-key: {bg_secondary_color};
                --text-primary: {text_primary_color};
                --text-secondary: {text_primary_color};
                --text-key: {text_primary_color};
                --accent-color: {accent_color};
                --accent-secondary: {accent_secondary_color};
                --border-color: {accent_color};
                --border-light: {accent_secondary_color};
                --border-key: {accent_color};
                
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                padding: 25px;
                line-height: 1.7;
                background: transparent;
                color: var(--text-primary);
                border-radius: 12px;
                max-width: 900px;
                margin: 0 auto;
                animation: fadeIn 0.5s ease-in-out;
            }}
            .tutorial-header {{
                text-align: center;
                margin-bottom: 35px;
                padding: 25px;
                background: linear-gradient(135deg, {accent_color} 0%, {accent_secondary_color} 100%);
                border-radius: 15px;
                color: white;
                box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            }}
            .tutorial-title {{
                margin: 0;
                font-size: 2.8em;
                font-weight: 700;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                letter-spacing: -0.5px;
            }}
            .tutorial-subtitle {{
                margin: 15px 0 0 0;
                font-size: 1.3em;
                opacity: 0.95;
                font-weight: 300;
            }}
            .overview-section {{
                background: transparent;
                padding: 25px;
                border-radius: 12px;
                margin: 25px 0;
                border-left: 6px solid {accent_color_2};
                box-shadow: none;
            }}
            .step-section {{
                background: transparent;
                padding: 20px;
                border-radius: 10px;
                margin: 25px 0;
                border-left: 5px solid {accent_color_2};
                box-shadow: none;
                transition: transform 0.2s ease;
            }}

        .step-section:hover {{
            transform: translateY(-2px);
        }}
        .step-title {{
            color: var(--text-primary);
            margin-top: 0;
            font-size: 1.4em;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }}
        .step-content {{
            color: var(--text-secondary);
            margin-bottom: 15px;
        }}
        .method-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }}
        .method-card {{
            background: var(--bg-card);
            border: 2px solid var(--border-light);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }}
        .method-card:hover {{
            border-color: var(--accent-color);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }}
        .method-title {{
            color: var(--accent-color);
            font-weight: 600;
            font-size: 1.2em;
            margin-bottom: 10px;
        }}
        .tips-section {{
            background: var(--bg-success);
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid var(--accent-color);
            margin: 25px 0;
            box-shadow: 0 3px 12px rgba(0,0,0,0.1);
        }}
        .tips-title {{
            color: var(--accent-color);
            margin-top: 0;
            font-weight: 600;
            font-size: 1.3em;
        }}
        .feature-list {{
            list-style: none;
            padding-left: 0;
        }}
        .feature-list li {{
            margin: 8px 0;
            padding-left: 25px;
            position: relative;
        }}
        .feature-list li:before {{
            content: '▶';
            position: absolute;
            left: 0;
            color: var(--accent-color);
            font-weight: bold;
        }}
        .keyboard-shortcuts {{
            background: var(--bg-code);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }}
        .shortcut-item {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 8px 0;
        }}
        .shortcut-key {{
            background: var(--bg-key);
            border: 1px solid var(--border-key);
            border-radius: 4px;
            padding: 4px 8px;
            font-family: monospace;
            font-weight: bold;
            color: var(--text-key);
        }}

        </style>
        <div class='tutorial-container'>
            <div class='tutorial-header'>
                <h1 class='tutorial-title'>🎯 Point Counting</h1>
                <p class='tutorial-subtitle'>Quantitative Modal Analysis & Mineral Composition</p>
            </div>
            
            <div class='overview-section'>
                <h2 style='color: {accent_color}; margin-top: 0; font-size: 1.6em;'>📋 What is Point Counting?</h2>
                <p style='font-size: 1.15em; margin-bottom: 15px;'>
                    Point counting is a fundamental quantitative technique in petrology, materials science, and microscopy for determining modal composition. By systematically placing points on an image and classifying the material beneath each point, you can calculate statistically accurate volume percentages of different components.
                </p>
                <p style='font-size: 1.05em;'>
                    <strong>Applications:</strong> Thin section analysis, ore microscopy, concrete analysis, soil composition, quality control in manufacturing, and any scenario requiring quantitative compositional analysis.
                </p>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_color};'>
                <h3 class='step-title'>📁 Step 1: Load and Prepare Images</h3>
                <div class='step-content'>
                    <p><strong>Supported Formats:</strong> High-resolution microscopy images (JPG, PNG, TIFF, BMP)</p>
                    <ul class='feature-list'>
                        <li>Navigate to the <strong>Point Counting</strong> page from the main menu</li>
                        <li>Load images from the <strong>Project Hub</strong> or drag-and-drop directly</li>
                        <li>Images appear in the gallery at the bottom - click to select for analysis</li>
                        <li>Ensure proper calibration and focus before analysis</li>
                        <li>Consider image preprocessing (contrast enhancement, noise reduction) if needed</li>
                    </ul>
                    <p><strong>💡 Image Quality Requirements:</strong></p>
                    <ul class='feature-list'>
                        <li>High resolution (minimum 1024x1024 pixels recommended)</li>
                        <li>Good contrast between different phases/minerals</li>
                        <li>Uniform illumination across the field of view</li>
                        <li>Sharp focus with minimal optical artifacts</li>
                    </ul>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_secondary_color};'>
                <h3 class='step-title'>⚙️ Step 2: Choose Point Generation Method</h3>
                <div class='step-content'>
                    <p style='margin-bottom: 20px;'>Select the optimal point placement strategy for your analysis:</p>
                    
                    <div class='method-grid'>
                        <div class='method-card'>
                            <h4 class='method-title'>🔲 Grid Method</h4>
                            <p><strong>Best for:</strong> Systematic sampling, reproducible results</p>
                            <ul class='feature-list'>
                                <li>Automatically places points on a regular grid</li>
                                <li>Adjust <strong>Grid Size</strong> (spacing between points)</li>
                                <li>Set <strong>Offset</strong> to avoid edge effects</li>
                                <li>Ensures even spatial distribution</li>
                                <li>Recommended for most applications</li>
                            </ul>
                        </div>
                        
                        <div class='method-card'>
                            <h4 class='method-title'>🎲 Random Method</h4>
                            <p><strong>Best for:</strong> Unbiased sampling, statistical analysis</p>
                            <ul class='feature-list'>
                                <li>Places points randomly across the image</li>
                                <li>Set <strong>Number of Points</strong> (typically 300-1000)</li>
                                <li>Use <strong>Random Seed</strong> for reproducibility</li>
                                <li>Eliminates systematic bias</li>
                                <li>Good for heterogeneous samples</li>
                            </ul>
                        </div>
                        
                <div class='method-card'>
                    <h4 class='method-title'>👆 Manual Click</h4>
                    <p><strong>Best for:</strong> Targeted analysis, specific features</p>
                    <ul class='feature-list'>
                        <li>No automatic point generation</li>
                        <li>Click directly on features of interest</li>
                                <li>Full control over point placement</li>
                                <li>Ideal for detailed examination</li>
                                <li>Can combine with other methods</li>
                            </ul>
                        </div>
                    </div>
                    
                    <p style='margin-top: 20px;'><strong>After configuring parameters, click <span style='color: {accent_color_2}; font-weight: bold;'>Generate Points</span> to place them on your image.</strong></p>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_color_2};'>
                <h3 class='step-title'>🏷️ Step 3: Manage Classification Classes</h3>
                <div class='step-content'>
                    <p>Set up your mineral/component classification system:</p>
                    <ul class='feature-list'>
                        <li><strong>Add Classes:</strong> Create categories for each mineral, phase, or component type</li>
                        <li><strong>Rename Classes:</strong> Use standard mineralogical or material names</li>
                        <li><strong>Color Assignment:</strong> Assign distinct colors for easy visual identification</li>
                        <li><strong>Class Management:</strong> Remove unused classes to streamline workflow</li>
                        <li><strong>Active Class Selection:</strong> Choose which class to assign to points</li>
                    </ul>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_secondary_color};'>
                <h3 class='step-title'>🎯 Step 4: Classify Points Efficiently</h3>
                <div class='step-content'>
                    <p>Navigate through points and assign classifications:</p>
                    
                    <div class='keyboard-shortcuts'>
                        <h4 style='margin-top: 0; color: {accent_color};'>⌨️ Keyboard Shortcuts</h4>
                        <div class='shortcut-item'>
                            <span>Next Point</span>
                            <span class='shortcut-key'>Spacebar</span>
                        </div>
                        <div class='shortcut-item'>
                            <span>Previous Point</span>
                            <span class='shortcut-key'>Backspace</span>
                        </div>
                        <div class='shortcut-item'>
                            <span>Classify Point (Class 1-9)</span>
                            <span class='shortcut-key'>1-9</span>
                        </div>
                        <div class='shortcut-item'>
                            <span>Toggle Quick Selector</span>
                            <span class='shortcut-key'>Q</span>
                        </div>
                    </div>
                    
                    <ul class='feature-list'>
                        <li><strong>Point Navigation:</strong> Current point highlighted with transparent circle</li>
                        <li><strong>Quick Classification:</strong> Use number keys (1-9) for rapid assignment</li>
                        <li><strong>Visual Feedback:</strong> Points change color immediately upon classification</li>
                        <li><strong>Manual Addition:</strong> In Manual Click mode, select class then click to add+classify</li>
                        <li><strong>Progress Tracking:</strong> Monitor classification progress in real-time</li>
                    </ul>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {bg_success};'>
                <h3 class='step-title'>🤖 Step 5: AI-Assisted Semi-Auto Classification</h3>
                <div class='step-content'>
                    <p>Leverage machine learning to accelerate your analysis:</p>
                    <ul class='feature-list'>
                        <li><strong>Training Phase:</strong> Manually classify 20-50 representative points per class</li>
                        <li><strong>Feature Analysis:</strong> AI analyzes color, texture, and spatial patterns at each point</li>
                        <li><strong>Auto-Classification:</strong> Click 'Semi-Auto Classify' to process remaining points</li>
                        <li><strong>Quality Review:</strong> Examine AI assignments and correct any errors</li>
                        <li><strong>Iterative Improvement:</strong> Re-run with additional manual examples if needed</li>
                        <li><strong>Undo Function:</strong> Revert all AI assignments if results are unsatisfactory</li>
                    </ul>
                    
                    <div style='background: {bg_warning}; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid {accent_color_2};'>
                        <p style='margin: 0; color: {text_primary_color}; font-weight: 600;'>⚠️ Important:</p>
                        <p style='margin: 5px 0 0 0; color: var(--text-primary, {text_primary_color});'>AI accuracy depends on image quality and training examples. Always review and validate automated classifications, especially for critical analyses.</p>
                    </div>
                </div>
            </div>
            </div>
            
            <div style='background: {bg_secondary_color}; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid {accent_secondary_color};'>
                <h3 style='color: {text_primary_color}; margin-top: 0;'>📊 Step 6: View and Export Results</h3>
                <ul style='color: {text_primary_color}99;'>
                    <li>The <strong>Summary</strong> panel shows the count for each class</li>
                    <li>The <strong>Class Distribution</strong> chart visualizes the percentages</li>
                    <li>Click <strong>Export Points (CSV)</strong> to save a file containing the coordinates and class of every point for external analysis</li>
                </ul>
            </div>
        </div>
        """
        
    def _show_grain_analysis_tutorial(self):
        """Return Grain Analysis tutorial content as HTML."""
        from src.gui.styles.theme_aware_buttons import get_theme_name
        theme_name = get_theme_name()
        colors = get_tutorial_colors(theme_name)
        bg_primary_color = colors['bg_primary']
        text_primary_color = colors['text_primary']
        accent_color = colors['accent']
        accent_secondary_color = colors['accent_secondary']
        bg_info_color = colors['bg_info']
        bg_secondary_color = colors['bg_secondary']
        bg_success = colors['bg_success']
        bg_warning = colors.get('bg_warning', colors['bg_info'])
        
        return f"""
        <style>
        .tutorial-container {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 25px;
            line-height: 1.7;
            background: transparent;
            color: {text_primary_color};
            border-radius: 12px;
            max-width: 900px;
            margin: 0 auto;
            
            /* Define CSS variables with theme colors */
            --bg-primary: {bg_primary_color};
            --bg-secondary: {bg_secondary_color};
            --bg-info: {bg_info_color};
            --bg-success: {bg_success};
            --bg-warning: {bg_warning};
            --bg-card: {bg_secondary_color};
            --bg-code: {colors['bg_code']};
            --bg-key: {bg_secondary_color};
            --text-primary: {text_primary_color};
            --text-secondary: {colors['text_secondary']};
            --text-key: {text_primary_color};
            --border-color: {colors['text_secondary']};
            --border-light: {bg_secondary_color};
            --border-key: {colors['text_secondary']};
            --accent-color: {accent_color};
        }}
        .tutorial-header {{
            text-align: center;
            margin-bottom: 35px;
            padding: 25px;
            background: linear-gradient(135deg, {accent_color} 0%, {accent_secondary_color} 100%);
            border-radius: 15px;
            color: white;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }}
        .tutorial-title {{
            margin: 0;
            font-size: 2.8em;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            letter-spacing: -0.5px;
        }}
        .tutorial-subtitle {{
            margin: 15px 0 0 0;
            font-size: 1.3em;
            opacity: 0.95;
            font-weight: 300;
        }}
        .overview-section {{
            background: transparent;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 6px solid #e67e22;
            box-shadow: none;
        }}
        .step-section {{
            background: transparent;
            padding: 20px;
            border-radius: 10px;
            margin: 25px 0;
            border-left: 5px solid var(--accent-color, #e67e22);
            box-shadow: none;
            transition: transform 0.2s ease;
        }}
        .step-section:hover {{
            transform: translateY(-2px);
        }}
        .step-title {{
            color: var(--text-primary, {text_primary_color});
            margin-top: 0;
            font-size: 1.4em;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }}
        .step-content {{
            color: var(--text-secondary, #34495e);
            margin-bottom: 15px;
        }}
        .analysis-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }}
        .analysis-card {{
            background: var(--bg-card, #ffffff);
            border: 2px solid var(--border-light, #e0e6ed);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }}
        .analysis-card:hover {{
            border-color: #e67e22;
            box-shadow: 0 4px 15px rgba(230, 126, 34, 0.2);
        }}
        .analysis-title {{
            color: #e67e22;
            font-weight: 600;
            font-size: 1.2em;
            margin-bottom: 10px;
        }}
        .feature-list {{
            list-style: none;
            padding-left: 0;
        }}
        .feature-list li {{
            margin: 8px 0;
            padding-left: 25px;
            position: relative;
        }}
        .feature-list li:before {{
            content: '▶';
            position: absolute;
            left: 0;
            color: var(--accent-color, #e67e22);
            font-weight: bold;
        }}
        .workflow-steps {{
            background: var(--bg-code, #f4f4f4);
            border: 1px solid var(--border-color, #ddd);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }}
        .workflow-step {{
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 10px;
            background: var(--bg-step, #ffffff);
            border-radius: 6px;
            border-left: 4px solid #e67e22;
        }}
        .step-number {{
            background: #e67e22;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }}

        </style>
        <div class='tutorial-container'>
            <div class='tutorial-header'>
                <h1 class='tutorial-title'>🤖 Grain Analysis</h1>
                <p class='tutorial-subtitle'>AI-Powered Image Segmentation for Grain Detection</p>
            </div>
            
            <div class='overview-section'>
                <h2 style='color: {accent_color}; margin-top: 0; font-size: 1.6em;'>📋 What is Grain An?</h2>
                <p style='font-size: 1.15em; margin-bottom: 15px;'>
                    The Grain Analysis page provides AI-powered image segmentation tools for identifying and delineating individual grains or particles in microscopic images. Using advanced deep learning models (FastSAM and MobileSAM), it automatically detects grain boundaries and creates segmentation masks.
                </p>
                <p style='font-size: 1.05em;'>
                    <strong>Applications:</strong> Automated segmentation of grains in thin section images, SEM images, and other microscopic imagery for geological, materials science, and research applications.
                </p>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_color};'>
                <h3 class='step-title'>🤖 AI Segmentation Models Available</h3>
                <div class='step-content'>
                    <div class='analysis-grid'>
                        <div class='analysis-card'>
                            <h4 class='analysis-title'>⚡ FastSAM</h4>
                            <ul class='feature-list'>
                                <li><strong>Speed:</strong> Fast inference for real-time segmentation</li>
                                <li><strong>Accuracy:</strong> High-quality boundary detection</li>
                                <li><strong>Parameters:</strong> Adjustable confidence and IoU thresholds</li>
                                <li><strong>Output:</strong> Precise grain boundary masks</li>
                                <li><strong>Best For:</strong> Quick analysis and batch processing</li>
                            </ul>
                        </div>
                        
                        <div class='analysis-card'>
                            <h4 class='analysis-title'>🎯 MobileSAM</h4>
                            <ul class='feature-list'>
                                <li><strong>Precision:</strong> High-accuracy segmentation</li>
                                <li><strong>Interactive:</strong> Point-based refinement capabilities</li>
                                <li><strong>Flexibility:</strong> Multiple segmentation strategies</li>
                                <li><strong>Quality:</strong> Superior boundary delineation</li>
                                <li><strong>Best For:</strong> Detailed analysis requiring precision</li>
                            </ul>
                        </div>
                        
                        <div class='analysis-card'>
                            <h4 class='analysis-title'>📊 Basic Statistics</h4>
                            <ul class='feature-list'>
                                <li><strong>Grain Count:</strong> Total number of detected grains</li>
                                <li><strong>Area Measurements:</strong> Individual grain areas</li>
                                <li><strong>Size Distribution:</strong> Basic size statistics</li>
                                <li><strong>Export Options:</strong> CSV data and COCO annotations</li>
                                <li><strong>Visualization:</strong> Overlay masks on original images</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_secondary_color};'>
                <h3 class='step-title'>🔄 Complete Workflow</h3>
                <div class='step-content'>
                    <div class='workflow-steps'>
                        <div class='workflow-step'>
                            <div class='step-number'>1- <strong>Load Image:</strong> <div>
                            <div>
                                Upload your microscopic image (thin sections, SEM images, or other grain imagery) using the file browser.
                            </div>
                        </div>
                        
                        <div class='workflow-step'>
                            <div class='step-number'>2- <strong>Select AI Model:</strong> </div>
                            <div>
                                Choose between FastSAM (faster processing) or MobileSAM (higher precision) based on your requirements.
                            </div>
                        </div>
                        
                        <div class='workflow-step'>
                            <div class='step-number'>3- <strong>Load Model:</strong> </div>
                            <div>
                                Initialize the selected segmentation model. The system will download and load the AI model if not already available.
                            </div>
                        </div>
                        
                        <div class='workflow-step'>
                            <div class='step-number'>4- <strong>Adjust Parameters:</strong> </div>
                            <div>
                                 Fine-tune model-specific parameters for optimal segmentation quality:
                                <ul style='margin: 8px 0; padding-left: 20px;'>
                                    <li><strong>FastSAM:</strong>
                                        <ul style='margin: 4px 0; padding-left: 15px; font-size: 0.95em;'>
                                            <li><em>Confidence Threshold (0.05-0.95):</em> Minimum confidence level for detecting objects - higher values = fewer but more certain detections</li>
                                            <li><em>IoU Threshold (0.1-0.95):</em> Overlap threshold for merging similar detections - higher values = less merging of overlapping objects</li>
                                            <li><em>Input Size (256-2048):</em> Image resolution for processing - larger sizes = better detail but slower processing</li>
                                            <li><em>Max Objects:</em> Maximum number of grains to detect in the image</li>
                                        </ul>
                                    </li>
                                    <li><strong>MobileSAM:</strong>
                                        <ul style='margin: 4px 0; padding-left: 15px; font-size: 0.95em;'>
                                            <li><em>Points Per Side (8-64):</em> Number of sampling points along each image edge - more points = finer segmentation but slower processing</li>
                                            <li><em>Prediction IoU Threshold (0.5-0.99):</em> Quality threshold for accepting segmentation masks - higher values = stricter quality control</li>
                                            <li><em>Stability Score Threshold (0.5-0.99):</em> Consistency requirement for mask predictions - higher values = more stable but potentially fewer detections</li>
                                            <li><em>Crop Layers (0-3):</em> Number of image subdivision levels for detailed analysis - more layers = better small object detection</li>
                                        </ul>
                                    </li>
                                    <li><strong>Artifact Handling:</strong> Intelligent removal of border artifacts, duplicates, and noise with adjustable sensitivity (Conservative/Balanced/Aggressive presets)</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class='workflow-step'>
                            <div class='step-number'>5- <strong>Run Segmentation:</strong> </div>
                            <div>
                                Process the image to automatically detect and delineate grain boundaries using the AI model.
                            </div>
                        </div>
                        
                        <div class='workflow-step'>
                            <div class='step-number'>6- <strong>Export Results:</strong> </div>
                            <div>
                                Save segmentation masks, basic statistics (CSV), or COCO annotation format for further analysis. Generate comprehensive statistical reports including grain size distribution, area measurements, and morphological parameters for quantitative analysis.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {bg_success};'>
                <h3 class='step-title'>📋 Output & Results</h3>
                <div class='step-content'>
                    <p>The grain analysis tool generates the following outputs:</p>
                    <ul class='feature-list'>
                        <li><strong>Segmentation Masks:</strong> Binary masks showing detected grain boundaries overlaid on original images</li>
                        <li><strong>Grain Count:</strong> Total number of grains detected by the AI model after intelligent artifact filtering</li>
                        <li><strong>Statistical Reports:</strong> Comprehensive grain size distribution, area measurements, and morphological parameters (CSV format)</li>
                        <li><strong>COCO Annotations:</strong> Industry-standard annotation format for machine learning workflows</li>
                        <li><strong>Visual Overlays:</strong> Interactive visualization of segmentation results on the original image</li>
                        <li><strong>Artifact Detection:</strong> Automatic removal of border artifacts, duplicates, and noise using intelligent filtering algorithms</li>
                        <li><strong>Model Performance:</strong> Processing time and confidence metrics for quality assessment</li>
                    </ul>
                    
                    <div style='background: {bg_info_color}; padding: 15px; border-radius: 8px; margin: 15px 0;'>
                        <p style='margin: 0; color: {accent_color}; font-weight: 600;'>💡 Pro Tip:</p>
                <p style='margin: 5px 0 0 0; color: var(--text-primary, {text_primary_color});'>For optimal segmentation results, use high-contrast images with clear grain boundaries. Adjust model parameters based on your specific image characteristics.</p>
                    </div>
                </div>
            </div>
        </div>
        """
        

        
    def _show_batch_processing_tutorial(self):
        """Return Unsupervised Segmentation tutorial content as HTML."""
        from src.gui.styles.theme_aware_buttons import get_theme_name
        theme_name = get_theme_name()
        colors = get_tutorial_colors(theme_name)
        bg_primary_color = colors['bg_primary']
        text_primary_color = colors['text_primary']
        accent_color = colors['accent']
        accent_secondary_color = colors['accent_secondary']
        bg_info_color = colors['bg_info']
        bg_secondary_color = colors['bg_secondary']
        bg_success = colors['bg_success']
        bg_warning = colors.get('bg_warning', colors['bg_info'])
        accent_color_2 = colors.get('accent_secondary', colors['accent'])
        
        return f"""
        <style>
            @keyframes fadeIn {{ from {{ opacity: 0; }} to {{ opacity: 1; }} }}
            .tutorial-container {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                padding: 25px;
                line-height: 1.7;
                background: transparent;
                color: {text_primary_color};
                border-radius: 12px;
                max-width: 900px;
                margin: 0 auto;
                animation: fadeIn 0.5s ease-in-out;
                
                /* Define CSS variables with theme colors */
                --bg-primary: {bg_primary_color};
                --bg-secondary: {bg_secondary_color};
                --bg-info: {bg_info_color};
                --bg-success: {bg_success};
                --bg-warning: {bg_warning};
                --bg-card: {bg_secondary_color};
                --bg-code: {colors['bg_code']};
                --bg-key: {bg_secondary_color};
                --text-primary: {text_primary_color};
                --text-secondary: {colors['text_secondary']};
                --text-key: {text_primary_color};
                --border-color: {colors['text_secondary']};
                --border-light: {bg_secondary_color};
                --border-key: {colors['text_secondary']};
                --accent-color: {accent_color};
            }}
            .tutorial-header {{
                text-align: center;
                margin-bottom: 35px;
                padding: 25px;
                background: linear-gradient(135deg, {accent_color} 0%, {accent_secondary_color} 100%);
                border-radius: 15px;
                color: white;
                box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            }}
            }}
        .tutorial-title {{
                margin: 0;
                font-size: 2.8em;
                font-weight: 700;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                letter-spacing: -0.5px;
            }}
            .tutorial-subtitle {{
                margin: 15px 0 0 0;
                font-size: 1.3em;
                opacity: 0.95;
                font-weight: 300;
            }}
            .overview-section {{
                background: transparent;
                padding: 25px;
                border-radius: 12px;
                margin: 25px 0;
                border-left: 6px solid {accent_color};
                box-shadow: none;
            }}
            .step-section {{
                background: transparent;
                padding: 20px;
                border-radius: 10px;
                margin: 25px 0;
                border-left: 5px solid {accent_color};
                box-shadow: 0 3px 12px rgba(0,0,0,0.08);
                transition: transform 0.2s ease, box-shadow 0.2s ease;
            }}
            .step-section:hover {{
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(0,0,0,0.12);
            }}
            .step-title {{
                color: {text_primary_color};
                margin-top: 0;
                font-size: 1.4em;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 10px;
            }}
            .feature-list {{
                list-style: none;
                padding-left: 0;
            }}
            .feature-list li {{
                margin: 8px 0;
                padding-left: 25px;
                position: relative;
            }}
            .feature-list li:before {{
                content: '▶';
                position: absolute;
                left: 0;
                color: {accent_color};
                font-weight: bold;
            }}
            .workflow-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 15px;
                margin: 20px 0;
            }}
            .workflow-step {{
                background: transparent;
                padding: 15px;
                border-radius: 8px;
                border-left: 4px solid {accent_color};
                box-shadow: none;
            }}
            .workflow-number {{
                color: {accent_color};
                font-weight: bold;
                font-size: 1.2em;
            }}
        </style>
        <div class='tutorial-container'>
            <div class='tutorial-header'>
                <h1 class='tutorial-title'>Automated Multi-Image Analysis </h1>
                <p class='tutorial-subtitle'>Batch Processing & Workflow Automation</p>
            </div>
            
            <div class='overview-section'>
                <h2 style='color: {accent_color}; margin-top: 0; font-size: 1.6em;'>🚀 What is Batch Processing?</h2>
                <p style='font-size: 1.15em; margin-bottom: 15px;'>
                    Batch Processing allows you to automatically analyze multiple images using the same parameters and methods. Perfect for processing large datasets with consistent analysis workflows, ensuring reproducible results across your entire image collection.
                    <strong>Supported Analysis Types:</strong> Grain Size Analysis, Trainable Segmentation, and Advanced Segmentation with FastSAM or MobileSAM models.
                </p>
                </div>
            </div>
            
            <div class='step-section'>
                <h3 class='step-title'>📋 How to Use Batch Processing</h2>
                <ul class='feature-list'>
                    <li><strong>Load Images:<strong> Import multiple images into your project using the "Import Images" button. Supports TIFF, PNG, JPG formats.</li>
                    <li><strong>Select Images:<strong> In the Project Hub, select multiple images from your gallery that you want to process together.</li>
                    <li><strong>Batch Process:<strong> With images selected, click the "Batch Process" button to open the Batch Processing page.</li>
                    <li><strong>Analysis Type:<strong> Select from Grain Size Analysis, Trainable Segmentation, or Advanced Segmentation based on your research needs.</li>
                    <li><strong>Parameters:<strong> Set segmentation method (FastSAM/MobileSAM), scale factors, detection thresholds, and quality control settings.</li>
                    <li><strong>Start Processing: <strong> Click "Start Batch" to begin automated processing. Monitor progress with real-time updates and logs.</li>
                </ul>
            </div>
            
            <div class='step-section'>
                <h3 class='step-title'>⚙️ Available Analysis Methods</h3>
                <ul class='feature-list'>
                    <li><strong>Grain Size Analysis:</strong> Automated grain detection and size measurement using FastSAM or MobileSAM models</li>
                    <li><strong>Trainable Segmentation:</strong> Apply custom trained models for specialized segmentation tasks</li>
                    <li><strong>Advanced Segmentation:</strong> High-precision segmentation with configurable parameters and artifact detection</li>
                    <li><strong>Scale Factor Support:</strong> Uniform scaling or individual scale factors per image for accurate measurements</li>
                    <li><strong>Intelligent Artifact Detection:</strong> Automatic removal of border artifacts and duplicate segments</li>
                    <li><strong>Quality Control:</strong> Configurable thresholds for IoU, stability scores, and minimum region areas</li>
                </ul>
            </div>
            
            <div class='step-section'>
                <h3 class='step-title'>📊 Monitoring & Results</h3>
                <ul class='feature-list'>
                    <li><strong>Real-time Progress:</strong> Live progress bars showing current image and overall completion status</li>
                    <li><strong>Detailed Logging:</strong> Comprehensive logs with processing details, errors, and performance metrics</li>
                    <li><strong>Error Handling:</strong> Automatic error recovery with detailed error reporting for failed images</li>
                    <li><strong>Results Export:</strong> Export processing results to CSV files and generate comprehensive reports</li>
                    <li><strong>Stop/Resume:</strong> Ability to stop processing at any time and resume later</li>
                    <li><strong>Resource Management:</strong> Optimized memory usage and GPU/CPU utilization</li>
                </ul>
            </div>
            
            <div class='overview-section'>
                <h2 style='color: {bg_success}; margin-top: 0; font-size: 1.4em;'>💡 Pro Tips</h2>
                <ul class='feature-list'>
                    <li>Test parameters on a single image first before batch processing</li>
                    <li>Use MobileSAM for GPU acceleration, FastSAM for CPU-only systems</li>
                    <li>Enable artifact detection for cleaner segmentation results</li>
                    <li>Save processing logs for reproducibility and troubleshooting</li>
                </ul>
            </div>
        </div>
        """
        
    def _show_image_lab_tutorial(self):
        """Return Image Lab tutorial content as HTML."""
        from src.gui.styles.theme_aware_buttons import get_theme_name
        theme_name = get_theme_name()
        colors = get_tutorial_colors(theme_name)
        bg_primary_color = colors['bg_primary']
        text_primary_color = colors['text_primary']
        accent_color = colors['accent']
        accent_secondary_color = colors['accent_secondary']
        bg_info_color = colors['bg_info']
        accent_color_2 = colors['accent']
        bg_secondary_color = colors['bg_secondary']
        bg_success = colors['bg_success']
        bg_warning = colors.get('bg_warning', colors['bg_info'])

        # FIX: Escaped all literal '{' and '}' characters in the CSS with '{{' and '}}'
        return f"""
        <style>
            @keyframes fadeIn {{ from {{ opacity: 0; }} to {{ opacity: 1; }} }}
            .tutorial-container {{
                --bg-primary: {bg_primary_color};
                --bg-secondary: {bg_secondary_color};
                --bg-info: {bg_info_color};
                --bg-success: {bg_success};
                --bg-warning: {bg_warning};
                --bg-card: {bg_primary_color};
                --bg-code: {bg_secondary_color};
                --bg-key: {bg_secondary_color};
                --text-primary: {text_primary_color};
                --text-secondary: {text_primary_color};
                --text-key: {text_primary_color};
                --accent-color: {accent_color};
                --accent-secondary: {accent_secondary_color};
                --border-color: {accent_color};
                --border-light: {accent_secondary_color};
                --border-key: {accent_color};
                
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                padding: 25px;
                line-height: 1.7;
                background: transparent;
                color: var(--text-primary);
                border-radius: 12px;
                max-width: 900px;
                margin: 0 auto;
                animation: fadeIn 0.5s ease-in-out;
            }}
            .tutorial-header {{
                text-align: center;
                margin-bottom: 35px;
                padding: 25px;
                background: linear-gradient(135deg, {accent_color} 0%, {accent_secondary_color} 100%);
                border-radius: 15px;
                color: white;
                box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            }}
            .tutorial-title {{
                margin: 0;
                font-size: 2.8em;
                font-weight: 700;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                letter-spacing: -0.5px;
            }}
            .tutorial-subtitle {{
                margin: 15px 0 0 0;
                font-size: 1.3em;
                opacity: 0.95;
                font-weight: 300;
            }}
            .overview-section {{
                background: transparent;
                padding: 25px;
                border-radius: 12px;
                margin: 25px 0;
                border-left: 6px solid {accent_color_2};
                box-shadow: none;
            }}
            .step-section {{
                background: transparent;
                padding: 20px;
                border-radius: 10px;
                margin: 25px 0;
                border-left: 5px solid {accent_color_2};
                box-shadow: none;
                transition: transform 0.2s ease;
            }}
            .step-section:hover {{
                transform: translateY(-2px);
            }}
            .step-title {{
                color: var(--text-primary);
                margin-top: 0;
                font-size: 1.4em;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 10px;
            }}
            .step-content {{
                color: var(--text-secondary);
                margin-bottom: 15px;
            }}
            .feature-list {{
                list-style: none;
                padding-left: 0;
            }}
            .feature-list li {{
                margin: 8px 0;
                padding-left: 25px;
                position: relative;
            }}
            .feature-list li:before {{
                content: '▶';
                position: absolute;
                left: 0;
                color: var(--accent-color);
                font-weight: bold;
            }}
            .tips-section {{
                background: var(--bg-success);
                padding: 20px;
                border-radius: 10px;
                border-left: 5px solid var(--accent-color);
                margin: 25px 0;
                box-shadow: 0 3px 12px rgba(0,0,0,0.1);
            }}
        </style>
        <div class='tutorial-container'>
            <div class='tutorial-header'>
                <h1 class='tutorial-title'>🔬 Image Lab</h1>
                <p class='tutorial-subtitle'>Advanced Image Processing & Enhancement</p>
            </div>
            
            <div class='overview-section'>
                <h2 style='color: {accent_color}; margin-top: 0; font-size: 1.6em;'>📋 What is Image Lab?</h2>
                <p style='font-size: 1.15em; margin-bottom: 15px;'>
                    The Image Lab is a comprehensive image processing toolkit designed for scientific and technical image analysis. It provides advanced enhancement, filtering, and transformation capabilities to prepare your images for analysis or improve their visual quality.
                </p>
                <p style='font-size: 1.05em;'>
                    <strong>Applications:</strong> Microscopy image enhancement, preprocessing for segmentation, noise reduction, contrast improvement, and batch processing workflows.
                </p>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_color};'>
                <h3 class='step-title'>📁 Step 1: Loading Images</h3>
                <div class='step-content'>
                    <p>To get started, you need to load images into the Image Lab:</p>
                    <ul class='feature-list'>
                        <li>Drag and drop image files directly into the application</li>
                        <li>Use the "Load Images" button in the Project Hub</li>
                        <li>Supported formats: JPG, PNG, TIFF, BMP</li>
                        <li>Images appear as thumbnails in the <strong>Image Gallery</strong> on the left panel</li>
                        <li>Click on any thumbnail to select it for processing</li>
                    </ul>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_secondary_color};'>
                <h3 class='step-title'>🖥️ Step 2: Understanding the Interface</h3>
                <div class='step-content'>
                    <p>The Image Lab page is organized into three main sections:</p>
                    <ul class='feature-list'>
                        <li><strong>Left Panel:</strong> Image Gallery and all processing controls</li>
                        <li><strong>Center Panel:</strong> Image display with "Side by Side" and "Full Processed View" options</li>
                        <li><strong>Right Panel:</strong> Image information and interactive histogram</li>
                    </ul>
                    <p style='margin-top: 15px;'>Switch between view modes to compare original and processed images effectively.</p>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_color_2};'>
                <h3 class='step-title'>⚡ Step 3: Basic Enhancements</h3>
                <div class='step-content'>
                    <p>The <strong>Basic Enhancement</strong> section provides quick image improvements:</p>
                    <ul class='feature-list'>
                        <li><strong>Histogram Equalization:</strong> Automatically enhances image contrast</li>
                        <li><strong>Auto Contrast:</strong> Stretches histogram to use full intensity range</li>
                        <li><strong>Brightness and Contrast:</strong> Manual adjustment using sliders</li>
                        <li><strong>Undo Last:</strong> Reverts the most recent enhancement operation</li>
                    </ul>
                    <div style='background: var(--bg-info, {bg_info_color}); padding: 15px; border-radius: 8px; margin: 15px 0;'>
                        <p style='margin: 0; color: {accent_color}; font-weight: 600;'>💡 Pro Tip:</p>
                        <p style='margin: 5px 0 0 0; color: {accent_color};'>Start with Auto Contrast for most images, then fine-tune with manual brightness/contrast adjustments.</p>
                    </div>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_secondary_color};'>
                <h3 class='step-title'>🔧 Step 4: Advanced Enhancements</h3>
                <div class='step-content'>
                    <p>For precise control, use the <strong>Advanced Enhancement</strong> section:</p>
                    <ul class='feature-list'>
                        <li><strong>Gamma Correction:</strong> Adjust image luminance and midtone response</li>
                        <li><strong>Morphological Operations:</strong> Erosion, dilation, opening, and closing</li>
                        <li>Modify object shapes and remove noise artifacts</li>
                        <li>Particularly useful for binary and grayscale images</li>
                    </ul>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_color};'>
                <h3 class='step-title'>🎛️ Step 5: Filtering Options</h3>
                <div class='step-content'>
                    <p>The <strong>Filtering</strong> section offers various image filters:</p>
                    <ul class='feature-list'>
                        <li><strong>Sharpen:</strong> Enhances image sharpness and edge definition</li>
                        <li><strong>Blur Methods:</strong> Gaussian, Median, and Bilateral filtering</li>
                        <li><strong>Edge Detection:</strong> Sobel and Laplacian filters for edge enhancement</li>
                        <li>Choose filters based on your specific image analysis needs</li>
                    </ul>
                </div>
            </div>

            <div class='step-section' style='border-left-color: {accent_color};'>
                <h3 class='step-title'>🎛️ Step 6: Thresholding</h3>
                <div class='step-content'>
                    <p>The <strong>Thresholding</strong> section support various thresholding methods:</p>
                    <ul class='feature-list'>
                        <li><strong>Binary:</strong> Applies a binary threshold to convert image to black and white</li>
                        <li><strong>Adaptive:</strong> Utilizes local thresholding for varying lighting conditions</li>
                        <li><strong>Otsu's Method:</strong> Automatically determines threshold value</li>
                        <li>Choose methods based on image contrast and object distinction</li>
                    </ul>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_secondary_color};'>
                <h3 class='step-title'>🔄 Step 7: Transformations</h3>
                <div class='step-content'>
                    <p>The <strong>Transformations</strong> section handles geometric modifications:</p>
                    <ul class='feature-list'>
                        <li><strong>Interactive Crop:</strong> Select and crop regions of interest</li>
                        <li><strong>Rotation:</strong> 90°, 180°, or 270° rotation options</li>
                        <li><strong>Flip Operations:</strong> Horizontal or vertical image flipping</li>
                        <li>Essential for image orientation and region selection</li>
                    </ul>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_color_2};'>
                <h3 class='step-title'>⚙️ Step 8: Batch Processing & Templates</h3>
                <div class='step-content'>
                    <p>The <strong>Batch Processing</strong> section enables efficient workflow automation:</p>
                    <ol style='list-style: decimal; padding-left: 20px;'>
                        <li><strong>Perform Operations:</strong> Apply any combination of enhancements, filters, and transformations</li>
                        <li><strong>Save Template:</strong> Enter a template name and click "Save Operations as Template"</li>
                        <li><strong>Apply Template:</strong> Use "Apply to Selected" or "Apply to All Images" buttons</li>
                        <li><strong>Manage Templates:</strong> View, rename, and delete saved processing templates</li>
                    </ol>
                    <div style='background: var(--bg-success, {bg_success}); padding: 15px; border-radius: 8px; margin: 15px 0;'>
                        <p style='margin: 0; color: {accent_color}; font-weight: 600;'>🚀 Workflow Tip:</p>
                        <p style='margin: 5px 0 0 0; color: {accent_color};'>Create templates for common processing workflows to maintain consistency across image batches.</p>
                    </div>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_color};'>
                <h3 class='step-title'>💾 Step 9: Exporting Results</h3>
                <div class='step-content'>
                    <p>Save your processed images for further analysis:</p>
                    <ul class='feature-list'>
                        <li>Click <strong>Export All Processed Images</strong> in the Batch Processing section</li>
                        <li>Choose your preferred output directory</li>
                        <li>Processed images maintain original quality and metadata</li>
                        <li>Export formats preserve processing history for reproducibility</li>
                    </ul>
                    <p style='margin-top: 15px; font-style: italic;'>We hope this tutorial helps you get the most out of the Image Lab. Happy processing!</p>
                </div>
            </div>
        </div>
        """
        
    def _show_ai_assistant_tutorial(self):
        """Return AI Assistant tutorial content as HTML."""
        from src.gui.styles.theme_aware_buttons import get_theme_name
        theme_name = get_theme_name()
        colors = get_tutorial_colors(theme_name)
        bg_primary_color = colors['bg_primary']
        text_primary_color = colors['text_primary']
        accent_color = colors['accent']
        accent_secondary_color = colors['accent_secondary']
        bg_info_color = colors['bg_info']
        accent_color_2 = colors['accent']
        bg_secondary_color = colors['bg_secondary']
        bg_success = colors['bg_success']
        bg_warning = colors.get('bg_warning', colors['bg_info'])

        # FIX: Escaped all literal '{' and '}' characters in the CSS with '{{' and '}}'
        return f"""
        <style>
            @keyframes fadeIn {{ from {{ opacity: 0; }} to {{ opacity: 1; }} }}
            .tutorial-container {{
                --bg-primary: {bg_primary_color};
                --bg-secondary: {bg_secondary_color};
                --bg-info: {bg_info_color};
                --bg-success: {bg_success};
                --bg-warning: {bg_warning};
                --bg-card: {bg_primary_color};
                --bg-code: {bg_secondary_color};
                --bg-key: {bg_secondary_color};
                --text-primary: {text_primary_color};
                --text-secondary: {text_primary_color};
                --text-key: {text_primary_color};
                --accent-color: {accent_color};
                --accent-secondary: {accent_secondary_color};
                --border-color: {accent_color};
                --border-light: {accent_secondary_color};
                --border-key: {accent_color};
                
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                padding: 25px;
                line-height: 1.7;
                background: transparent;
                color: var(--text-primary);
                border-radius: 12px;
                max-width: 900px;
                margin: 0 auto;
                animation: fadeIn 0.5s ease-in-out;
            }}
            .tutorial-header {{
                text-align: center;
                margin-bottom: 35px;
                padding: 25px;
                background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
                border-radius: 15px;
                color: white;
                box-shadow: 0 8px 25px rgba(155, 89, 182, 0.3);
            }}
            .tutorial-title {{
                margin: 0;
                font-size: 2.8em;
                font-weight: 700;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                letter-spacing: -0.5px;
            }}
            .tutorial-subtitle {{
                margin: 15px 0 0 0;
                font-size: 1.3em;
                opacity: 0.95;
                font-weight: 300;
            }}
            .overview-section {{
                background: transparent;
                padding: 25px;
                border-radius: 12px;
                margin: 25px 0;
                border-left: 6px solid {accent_color_2};
                box-shadow: none;
            }}
            .step-section {{
                background: transparent;
                padding: 20px;
                border-radius: 10px;
                margin: 25px 0;
                border-left: 5px solid {accent_color_2};
                box-shadow: none;
                transition: transform 0.2s ease;
            }}
            .step-section:hover {{
                transform: translateY(-2px);
            }}
            .step-title {{
                color: var(--text-primary);
                margin-top: 0;
                font-size: 1.4em;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 10px;
            }}
            .step-content {{
                color: var(--text-secondary);
                margin-bottom: 15px;
            }}
            .feature-list {{
                list-style: none;
                padding-left: 0;
            }}
            .feature-list li {{
                margin: 8px 0;
                padding-left: 25px;
                position: relative;
            }}
            .feature-list li:before {{
                content: '▶';
                position: absolute;
                left: 0;
                color: var(--accent-color);
                font-weight: bold;
            }}
            .workflow-steps {{
                background: var(--bg-code);
                border: 1px solid var(--border-color);
                border-radius: 8px;
                padding: 20px;
                margin: 20px 0;
            }}
            .workflow-step {{
                display: flex;
                align-items: center;
                margin: 15px 0;
                padding: 10px;
                background: var(--bg-primary);
                border-radius: 6px;
                border-left: 4px solid #9b59b6;
            }}
            .step-number {{
                background: #9b59b6;
                color: white;
                border-radius: 50%;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                margin-right: 15px;
                flex-shrink: 0;
            }}
            .example-box {{
                background: var(--bg-code);
                border: 1px solid var(--border-light);
                border-radius: 8px;
                padding: 15px;
                margin: 15px 0;
                font-family: 'Courier New', monospace;
                font-size: 0.95em;
            }}
            .example-title {{
                color: #9b59b6;
                font-weight: 600;
                margin-bottom: 10px;
                font-family: 'Segoe UI', sans-serif;
            }}
            .tips-section {{
                background: var(--bg-success);
                padding: 20px;
                border-radius: 10px;
                border-left: 5px solid var(--accent-color);
                margin: 25px 0;
                box-shadow: 0 3px 12px rgba(0,0,0,0.1);
            }}
            .interface-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 20px;
                margin: 20px 0;
            }}
            .interface-card {{
                background: var(--bg-card);
                border: 1px solid var(--border-light);
                border-radius: 10px;
                padding: 20px;
                transition: transform 0.2s ease, box-shadow 0.2s ease;
            }}
            .interface-card:hover {{
                transform: translateY(-3px);
                box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            }}
            .card-title {{
                color: #9b59b6;
                font-weight: 600;
                margin: 0 0 12px 0;
                font-size: 1.2em;
            }}
        </style>
        <div class='tutorial-container'>
            <div class='tutorial-header'>
                <h1 class='tutorial-title'>🤖 AI Assistant</h1>
                <p class='tutorial-subtitle'>AI-Powered Image Analysis & Geological Interpretation</p>
            </div>
            
            <div class='overview-section'>
                <h2 style='color: {accent_color}; margin-top: 0; font-size: 1.6em;'>🧠 What is AI Assistant?</h2>
                <p style='font-size: 1.15em; margin-bottom: 15px;'>
                    The AI Assistant is a powerful image analysis tool that uses advanced Vision Language Models (VLM) to analyze and interpret your geological images. It can identify features, describe compositions, and provide detailed insights about your samples through AI-powered visual understanding.
                </p>
                <p style='font-size: 1.05em;'>
                    <strong>Applications:</strong> Automated image description, mineral identification, texture analysis, compositional assessment, feature detection, and intelligent image interpretation for geological samples.
                </p>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_color};'>
                <h3 class='step-title'>🎯 Step 1: Accessing the AI Assistant</h3>
                <div class='step-content'>
                    <p>Navigate to the AI Assistant page to start analyzing your images with AI:</p>
                    <ul class='feature-list'>
                        <li>Click on the <strong>AI Assistant</strong> tab in the main navigation</li>
                        <li>The interface opens with image upload and analysis capabilities</li>
                        <li>Upload your geological images for AI-powered analysis</li>
                        <li>Choose from predefined analysis templates or create custom prompts</li>
                        <li>View detailed AI-generated descriptions and insights</li>
                    </ul>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_secondary_color};'>
                <h3 class='step-title'>🖥️ Step 2: Understanding the Interface</h3>
                <div class='step-content'>
                    <p>The AI Assistant interface is designed for efficient image analysis:</p>
                    <div class='interface-grid'>
                        <div class='interface-card'>
                            <h4 class='card-title'>🖼️ Image Preview</h4>
                            <p>View your uploaded images with zoom and navigation controls for detailed examination before analysis.</p>
                        </div>
                        <div class='interface-card'>
                            <h4 class='card-title'>📝 Prompt Templates</h4>
                            <p>Pre-built analysis templates for common geological analysis tasks like mineral identification and texture analysis.</p>
                        </div>
                        <div class='interface-card'>
                            <h4 class='card-title'>📊 Analysis Results</h4>
                            <p>Detailed AI-generated descriptions, interpretations, and insights about your geological samples and features.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_color_2};'>
                <h3 class='step-title'>🛠️ Step 3: Core Capabilities</h3>
                <div class='step-content'>
                    <p>The AI Assistant provides powerful image analysis capabilities using advanced vision models:</p>
                    <ul class='feature-list'>
                        <li><strong>Mineral Identification:</strong> Automatically identify and describe minerals present in thin sections</li>
                        <li><strong>Texture Analysis:</strong> Analyze grain size, shape, sorting, and textural relationships</li>
                        <li><strong>Compositional Assessment:</strong> Estimate modal compositions and mineral percentages</li>
                        <li><strong>Feature Detection:</strong> Identify pores, fractures, cement types, and other geological features</li>
                        <li><strong>Structural Analysis:</strong> Describe fabric, orientation, and deformation features</li>
                        <li><strong>Sample Description:</strong> Generate comprehensive descriptions of rock samples and thin sections</li>
                    </ul>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_secondary_color};'>
                <h3 class='step-title'>💬 Step 4: Using Image Analysis Effectively</h3>
                <div class='step-content'>
                    <p>Get the most accurate analysis results by following these best practices:</p>
                    <div class='workflow-steps'>
                        <div class='workflow-step'>
                            <div class='step-number'>1- <strong>Image Quality:</strong> </div>
                            <div>
                                Use high-resolution, well-lit images with good contrast for optimal AI analysis.
                            </div>
                        </div>
                        
                        <div class='workflow-step'>
                            <div class='step-number'>2- <strong>Choose Appropriate Templates:</strong></div>
                            <div>
                                Select analysis templates that match your specific geological context and objectives.
                            </div>
                        </div>
                        
                        <div class='workflow-step'>
                            <div class='step-number'>3- <strong>Provide Context:</strong></div>
                            <div>
                                Add relevant information about sample type, formation, or specific features of interest.
                            </div>
                        </div>
                        
                        <div class='workflow-step'>
                            <div class='step-number'>4- <strong>Review Results:</strong></div>
                            <div>
                                Carefully examine AI-generated descriptions and cross-reference with your observations.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_color};'>
                <h3 class='step-title'>📝 Step 5: Example Analysis Prompts</h3>
                <div class='step-content'>
                    <p>Here are examples of effective prompts for different types of image analysis:</p>
                    
                    <div class='example-box'>
                        <div class='example-title'>🔬 Mineral Identification:</div>
                        "Identify and describe the minerals present in this thin section. Focus on grain size, crystal habits, and optical properties."
                    </div>
                    
                    <div class='example-box'>
                        <div class='example-title'>⚙️ Texture Analysis:</div>
                        "Analyze the texture of this sandstone sample. Describe grain size distribution, sorting, roundness, and cement types."
                    </div>
                    
                    <div class='example-box'>
                        <div class='example-title'>🔧 Porosity Assessment:</div>
                        "Examine this carbonate sample and identify different types of porosity. Describe pore shapes, sizes, and connectivity."
                    </div>
                    
                    <div class='example-box'>
                        <div class='example-title'>📊 Compositional Analysis:</div>
                        "Estimate the modal composition of this rock sample. Provide approximate percentages of major mineral phases."
                    </div>
                    
                    <div class='example-box'>
                        <div class='example-title'>🎓 Structural Features:</div>
                        "Describe any deformation features, fractures, or structural elements visible in this thin section image."
                    </div>
                </div>
            </div>
            
            <div class='tips-section'>
                <h3 style='color: {accent_color}; margin-top: 0; font-size: 1.4em;'>💡 Pro Tips for Best Results</h3>
                <ul class='feature-list'>
                    <li><strong>Image Quality Matters:</strong> Use high-resolution images with good lighting and focus for accurate AI analysis</li>
                    <li><strong>Be Specific:</strong> Clearly specify what aspects you want analyzed (minerals, texture, porosity, etc.)</li>
                    <li><strong>Use Templates:</strong> Start with predefined analysis templates and customize them for your specific needs</li>
                    <li><strong>Cross-Validate:</strong> Compare AI results with your own observations and expertise</li>
                    <li><strong>Multiple Views:</strong> Analyze different areas or magnifications of the same sample for comprehensive results</li>
                </ul>
                <div style='background: var(--bg-info, {bg_info_color}); padding: 15px; border-radius: 8px; margin: 15px 0;'>
                    <p style='margin: 0; color: {accent_color}; font-weight: 600;'>🎯 Remember:</p>
                    <p style='margin: 5px 0 0 0; color: {accent_color};'>The AI Assistant provides automated analysis to supplement your expertise. Always validate results against your geological knowledge and field observations.</p>
                </div>
            </div>
        </div>
        """
        

        
    def _show_generic_tutorial(self, page_name):
        """Return generic tutorial content as HTML."""
        # FIX: Escaped all CSS curly braces with {{ and }}
        return f"""
        <style>
        .tutorial-container {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 25px;
            line-height: 1.7;
            background: var(--bg-primary, #ffffff);
            color: var(--text-primary, {text_primary_color});
            border-radius: 12px;
            max-width: 900px;
            margin: 0 auto;
        }}
        .tutorial-header {{
            text-align: center;
            margin-bottom: 35px;
            padding: 25px;
            background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
            border-radius: 15px;
            color: white;
            box-shadow: 0 8px 25px rgba(142, 68, 173, 0.3);
        }}
        .tutorial-title {{
            margin: 0;
            font-size: 2.8em;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            letter-spacing: -0.5px;
        }}
        .tutorial-subtitle {{
            margin: 15px 0 0 0;
            font-size: 1.3em;
            opacity: 0.95;
            font-weight: 300;
        }}
        .overview-section {{
            background: var(--bg-info, #ecf0f1);
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 6px solid #8e44ad;
            box-shadow: 0 4px 15px rgba(142, 68, 173, 0.1);
        }}
        .step-section {{
            background: var(--bg-secondary, #f8f9fa);
            padding: 20px;
            border-radius: 10px;
            margin: 25px 0;
            border-left: 5px solid var(--accent-color, #8e44ad);
            box-shadow: 0 3px 12px rgba(0,0,0,0.08);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }}
        .step-section:hover {{
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.12);
        }}
        .step-title {{
            color: var(--text-primary, {text_primary_color});
            margin-top: 0;
            font-size: 1.4em;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }}
        .step-content {{
            color: var(--text-secondary, #34495e);
            margin-bottom: 15px;
        }}
        .feature-list {{
            list-style: none;
            padding-left: 0;
        }}
        .feature-list li {{
            margin: 8px 0;
            padding-left: 25px;
            position: relative;
        }}
        .feature-list li:before {{
            content: '▶';
            position: absolute;
            left: 0;
            color: var(--accent-color, #8e44ad);
            font-weight: bold;
        }}
        .getting-started-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }}
        .getting-started-card {{
            background: var(--bg-card, #ffffff);
            border: 1px solid var(--border-light, #e0e0e0);
            border-radius: 8px;
            padding: 15px;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }}
        .getting-started-card:hover {{
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }}
        .card-title {{
            color: #8e44ad;
            font-weight: 600;
            margin: 0 0 8px 0;
        }}

        </style>
        <div class='tutorial-container'>
            <div class='tutorial-header'>
                <h1 class='tutorial-title'>📚 {page_name}</h1>
                <p class='tutorial-subtitle'>Module Tutorial & Guide</p>
            </div>
            
            <div class='overview-section'>
                <h2 style='color: {accent_color}; margin-top: 0; font-size: 1.6em;'>🚀 Welcome to {page_name}!</h2>
            <p style='color: {text_primary_color}; font-size: 1.15em; margin-bottom: 15px;'>
                    Welcome to the {page_name} module! This tutorial is currently being developed to provide you with comprehensive guidance.
                </p>
                <p style='color: {text_primary_color}; font-size: 1.05em;'>
                    <strong>In the meantime:</strong> Explore the available resources below to get started with your workflow.
                </p>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_color};'>
                <h3 class='step-title'>🎯 Getting Started</h3>
                <div class='step-content'>
                    <div class='getting-started-grid'>
                        <div class='getting-started-card'>
                            <h4 class='card-title'>🔍 Explore Interface</h4>
                            <p>Navigate through the interface and familiarize yourself with available tools and options</p>
                        </div>
                        
                        <div class='getting-started-card'>
                            <h4 class='card-title'>🤖 AI Assistant</h4>
                            <p>Use the AI Assistant for specific questions about features and functionality</p>
                        </div>
                        
                        <div class='getting-started-card'>
                            <h4 class='card-title'>📚 Quick Start Guide</h4>
                            <p>Check the Quick Start Guide for general workflow and best practices</p>
                        </div>
                        
                        <div class='getting-started-card'>
                            <h4 class='card-title'>💡 Tooltips & Help</h4>
                            <p>Refer to tooltips and help text throughout the interface for contextual guidance</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {bg_success};'>
                <h3 class='step-title'>✨ Available Resources</h3>
                <div class='step-content'>
                    <ul class='feature-list'>
                        <li><strong>Interactive Interface:</strong> Explore tools and features through hands-on interaction</li>
                        <li><strong>AI-Powered Help:</strong> Get instant answers to your questions using the AI Assistant</li>
                        <li><strong>Contextual Guidance:</strong> Hover over elements for helpful tooltips and information</li>
                        <li><strong>Quick Start Resources:</strong> Access general workflow guides and documentation</li>
                        <li><strong>Community Support:</strong> Connect with other users for tips and best practices</li>
                    </ul>
                </div>
            </div>
            
            <div class='step-section' style='border-left-color: {accent_color_2};'>
                <h3 class='step-title'>🔄 Coming Soon</h3>
                <div class='step-content'>
                    <div style='background: linear-gradient(135deg, {accent_color} 0%, {accent_color_2} 100%); padding: 20px; border-radius: 8px; margin: 15px 0; color: {colors["text_on_accent"]};'>
                        <p style='margin: 0 0 10px 0; font-weight: 600;'>📈 Enhanced Tutorials:</p>
                        <p style='margin: 0; opacity: 0.95;'>More detailed tutorials are being developed for this module. Stay tuned for comprehensive step-by-step guides, video tutorials, and advanced workflow examples!</p>
                    </div>
                    
                    <div style='background: {bg_info_color}; padding: 15px; border-radius: 8px; margin: 15px 0;'>
                        <p style='margin: 0; color: {accent_color}; font-weight: 600;'>💡 Pro Tip:</p>
                <p style='margin: 5px 0 0 0; color: var(--text-primary, {text_primary_color});'>Don't hesitate to experiment with the interface - most actions can be undone, and the AI Assistant is always available to help guide you through any challenges!</p>
                    </div>
                </div>
            </div>
        </div>
        """