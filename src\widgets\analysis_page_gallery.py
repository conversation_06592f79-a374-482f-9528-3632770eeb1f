# src/widgets/analysis_page_gallery.py

from PySide6.QtWidgets import QPushButton, QHBoxLayout, QWidget
# QIcon is used in the parent class
from PySide6.QtCore import Signal

from src.widgets.page_image_gallery import PageImageGallery

class AnalysisPageGallery(PageImageGallery):
    """Image gallery specifically for the Analysis Page."""

    # Additional signals specific to analysis page
    analysis_requested = Signal(int)  # Signal to request analysis of an image

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_analysis_controls()

    def setup_analysis_controls(self):
        """Adds analysis-specific controls to the gallery."""
        # This method is kept for compatibility, but the Analyze Selected button has been removed
        pass

    def _on_analyze_clicked(self):
        """Handler for analyze button clicks."""
        if self.selected_index >= 0:
            self.analysis_requested.emit(self.selected_index)

    def get_all_images(self):
        """Returns all images in the gallery."""
        return self.images.copy()

    def get_all_file_paths(self):
        """Returns all file paths in the gallery."""
        return self.file_paths.copy()

    def clear_images(self):
        """Removes all images and thumbnails from the gallery."""
        # Call the parent class's clear method to handle the actual clearing
        self.clear()
        print("AnalysisPageGallery: All images cleared.")
