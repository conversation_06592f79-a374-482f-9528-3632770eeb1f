# src/gui/handlers/image_handlers.py

import os
import numpy as np
import cv2
import logging
from PySide6.QtWidgets import QFileDialog, QMessageBox
from PySide6.QtGui import QPixmap, QImage

from src.utils.image_utils import resize_image, convert_cvimage_to_qimage, convert_cvimage_to_qpixmap

logger = logging.getLogger(__name__)

class ImageHandlers:
    """Class for handling image loading, display, and manipulation."""

    def upload_image(self):
        """Handles image uploading."""
        file_types = "Image files (*.jpg *.jpeg *.png *.bmp *.tiff *.tif *.webp)"
        file_paths, _ = QFileDialog.getOpenFileNames(self, "Open Image Files", "", file_types)

        if file_paths:
            # Clear existing images
            self.image_gallery.clear()
            self.images = []  # List to store all loaded images
            self.image_filenames = []  # List to store all image filenames
            self.image_full_paths = []  # List to store all image full paths

            for index, file_path in enumerate(file_paths):
                try:
                    # Load Image
                    image = cv2.imdecode(np.fromfile(file_path, dtype=np.uint8), cv2.IMREAD_COLOR)
                    if image is None:
                        QMessageBox.warning(self, "Warning", f"Error loading image: {os.path.basename(file_path)}")
                        continue

                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

                    # Store image information
                    self.images.append(image)
                    self.image_filenames.append(os.path.basename(file_path))
                    self.image_full_paths.append(file_path)

                    # Create thumbnail and add to gallery
                    thumbnail = convert_cvimage_to_qpixmap(image, already_rgb=True)
                    self.image_gallery.add_image(thumbnail, index)

                    # Set first image as current
                    if index == 0:
                        self.select_image(0)

                except Exception as e:
                    QMessageBox.warning(self, "Warning", f"Failed to load image {os.path.basename(file_path)}: {str(e)}")

            # Enable segment button if images were loaded successfully
            if hasattr(self, 'segment_button') and self.images:
                self.segment_button.setEnabled(True)

            # Connect gallery click event if not already connected
            # Safely disconnect and reconnect signal
            try:
                self.image_gallery.image_clicked.disconnect(self.select_image)
            except RuntimeError:
                pass  # Signal was not connected
            self.image_gallery.image_clicked.connect(self.select_image)

            # Update analysis page image counter if available
            if hasattr(self, 'update_analysis_image_counter'):
                self.update_analysis_image_counter()

    def select_image(self, index):
        """Selects and displays an image from the gallery."""
        if 0 <= index < len(self.images):
            # Save current analysis state if needed
            if hasattr(self, 'save_analysis_state'):
                self.save_analysis_state()

            # Get the full path of the selected image
            image_full_path = self.image_full_paths[index]

            # Update current image properties
            self.image = self.images[index]
            self.image_filename = self.image_filenames[index]
            self.image_full_path = self.image_full_paths[index]
            self.original_image_size = self.image.shape[:2]
            self.processed_image = self.image.copy()

            # Use switch_image to properly save current state and load new state
            if hasattr(self, 'switch_image'):
                self.switch_image(image_full_path)

            # Always display the original image for the current selection
            if not self.common_size:
                self.common_size = (750, 750)
                self.target_size_width.setValue(750)
                self.target_size_height.setValue(750)

            self.image_resized = resize_image(self.image, self.common_size)
            self.display_original_image(self.image_resized)

            # If no segmentation state was loaded, initialize display
            if not hasattr(self, 'segmented_image') or self.segmented_image is None:
                # Reset processing state if no saved state
                self.new_colors = {}
                self.label_percentages = {}

                # Clear segmented image display and progress
                self.segmented_image_view.clear()
                self.progress.setValue(0)

                # Clear info and merge frames if no saved state
                self.clear_info_frames()
                self.reset_process_image()

            # Update gallery selection
            self.image_gallery.set_selected(index)

            # Update image counter for the current page only
            if hasattr(self, 'update_process_image_counter'):
                self.update_process_image_counter()

            # We no longer automatically load the analysis state
            # to maintain page independence

    def clear_info_frames(self):
        """Clears info and merge frames."""
        # Clear info frame
        for i in reversed(range(self.info_frame.layout().count())):
            widget = self.info_frame.layout().itemAt(i).widget()
            if widget is not None:
                widget.deleteLater()

        # Clear merge frame
        for i in reversed(range(self.merge_inner_layout.count())):
            widget = self.merge_inner_layout.itemAt(i).widget()
            if widget is not None:
                widget.deleteLater()

    def display_original_image(self, image):
        """Displays the original image."""
        if image is None or image.size == 0:
            print("Warning: Cannot display None or empty original image")
            return
            
        q_img = convert_cvimage_to_qimage(image, already_rgb=True)
        if q_img is None:
            print("Warning: Failed to convert original image to QImage")
            return
            
        pixmap = QPixmap.fromImage(q_img)
        if pixmap.isNull():
            print("Warning: Failed to create pixmap from original image")
            return
            
        self.original_image_view.setPixmap(pixmap)
        print(f"DEBUG: Displayed original image with dimensions: {pixmap.width()}x{pixmap.height()}")

        # Also update the synchronized view if it exists
        if hasattr(self, 'original_sync_view'):
            # Store the pixmap for potential synchronization with segmented view
            self._original_pixmap = pixmap
            self.original_sync_view.set_pixmap(pixmap)

            # If we also have a segmented image, ensure consistent sizing
            self._sync_image_sizes()

        # We no longer automatically add images to the process gallery
        # to maintain page independence

    def display_segmented_image(self, image):
        """Displays the segmented image."""
        if image is None or image.size == 0:
            print("Warning: Cannot display None or empty segmented image")
            return
            
        q_img = convert_cvimage_to_qimage(image, already_rgb=True)
        if q_img is None:
            print("Warning: Failed to convert segmented image to QImage")
            return
            
        pixmap = QPixmap.fromImage(q_img)
        if pixmap.isNull():
            print("Warning: Failed to create pixmap from segmented image")
            return
            
        self.segmented_image_view.setPixmap(pixmap)
        print(f"DEBUG: Displayed segmented image with dimensions: {pixmap.width()}x{pixmap.height()}")

        # Also update the synchronized view if it exists
        if hasattr(self, 'segmented_sync_view'):
            # Store the pixmap for potential synchronization with original view
            self._segmented_pixmap = pixmap
            self.segmented_sync_view.set_pixmap(pixmap)

            # If we also have an original image, ensure consistent sizing
            self._sync_image_sizes()

    def _sync_image_sizes(self):
        """Ensure both synchronized views have consistent image sizing."""
        if not (hasattr(self, 'original_sync_view') and hasattr(self, 'segmented_sync_view')):
            return

        # Only sync if both views have images
        if (hasattr(self, '_original_pixmap') and hasattr(self, '_segmented_pixmap') and
            hasattr(self.original_sync_view, 'pixmap_item') and self.original_sync_view.pixmap_item and
            hasattr(self.segmented_sync_view, 'pixmap_item') and self.segmented_sync_view.pixmap_item):

            # Get the zoom factor from the view that was updated first
            # This ensures both views use the same zoom level
            reference_zoom = self.original_sync_view.zoom_factor

            # Apply the same zoom to both views
            self.original_sync_view.ignore_sync = True
            self.segmented_sync_view.ignore_sync = True

            # Set the same zoom factor for both views
            self.original_sync_view.zoom_factor = reference_zoom
            self.segmented_sync_view.zoom_factor = reference_zoom

            # Apply transforms
            from PySide6.QtGui import QTransform
            transform = QTransform().scale(reference_zoom, reference_zoom)
            self.original_sync_view.setTransform(transform)
            self.segmented_sync_view.setTransform(transform)

            # Center both images
            if self.original_sync_view.pixmap_item:
                self.original_sync_view.centerOn(self.original_sync_view.pixmap_item.boundingRect().center())
            if self.segmented_sync_view.pixmap_item:
                self.segmented_sync_view.centerOn(self.segmented_sync_view.pixmap_item.boundingRect().center())

            # Re-enable synchronization
            self.original_sync_view.ignore_sync = False
            self.segmented_sync_view.ignore_sync = False

    def download_image(self):
        """Saves the segmented image to disk."""
        if self.segmented_image is None:
            QMessageBox.warning(self, "Warning", "No segmented image to save.")
            return

        file_types = "PNG (*.png);;JPEG (*.jpg);;TIFF (*.tiff);;BMP (*.bmp)"
        default_name = os.path.splitext(self.image_filename)[0] + "_segmented.png" if self.image_filename else "segmented.png"
        file_path, selected_filter = QFileDialog.getSaveFileName(self, "Save Segmented Image", default_name, file_types)

        if file_path:
            try:
                # Convert from RGB to BGR for OpenCV
                image_bgr = cv2.cvtColor(self.segmented_image, cv2.COLOR_RGB2BGR)
                cv2.imwrite(file_path, image_bgr)
                QMessageBox.information(self, "Success", f"Image saved successfully to {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to save image: {str(e)}")

    def update_image_info(self, image):
        """Updates the image information display."""
        if image is None:
            self.image_info_label.setText("No image loaded")
            return

        # Handle different image types
        if isinstance(image, np.ndarray):
            # NumPy array (OpenCV image)
            height, width = image.shape[:2]
            channels = 1 if len(image.shape) == 2 else image.shape[2]
            dtype = image.dtype

            info_text = f"Dimensions: {width} x {height}\n"
            info_text += f"Channels: {channels}\n"
            info_text += f"Data Type: {dtype}\n"
        elif isinstance(image, QPixmap):
            # QPixmap
            width = image.width()
            height = image.height()
            depth = image.depth()

            info_text = f"Dimensions: {width} x {height}\n"
            info_text += f"Bit Depth: {depth}\n"
            info_text += f"Type: QPixmap\n"
        elif isinstance(image, QImage):
            # QImage
            width = image.width()
            height = image.height()
            depth = image.depth()
            format_name = str(image.format())

            info_text = f"Dimensions: {width} x {height}\n"
            info_text += f"Bit Depth: {depth}\n"
            info_text += f"Format: {format_name}\n"
            info_text += f"Type: QImage\n"
        else:
            # Unknown type
            info_text = f"Type: {type(image).__name__}\n"
            info_text += "Details not available for this image type\n"

        # Add filename if available
        if hasattr(self, 'image_filename') and self.image_filename:
            info_text += f"File: {self.image_filename}\n"

        self.image_info_label.setText(info_text)

        # Update histogram if available
        self.update_histogram(image)

    def update_histogram(self, image):
        """Updates the histogram display for the image."""
        if image is None or not hasattr(self, 'histogram_label'):
            return

        try:
            # Convert image to numpy array if it's not already
            if isinstance(image, np.ndarray):
                # Already a numpy array, use as is
                numpy_image = image
            elif isinstance(image, QPixmap):
                # Convert QPixmap to QImage
                qimg = image.toImage()
                # Convert QImage to numpy array
                width = qimg.width()
                height = qimg.height()
                ptr = qimg.constBits()
                ptr.setsize(height * width * 4)  # 4 bytes per pixel (RGBA)
                # Create numpy array from the image data
                arr = np.array(ptr).reshape(height, width, 4)  # RGBA format
                numpy_image = arr[:, :, :3]  # Convert to RGB by taking first 3 channels
            elif isinstance(image, QImage):
                # Convert QImage to numpy array
                width = image.width()
                height = image.height()
                ptr = image.constBits()
                ptr.setsize(height * width * 4)  # 4 bytes per pixel (RGBA)
                # Create numpy array from the image data
                arr = np.array(ptr).reshape(height, width, 4)  # RGBA format
                numpy_image = arr[:, :, :3]  # Convert to RGB by taking first 3 channels
            else:
                # Unsupported image type
                logger.warning(f"Cannot create histogram for image type: {type(image)}")
                return

            # Create histogram
            hist_image = np.zeros((200, 256, 3), dtype=np.uint8)

            if len(numpy_image.shape) == 3:  # Color image
                colors = [(0, 0, 255), (0, 255, 0), (255, 0, 0)]  # BGR for display
                for i, color in enumerate(colors):
                    hist = cv2.calcHist([numpy_image], [i], None, [256], [0, 256])
                    cv2.normalize(hist, hist, 0, 199, cv2.NORM_MINMAX)

                    for x, y in enumerate(hist):
                        cv2.line(hist_image, (x, 199), (x, 199 - int(y)), color, 1)
            else:  # Grayscale image
                hist = cv2.calcHist([numpy_image], [0], None, [256], [0, 256])
                cv2.normalize(hist, hist, 0, 199, cv2.NORM_MINMAX)

                for x, y in enumerate(hist):
                    cv2.line(hist_image, (x, 199), (x, 199 - int(y)), (200, 200, 200), 1)

            # Convert to QPixmap and display
            hist_qimg = convert_cvimage_to_qimage(hist_image)
            hist_pixmap = QPixmap.fromImage(hist_qimg)
            self.histogram_label.setPixmap(hist_pixmap)
        except Exception as e:
            logger.error(f"Error updating histogram: {e}")

    def reset_process_image(self):
        """Resets the image view for the unsupervised segmentation page only.
        This is separate from the reset_analysis_image method in AnalysisHandlers."""
        # This method is now a no-op to maintain page independence
        # Each page should handle its own image reset
        logger.info("Process image reset called (no-op)")

    # Image Navigation Methods for Unsupervised Segmentation Page
    def setup_process_navigation(self):
        """Sets up the image navigation controls for the unsupervised segmentation page."""
        # Connect navigation buttons
        if hasattr(self, 'process_prev_image_btn'):
            self.process_prev_image_btn.clicked.connect(self.previous_process_image)
        if hasattr(self, 'process_next_image_btn'):
            self.process_next_image_btn.clicked.connect(self.next_process_image)

        # Update image counter
        self.update_process_image_counter()

    def update_process_image_counter(self):
        """Updates the image counter label and navigation buttons for the unsupervised segmentation page."""
        if not hasattr(self, 'images') or not self.images or not hasattr(self, 'process_image_counter_label'):
            if hasattr(self, 'process_image_counter_label'):
                self.process_image_counter_label.setText("No images loaded")
            if hasattr(self, 'process_prev_image_btn'):
                self.process_prev_image_btn.setEnabled(False)
            if hasattr(self, 'process_next_image_btn'):
                self.process_next_image_btn.setEnabled(False)
            return

        # Find current image index
        current_index = -1
        if hasattr(self, 'image_full_path') and hasattr(self, 'image_full_paths'):
            try:
                current_index = self.image_full_paths.index(self.image_full_path)
            except ValueError:
                current_index = -1

        # Update counter label
        if current_index >= 0 and hasattr(self, 'process_image_counter_label'):
            self.process_image_counter_label.setText(f"Image {current_index + 1} of {len(self.images)}")
        else:
            if hasattr(self, 'process_image_counter_label'):
                self.process_image_counter_label.setText(f"{len(self.images)} images loaded")

        # Update button states
        if hasattr(self, 'process_prev_image_btn'):
            self.process_prev_image_btn.setEnabled(current_index > 0)
        if hasattr(self, 'process_next_image_btn'):
            self.process_next_image_btn.setEnabled(current_index >= 0 and current_index < len(self.images) - 1)

    def previous_process_image(self):
        """Navigates to the previous image in the unsupervised segmentation page."""
        if not hasattr(self, 'images') or not self.images:
            return

        # Find current image index
        current_index = -1
        if hasattr(self, 'image_full_path') and hasattr(self, 'image_full_paths'):
            try:
                current_index = self.image_full_paths.index(self.image_full_path)
            except ValueError:
                current_index = -1

        if current_index > 0:
            # Select previous image
            self.select_image(current_index - 1)

    def next_process_image(self):
        """Navigates to the next image in the unsupervised segmentation page."""
        if not hasattr(self, 'images') or not self.images:
            return

        # Find current image index
        current_index = -1
        if hasattr(self, 'image_full_path') and hasattr(self, 'image_full_paths'):
            try:
                current_index = self.image_full_paths.index(self.image_full_path)
            except ValueError:
                current_index = -1

        if current_index >= 0 and current_index < len(self.images) - 1:
            # Select next image
            self.select_image(current_index + 1)