import logging
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QColor, QIcon, QPixmap
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                              QListWidget, QListWidgetItem, QLineEdit, QColorDialog,
                              QMessageBox, QWidget, QFormLayout, QSpinBox)
import numpy as np

logger = logging.getLogger(__name__)

# Define constants for default label names and colors
DEFAULT_LABEL_NAMES = [
    "Segment 1",
    "Segment 2",
    "Segment 3",
    "Segment 4",
    "Segment 5"
]

# Define a constant for mask colors to avoid repetition
DEFAULT_MASK_COLORS = np.array([
    [255, 0, 0],  # Segment 1 (red)
    [0, 255, 0],  # Segment 2 (green)
    [0, 0, 255],  # Segment 3 (blue)
    [255, 255, 0],  # Segment 4 (yellow)
    [255, 0, 255],  # Segment 5 (magenta)
    [0, 0, 0]  # Background (transparent)
])

class LabelManagementDialog(QDialog):
    """Dialog for managing label names and colors for trainable segmentation."""

    label_data_changed = Signal(dict)  # Signal emitted when label data changes
    label_removed = Signal(int)  # Signal emitted when a label is removed

    def __init__(self, parent=None, label_names=None, mask_colors=None):
        super().__init__(parent)
        self.setWindowTitle("Manage Labels")
        self.setMinimumWidth(500)
        self.setMinimumHeight(400)

        # Set dialog flags to ensure proper closing behavior
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint)

        # Ensure the dialog is modal
        self.setModal(True)

        # Initialize label data
        self.label_names = label_names or {}

        # Convert mask_colors to a dictionary if it's a numpy array
        if isinstance(mask_colors, np.ndarray):
            self.mask_colors = {}
            for i, color in enumerate(mask_colors):
                if isinstance(color, np.ndarray):
                    color = color.tolist()
                self.mask_colors[i+1] = color
        else:
            self.mask_colors = mask_colors or {}

        # Set up the UI
        self.setup_ui()

        # Populate the list with existing labels
        self.populate_label_list()

        # Flag to track if the dialog is closing
        self.is_closing = False

    def setup_ui(self):
        """Set up the dialog UI."""
        main_layout = QVBoxLayout(self)

        # Add a note about label persistence
        note_label = QLabel("Note: Label settings will reset to defaults when the application is closed.")
        note_label.setStyleSheet("color: #666; font-style: italic;")
        main_layout.addWidget(note_label)

        # Label list
        self.label_list = QListWidget()
        self.label_list.setSelectionMode(QListWidget.SingleSelection)
        self.label_list.currentItemChanged.connect(self.on_label_selected)
        main_layout.addWidget(QLabel("Labels:"))
        main_layout.addWidget(self.label_list)

        # Label editing form
        form_widget = QWidget()
        form_layout = QFormLayout(form_widget)

        # Label ID
        self.label_id_spin = QSpinBox()
        self.label_id_spin.setMinimum(1)
        self.label_id_spin.setMaximum(100)
        form_layout.addRow("Label ID:", self.label_id_spin)

        # Label name
        self.label_name_edit = QLineEdit()
        form_layout.addRow("Label Name:", self.label_name_edit)

        # Label color
        color_layout = QHBoxLayout()
        self.color_preview = QLabel()
        self.color_preview.setFixedSize(24, 24)
        self.color_preview.setStyleSheet("background-color: #FF0000; border: 1px solid #000000;")
        self.current_color = QColor(255, 0, 0)  # Default to red

        self.color_button = QPushButton("Choose Color")
        self.color_button.clicked.connect(self.choose_color)

        color_layout.addWidget(self.color_preview)
        color_layout.addWidget(self.color_button)
        color_layout.addStretch()

        form_layout.addRow("Label Color:", color_layout)

        main_layout.addWidget(form_widget)

        # Buttons for adding, updating, and removing labels
        buttons_layout = QHBoxLayout()

        self.add_button = QPushButton("Add Label")
        self.add_button.clicked.connect(self.add_label)

        self.update_button = QPushButton("Update Label")
        self.update_button.clicked.connect(self.update_label)
        self.update_button.setEnabled(False)

        self.remove_button = QPushButton("Remove Label")
        self.remove_button.clicked.connect(self.remove_label)
        self.remove_button.setEnabled(False)

        self.reset_button = QPushButton("Reset to Defaults")
        self.reset_button.clicked.connect(self.reset_to_defaults)

        buttons_layout.addWidget(self.add_button)
        buttons_layout.addWidget(self.update_button)
        buttons_layout.addWidget(self.remove_button)
        buttons_layout.addWidget(self.reset_button)

        main_layout.addLayout(buttons_layout)

        # Dialog buttons
        dialog_buttons_layout = QHBoxLayout()

        self.ok_button = QPushButton("OK")
        # Use a direct connection to ensure the signal is processed immediately
        self.ok_button.clicked.connect(self.accept_and_close)

        self.cancel_button = QPushButton("Cancel")
        # Use a direct connection to ensure the signal is processed immediately
        self.cancel_button.clicked.connect(self.reject_and_close)

        dialog_buttons_layout.addStretch()
        dialog_buttons_layout.addWidget(self.ok_button)
        dialog_buttons_layout.addWidget(self.cancel_button)

        main_layout.addLayout(dialog_buttons_layout)

    def populate_label_list(self):
        """Populate the label list with existing labels."""
        self.label_list.clear()

        # Sort labels by ID
        sorted_label_ids = sorted(self.label_names.keys())

        for label_id in sorted_label_ids:
            label_name = self.label_names[label_id]

            # Get color as QColor
            if label_id in self.mask_colors:
                color = self.mask_colors[label_id]
                if isinstance(color, list):
                    qcolor = QColor(color[0], color[1], color[2])
                else:
                    qcolor = QColor(255, 0, 0)  # Default to red
            else:
                qcolor = QColor(255, 0, 0)  # Default to red

            item = QListWidgetItem(f"{label_id}: {label_name}")

            # Create a colored square icon
            pixmap = QPixmap(16, 16)
            pixmap.fill(qcolor)
            item.setIcon(QIcon(pixmap))

            # Store the label ID as item data
            item.setData(Qt.UserRole, label_id)

            # Mark special labels with a different background
            if label_name.lower() in ["porosity", "glauconite", "quartz"]:
                item.setBackground(QColor(100, 120, 180))  # Darker blue background
                item.setForeground(QColor(255, 255, 255))  # White text for better contrast

            self.label_list.addItem(item)

    def on_label_selected(self, current, previous):
        """Handle label selection in the list."""
        if current is None:
            self.update_button.setEnabled(False)
            self.remove_button.setEnabled(False)
            return

        # Get the label ID from the item data
        label_id = current.data(Qt.UserRole)

        # Update the form with the selected label data
        self.label_id_spin.setValue(label_id)
        self.label_name_edit.setText(self.label_names[label_id])

        # Update color preview
        if label_id in self.mask_colors:
            color = self.mask_colors[label_id]
            if isinstance(color, list):
                self.current_color = QColor(color[0], color[1], color[2])
            else:
                self.current_color = QColor(255, 0, 0)  # Default to red
        else:
            self.current_color = QColor(255, 0, 0)  # Default to red

        self.update_color_preview()

        # Check if this is a special label
        label_name = self.label_names[label_id].lower()
        is_special_label = label_name in ["porosity", "glauconite", "quartz"]

        # Enable update and remove buttons
        self.update_button.setEnabled(True)
        # Don't allow removing special labels
        self.remove_button.setEnabled(not is_special_label)

    def choose_color(self):
        """Open a color dialog to choose a label color."""
        color = QColorDialog.getColor(self.current_color, self, "Choose Label Color")
        if color.isValid():
            self.current_color = color
            self.update_color_preview()

    def update_color_preview(self):
        """Update the color preview label."""
        self.color_preview.setStyleSheet(f"background-color: {self.current_color.name()}; border: 1px solid #000000;")

    def add_label(self):
        """Add a new label."""
        label_id = self.label_id_spin.value()
        label_name = self.label_name_edit.text().strip()

        if not label_name:
            QMessageBox.warning(self, "Warning", "Please enter a label name.")
            return

        if label_id in self.label_names:
            QMessageBox.warning(self, "Warning", f"Label ID {label_id} already exists. Please choose a different ID.")
            return

        # Add the new label
        self.label_names[label_id] = label_name
        self.mask_colors[label_id] = [
            self.current_color.red(),
            self.current_color.green(),
            self.current_color.blue()
        ]

        # Update the list
        self.populate_label_list()

        # Select the new label in the list
        for i in range(self.label_list.count()):
            item = self.label_list.item(i)
            if item.data(Qt.UserRole) == label_id:
                self.label_list.setCurrentItem(item)
                break

        # Emit the signal
        self.label_data_changed.emit(self.get_label_data())

        logger.info(f"Added label {label_id}: {label_name}")

    def update_label(self):
        """Update the selected label."""
        current_item = self.label_list.currentItem()
        if current_item is None:
            return

        old_label_id = current_item.data(Qt.UserRole)
        new_label_id = self.label_id_spin.value()
        label_name = self.label_name_edit.text().strip()

        if not label_name:
            QMessageBox.warning(self, "Warning", "Please enter a label name.")
            return

        if new_label_id != old_label_id and new_label_id in self.label_names:
            QMessageBox.warning(self, "Warning", f"Label ID {new_label_id} already exists. Please choose a different ID.")
            return

        # Remove the old label if the ID changed
        if new_label_id != old_label_id:
            del self.label_names[old_label_id]
            if old_label_id in self.mask_colors:
                del self.mask_colors[old_label_id]

        # Update the label
        self.label_names[new_label_id] = label_name
        self.mask_colors[new_label_id] = [
            self.current_color.red(),
            self.current_color.green(),
            self.current_color.blue()
        ]

        # Update the list
        self.populate_label_list()

        # Select the updated label in the list
        for i in range(self.label_list.count()):
            item = self.label_list.item(i)
            if item.data(Qt.UserRole) == new_label_id:
                self.label_list.setCurrentItem(item)
                break

        # Emit the signal
        self.label_data_changed.emit(self.get_label_data())

        logger.info(f"Updated label {old_label_id} to {new_label_id}: {label_name}")

    def remove_label(self):
        """Remove the selected label."""
        current_item = self.label_list.currentItem()
        if current_item is None:
            return

        label_id = current_item.data(Qt.UserRole)
        label_name = self.label_names[label_id]

        # Don't allow removing special labels
        if label_name.lower() in ["porosity", "glauconite", "quartz"]:
            QMessageBox.warning(self, "Warning", f"Cannot remove special label '{label_name}'.")
            return

        # Confirm deletion with warning about annotations
        result = QMessageBox.question(
            self,
            "Confirm Deletion",
            f"Are you sure you want to remove label {label_id}: {label_name}?\n\n"
            f"WARNING: All annotations using this label will be permanently removed from all images.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if result == QMessageBox.Yes:
            # Emit the signal to remove annotations before removing the label
            logger.info(f"Emitting signal to remove annotations for label {label_id}")
            self.label_removed.emit(label_id)

            # Remove the label
            del self.label_names[label_id]
            if label_id in self.mask_colors:
                del self.mask_colors[label_id]

            # Update the list
            self.populate_label_list()

            # Clear the form
            self.label_id_spin.setValue(1)
            self.label_name_edit.clear()
            self.current_color = QColor(255, 0, 0)
            self.update_color_preview()

            # Disable update and remove buttons
            self.update_button.setEnabled(False)
            self.remove_button.setEnabled(False)

            # Emit the signal for label data changes
            self.label_data_changed.emit(self.get_label_data())

            logger.info(f"Removed label {label_id}")

    def reset_to_defaults(self):
        """Reset labels to default values."""
        # Confirm reset
        result = QMessageBox.question(
            self,
            "Confirm Reset",
            "Are you sure you want to reset all labels to defaults?\n\nThis will affect all images.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if result == QMessageBox.Yes:
            # Reset to defaults
            self.label_names = {}
            self.mask_colors = {}

            # Add default labels
            for i, name in enumerate(DEFAULT_LABEL_NAMES):
                self.label_names[i+1] = name
                if i < len(DEFAULT_MASK_COLORS):
                    self.mask_colors[i+1] = DEFAULT_MASK_COLORS[i].tolist()
                else:
                    # Generate a random color if we run out of defaults
                    import random
                    self.mask_colors[i+1] = [random.randint(0, 255), random.randint(0, 255), random.randint(0, 255)]

            # Update the list
            self.populate_label_list()

            # Clear the form
            self.label_id_spin.setValue(1)
            self.label_name_edit.clear()
            self.current_color = QColor(255, 0, 0)
            self.update_color_preview()
            self.special_label_checkbox.setChecked(False)

            # Disable update and remove buttons
            self.update_button.setEnabled(False)
            self.remove_button.setEnabled(False)

            # Emit the signal
            self.label_data_changed.emit(self.get_label_data())

            logger.info("Reset labels to defaults")

    def get_label_data(self):
        """Get the current label data."""
        return {
            'label_names': self.label_names.copy(),
            'mask_colors': self.mask_colors.copy()
        }

    def accept(self):
        """Handle dialog acceptance."""
        # Prevent multiple calls to accept
        if self.is_closing:
            return

        self.is_closing = True

        # Emit the signal one last time to ensure the latest data is sent
        self.label_data_changed.emit(self.get_label_data())

        # Disconnect all signals to prevent multiple emissions
        try:
            self.label_data_changed.disconnect()
        except TypeError:
            # It's okay if there are no connections
            pass

        # Call the parent class accept method to close the dialog
        QDialog.accept(self)

    def reject(self):
        """Handle dialog rejection."""
        # Prevent multiple calls to reject
        if self.is_closing:
            return

        self.is_closing = True

        # Disconnect all signals to prevent multiple emissions
        try:
            self.label_data_changed.disconnect()
        except TypeError:
            # It's okay if there are no connections
            pass

        # Call the parent class reject method to close the dialog
        QDialog.reject(self)

    def closeEvent(self, event):
        """Handle dialog close event (X button)."""
        # Prevent multiple close events
        if self.is_closing:
            event.accept()
            return

        self.is_closing = True

        # Disconnect all signals to prevent multiple emissions
        try:
            self.label_data_changed.disconnect()
        except TypeError:
            # It's okay if there are no connections
            pass

        # Accept the close event
        event.accept()

        # Call the parent class closeEvent
        super().closeEvent(event)

    def accept_and_close(self):
        """Custom slot to handle OK button click."""
        # Only process if not already closing
        if not self.is_closing:
            logger.debug("OK button clicked, accepting dialog")
            self.accept()

    def reject_and_close(self):
        """Custom slot to handle Cancel button click."""
        # Only process if not already closing
        if not self.is_closing:
            logger.debug("Cancel button clicked, rejecting dialog")
            self.reject()
