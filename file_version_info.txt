VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(4, 0, 0, 0),
    prodvers=(4, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'040904B0',
          [StringStruct(u'CompanyName', u'VisionLab Ai'),
          StringStruct(u'FileDescription', u'VisionLab Ai - Petrographic Image Analysis Software'),
          StringStruct(u'FileVersion', u'4.0.0'),
          StringStruct(u'InternalName', u'VisionLab Ai'),
          StringStruct(u'LegalCopyright', u'Copyright © 2024 VisionLab Ai. All rights reserved.'),
          StringStruct(u'OriginalFilename', u'VisionLab_Ai.exe'),
          StringStruct(u'ProductName', u'VisionLab Ai'),
          StringStruct(u'ProductVersion', u'4.0.0')])
      ]),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
