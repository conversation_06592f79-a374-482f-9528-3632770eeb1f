# src/widgets/collapsible_section.py
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame
from PySide6.QtCore import Qt, Signal, QSize
from PySide6.QtGui import QIcon, QPixmap

class CollapsibleSection(QWidget):
    """A custom widget that can be collapsed/expanded to show/hide its content."""
    
    # Signal emitted when section is expanded or collapsed
    toggled = Signal(bool)
    
    def __init__(self, title, parent=None):
        super().__init__(parent)
        self.title = title
        self.expanded = True
        
        # Main layout
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # Header layout
        self.header_widget = QWidget()
        self.header_widget.setCursor(Qt.PointingHandCursor)
        self.header_layout = QHBoxLayout(self.header_widget)
        self.header_layout.setContentsMargins(5, 5, 5, 5)
        
        # Toggle indicator (+ or -)
        self.toggle_label = QLabel("-")
        self.toggle_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        self.header_layout.addWidget(self.toggle_label)
        
        # Title label
        self.title_label = QLabel(title)
        self.title_label.setStyleSheet("font-weight: bold;")
        self.header_layout.addWidget(self.title_label)
        
        # Add stretch to push title to the left
        self.header_layout.addStretch()
        
        # Add header to main layout
        self.main_layout.addWidget(self.header_widget)
        
        # Add separator line
        self.separator = QFrame()
        self.separator.setFrameShape(QFrame.HLine)
        self.separator.setFrameShadow(QFrame.Sunken)
        self.main_layout.addWidget(self.separator)
        
        # Content widget
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(10, 10, 10, 10)
        self.main_layout.addWidget(self.content_widget)
        
        # Connect header click to toggle
        self.header_widget.mousePressEvent = self.toggle_section
        
    def toggle_section(self, event=None):
        """Toggles the section between expanded and collapsed states."""
        self.expanded = not self.expanded
        self.content_widget.setVisible(self.expanded)
        self.toggle_label.setText("-" if self.expanded else "+")
        self.toggled.emit(self.expanded)
        
    def add_widget(self, widget):
        """Adds a widget to the content layout."""
        self.content_layout.addWidget(widget)
        
    def add_layout(self, layout):
        """Adds a layout to the content layout."""
        self.content_layout.addLayout(layout)
        
    def set_expanded(self, expanded):
        """Sets the expanded state of the section."""
        if self.expanded != expanded:
            self.toggle_section()
