# src/utils/point_feature_extractor.py

import numpy as np
import cv2
from skimage import feature
from skimage.color import rgb2hsv, rgb2lab
from skimage.feature import local_binary_pattern
from skimage.feature import graycomatrix, graycoprops

class PointFeatureExtractor:
    """Extracts features from image patches around points for classification."""

    def __init__(self, patch_size=15, use_color=True, use_texture=True):
        """
        Initialize the feature extractor.

        Args:
            patch_size (int): Size of the square patch around each point (should be odd)
            use_color (bool): Whether to extract color features
            use_texture (bool): Whether to extract texture features
        """
        self.patch_size = patch_size if patch_size % 2 == 1 else patch_size + 1  # Ensure odd size
        self.half_size = self.patch_size // 2
        self.use_color = use_color
        self.use_texture = use_texture

    def extract_features(self, image, points):
        """
        Extract features for a list of points from an image.

        Args:
            image (numpy.ndarray): RGB image as numpy array
            points (list): List of (x, y, _) tuples representing point coordinates

        Returns:
            numpy.ndarray: Feature matrix with shape (n_points, n_features)
        """
        if len(points) == 0:
            return np.array([])

        # Pad image to handle points near edges
        padded_image = np.pad(image,
                             ((self.half_size, self.half_size),
                              (self.half_size, self.half_size),
                              (0, 0)),
                             mode='reflect')

        # Initialize feature list
        features_list = []

        # Extract patches and compute features for each point
        for point in points:
            # Handle different point formats
            if isinstance(point, tuple) and len(point) >= 2:
                x, y = point[:2]  # Just take the x, y coordinates
            else:
                # Skip invalid points
                continue
            # Convert to int if they're floats
            x_int, y_int = int(round(x)), int(round(y))

            # Extract patch from padded image (add padding offset)
            x_pad, y_pad = x_int + self.half_size, y_int + self.half_size
            patch = padded_image[y_pad-self.half_size:y_pad+self.half_size+1,
                                x_pad-self.half_size:x_pad+self.half_size+1]

            # Extract features from patch
            patch_features = self._extract_patch_features(patch)
            features_list.append(patch_features)

        # Stack all features into a single array
        return np.vstack(features_list)

    def _extract_patch_features(self, patch):
        """
        Extract features from a single patch.

        Args:
            patch (numpy.ndarray): Image patch

        Returns:
            numpy.ndarray: Feature vector
        """
        features = []

        # Color features
        if self.use_color:
            # RGB statistics
            for channel in range(3):
                channel_data = patch[:, :, channel].flatten()
                features.extend([
                    np.mean(channel_data),  # Mean
                    np.std(channel_data),   # Standard deviation
                    np.percentile(channel_data, 25),  # 1st quartile
                    np.percentile(channel_data, 75),  # 3rd quartile
                ])

            # HSV features
            hsv_patch = rgb2hsv(patch)
            for channel in range(3):
                channel_data = hsv_patch[:, :, channel].flatten()
                features.extend([
                    np.mean(channel_data),
                    np.std(channel_data),
                ])

            # LAB features
            try:
                lab_patch = rgb2lab(patch)
                for channel in range(3):
                    channel_data = lab_patch[:, :, channel].flatten()
                    features.extend([
                        np.mean(channel_data),
                        np.std(channel_data),
                    ])
            except Exception as e:
                # If LAB conversion fails, add zeros
                features.extend([0, 0] * 3)

        # Texture features
        if self.use_texture:
            # Convert to grayscale for texture features
            gray_patch = cv2.cvtColor(patch, cv2.COLOR_RGB2GRAY)

            # Local Binary Pattern
            try:
                radius = 1
                n_points = 8 * radius
                lbp = local_binary_pattern(gray_patch, n_points, radius, method='uniform')
                lbp_hist, _ = np.histogram(lbp.ravel(), bins=n_points + 2, range=(0, n_points + 2), density=True)
                features.extend(lbp_hist)
            except Exception as e:
                # If LBP fails, add zeros
                features.extend([0] * (8 * radius + 2))

            # GLCM features (Gray Level Co-occurrence Matrix)
            try:
                # Quantize gray levels to reduce computation
                gray_patch_8bit = (gray_patch * 255).astype(np.uint8)
                gray_patch_quantized = (gray_patch_8bit // 32).astype(np.uint8)  # 8 gray levels

                # Compute GLCM
                distances = [1]
                angles = [0, np.pi/4, np.pi/2, 3*np.pi/4]
                glcm = graycomatrix(gray_patch_quantized, distances, angles, levels=8, symmetric=True, normed=True)

                # Extract properties
                props = ['contrast', 'dissimilarity', 'homogeneity', 'energy', 'correlation']
                for prop in props:
                    features.extend(graycoprops(glcm, prop).flatten())
            except Exception as e:
                # If GLCM fails, add zeros
                features.extend([0] * (len(distances) * len(angles) * 5))

        return np.array(features)
