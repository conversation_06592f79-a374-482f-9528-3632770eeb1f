# src/gui/single_segment_dialog.py

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                              QPushButton, QListWidget, QListWidgetItem, QFrame,
                              QSizePolicy)
from PySide6.QtGui import QColor, QBrush
from PySide6.QtCore import Qt, Signal

class SingleSegmentDialog(QDialog):
    """Dialog for selecting a single segment to display."""

    segment_selected = Signal(tuple)  # Signal emitting the selected segment color

    def __init__(self, segment_colors, segment_names, parent=None):
        """Initialize the dialog.

        Args:
            segment_colors: Dictionary of segment colors (RGB tuples)
            segment_names: Dictionary mapping colors to segment names
            parent: Parent widget
        """
        super().__init__(parent)
        self.setWindowTitle("Select Segment to Display")
        self.setMinimumWidth(300)
        self.setMinimumHeight(400)

        self.segment_colors = segment_colors
        self.segment_names = segment_names

        self.setup_ui()

    def setup_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout(self)

        # Instructions
        instructions = QLabel("Select a segment to display:")
        instructions.setStyleSheet("font-weight: bold; font-size: 12pt;")
        layout.addWidget(instructions)

        # Description
        description = QLabel("Click on a segment below to display only that segment. Select 'All Segments' to return to the full segmentation view.")
        description.setWordWrap(True)
        layout.addWidget(description)

        # Segment list
        self.segment_list = QListWidget()
        self.segment_list.setSelectionMode(QListWidget.SelectionMode.SingleSelection)
        self.segment_list.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.segment_list.setMinimumHeight(250)  # Set minimum height
        self.segment_list.itemDoubleClicked.connect(self.accept_selection)  # Add double-click support
        layout.addWidget(self.segment_list, 1)  # Give it a stretch factor

        # Populate the list
        self.populate_segment_list()

        # Buttons
        button_layout = QHBoxLayout()

        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)

        self.display_button = QPushButton("Display")
        self.display_button.clicked.connect(self.accept_selection)
        self.display_button.setDefault(True)
        button_layout.addWidget(self.display_button)

        layout.addLayout(button_layout)

    def populate_segment_list(self):
        """Populate the list with segments."""
        self.segment_list.clear()

        # Add "All Segments" option at the top
        all_item = QListWidgetItem("All Segments (Reset)")
        all_item.setData(Qt.ItemDataRole.UserRole, None)  # No specific color
        self.segment_list.addItem(all_item)

        # Add a separator
        separator = QListWidgetItem()
        separator.setFlags(Qt.ItemFlag.NoItemFlags)
        separator_widget = QFrame()
        separator_widget.setFrameShape(QFrame.Shape.HLine)
        separator_widget.setFrameShadow(QFrame.Shadow.Sunken)
        self.segment_list.addItem(separator)
        self.segment_list.setItemWidget(separator, separator_widget)

        # Add each segment
        for i, color in enumerate(sorted(self.segment_colors.keys())):
            # Get segment name if available, otherwise use default
            segment_name = self.segment_names.get(color, f"Segment {i+1}")

            item = QListWidgetItem(segment_name)

            # Create a colored square
            color_rgb = self.segment_colors.get(color, color)
            item.setBackground(QBrush(QColor(color_rgb[0], color_rgb[1], color_rgb[2])))

            # Set text color for better visibility
            brightness = (0.299 * color_rgb[0] + 0.587 * color_rgb[1] + 0.114 * color_rgb[2]) / 255
            if brightness < 0.5:
                item.setForeground(QBrush(QColor(255, 255, 255)))  # White text for dark backgrounds
            else:
                item.setForeground(QBrush(QColor(0, 0, 0)))  # Black text for light backgrounds

            # Store the color as user data
            item.setData(Qt.ItemDataRole.UserRole, color)

            self.segment_list.addItem(item)

    def accept_selection(self):
        """Accept the selected segment and emit signal."""
        selected_items = self.segment_list.selectedItems()
        if not selected_items:
            return

        selected_color = selected_items[0].data(Qt.ItemDataRole.UserRole)
        self.segment_selected.emit(selected_color)
        self.accept()
