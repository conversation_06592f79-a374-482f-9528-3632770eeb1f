# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_all

datas = [('weights', 'weights'), ('src/gui/styles', 'src/gui/styles'), ('src/gui/icons', 'src/gui/icons'), ('src/grainsight_components/models', 'src/grainsight_components/models'), ('src/grainsight_components/weights', 'src/grainsight_components/weights'), ('src/detectron2', 'detectron2')]
binaries = []
hiddenimports = ['PySide6.QtCore', 'PySide6.QtGui', 'PySide6.QtWidgets', 'numpy', 'PIL', 'cv2', 'torch', 'torchvision', 'yaml', 'ultralytics', 'mobile_sam', 'xgboost', 'torch._C', 'torch.utils', 'torch.utils.data', 'torch.nn', 'torch.optim', 'detectron2', 'fvcore', 'iopath', 'omegaconf', 'expecttest']
tmp_ret = collect_all('xgboost')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]
tmp_ret = collect_all('fvcore')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]
tmp_ret = collect_all('iopath')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]


a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['tensorflow', 'tensorflow-plugins', 'keras', 'openvino', 'torch.testing._internal.opinfo', 'torch.distributed.elastic.multiprocessing.redirects', 'torch.distributed._sharding_spec', 'torch.distributed._sharded_tensor', 'torch.distributed._shard.checkpoint'],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='VisionLab Ai',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='VisionLab Ai',
)
