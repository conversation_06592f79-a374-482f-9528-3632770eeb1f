from PySide6.QtWidgets import (QDialog, QVBoxLayout, QGridLayout, QLabel,
                              QPushButton, QScrollArea, QWidget, QFileDialog)
from PySide6.QtCore import Qt, Signal
from src.utils.image_utils import resize_image, convert_cvimage_to_qpixmap
import cv2

class ImageThumbnail(QLabel):
    clicked = Signal(object)

    def __init__(self, image, parent=None):
        super().__init__(parent)
        self.image = image
        self.setFixedSize(150, 150)
        self.setAlignment(Qt.AlignCenter)
        thumbnail = resize_image(image.copy(), (140, 140))
        self.setPixmap(convert_cvimage_to_qpixmap(thumbnail))
        self.setStyleSheet("""
            QLabel {
                border: 2px solid #ccc;
                border-radius: 5px;
                padding: 5px;
            }
            QLabel:hover {
                border-color: #3498db;
            }
        """)

    def mousePressEvent(self, event):
        self.clicked.emit(self.image)

class ImageGalleryDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Image Gallery")
        self.resize(800, 600)
        self.images = []

        layout = QVBoxLayout(self)

        # Button layout removed - images are now only loaded from project hub

        # Scroll area for thumbnails
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        self.gallery_widget = QWidget()
        self.gallery_layout = QGridLayout(self.gallery_widget)
        scroll.setWidget(self.gallery_widget)
        layout.addWidget(scroll)

    # upload_images method removed - images are now only loaded from project hub

    def add_image(self, image):
        self.images.append(image)
        thumbnail = ImageThumbnail(image)
        thumbnail.clicked.connect(self.open_preprocessing)
        row = (len(self.images) - 1) // 4
        col = (len(self.images) - 1) % 4
        self.gallery_layout.addWidget(thumbnail, row, col)

    def open_preprocessing(self, image):
        from src.gui.preprocessing_dialog import PreprocessingDialog
        dialog = PreprocessingDialog(self.images, self)  # Pass all images
        # Find index of clicked image
        current_index = self.images.index(image)
        # Load current image
        dialog.current_index = current_index
        dialog.load_current_image()
        if dialog.exec():
            processed_images = dialog.get_result_images()
            # Update our stored images with the processed ones
            self.images = processed_images
            # Refresh all thumbnails
            self.refresh_thumbnails()

    def refresh_thumbnails(self):
        """Refreshes all thumbnails with current images."""
        # Clear existing thumbnails
        for i in reversed(range(self.gallery_layout.count())):
            self.gallery_layout.itemAt(i).widget().setParent(None)

        # Re-add all thumbnails
        for i, image in enumerate(self.images):
            thumbnail = ImageThumbnail(image)
            thumbnail.clicked.connect(self.open_preprocessing)
            row = i // 4
            col = i % 4
            self.gallery_layout.addWidget(thumbnail, row, col)
