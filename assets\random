import matplotlib.pyplot as plt
import numpy as np

# --- Data from the provided logs ---

# Log Set 1 (Faster R-CNN inferred)
times_set1 = [
    0.29744490000302903, 0.29867215000558645, 0.29429234999406617,
    0.2978062000038335, 0.28554944999632426, 0.31739110000489745,
    0.2944538999872748, 0.31058064999524504, 0.2901985000062268,
    0.3138496500032488, 0.30241920001572, 0.32907404999423306,
    0.3114814000145998, 0.2960969000123441, 0.31427430000621825,
    0.32442445000924636, 0.3344115000072634, 0.3359432500001276,
    0.32219330001680646, 0.320218750013737, 0.3163038000056986,
    0.31534990000363905, 0.31689339999866206, 0.3205859000008786,
    0.328044449997833
]

# Log Set 2 (<PERSON> <PERSON>-<PERSON> inferred)
times_set2 = [
    0.4267341000086162, 0.43398739998519886, 0.4277849500067532,
    0.4527282000053674, 0.4335743500123499, 0.43856189999496564,
    0.4414392500038957, 0.4457706999965012, 0.4453120499965735,
    0.4431586000137031, 0.44895740000356454, 0.445847250011866,
    0.4478753000003053, 0.46846100001130253, 0.4809535499953199,
    0.460902899998473, 0.44687725001131184, 0.4750052500021411,
    0.48703084999579005, 0.48158714998862706, 0.4661400000040885,
    0.4961817499861354, 0.48539195000194013, 0.46956779999891296,
    0.46630490000825375
]

# Calculate total time more accurately based on iteration jumps (19, then 20)
total_time_set1 = (times_set1[0] * 19) + sum(t * 20 for t in times_set1[1:])
total_time_set2 = (times_set2[0] * 19) + sum(t * 20 for t in times_set2[1:])

models = ['Faster R-CNN', 'Mask R-CNN']
total_times = [total_time_set1, total_time_set2]

# Calculate percentage difference
percentage_increase = ((total_times[1] - total_times[0]) / total_times[0]) * 100

# --- Create the Bar Chart ---

fig, ax = plt.subplots(figsize=(8, 6)) # Adjust figure size as needed

bars = ax.bar(models, total_times, color=['skyblue', 'lightcoral'])

# Add titles and labels
ax.set_title('Total Estimated Training Time (up to Iteration 499)', fontsize=14)
ax.set_xlabel('Model Architecture', fontsize=12)
ax.set_ylabel('Total Time (Seconds)', fontsize=12)
ax.set_ylim(0, max(total_times) * 1.15) # Add some space above the tallest bar

# Add the value on top of each bar
for bar in bars:
    yval = bar.get_height()
    plt.text(bar.get_x() + bar.get_width()/2.0, yval, f'{yval:.2f}', va='bottom', ha='center') # Format to 2 decimal places

# Add percentage difference annotation
annotation_text = f'{percentage_increase:.1f}% longer'
ax.annotate(annotation_text,
            xy=(bars[1].get_x() + bars[1].get_width()/2, bars[1].get_height()), # Position near the top of the Mask R-CNN bar
            xytext=(bars[1].get_x() + bars[1].get_width()/2, bars[1].get_height() + max(total_times)*0.08), # Offset text slightly
            ha='center', va='bottom',
            fontsize=12, color='red',
            arrowprops=dict(facecolor='black', arrowstyle='wedge,tail width=0.7,shrink tip=5', alpha=0.5))


plt.tight_layout() # Adjust layout to prevent labels overlapping

# Save the figure as an image
# You can specify a different format like .jpg, .png, .svg etc.
output_filename = 'slide2_total_training_time.png'
plt.savefig(output_filename, dpi=300) # Save with decent resolution

print(f"Created '{output_filename}' for Slide 2.")