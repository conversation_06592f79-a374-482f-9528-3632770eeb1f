from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QGridLayout, QScrollArea, QWidget, QColorDialog, QLineEdit, QFormLayout)
from PySide6.QtGui import QColor
from PySide6.QtCore import Qt, Signal

class ColorPickerDialog(QDialog):
    """A dialog for picking colors and names for multiple segments simultaneously."""

    colorsChanged = Signal(dict)  # Signal emitted when colors are changed
    namesChanged = Signal(dict)   # Signal emitted when names are changed

    def __init__(self, segment_colors, parent=None, segment_names=None):
        """Initialize the color picker dialog.

        Args:
            segment_colors (dict): Dictionary mapping segment labels to their colors
            parent: Parent widget
            segment_names (dict, optional): Dictionary mapping segment labels to their names
        """
        super().__init__(parent)
        self.setWindowTitle("Customize Segments")
        self.setModal(True)
        self.resize(700, 500)

        self.segment_colors = segment_colors.copy()
        self.segment_names = segment_names.copy() if segment_names else {}
        self.color_buttons = {}
        self.name_fields = {}

        # Initialize default names if not provided
        for segment in self.segment_colors:
            if segment not in self.segment_names:
                self.segment_names[segment] = f"Segment {segment}"

        self.setup_ui()

    def setup_ui(self):
        """Set up the dialog's user interface."""
        main_layout = QVBoxLayout(self)

        # Add title and description
        title_label = QLabel("Customize Segments")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        main_layout.addWidget(title_label)

        description_label = QLabel("Change colors and names for each segment in your image.")
        description_label.setWordWrap(True)
        main_layout.addWidget(description_label)
        main_layout.addSpacing(10)

        # Create a scroll area for the color grid
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # Container widget for the grid
        container = QWidget()
        grid_layout = QGridLayout(container)
        grid_layout.setSpacing(15)

        # Create color buttons for each segment
        row = 0
        col = 0
        max_cols = 3  # Number of columns in the grid

        for segment, color in sorted(self.segment_colors.items()):
            # Create container for each segment
            segment_widget = QWidget()
            segment_layout = QVBoxLayout(segment_widget)
            segment_layout.setSpacing(8)

            # Add segment number label
            segment_num_label = QLabel(f"Segment {segment}")
            segment_num_label.setStyleSheet("font-weight: bold; color: #0078d4;")
            segment_layout.addWidget(segment_num_label)

            # Add form layout for better organization
            form_layout = QFormLayout()
            form_layout.setSpacing(5)

            # Create color button
            color_button = QPushButton()
            color_button.setFixedSize(60, 60)
            self.update_button_color(color_button, color)
            color_button.clicked.connect(lambda checked=False, s=segment: self.pick_color(s))
            self.color_buttons[segment] = color_button

            # Create name field
            name_field = QLineEdit(self.segment_names.get(segment, f"Segment {segment}"))
            name_field.setPlaceholderText("Enter segment name")
            name_field.textChanged.connect(lambda text, s=segment: self.update_segment_name(s, text))
            self.name_fields[segment] = name_field

            # Add to form layout
            form_layout.addRow("Color:", color_button)
            form_layout.addRow("Name:", name_field)

            # Add form layout to segment layout
            segment_layout.addLayout(form_layout)

            # Add to grid
            grid_layout.addWidget(segment_widget, row, col)

            # Update grid position
            col += 1
            if col >= max_cols:
                col = 0
                row += 1

        scroll.setWidget(container)
        main_layout.addWidget(scroll)

        # Add button box
        button_layout = QHBoxLayout()
        apply_button = QPushButton("Apply")
        cancel_button = QPushButton("Cancel")

        apply_button.clicked.connect(self.accept)
        cancel_button.clicked.connect(self.reject)

        button_layout.addWidget(apply_button)
        button_layout.addWidget(cancel_button)
        main_layout.addLayout(button_layout)

    def update_button_color(self, button, color):
        """Update the color button's background color."""
        if isinstance(color, str):
            color = QColor(color)
        button.setStyleSheet(
            f"background-color: {color.name()}; "
            "border: 2px solid #666;"
        )

    def pick_color(self, segment):
        """Open color dialog for a specific segment."""
        current_color = QColor(self.segment_colors[segment])
        color = QColorDialog.getColor(current_color, self)

        if color.isValid():
            self.segment_colors[segment] = color.name()
            self.update_button_color(self.color_buttons[segment], color)

    def update_segment_name(self, segment, name):
        """Update the name for a specific segment."""
        self.segment_names[segment] = name

    def get_colors(self):
        """Get the current color mapping."""
        return self.segment_colors.copy()

    def get_names(self):
        """Get the current segment name mapping."""
        return self.segment_names.copy()