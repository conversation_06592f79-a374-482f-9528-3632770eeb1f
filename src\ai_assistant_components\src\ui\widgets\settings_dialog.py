# settings_dialog.py
# Dialog for managing API key and related settings for the AI assistant

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QLabel, QLineEdit, QDialogButtonBox, QMessageBox
)
from PySide6.QtCore import QSettings

class SettingsDialog(QDialog):
    """Dialog to configure application settings like API keys."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Settings")
        self.setMinimumWidth(400)

        self.settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4") # Company, App name

        layout = QVBoxLayout(self)

        # API Key
        layout.addWidget(QLabel("Google Gemini API Key:"))
        self.api_key_input = QLineEdit()
        self.api_key_input.setEchoMode(QLineEdit.EchoMode.Password)
        # Load saved key
        saved_key = self.settings.value("gemini/api_key", "")
        self.api_key_input.setText(saved_key)
        layout.addWidget(self.api_key_input)

        # Add more settings fields here if needed

        # Buttons
        self.button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        layout.addWidget(self.button_box)

    def accept(self):
        """Save settings when OK is clicked."""
        new_key = self.api_key_input.text().strip()
        self.settings.setValue("gemini/api_key", new_key)
        self.settings.sync()  # Force settings to be written to disk immediately

        # No message boxes - just save and close
        super().accept()

    @staticmethod
    def get_api_key():
        """Static method to retrieve the saved API key."""
        settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")
        return settings.value("gemini/api_key", "") # Return empty string if not set