# src/gui/ui/menu_ui.py

# No imports needed since we're not creating a menu bar anymore

class MenuUI:
    """Class for creating and managing the application menu."""

    def create_menu(self):
        """Creates the application menu bar or initializes menu actions."""
        # Initialize menu attributes as None to avoid NoneType errors
        self.fileMenu = None
        self.editMenu = None
        self.segMenu = None
        self.helpMenu = None

        # Initialize actions as None to avoid NoneType errors
        self.open_action = None
        self.save_action = None
        self.exit_action = None
        self.rand_colors_action = None
        self.pick_colors_action = None
        self.calc_percent_action = None
        self.start_seg_action = None
        self.merge_seg_action = None
        self.preprocess_action = None
        self.about_action = None

        # Skip creating the actual menu bar since we're using the toolbar instead