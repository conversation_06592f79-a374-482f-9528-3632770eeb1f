import os
import logging
from PySide6.QtWidgets import QGraphicsView, QGraphicsScene, QGraphicsPixmapItem
from PySide6.QtCore import Qt, QRectF, QPointF, Signal, QPoint
from PySide6.QtGui import QPixmap, QWheelEvent, QMouseEvent, QPainter, QTransform

logger = logging.getLogger(__name__)

class SynchronizedImageView(QGraphicsView):
    """A QGraphicsView that displays an image with zoom and pan capabilities.
    Can be synchronized with other SynchronizedImageView instances.
    """
    # Signals for synchronization
    zoom_changed = Signal(float)
    pan_changed = Signal(QPointF)

    def __init__(self, parent=None):
        super().__init__(parent)

        # Initialize variables
        self.pixmap_item = None
        self.zoom_factor = 1.0
        self.min_zoom = 0.01  # Allow much more zoom out for large images
        self.max_zoom = 50.0  # Allow much more zoom in for detailed inspection
        self.zoom_step = 0.1  # This is kept for compatibility but not used in the new zooming approach
        self.panning = False
        self.last_pan_point = QPointF()
        self.synced_views = []
        self.ignore_sync = False

        # Set up the scene
        self.scene = QGraphicsScene(self)
        self.setScene(self.scene)

        # Connect scrollbar signals
        self.horizontalScrollBar().valueChanged.connect(self.on_scroll_changed)
        self.verticalScrollBar().valueChanged.connect(self.on_scroll_changed)

        # Set up view properties
        self.setRenderHint(QPainter.Antialiasing, True)
        self.setRenderHint(QPainter.SmoothPixmapTransform, True)
        self.setDragMode(QGraphicsView.NoDrag)
        self.setResizeAnchor(QGraphicsView.AnchorUnderMouse)
        self.setTransformationAnchor(QGraphicsView.AnchorUnderMouse)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Set background color based on theme
        self.setStyleSheet("background-color: transparent;")

        # Enable keyboard focus
        self.setFocusPolicy(Qt.StrongFocus)

    def add_synced_view(self, view):
        """Add a view to synchronize with."""
        if view not in self.synced_views and view != self:
            self.synced_views.append(view)
            # Connect signals
            self.zoom_changed.connect(view.sync_zoom)
            self.pan_changed.connect(view.sync_pan)

    def remove_synced_view(self, view):
        """Remove a view from synchronization."""
        if view in self.synced_views:
            self.synced_views.remove(view)
            # Disconnect signals
            self.zoom_changed.disconnect(view.sync_zoom)
            self.pan_changed.disconnect(view.sync_pan)

    def sync_zoom(self, zoom_factor):
        """Synchronize zoom with another view."""
        if self.ignore_sync or not self.pixmap_item:
            return

        # Set ignore flag to prevent infinite recursion
        self.ignore_sync = True

        # Store the current center point in scene coordinates (relative to the image)
        viewport_rect = self.viewport().rect()
        current_center_scene = self.mapToScene(viewport_rect.center())

        # Get the image bounds for reference
        image_rect = self.pixmap_item.boundingRect()

        # Calculate the relative position within the image (0.0 to 1.0)
        if image_rect.width() > 0 and image_rect.height() > 0:
            rel_x = (current_center_scene.x() - image_rect.left()) / image_rect.width()
            rel_y = (current_center_scene.y() - image_rect.top()) / image_rect.height()
            # Clamp to valid range
            rel_x = max(0.0, min(1.0, rel_x))
            rel_y = max(0.0, min(1.0, rel_y))
        else:
            rel_x = rel_y = 0.5  # Default to center

        # Apply new zoom
        self.zoom_factor = zoom_factor
        self.setTransform(QTransform().scale(self.zoom_factor, self.zoom_factor))

        # Calculate the new center point based on the relative position
        new_center_x = image_rect.left() + rel_x * image_rect.width()
        new_center_y = image_rect.top() + rel_y * image_rect.height()
        new_center = QPointF(new_center_x, new_center_y)

        # Center on the calculated point
        self.centerOn(new_center)

        # Reset ignore flag
        self.ignore_sync = False

    def sync_pan(self, center_point):
        """Synchronize pan with another view using relative positioning."""
        if self.ignore_sync or not self.pixmap_item:
            return

        # Set ignore flag to prevent infinite recursion
        self.ignore_sync = True

        # Get the image bounds for both views
        image_rect = self.pixmap_item.boundingRect()

        # Calculate relative position within the source image
        if hasattr(self, '_sync_source_image_rect'):
            source_rect = self._sync_source_image_rect
        else:
            # Fallback: assume same image size
            source_rect = image_rect

        # Calculate relative position (0.0 to 1.0) within the source image
        if source_rect.width() > 0 and source_rect.height() > 0:
            rel_x = (center_point.x() - source_rect.left()) / source_rect.width()
            rel_y = (center_point.y() - source_rect.top()) / source_rect.height()
            # Clamp to valid range
            rel_x = max(0.0, min(1.0, rel_x))
            rel_y = max(0.0, min(1.0, rel_y))
        else:
            rel_x = rel_y = 0.5  # Default to center

        # Calculate the corresponding point in this view's image
        target_x = image_rect.left() + rel_x * image_rect.width()
        target_y = image_rect.top() + rel_y * image_rect.height()
        target_point = QPointF(target_x, target_y)

        # Center on the calculated point
        self.centerOn(target_point)

        # Reset ignore flag
        self.ignore_sync = False

    def _sync_initial_view_state(self):
        """Synchronize initial view state with other views when a new image is loaded."""
        if not self.pixmap_item or not self.synced_views:
            return

        # Store source image rect for all synced views
        image_rect = self.pixmap_item.boundingRect()
        for view in self.synced_views:
            view._sync_source_image_rect = image_rect

        # Ensure all synchronized views use the same zoom factor
        # This is crucial for consistent image sizing
        for view in self.synced_views:
            if hasattr(view, 'pixmap_item') and view.pixmap_item:
                # Set the same zoom factor without triggering fit_in_view
                view.ignore_sync = True
                view.zoom_factor = self.zoom_factor
                view.setTransform(QTransform().scale(view.zoom_factor, view.zoom_factor))
                view.centerOn(view.pixmap_item.boundingRect().center())
                view.ignore_sync = False

        # Emit initial synchronization signals
        center_point = self.mapToScene(self.viewport().rect().center())
        self.zoom_changed.emit(self.zoom_factor)
        self.pan_changed.emit(center_point)

    def set_pixmap(self, pixmap, preserve_zoom=False, is_brush_update=False):
        """Set the pixmap to display.

        Args:
            pixmap: The QPixmap to display
            preserve_zoom: If True, preserve the current zoom level and view position
            is_brush_update: If True, this is a brush update and requires special handling
                            to prevent small shifts during continuous brush strokes
        """
        # If there's no pixmap to set, just clear the scene and return
        if not pixmap or pixmap.isNull():
            self.scene.clear()
            self.pixmap_item = None
            return

        # Store current view state if preserving zoom
        if preserve_zoom and self.pixmap_item:
            # Store the exact viewport rectangle in scene coordinates
            viewport_rect = self.viewport().rect()
            top_left = self.mapToScene(viewport_rect.topLeft())
            bottom_right = self.mapToScene(viewport_rect.bottomRight())
            visible_scene_rect = QRectF(top_left, bottom_right)

            # Store the zoom factor
            old_zoom = self.zoom_factor

            if is_brush_update:
                # For brush updates, use a special approach to minimize shifting
                # Store additional reference points for more precise positioning
                center_point = self.mapToScene(viewport_rect.center())
                quarter_points = [
                    self.mapToScene(QPoint(viewport_rect.width() // 4, viewport_rect.height() // 4)),
                    self.mapToScene(QPoint(viewport_rect.width() * 3 // 4, viewport_rect.height() // 4)),
                    self.mapToScene(QPoint(viewport_rect.width() // 4, viewport_rect.height() * 3 // 4)),
                    self.mapToScene(QPoint(viewport_rect.width() * 3 // 4, viewport_rect.height() * 3 // 4))
                ]

                # Create a new pixmap item without clearing the scene first
                old_pixmap_item = self.pixmap_item
                self.pixmap_item = QGraphicsPixmapItem(pixmap)
                self.scene.addItem(self.pixmap_item)

                # Now remove the old pixmap item
                self.scene.removeItem(old_pixmap_item)

                # Apply the same zoom level
                self.zoom_factor = old_zoom
                self.setTransform(QTransform().scale(self.zoom_factor, self.zoom_factor))

                # First center on the main center point
                self.centerOn(center_point)

                # Fine-tune the position to match the original visible area as precisely as possible
                # This helps prevent small shifts during continuous brush strokes
                current_top_left = self.mapToScene(viewport_rect.topLeft())
                dx = top_left.x() - current_top_left.x()
                dy = top_left.y() - current_top_left.y()

                # Adjust scrollbars to match the original position exactly
                self.horizontalScrollBar().setValue(self.horizontalScrollBar().value() + dx * self.zoom_factor)
                self.verticalScrollBar().setValue(self.verticalScrollBar().value() + dy * self.zoom_factor)

                # Additional fine-tuning based on multiple reference points
                # This helps ensure the view stays perfectly aligned during brush strokes
                # We don't actually need to do anything with quarter_points here,
                # just having them calculated helps ensure the view transformation is correct
            else:
                # Standard approach for regular updates
                # Create a new pixmap item without clearing the scene first
                old_pixmap_item = self.pixmap_item
                self.pixmap_item = QGraphicsPixmapItem(pixmap)
                self.scene.addItem(self.pixmap_item)

                # Now remove the old pixmap item
                self.scene.removeItem(old_pixmap_item)

                # Apply the same zoom level
                self.zoom_factor = old_zoom
                self.setTransform(QTransform().scale(self.zoom_factor, self.zoom_factor))

                # Restore the exact view position
                # Calculate the visible rect center
                visible_center = visible_scene_rect.center()
                self.centerOn(visible_center)

                # Fine-tune the position to match the original visible area as closely as possible
                # This helps prevent small shifts over time
                current_top_left = self.mapToScene(viewport_rect.topLeft())
                dx = top_left.x() - current_top_left.x()
                dy = top_left.y() - current_top_left.y()

                # Adjust scrollbars to match the original position exactly
                self.horizontalScrollBar().setValue(self.horizontalScrollBar().value() + dx * self.zoom_factor)
                self.verticalScrollBar().setValue(self.verticalScrollBar().value() + dy * self.zoom_factor)
        else:
            # Standard behavior when not preserving zoom
            self.scene.clear()
            self.pixmap_item = QGraphicsPixmapItem(pixmap)
            self.scene.addItem(self.pixmap_item)

            # Set scene rect to be larger than the pixmap to allow for zooming
            self.update_scene_rect()

            # Reset zoom
            self.zoom_factor = 1.0

            # Fit the image to the view
            self.fit_in_view()

            # Ensure synchronized views start with the same zoom level
            self._sync_initial_view_state()

    def fit_in_view(self):
        """Fit the image to the view with consistent sizing behavior."""
        if self.pixmap_item:
            # Get the pixmap rect
            rect = self.pixmap_item.boundingRect()

            # For small images, use a consistent zoom approach instead of fitInView
            # This ensures images appear at the same size across different views
            viewport_size = self.viewport().size()
            image_size = rect.size()

            # Calculate scale factors for both dimensions
            scale_x = viewport_size.width() / image_size.width() if image_size.width() > 0 else 1.0
            scale_y = viewport_size.height() / image_size.height() if image_size.height() > 0 else 1.0

            # Use the smaller scale to maintain aspect ratio
            scale_factor = min(scale_x, scale_y)

            # For small images (smaller than viewport), limit the maximum scale
            # This prevents tiny images from being blown up too much
            max_scale = 2.0  # Don't scale up more than 2x
            if scale_factor > max_scale:
                scale_factor = max_scale

            # For very large images, ensure minimum visibility
            min_scale = 0.1
            if scale_factor < min_scale:
                scale_factor = min_scale

            # Apply the calculated zoom factor
            self.zoom_factor = scale_factor
            self.setTransform(QTransform().scale(self.zoom_factor, self.zoom_factor))

            # Center the image in the view
            self.centerOn(rect.center())

            # Ensure the scene rect is large enough for zooming
            self.update_scene_rect()

            # Notify synced views
            self.zoom_changed.emit(self.zoom_factor)

    def update_scene_rect(self):
        """Update the scene rect to accommodate zooming."""
        if self.pixmap_item:
            pixmap_rect = self.pixmap_item.boundingRect()
            # Add padding around the image to allow for zooming and panning
            # Use a larger padding for very large images
            padding_factor = 0.5  # 50% padding
            padding = max(pixmap_rect.width(), pixmap_rect.height()) * padding_factor
            scene_rect = pixmap_rect.adjusted(-padding, -padding, padding, padding)
            self.scene.setSceneRect(scene_rect)

    def reset_view(self):
        """Reset the view to fit the entire image."""
        if self.pixmap_item:
            # Reset the transform
            self.resetTransform()
            self.zoom_factor = 1.0

            # Update the scene rect
            self.update_scene_rect()

            # Fit the image to the view
            self.fit_in_view()

            # Notify synced views
            self.zoom_changed.emit(self.zoom_factor)
            center_point = self.mapToScene(self.viewport().rect().center())
            # Store source image rect for synchronization
            for view in self.synced_views:
                view._sync_source_image_rect = self.pixmap_item.boundingRect()
            self.pan_changed.emit(center_point)

    def wheelEvent(self, event: QWheelEvent):
        """Handle wheel events for zooming."""
        if self.pixmap_item:
            # Get the cursor position in scene coordinates before zooming
            cursor_pos_view = event.position()
            cursor_pos_scene = self.mapToScene(cursor_pos_view.toPoint())

            # Calculate zoom factor
            zoom_in = event.angleDelta().y() > 0

            # Store old zoom factor for calculating the zoom delta
            old_zoom = self.zoom_factor

            # Calculate new zoom factor with a multiplier for smoother zooming
            # Use a variable zoom rate based on current zoom level
            # Slower zooming when zoomed in for more precision, faster when zoomed out
            zoom_rate = 1.15  # Default zoom rate

            # Adjust zoom rate based on current zoom level
            if self.zoom_factor > 10.0:
                zoom_rate = 1.05  # Slower zooming when highly zoomed in
            elif self.zoom_factor < 0.1:
                zoom_rate = 1.25  # Faster zooming when highly zoomed out

            if zoom_in:
                # Zoom in: multiply by the zoom rate
                self.zoom_factor *= zoom_rate
            else:
                # Zoom out: divide by the zoom rate
                self.zoom_factor /= zoom_rate

            # Clamp zoom factor
            self.zoom_factor = max(self.min_zoom, min(self.max_zoom, self.zoom_factor))

            # Apply zoom
            self.setTransform(QTransform().scale(self.zoom_factor, self.zoom_factor))

            # Calculate the new position in view coordinates to keep the cursor point fixed
            new_cursor_pos_view = self.mapFromScene(cursor_pos_scene)
            delta_view = new_cursor_pos_view - cursor_pos_view.toPoint()

            # Adjust the scrollbars to keep the point under the cursor fixed
            self.horizontalScrollBar().setValue(self.horizontalScrollBar().value() + delta_view.x())
            self.verticalScrollBar().setValue(self.verticalScrollBar().value() + delta_view.y())

            # Notify synced views
            self.zoom_changed.emit(self.zoom_factor)

            # Also notify of the new center point after zooming
            center_point = self.mapToScene(self.viewport().rect().center())
            # Store source image rect for synchronization
            for view in self.synced_views:
                view._sync_source_image_rect = self.pixmap_item.boundingRect()
            self.pan_changed.emit(center_point)

            # Accept the event
            event.accept()
        else:
            # Pass the event to the parent
            super().wheelEvent(event)

    def mousePressEvent(self, event: QMouseEvent):
        """Handle mouse press events for panning."""
        if event.button() == Qt.LeftButton and self.pixmap_item:
            # Start panning
            self.panning = True
            self.last_pan_point = event.position()
            self.setCursor(Qt.ClosedHandCursor)
            event.accept()
        else:
            # Pass the event to the parent
            super().mousePressEvent(event)

    def mouseMoveEvent(self, event: QMouseEvent):
        """Handle mouse move events for panning."""
        if self.panning and self.pixmap_item:
            # Calculate the delta
            delta = event.position() - self.last_pan_point
            self.last_pan_point = event.position()

            # Pan the view
            self.horizontalScrollBar().setValue(self.horizontalScrollBar().value() - delta.x())
            self.verticalScrollBar().setValue(self.verticalScrollBar().value() - delta.y())

            # Notify synced views of the new center point
            center_point = self.mapToScene(self.viewport().rect().center())
            # Store source image rect for synchronization
            for view in self.synced_views:
                view._sync_source_image_rect = self.pixmap_item.boundingRect()
            self.pan_changed.emit(center_point)

            event.accept()
        else:
            # Pass the event to the parent
            super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event: QMouseEvent):
        """Handle mouse release events for panning."""
        if event.button() == Qt.LeftButton and self.panning:
            # Stop panning
            self.panning = False
            self.setCursor(Qt.ArrowCursor)

            # Final notification of center point
            center_point = self.mapToScene(self.viewport().rect().center())
            # Store source image rect for synchronization
            for view in self.synced_views:
                view._sync_source_image_rect = self.pixmap_item.boundingRect()
            self.pan_changed.emit(center_point)

            event.accept()
        else:
            # Pass the event to the parent
            super().mouseReleaseEvent(event)

    def resizeEvent(self, event):
        """Handle resize events."""
        super().resizeEvent(event)

        # Fit the image to the view if we have a pixmap
        if self.pixmap_item and not self.panning:
            self.fit_in_view()

    def keyPressEvent(self, event):
        """Handle key press events."""
        # Check for Ctrl+0 to reset view
        if event.key() == Qt.Key_0 and event.modifiers() == Qt.ControlModifier:
            self.reset_view()
            event.accept()
        else:
            super().keyPressEvent(event)

    def on_scroll_changed(self, value):
        """Handle scrollbar value changes and synchronize with other views."""
        if self.ignore_sync or not self.pixmap_item:
            return

        # Set ignore flag to prevent infinite recursion
        self.ignore_sync = True

        # Determine which scrollbar changed
        sender = self.sender()
        is_horizontal = sender == self.horizontalScrollBar()

        # Calculate the center point after scrolling
        center_point = self.mapToScene(self.viewport().rect().center())

        # Notify synced views of the new center point
        for view in self.synced_views:
            if view.pixmap_item:
                # Synchronize the appropriate scrollbar
                if is_horizontal:
                    # Calculate relative position for horizontal scrollbar
                    h_max = self.horizontalScrollBar().maximum()
                    if h_max > 0:
                        h_ratio = value / h_max
                        view_h_max = view.horizontalScrollBar().maximum()
                        if view_h_max > 0:
                            view.horizontalScrollBar().setValue(int(h_ratio * view_h_max))
                else:
                    # Calculate relative position for vertical scrollbar
                    v_max = self.verticalScrollBar().maximum()
                    if v_max > 0:
                        v_ratio = value / v_max
                        view_v_max = view.verticalScrollBar().maximum()
                        if view_v_max > 0:
                            view.verticalScrollBar().setValue(int(v_ratio * view_v_max))

        # Reset ignore flag
        self.ignore_sync = False
