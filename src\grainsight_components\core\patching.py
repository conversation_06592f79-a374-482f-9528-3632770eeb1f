# core/patching.py

import logging
import numpy as np
import torch
from typing import Generator, Tuple, List, Optional, Dict, Any, TypedDict
from PIL import Image
import cv2 # For bounding box NMS and contour finding
import time # For basic profiling

logger = logging.getLogger(__name__)

# --- generate_patch_coordinates (unchanged, assumed correct) ---
# (Include the generate_patch_coordinates function here as before)
def generate_patch_coordinates(
    image_width: int,
    image_height: int,
    rows: int,
    cols: int,
    overlap: float # Fraction (e.g., 0.15 for 15%)
    ) -> Generator[Tuple[int, int, int, int], None, None]:
    """
    Generates coordinates (x_start, y_start, patch_width, patch_height) for patches.
    [Code as provided previously, with potential improvements from last review]
    """
    if rows < 1 or cols < 1:
        raise ValueError("Rows and columns must be at least 1.")
    if not (0.0 <= overlap < 1.0):
        raise ValueError("Overlap must be between 0.0 and < 1.0")

    # Ensure floating point division
    step_x = float(image_width) / cols
    step_y = float(image_height) / rows

    overlap_w_px = step_x * overlap
    overlap_h_px = step_y * overlap

    patch_w_calc = step_x + overlap_w_px
    patch_h_calc = step_y + overlap_h_px

    patch_w = min(int(round(patch_w_calc)), image_width)
    patch_h = min(int(round(patch_h_calc)), image_height)

    advance_x = step_x
    advance_y = step_y


    logger.info(f"Patch Grid: {rows}x{cols}, Overlap: {overlap*100:.1f}%, "
                f"Image Size: ({image_width}x{image_height}), "
                f"Used Patch Size: ({patch_w}x{patch_h}), "
                f"Advance Step: ({advance_x:.1f}x{advance_y:.1f})")

    for r in range(rows):
        for c in range(cols):
            center_x = (c + 0.5) * advance_x
            center_y = (r + 0.5) * advance_y

            x_start = int(round(center_x - patch_w / 2.0))
            y_start = int(round(center_y - patch_h / 2.0))

            x_start = max(0, x_start)
            y_start = max(0, y_start)

            if x_start + patch_w > image_width:
                x_start = image_width - patch_w
            if y_start + patch_h > image_height:
                y_start = image_height - patch_h

            x_start = max(0, x_start)
            y_start = max(0, y_start)

            current_patch_w = min(patch_w, image_width - x_start)
            current_patch_h = min(patch_h, image_height - y_start)

            if current_patch_w > 0 and current_patch_h > 0:
                # logger.debug(f"Yielding patch ({r},{c}): Start=({x_start},{y_start}), Size=({current_patch_w}x{current_patch_h})")
                yield x_start, y_start, current_patch_w, current_patch_h
            else:
                 logger.warning(f"Skipping invalid patch ({r},{c}) at grid pos ({r},{c}) with zero dimension after clamping. Center=({center_x:.1f},{center_y:.1f}), Start=({x_start},{y_start}), Size=({patch_w}x{patch_h})")


# --- NMS function (modified to require scores, no mask input needed) ---
def non_max_suppression_by_bbox(
    bboxes: np.ndarray,          # BBoxes [N, 4] as numpy array [x1, y1, x2, y2]
    scores: np.ndarray,          # Scores [N] as numpy array
    iou_threshold: float = 0.2,
    containment_threshold: float = 0.85, # Threshold for suppressing contained boxes
    size_ratio_threshold: float = 5.0    # Minimum size ratio to consider containment
    ) -> List[int]:
    """
    Performs Non-Maximum Suppression based on bounding box IoU and containment.
    Operates purely on bounding boxes and scores.

    Args:
        bboxes: Numpy array of bounding boxes (N, 4) in [x1, y1, x2, y2] format.
        scores: Numpy array of confidence scores (N,).
        iou_threshold: IoU threshold for suppression based on standard IoU.
        containment_threshold: IoA threshold for suppression. If Intersection / Area(other_box)
                               is greater than this value, suppress the other_box.

    Returns:
        List[int]: Indices of the bboxes/scores to keep.
    """
    t_start_nms = time.time()
    if bboxes.shape[0] == 0:
        return []
    if bboxes.shape[0] != scores.shape[0]:
         raise ValueError(f"Shape mismatch: bboxes {bboxes.shape}, scores {scores.shape}")

    # Ensure float32 for calculations
    bboxes = bboxes.astype(np.float32)
    scores = scores.astype(np.float32)

    # --- NMS Implementation with IoU and Containment Check ---
    x1 = bboxes[:, 0]
    y1 = bboxes[:, 1]
    x2 = bboxes[:, 2]
    y2 = bboxes[:, 3]

    # Add 1 for area calculation robustness? Check if needed or causes issues.
    # Let's assume non-zero width/height from checks before calling NMS.
    areas = (np.maximum(0.0, x2 - x1)) * (np.maximum(0.0, y2 - y1))
    # areas = (np.maximum(0.0, x2 - x1 + 1)) * (np.maximum(0.0, y2 - y1 + 1)) # Original way

    # Handle potential zero areas gracefully to avoid NaN/inf
    areas = np.where(areas == 0, 1e-6, areas) # Replace 0 area with small epsilon

    order = scores.argsort()[::-1] # Sort by score descending

    keep_indices = []
    suppressed_by_iou = 0
    suppressed_by_containment = 0

    while order.size > 0:
        i = order[0]
        keep_indices.append(i)
        if order.size == 1: # No more boxes to compare with
            break

        # Calculate IoU and IoA between the current box 'i' and remaining boxes 'order[1:]'
        idx_others = order[1:]
        xx1 = np.maximum(x1[i], x1[idx_others])
        yy1 = np.maximum(y1[i], y1[idx_others])
        xx2 = np.minimum(x2[i], x2[idx_others])
        yy2 = np.minimum(y2[i], y2[idx_others])

        w = np.maximum(0.0, xx2 - xx1)
        h = np.maximum(0.0, yy2 - yy1)
        # w = np.maximum(0.0, xx2 - xx1 + 1)
        # h = np.maximum(0.0, yy2 - yy1 + 1)
        intersection = w * h

        # Standard IoU: Intersection / Union
        union = areas[i] + areas[idx_others] - intersection
        # Add epsilon to denominator to avoid division by zero
        iou = intersection / np.maximum(union, 1e-6)

        # Intersection over Area (IoA) of the *other* boxes (idx_others)
        ioa_other = intersection / areas[idx_others] # areas[idx_others] already has epsilon

        # Calculate size ratios to avoid suppressing small grains inside large ones
        area_i = areas[i]
        area_others = areas[idx_others]
        size_ratios = np.maximum(area_i / area_others, area_others / area_i)

        # Only apply containment check when size ratio exceeds threshold
        # This prevents large grains from eliminating smaller ones inside them
        containment_check = (ioa_other > containment_threshold) & (size_ratios < size_ratio_threshold)

        # Identify indices to remove based on *either* IoU or modified Containment threshold
        remove_mask = (iou > iou_threshold) | containment_check
        inds_to_remove = np.where(remove_mask)[0]
        inds_to_keep = np.where(~remove_mask)[0] # Indices of boxes to keep *relative to order[1:]*

        # --- Debugging Counts (Optional) ---
        # iou_triggered = iou > iou_threshold
        # containment_triggered = ioa_other > containment_threshold
        # suppressed_by_iou += np.sum(iou_triggered & (~containment_triggered))
        # suppressed_by_containment += np.sum(containment_triggered) # Count if containment is met, regardless of iou
        # -----------------------------------

        # Update order by keeping only the non-suppressed boxes
        order = order[inds_to_keep + 1] # +1 because inds_to_keep are relative to order[1:]

    t_end_nms = time.time()
    total_suppressed = bboxes.shape[0] - len(keep_indices)
    # logger.info(f"NMS complete in {t_end_nms - t_start_nms:.3f}s. Kept {len(keep_indices)} / {bboxes.shape[0]} objects. "
                # f"(Suppressed: {total_suppressed} total)") # Simplified logging
                # f"~{suppressed_by_iou} by IoU, "
                # f"~{suppressed_by_containment} by Containment)")

    # Return indices sorted ascending - they refer to the original input arrays
    return sorted(keep_indices)


# --- Function to detect subgrains with straight edges parallel to x/y axis ---
def is_rectangular_subgrain(contour: np.ndarray,
                           min_rect_angle_threshold: float = 5.0,
                           min_edge_straightness: float = 0.95,
                           min_parallel_edges: int = 2,
                           min_straight_edge_ratio: float = 0.25) -> bool:
    """
    Detects if a contour is likely a subgrain with straight edges parallel to x/y axis.
    Designed to detect grain fragments that have been cut by patch boundaries.

    Args:
        contour: OpenCV contour (Nx1x2 or Nx2)
        min_rect_angle_threshold: Maximum angle deviation from 0/90 degrees to consider edges aligned with x/y axis
        min_edge_straightness: Minimum ratio of polygon perimeter to actual contour perimeter
        min_parallel_edges: Minimum number of edges that must be parallel to x/y axis
        min_straight_edge_ratio: Minimum ratio of straight edge length to perimeter

    Returns:
        bool: True if the contour is likely a subgrain with straight edges parallel to x/y axis, False otherwise
    """
    if contour is None or len(contour) < 4:  # Need at least 4 points
        return False

    try:
        # Ensure contour is in the right format
        if isinstance(contour, np.ndarray):
            if len(contour.shape) == 3:  # Nx1x2 format
                contour = contour.reshape(contour.shape[0], 2)
        else:
            # Try to convert to numpy array if it's not already
            try:
                contour = np.array(contour)
                if len(contour.shape) == 3:  # Nx1x2 format
                    contour = contour.reshape(contour.shape[0], 2)
            except Exception as e:
                logger.error(f"Error converting contour to numpy array: {e}")
                return False

        # Get basic contour properties
        try:
            perimeter = cv2.arcLength(contour, True)
            area = cv2.contourArea(contour)
            if perimeter <= 0 or area <= 0:
                return False
        except Exception as e:
            logger.error(f"Error calculating contour properties: {e}")
            return False

        # Use polygon approximation to identify straight edges
        try:
            epsilon = 0.02 * perimeter
            approx = cv2.approxPolyDP(contour, epsilon, True)
        except Exception as e:
            logger.error(f"Error approximating polygon: {e}")
            return False

        # Get minimum area rectangle for orientation analysis
        try:
            min_rect = cv2.minAreaRect(contour)
            width, height = min_rect[1]
            rect_angle = min_rect[2]
        except Exception as e:
            logger.error(f"Error calculating minimum area rectangle: {e}")
            return False

        # Calculate aspect ratio to identify elongated shapes
        aspect_ratio = max(width, height) / min(width, height) if min(width, height) > 0 else 1.0
        is_elongated = aspect_ratio > 1.5  # Adjust this threshold as needed

        # Identify straight edges parallel to x/y axis
        straight_edges = []
        parallel_edges = 0
        total_straight_edge_length = 0

        for i in range(len(approx)):
            try:
                p1 = approx[i][0] if len(approx.shape) == 3 else approx[i]
                p2 = approx[(i + 1) % len(approx)][0] if len(approx.shape) == 3 else approx[(i + 1) % len(approx)]

                # Calculate edge properties
                dx = p2[0] - p1[0]
                dy = p2[1] - p1[1]
                edge_length = np.sqrt(dx*dx + dy*dy)

                # Skip very short edges
                if edge_length < 0.01 * perimeter:
                    continue

                # Check if edge is parallel to x or y axis
                is_parallel = False
                if abs(dx) < 1e-6:  # Vertical edge
                    is_parallel = True
                elif abs(dy) < 1e-6:  # Horizontal edge
                    is_parallel = True
                else:
                    # For non-axis-aligned edges, check if they're close to horizontal or vertical
                    angle_rad = np.arctan2(dy, dx)
                    angle_deg = np.degrees(angle_rad) % 180
                    if angle_deg > 90:
                        angle_deg = 180 - angle_deg

                    # Check if angle is close to 0 or 90 degrees
                    if angle_deg < min_rect_angle_threshold or abs(angle_deg - 90) < min_rect_angle_threshold:
                        is_parallel = True

                if is_parallel:
                    parallel_edges += 1
                    total_straight_edge_length += edge_length
                    straight_edges.append((p1, p2, edge_length))
            except Exception as e:
                logger.error(f"Error processing edge {i}: {e}")
                continue

        # Calculate ratio of straight edge length to perimeter
        straight_edge_ratio = total_straight_edge_length / perimeter if perimeter > 0 else 0

        # For debugging
        logger.debug(f"Subgrain detection: parallel_edges={parallel_edges}, "
                    f"straight_edge_ratio={straight_edge_ratio:.2f}, "
                    f"aspect_ratio={aspect_ratio:.2f}")

        # Return True if the contour meets the criteria for being a subgrain
        # 1. It should have at least min_parallel_edges parallel to x/y axis
        # 2. The straight edges should make up a significant portion of the perimeter
        return (parallel_edges >= min_parallel_edges and
                straight_edge_ratio >= min_straight_edge_ratio)

    except Exception as e:
        logger.error(f"Error in is_rectangular_subgrain: {e}")
        return False

# --- Define a structure for storing mask info before NMS ---
class MaskInfo(TypedDict):
    bbox: List[int]           # [x1, y1, x2, y2] in full image coordinates
    score: float              # Confidence or area (potentially adjusted for border proximity)
    patch_mask: torch.Tensor  # The actual mask *tensor slice* from the patch (on CPU)
    offset: Tuple[int, int]   # (offset_x, offset_y) of the patch origin


# --- Optimized merge_patch_results ---
def merge_patch_results(
    patch_annotations: List[Optional[Tuple[torch.Tensor, Tuple[int, int]]]], # List allowing None for failed patches
    full_image_shape: Tuple[int, int], # (height, width)
    device: torch.device,
    nms_iou_threshold: float = 0.5,
    nms_containment_threshold: float = 0.85,
    min_mask_area_threshold: int = 10,
    border_penalty_factor: float = 0.5, # Factor to penalize border masks (0=none, 1=max penalty)
    size_ratio_threshold: float = 5.0,  # Minimum size ratio to consider containment
    filter_subgrains: bool = True,      # Whether to filter out subgrains with straight edges
    subgrain_angle_threshold: float = 5.0,  # Maximum angle deviation for subgrain detection
    subgrain_edge_straightness: float = 0.95,  # Minimum edge straightness for subgrain detection
    subgrain_parallel_edges: int = 2,   # Minimum number of parallel edges for subgrain detection
    min_straight_edge_ratio: float = 0.25  # Minimum ratio of straight edge length to perimeter
    ) -> torch.Tensor:
    """
    Merges annotations from patches into a single tensor for the full image using
    optimized NMS pre-processing and border artifact handling.

    Args:
        patch_annotations: List containing tuples of (annotations tensor for patch, (offset_x, offset_y))
                           or None if a patch failed. Annotation tensors are (N, H_patch, W_patch).
        full_image_shape: Tuple (height, width) of the full original image.
        device: The torch device for final tensors.
        nms_iou_threshold: IoU threshold for standard NMS.
        nms_containment_threshold: Containment threshold for NMS.
        min_mask_area_threshold: Minimum pixel area (in the patch) for a mask to be processed.
        border_penalty_factor: How much to penalize the score of masks touching the patch border.
                               0 means no penalty, 1 means max penalty based on border contact.
        size_ratio_threshold: Minimum size ratio to consider containment.
        filter_subgrains: Whether to filter out subgrains with straight edges parallel to x/y axis.
        subgrain_angle_threshold: Maximum angle deviation for subgrain detection.
        subgrain_edge_straightness: Minimum edge straightness for subgrain detection.
        subgrain_parallel_edges: Minimum number of parallel edges for subgrain detection.
        min_straight_edge_ratio: Minimum ratio of straight edge length to perimeter for subgrain detection.

    Returns:
        torch.Tensor: A single tensor (M, H_full, W_full) containing the merged,
                      non-overlapping masks on the specified device. Returns empty tensor on failure.
    """
    t_start_merge = time.time()
    full_height, full_width = full_image_shape
    all_mask_info: List[MaskInfo] = [] # Store lightweight info before NMS

    total_patches = len(patch_annotations)
    logger.info(f"Starting merge for {total_patches} patches. Min area: {min_mask_area_threshold}. "
                f"NMS IoU: {nms_iou_threshold}, Containment: {nms_containment_threshold}. "
                f"Border Penalty: {border_penalty_factor:.2f}")

    processed_mask_count = 0
    skipped_empty = 0
    skipped_contour_fail = 0
    skipped_bbox_clamp = 0
    skipped_by_area = 0
    skipped_large_area_artifact = 0 # Counter for likely border artifacts based on large area
    skipped_subgrains = 0 # Counter for filtered rectangular subgrains
    candidate_masks = 0 # Total masks found in patches before filtering

    # --- Step 1: Collect candidate mask info (bboxes, scores, references) ---
    t_start_collect = time.time()
    for i, patch_data in enumerate(patch_annotations):
        if patch_data is None:
             skipped_empty += 1
             continue
        patch_mask_tensor, (offset_x, offset_y) = patch_data

        if patch_mask_tensor is None or patch_mask_tensor.dim() != 3 or patch_mask_tensor.shape[0] == 0:
            skipped_empty += 1
            continue # Skip empty or invalid tensors

        # Move to CPU once for processing this patch's masks
        try:
            patch_mask_tensor_cpu = patch_mask_tensor.cpu()
        except Exception as e:
             logger.error(f"Failed to move patch {i} tensor to CPU: {e}")
             skipped_empty += 1
             continue

        patch_h, patch_w = patch_mask_tensor_cpu.shape[1], patch_mask_tensor_cpu.shape[2]
        num_masks_in_patch = patch_mask_tensor_cpu.shape[0]
        candidate_masks += num_masks_in_patch
        # logger.debug(f"Processing patch {i+1}/{total_patches} at offset ({offset_x},{offset_y}) with {num_masks_in_patch} masks.")


        for mask_idx in range(num_masks_in_patch):
            mask_patch_tensor = patch_mask_tensor_cpu[mask_idx] # This is a view, efficient

            # --- Quick Area Check (on patch mask) ---
            try:
                 # Ensure boolean or int for sum
                 mask_area = torch.sum(mask_patch_tensor > 0.5 if mask_patch_tensor.is_floating_point() else mask_patch_tensor).item()
                 if mask_area < min_mask_area_threshold:
                     skipped_by_area += 1
                     continue
            except Exception as e:
                 logger.warning(f"Could not calculate area for mask {mask_idx} in patch {i}: {e}. Skipping.")
                 skipped_by_area+=1
                 continue

            # --- Filter potential large border artifacts early ---
            patch_area = patch_w * patch_h
            if patch_area > 0 and mask_area / patch_area > 0.95: # If mask covers >95% of patch, likely artifact
                skipped_large_area_artifact += 1
                continue

            try:
                # --- Get BBox using findContours (requires numpy) ---
                mask_patch_np = mask_patch_tensor.numpy().astype(np.uint8) # Convert only the single mask
                if mask_patch_np.max() == 1: mask_patch_np *= 255 # Ensure {0, 255}

                contours, _ = cv2.findContours(mask_patch_np, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                if not contours:
                    skipped_contour_fail += 1
                    continue

                contour = max(contours, key=cv2.contourArea) # Use largest contour

                # Check if this is a subgrain with straight edges parallel to x/y axis
                if filter_subgrains:
                    try:
                        is_subgrain = is_rectangular_subgrain(
                            contour,
                            min_rect_angle_threshold=subgrain_angle_threshold,
                            min_edge_straightness=subgrain_edge_straightness,
                            min_parallel_edges=subgrain_parallel_edges,
                            min_straight_edge_ratio=min_straight_edge_ratio
                        )
                        if is_subgrain:
                            skipped_subgrains += 1
                            continue
                    except Exception as e:
                        logger.error(f"Error in subgrain detection for contour: {e}")
                        # Continue processing this mask even if subgrain detection fails

                x_rel, y_rel, w_rel, h_rel = cv2.boundingRect(contour)

                # Calculate bbox relative to *full image* & clamp
                x1_full = offset_x + x_rel
                y1_full = offset_y + y_rel
                x2_full = x1_full + w_rel
                y2_full = y1_full + h_rel

                x1_clamped = max(0, x1_full)
                y1_clamped = max(0, y1_full)
                x2_clamped = min(full_width, x2_full) # Use full_width (exclusive index)
                y2_clamped = min(full_height, y2_full) # Use full_height (exclusive index)


                # Check if bbox is valid (non-zero width/height) AFTER clamping
                if x1_clamped >= x2_clamped or y1_clamped >= y2_clamped:
                    skipped_bbox_clamp +=1
                    continue

                # --- Calculate Border Proximity Score Penalty ---
                border_penalty = 0.0
                if border_penalty_factor > 0:
                    # Check how close the *relative* bbox is to the patch edges
                    touches_left = (x_rel == 0)
                    touches_right = (x_rel + w_rel == patch_w)
                    touches_top = (y_rel == 0)
                    touches_bottom = (y_rel + h_rel == patch_h)

                    # Calculate the percentage of the mask's perimeter that touches the border
                    # This is more accurate than just counting touches
                    perimeter = 2 * (w_rel + h_rel)  # Simple perimeter calculation
                    border_length = 0.0

                    if touches_left: border_length += h_rel
                    if touches_right: border_length += h_rel
                    if touches_top: border_length += w_rel
                    if touches_bottom: border_length += w_rel

                    # Calculate border contact ratio (0 to 1)
                    border_contact_ratio = min(border_length / perimeter, 1.0) if perimeter > 0 else 0.0

                    # Calculate area ratio (how much of the mask is near the border)
                    # This helps identify if the mask is just grazing the border or significantly cut by it
                    border_margin = min(patch_w, patch_h) * 0.05  # 5% of patch dimension as margin
                    near_border_area = 0

                    # Count pixels near each border the mask touches
                    if touches_left: near_border_area += h_rel * border_margin
                    if touches_right: near_border_area += h_rel * border_margin
                    if touches_top: near_border_area += w_rel * border_margin
                    if touches_bottom: near_border_area += w_rel * border_margin

                    # Normalize by mask area
                    area_ratio = min(near_border_area / (w_rel * h_rel), 1.0) if (w_rel * h_rel) > 0 else 0.0

                    # Combine both metrics with weights
                    border_proximity = 0.7 * border_contact_ratio + 0.3 * area_ratio

                    # Apply the penalty factor
                    border_penalty = border_proximity * border_penalty_factor


                # --- Calculate Final Score ---
                # Base score is mask area. Penalize based on border proximity.
                final_score = float(mask_area) * (1.0 - border_penalty)
                final_score = max(final_score, 1e-6) # Ensure score is not zero or negative


                # --- Store the lightweight info ---
                mask_info: MaskInfo = {
                    'bbox': [x1_clamped, y1_clamped, x2_clamped, y2_clamped],
                    'score': final_score, # Use the adjusted score
                    'patch_mask': mask_patch_tensor, # Store the tensor slice (on CPU)
                    'offset': (offset_x, offset_y)
                }
                all_mask_info.append(mask_info)
                processed_mask_count += 1

            except Exception as e:
                 logger.error(f"Error processing mask {mask_idx} from patch {i} during bbox/storage: {e}", exc_info=False) # Less verbose exc_info
                 # Optionally add a counter for these errors
                 continue
    t_end_collect = time.time()
    logger.info(f"Collected info for {processed_mask_count} candidates from {candidate_masks} raw masks in {t_end_collect - t_start_collect:.3f}s.")
    logger.info(f"(Skipped: {skipped_empty} empty/failed patches, {skipped_by_area} by area, "
                f"{skipped_large_area_artifact} large area artifacts, {skipped_contour_fail} contour fails, "
                f"{skipped_bbox_clamp} clamp fails, {skipped_subgrains} rectangular subgrains)")

    # Additional logging for debugging
    logger.info(f"Subgrain filtering parameters: enabled={filter_subgrains}, "
               f"angle_threshold={subgrain_angle_threshold}, "
               f"edge_straightness={subgrain_edge_straightness}, "
               f"parallel_edges={subgrain_parallel_edges}, "
               f"straight_edge_ratio={min_straight_edge_ratio}")

    if not all_mask_info:
         logger.warning("No valid mask candidates found after filtering.")
         return torch.empty((0, full_height, full_width), dtype=torch.uint8, device=device)

    # --- Step 2: Perform NMS ---
    t_start_nms_call = time.time()
    # Prepare data for NMS function
    bboxes_np = np.array([info['bbox'] for info in all_mask_info], dtype=np.float32)
    scores_np = np.array([info['score'] for info in all_mask_info], dtype=np.float32)

    keep_indices = non_max_suppression_by_bbox(
        bboxes_np,
        scores_np, # Use the adjusted scores
        iou_threshold=nms_iou_threshold,
        containment_threshold=nms_containment_threshold,
        size_ratio_threshold=size_ratio_threshold
    )
    t_end_nms_call = time.time()
    logger.info(f"NMS completed in {t_end_nms_call - t_start_nms_call:.3f}s. Keeping {len(keep_indices)} / {processed_mask_count} candidates.")


    if not keep_indices:
         logger.warning("NMS suppressed all candidates.")
         return torch.empty((0, full_height, full_width), dtype=torch.uint8, device=device)

    # --- Step 3: Reconstruct final masks ONLY for kept indices ---
    t_start_reconstruct = time.time()
    final_masks_list = []
    for idx in keep_indices:
        try:
            info = all_mask_info[idx]
            patch_mask_tensor = info['patch_mask'] # Already on CPU
            offset_x, offset_y = info['offset']
            patch_h, patch_w = patch_mask_tensor.shape[0], patch_mask_tensor.shape[1] # Shape of patch mask

            # Create the full-size mask *now* (on CPU initially)
            # Ensure the final mask is uint8
            full_mask = torch.zeros((full_height, full_width), dtype=torch.uint8, device='cpu')

            # Determine placement slices carefully
            y_start_place = offset_y
            y_end_place = min(offset_y + patch_h, full_height)
            x_start_place = offset_x
            x_end_place = min(offset_x + patch_w, full_width)

            # Determine slices from the patch mask tensor
            patch_y_start_slice = 0
            patch_y_end_slice = y_end_place - y_start_place
            patch_x_start_slice = 0
            patch_x_end_slice = x_end_place - x_start_place

            # Ensure slices are valid before assignment
            if patch_y_end_slice > 0 and patch_x_end_slice > 0 and \
               y_end_place > y_start_place and x_end_place > x_start_place:

                # Get the relevant slice and ensure it's boolean/binary for placement
                mask_slice = patch_mask_tensor[patch_y_start_slice:patch_y_end_slice, patch_x_start_slice:patch_x_end_slice]
                mask_slice_binary = (mask_slice > 0.5 if mask_slice.is_floating_point() else mask_slice).to(torch.uint8)

                # Place the patch mask data
                # **Important**: Use maximum operator to combine overlapping masks from NMS survivors
                # This ensures that if two kept masks overlap, the union is preserved.
                existing_mask_region = full_mask[y_start_place:y_end_place, x_start_place:x_end_place]
                full_mask[y_start_place:y_end_place, x_start_place:x_end_place] = torch.maximum(existing_mask_region, mask_slice_binary)

                # We add the mask to the list *after* potential modification by torch.maximum
                # It might be better to build a single large tensor and place into it directly?
                # For now, let's stick to the list approach, assuming NMS did its job well.
                # If we stack at the end, the last mask written wins in overlapping areas.
                # Let's refine the placement logic slightly.

                # Alternative: Create a list of masks and stack at the end.
                # The current approach modifies the *same* full_mask if indices are processed sequentially.
                # Let's create a *new* full_mask for each kept index and stack later.

                current_full_mask = torch.zeros((full_height, full_width), dtype=torch.uint8, device='cpu')
                current_full_mask[y_start_place:y_end_place, x_start_place:x_end_place] = mask_slice_binary
                final_masks_list.append(current_full_mask) # Add the individual mask

            else:
                 logger.warning(f"Skipping reconstruction for kept index {idx} due to invalid slice dimensions.")

        except Exception as e:
            logger.error(f"Error reconstructing final mask for kept index {idx}: {e}", exc_info=True)
            continue # Skip this mask

    t_end_reconstruct = time.time()
    logger.info(f"Reconstructed {len(final_masks_list)} final masks in {t_end_reconstruct - t_start_reconstruct:.3f}s.")


    if not final_masks_list:
         logger.warning("No masks were successfully reconstructed after NMS.")
         return torch.empty((0, full_height, full_width), dtype=torch.uint8, device=device)

    # --- Step 4: Stack final masks and move to target device ---
    # Stacking now correctly overlays masks based on the order NMS provided (implicitly)
    t_start_stack = time.time()
    try:
        # Stack on CPU first, then move, often more robust for large tensors
        final_masks_tensor_cpu = torch.stack(final_masks_list)
        # Combine overlapping masks after stacking using max projection
        # This ensures the final output has one layer but contains the union of kept masks
        # final_combined_mask = torch.max(final_masks_tensor_cpu, dim=0)[0] # Max projection if single layer output needed

        # Keep separate masks as output (M, H, W)
        final_masks = final_masks_tensor_cpu.to(device)

    except Exception as e:
        logger.error(f"Error stacking final masks (shape attempt: ({len(final_masks_list)}, {full_height}, {full_width})): {e}", exc_info=True)
        return torch.empty((0, full_height, full_width), dtype=torch.uint8, device=device)
    t_end_stack = time.time()

    t_end_merge = time.time()
    logger.info(f"Stacking and moving to device {device} took {t_end_stack - t_start_stack:.3f}s.")
    logger.info(f"Merging complete in {t_end_merge - t_start_merge:.3f}s. Final tensor shape: {final_masks.shape} on device {final_masks.device}")
    return final_masks # Shape (M, H_full, W_full)