; VisionLab Ai Professional Windows Installer Script
; Created for PetroSEG Vision Lab v4.0
; Inno Setup 6.0+ required

#define MyAppName "VisionLab Ai"
#define MyAppVersion "4.0.0"
#define MyAppPublisher "VisionLab Ai"
#define MyAppURL "https://visionlab-ai.com"
#define MyAppExeName "VisionLab_Ai_Simple.exe"
#define MyAppDescription "Petrographic Image Analysis Software"
#define MyAppCopyright "Copyright © 2024 VisionLab Ai. All rights reserved."
#define MyAppContact "<EMAIL>"

[Setup]
; NOTE: The value of AppId uniquely identifies this application.
; Do not use the same AppId value in installers for other applications.
AppId={{A1B2C3D4-E5F6-7890-ABCD-123456789012}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
AppCopyright={#MyAppCopyright}
AppContact={#MyAppContact}
AppComments={#MyAppDescription}

; Installation directories
DefaultDirName={autopf}\{#MyAppName}
DefaultGroupName={#MyAppName}
AllowNoIcons=yes

; Output configuration
OutputDir=output
OutputBaseFilename=VisionLab_Ai_v{#MyAppVersion}_Setup
; Custom branding files (uncomment if available)
; SetupIconFile=resources\app_icon.ico
; WizardImageFile=resources\wizard_image.bmp
; WizardSmallImageFile=resources\wizard_small.bmp

; Compression and encryption
Compression=lzma2/ultra64
SolidCompression=yes
LZMAUseSeparateProcess=yes
LZMADictionarySize=1048576

; System requirements
MinVersion=6.1sp1
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

; Privileges and behavior
PrivilegesRequired=admin
DisableProgramGroupPage=yes
DisableReadyPage=no
DisableFinishedPage=no
DisableWelcomePage=no

; Uninstaller
UninstallDisplayIcon={app}\{#MyAppExeName}
UninstallDisplayName={#MyAppName}
CreateUninstallRegKey=yes

; License and info
LicenseFile=resources\license.txt
InfoBeforeFile=resources\readme.txt

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1
Name: "associate"; Description: "Associate VisionLab Ai with project files (.vlab)"; GroupDescription: "File Associations"; Flags: unchecked
Name: "addtopath"; Description: "Add VisionLab Ai to system PATH (for command line access)"; GroupDescription: "System Integration"; Flags: unchecked

[Files]
; Main application files
Source: "..\dist\VisionLab_Ai_Simple\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
; NOTE: Don't use "Flags: ignoreversion" on any shared system files

[Icons]
; Start Menu shortcuts
Name: "{group}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\{#MyAppExeName}"; Comment: "{#MyAppDescription}"
Name: "{group}\Uninstall {#MyAppName}"; Filename: "{uninstallexe}"; Comment: "Uninstall {#MyAppName}"

; Desktop shortcut (optional)
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\{#MyAppExeName}"; Comment: "{#MyAppDescription}"; Tasks: desktopicon

; Quick Launch shortcut (optional, for older Windows versions)
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: quicklaunchicon

[Registry]
; File associations for .vlab project files
Root: HKCR; Subkey: ".vlab"; ValueType: string; ValueName: ""; ValueData: "VisionLabProject"; Flags: uninsdeletevalue; Tasks: associate
Root: HKCR; Subkey: "VisionLabProject"; ValueType: string; ValueName: ""; ValueData: "VisionLab Ai Project"; Flags: uninsdeletekey; Tasks: associate
Root: HKCR; Subkey: "VisionLabProject\DefaultIcon"; ValueType: string; ValueName: ""; ValueData: "{app}\{#MyAppExeName},0"; Tasks: associate
Root: HKCR; Subkey: "VisionLabProject\shell\open\command"; ValueType: string; ValueName: ""; ValueData: """{app}\{#MyAppExeName}"" ""%1"""; Tasks: associate

; Add to system PATH
Root: HKLM; Subkey: "SYSTEM\CurrentControlSet\Control\Session Manager\Environment"; ValueType: expandsz; ValueName: "Path"; ValueData: "{olddata};{app}"; Check: NeedsAddPath('{app}'); Tasks: addtopath

; Application registration for proper uninstall support
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppName}"; ValueType: string; ValueName: "DisplayName"; ValueData: "{#MyAppName}"; Flags: uninsdeletekey
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppName}"; ValueType: string; ValueName: "DisplayVersion"; ValueData: "{#MyAppVersion}"
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppName}"; ValueType: string; ValueName: "Publisher"; ValueData: "{#MyAppPublisher}"
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppName}"; ValueType: string; ValueName: "URLInfoAbout"; ValueData: "{#MyAppURL}"
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppName}"; ValueType: string; ValueName: "DisplayIcon"; ValueData: "{app}\{#MyAppExeName}"
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppName}"; ValueType: string; ValueName: "InstallLocation"; ValueData: "{app}"
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppName}"; ValueType: dword; ValueName: "NoModify"; ValueData: 1
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppName}"; ValueType: dword; ValueName: "NoRepair"; ValueData: 1

; Application settings registry
Root: HKLM; Subkey: "Software\{#MyAppPublisher}\{#MyAppName}"; ValueType: string; ValueName: "InstallPath"; ValueData: "{app}"; Flags: uninsdeletekey
Root: HKLM; Subkey: "Software\{#MyAppPublisher}\{#MyAppName}"; ValueType: string; ValueName: "Version"; ValueData: "{#MyAppVersion}"

[Run]
; Option to run the application after installation
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
; Clean up any files created during runtime
Type: filesandordirs; Name: "{app}\logs"
Type: filesandordirs; Name: "{app}\temp"
Type: filesandordirs; Name: "{app}\cache"
Type: files; Name: "{app}\*.log"

[Code]
// System requirements checking
function CheckSystemRequirements(): Boolean;
var
  Version: TWindowsVersion;
begin
  Result := True;

  // Check Windows version (Windows 10 or later)
  GetWindowsVersionEx(Version);
  if (Version.Major < 10) then
  begin
    if MsgBox('VisionLab Ai is designed for Windows 10 or later.' + #13#10 +
              'Your current version may not be fully supported.' + #13#10#13#10 +
              'Continue with installation anyway?', mbConfirmation, MB_YESNO) = IDNO then
    begin
      Result := False;
      Exit;
    end;
  end;
end;

// Simple check for running application (basic version)
function IsApplicationRunning(): Boolean;
begin
  // Simplified - let Windows installer handle process conflicts
  Result := False;
end;

// Check if path needs to be added to system PATH
function NeedsAddPath(Param: string): boolean;
var
  OrigPath: string;
begin
  if not RegQueryStringValue(HKEY_LOCAL_MACHINE,
    'SYSTEM\CurrentControlSet\Control\Session Manager\Environment',
    'Path', OrigPath)
  then begin
    Result := True;
    exit;
  end;
  // look for the path with leading and trailing semicolon
  // Pos() returns 0 if not found
  Result := Pos(';' + Param + ';', ';' + OrigPath + ';') = 0;
end;

// Custom installation logic
function InitializeSetup(): Boolean;
begin
  Result := CheckSystemRequirements();

  // Check if application is already running
  if Result and IsApplicationRunning() then
  begin
    if MsgBox('VisionLab Ai is currently running. Please close it before continuing with the installation.' + #13#10#13#10 +
              'Continue anyway?', mbConfirmation, MB_YESNO) = IDNO then
      Result := False;
  end;
end;

procedure InitializeWizard();
begin
  // Customize the installer wizard appearance
  WizardForm.WelcomeLabel1.Caption := 'Welcome to VisionLab Ai Setup';
  WizardForm.WelcomeLabel2.Caption := 'This will install VisionLab Ai v{#MyAppVersion} on your computer.' + #13#10#13#10 +
    'VisionLab Ai is a professional petrographic image analysis software for geological research and analysis.' + #13#10#13#10 +
    'Features include AI-powered segmentation, grain analysis, batch processing, and comprehensive reporting tools.' + #13#10#13#10 +
    'Click Next to continue, or Cancel to exit Setup.';
end;

function PrepareToInstall(var NeedsRestart: Boolean): String;
begin
  Result := '';
  NeedsRestart := False;
  // Let Inno Setup handle disk space and permission checks automatically
end;

procedure CurStepChanged(CurStep: TSetupStep);
var
  ConfigDir: String;
  LogDir: String;
begin
  if CurStep = ssPostInstall then
  begin
    // Create application data directories
    ConfigDir := ExpandConstant('{userappdata}\VisionLab Ai');
    LogDir := ExpandConstant('{userappdata}\VisionLab Ai\logs');

    ForceDirectories(ConfigDir);
    ForceDirectories(LogDir);

    // Set proper permissions for application data
    // This ensures the application can write configuration and log files
  end;
end;

function ShouldSkipPage(PageID: Integer): Boolean;
begin
  Result := False;
  // Skip components page if only one component
  if PageID = wpSelectComponents then
    Result := True;
end;

// Check if application is running before uninstall
function InitializeUninstall(): Boolean;
begin
  Result := True;

  if IsApplicationRunning() then
  begin
    if MsgBox('VisionLab Ai is currently running. Please close it before continuing with the uninstall.' + #13#10#13#10 +
              'Continue anyway?', mbConfirmation, MB_YESNO) = IDNO then
      Result := False;
  end;
end;

// Custom uninstall cleanup
procedure CurUninstallStepChanged(CurUninstallStep: TUninstallStep);
var
  UserDataDir: String;
  Response: Integer;
begin
  if CurUninstallStep = usPostUninstall then
  begin
    // Ask user if they want to remove user data
    UserDataDir := ExpandConstant('{userappdata}\VisionLab Ai');
    if DirExists(UserDataDir) then
    begin
      Response := MsgBox('Do you want to remove user data and settings?' + #13#10 +
                        'This includes projects, preferences, and logs.' + #13#10#13#10 +
                        'Choose "No" to keep your data for future installations.',
                        mbConfirmation, MB_YESNO);
      if Response = IDYES then
      begin
        DelTree(UserDataDir, True, True, True);
      end;
    end;
  end;
end;
