#!/usr/bin/env python3
"""
Debug script to test drawing and erasing functionality.
This script will add temporary debug output to help identify the issue.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.gui.handlers.trainable_segmentation_handlers import TrainableSegmentationHandlers

# Add debug prints to key methods
original_on_mouse_pressed = TrainableSegmentationHandlers.on_mouse_pressed
original_on_mouse_moved = TrainableSegmentationHandlers.on_mouse_moved
original_apply_brush_or_erase = TrainableSegmentationHandlers.apply_brush_or_erase
original_toggle_draw_mode = TrainableSegmentationHandlers.toggle_draw_mode
original_toggle_erase_mode = TrainableSegmentationHandlers.toggle_erase_mode

def debug_on_mouse_pressed(self, pos):
    print(f"\n=== DEBUG: on_mouse_pressed called at ({pos.x()}, {pos.y()}) ===")
    print(f"Drawing mode: {getattr(self, 'drawing_mode', False)}")
    print(f"Erasing mode: {getattr(self, 'erasing_mode', False)}")
    if hasattr(self, 'trainable_original_view'):
        print(f"View drawing_enabled: {getattr(self.trainable_original_view, 'drawing_enabled', False)}")
        print(f"View erasing_enabled: {getattr(self.trainable_original_view, 'erasing_enabled', False)}")
    return original_on_mouse_pressed(self, pos)

def debug_on_mouse_moved(self, pos):
    print(f"\n=== DEBUG: on_mouse_moved called at ({pos.x()}, {pos.y()}) ===")
    print(f"Drawing mode: {getattr(self, 'drawing_mode', False)}")
    print(f"Erasing mode: {getattr(self, 'erasing_mode', False)}")
    print(f"Drawing flag: {getattr(self, 'drawing', False)}")
    return original_on_mouse_moved(self, pos)

def debug_apply_brush_or_erase(self, x, y):
    print(f"\n=== DEBUG: apply_brush_or_erase called at ({x}, {y}) ===")
    print(f"Drawing mode: {getattr(self, 'drawing_mode', False)}")
    print(f"Erasing mode: {getattr(self, 'erasing_mode', False)}")
    print(f"Trainable image exists: {hasattr(self, 'trainable_image') and self.trainable_image is not None}")
    if hasattr(self, 'trainable_image') and self.trainable_image is not None:
        print(f"Image shape: {self.trainable_image.shape}")
    return original_apply_brush_or_erase(self, x, y)

def debug_toggle_draw_mode(self):
    print(f"\n=== DEBUG: toggle_draw_mode called ===")
    print(f"Current drawing_mode before toggle: {getattr(self, 'drawing_mode', False)}")
    result = original_toggle_draw_mode(self)
    print(f"Drawing_mode after toggle: {getattr(self, 'drawing_mode', False)}")
    if hasattr(self, 'trainable_original_view'):
        print(f"View drawing_enabled after toggle: {getattr(self.trainable_original_view, 'drawing_enabled', False)}")
    return result

def debug_toggle_erase_mode(self):
    print(f"\n=== DEBUG: toggle_erase_mode called ===")
    print(f"Current erasing_mode before toggle: {getattr(self, 'erasing_mode', False)}")
    result = original_toggle_erase_mode(self)
    print(f"Erasing_mode after toggle: {getattr(self, 'erasing_mode', False)}")
    if hasattr(self, 'trainable_original_view'):
        print(f"View erasing_enabled after toggle: {getattr(self.trainable_original_view, 'erasing_enabled', False)}")
    return result

# Monkey patch the methods
TrainableSegmentationHandlers.on_mouse_pressed = debug_on_mouse_pressed
TrainableSegmentationHandlers.on_mouse_moved = debug_on_mouse_moved
TrainableSegmentationHandlers.apply_brush_or_erase = debug_apply_brush_or_erase
TrainableSegmentationHandlers.toggle_draw_mode = debug_toggle_draw_mode
TrainableSegmentationHandlers.toggle_erase_mode = debug_toggle_erase_mode

print("Debug patches applied to TrainableSegmentationHandlers")
print("Now run the main application and test drawing/erasing functionality")
print("Watch the console output for detailed debug information")

if __name__ == "__main__":
    # Run the main application by executing main.py
    import subprocess
    import sys
    print("\nStarting main application with debug patches...")
    print("Please test the drawing and erasing functionality and watch the console output.")
    subprocess.run([sys.executable, "main.py"])