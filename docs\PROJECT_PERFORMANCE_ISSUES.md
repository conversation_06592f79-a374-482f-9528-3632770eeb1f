# VisionLab Project Performance Issues - Diagnosis and Repair

## Overview

This document addresses a critical performance issue that can occur in VisionLab projects where image import operations result in extremely slow project loading times due to large uncompressed annotation files.

## Problem Description

### Symptoms
- **Extremely slow project loading** (minutes instead of seconds)
- **Large project file sizes** (multiple GB for small projects)
- **Application freezing** during project open operations
- **High memory usage** when loading projects

### Root Cause
The issue is caused by **large uncompressed annotation files** stored in the project's grain analysis state data. When users perform grain analysis on high-resolution images with many detected grains, the annotation data can become massive (2-3 GB per image) if stored without compression.

### Technical Details
- **File Format**: Annotations are stored as NumPy arrays in `.npz` files
- **Data Structure**: Arrays with shape like `(689, 1075, 3720)` representing individual grain masks
- **Storage Method**: Previously used `np.savez()` (uncompressed) instead of `np.savez_compressed()`
- **Impact**: A single annotation file can reach 2.7 GB uncompressed vs ~400 MB compressed

## Diagnosis

### Quick Check
1. **Check project file size**: If your `.vlp` file is larger than 1 GB, it likely has this issue
2. **Loading time**: If project loading takes more than 30 seconds, performance issues are likely
3. **Use the analysis tool**:
   ```bash
   python repair_project.py your_project.vlp --analyze-only
   ```

### Detailed Analysis
The repair tool will show:
- Total project size
- Number of large annotation files
- Estimated space savings from compression
- Specific files causing issues

## Solution

### For New Projects (Prevention)
The issue has been **fixed in the latest version** of VisionLab. New projects will automatically use compressed annotation storage and include:

- **Automatic compression** of annotation files
- **Size monitoring** with warnings for large files
- **Performance checks** during project loading
- **Optimized storage** for grain analysis data

### For Existing Corrupted Projects (Repair)

#### Option 1: Automated Repair Tool (Recommended)
Use the built-in repair tool to fix corrupted projects:

```bash
# Analyze the project first
python repair_project.py your_project.vlp --analyze-only

# Repair with backup (recommended)
python repair_project.py your_project.vlp --backup

# Repair without backup (faster, but risky)
python repair_project.py your_project.vlp --no-backup
```

#### Option 2: Manual Repair
If the automated tool doesn't work:

1. **Create a backup** of your project file
2. **Extract the project** using any ZIP tool (`.vlp` files are ZIP archives)
3. **Find large annotation files** in `state/grain_analysis/*/annotations.npz`
4. **Compress the files** using Python:
   ```python
   import numpy as np
   
   # Load and re-save with compression
   with np.load('annotations.npz', allow_pickle=True) as data:
       file_data = {key: data[key] for key in data.keys()}
   np.savez_compressed('annotations.npz', **file_data)
   ```
5. **Recreate the ZIP file** with the compressed annotations

#### Option 3: Start Fresh
For severely corrupted projects:
1. **Export your images** from the corrupted project
2. **Create a new project** in the latest VisionLab version
3. **Re-import the images** (the new version will prevent the issue)
4. **Re-run analysis** if needed (will be stored efficiently)

## Expected Results

After repair, you should see:
- **90-95% reduction** in project file size
- **10-50x faster** project loading times
- **Reduced memory usage** during operation
- **Normal application responsiveness**

### Example Results
```
Original project: 7.2 GB → Repaired project: 450 MB
Loading time: 5 minutes → Loading time: 15 seconds
Space saved: 6.8 GB (94% reduction)
```

## Prevention Best Practices

### For Users
1. **Update to the latest version** of VisionLab
2. **Monitor project sizes** - if a project exceeds 500 MB, investigate
3. **Use appropriate image resolutions** - extremely high-resolution images create more annotation data
4. **Process images in batches** rather than all at once for very large datasets

### For Developers
1. **Always use `np.savez_compressed()`** for annotation storage
2. **Monitor file sizes** during save operations
3. **Implement size warnings** for large annotation files
4. **Use lazy loading** for annotation data when possible

## Troubleshooting

### Repair Tool Issues
- **"Permission denied"**: Close VisionLab before running repair
- **"Memory error"**: Try repairing on a machine with more RAM
- **"Corruption detected"**: Use manual repair method

### Still Slow After Repair
- **Check available RAM**: Large projects need sufficient memory
- **Verify SSD storage**: Projects load faster from SSD drives
- **Update VisionLab**: Ensure you have the latest performance improvements

### Prevention Failed
If new projects still create large files:
- **Check VisionLab version**: Ensure you have the fix
- **Review image sizes**: Consider reducing resolution for very large images
- **Monitor during analysis**: Watch for size warnings in the logs

## Technical Implementation

The fix involves:
1. **Replacing `np.savez()` with `np.savez_compressed()`** in annotation saving
2. **Adding size monitoring** and warnings during save operations
3. **Implementing performance checks** during project loading
4. **Creating repair utilities** for existing corrupted projects

## Support

If you continue to experience issues:
1. **Check this documentation** for troubleshooting steps
2. **Run the analysis tool** to get detailed information
3. **Contact support** with the analysis results
4. **Provide project details**: size, number of images, analysis types performed

For technical support: <EMAIL>
