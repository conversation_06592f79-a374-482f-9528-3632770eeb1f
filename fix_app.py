import os
import sys
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_app():
    """Fix the VisionLabAiApp class to handle missing YOLOv8 settings."""
    try:
        # Path to the app.py file
        app_py_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src', 'gui', 'app.py')
        
        # Read the file
        with open(app_py_path, 'r') as f:
            content = f.read()
        
        # Add a method to handle missing YOLOv8 settings
        if 'def __getattr__(self, name):' not in content:
            # Find the class definition
            class_def = 'class VisionLabAiApp(VisionLabAiAppUI, ImageHandlers, SegmentationHandlers, AnalysisHandlers, SettingsHandlers, TrainableSegmentationHandlers, GalleryHandlers, AIAssistantHandlers, AIAssistantMarkdownHandlers):'
            
            # Add the __getattr__ method after the class definition
            getattr_method = '''
    def __getattr__(self, name):
        """Handle missing attributes gracefully."""
        if name.startswith('default_yolo_'):
            # Return a dummy object for YOLOv8 settings
            class DummyValue:
                def value(self):
                    return 0.25  # Default value
            return DummyValue()
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")
'''
            
            # Insert the method after the class definition
            new_content = content.replace(class_def, class_def + getattr_method)
            
            # Write the updated content back to the file
            with open(app_py_path, 'w') as f:
                f.write(new_content)
            
            logger.info(f"Added __getattr__ method to {app_py_path}")
        else:
            logger.info(f"__getattr__ method already exists in {app_py_path}")
        
        return True
    except Exception as e:
        logger.error(f"Error fixing app: {e}")
        return False

if __name__ == "__main__":
    logger.info("Starting app fix...")
    success = fix_app()
    if success:
        logger.info("App fix completed successfully.")
    else:
        logger.error("App fix failed.")
