
import sys
import os
import math
import json
import logging
import threading
import time
from datetime import datetime
import locale
import cv2
import numpy as np
import pandas as pd
import torch
from ultralytics import YOLO
import matplotlib
matplotlib.use('QtAgg')  # Use QtAgg backend compatible with PySide6
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
from PIL import Image, ImageDraw, ImageFont
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtCore import (Qt, QSize, QPoint, QRect, QPointF, QRectF, # <--- ADDED QRectF HERE
                           QItemSelectionModel, QItemSelection,
                           Signal, Slot, QObject, QThread, QTimer,
                           QMetaObject, QMetaMethod, Q_ARG, Q_RETURN_ARG) # <--- Existing Q_ARG

# MobileSAM imports
from mobile_sam.predictor import SamPredictor
from mobile_sam.build_sam import sam_model_registry
from mobile_sam.automatic_mask_generator import SamA<PERSON>maticMaskGenerator

from PySide6.QtGui import (QPixmap, QImage, QIcon, QPainter, QPen, QColor,
                           QAction, QFont, QFontDatabase, QKeySequence, QPalette, QStandardItemModel, QStandardItem)
from src.grainsight_components.gui.utils import define_light_theme, define_dark_theme, LIGHT_STYLESHEET, DARK_STYLESHEET
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QFileDialog, QMenu,
                               QMessageBox, QToolBar, QStatusBar, QVBoxLayout,
                               QLabel, QSlider, QCheckBox, QLineEdit, QPushButton,
                               QGraphicsView, QGraphicsScene, QGraphicsPixmapItem,
                               QGraphicsLineItem, QFrame, QGridLayout, QRadioButton,
                               QHBoxLayout, QScrollArea, QTreeView, QHeaderView, QStyledItemDelegate,
                               QProgressDialog, QDialog, QAbstractItemView, QSizePolicy,
                               QGraphicsPolygonItem, QGraphicsTextItem, QTabWidget, QGroupBox,
                               QListWidget, QListWidgetItem, QSplitter, QGraphicsRectItem,
                               QToolButton, QProgressBar, QStackedWidget # <--- ADDED QStackedWidget HERE
                               )

# import qmetaobject and qarg
from PySide6.QtCore import QMetaObject, QMetaMethod, Q_ARG
# import qreacf




# --- Locale Setup ---
try:
    # Try setting to user's default locale for formatting
    locale.setlocale(locale.LC_ALL, '')
except locale.Error:
    try:
        # Fallback to a common locale like 'en_US.UTF-8' or 'C'
        locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
    except locale.Error:
        locale.setlocale(locale.LC_ALL, 'C') # Last resort

# --- Logging Setup ---
logging.basicConfig(
    level=logging.DEBUG, # Set to DEBUG for development, INFO for release
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("grainsight_analysis.log", mode='w'), # Overwrite log each time
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# --- Resource Path ---
if getattr(sys, 'frozen', False):
    application_path = sys._MEIPASS
else:
    application_path = os.path.dirname(os.path.abspath(__file__))

def resource_path(relative_path):
    """ Get absolute path to resource, works for dev and for PyInstaller """
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

# --- Constants ---
SOLIDITY_THRESHOLD = 1.1 # Solidity should theoretically be <= 1. Allow a small tolerance.
DEFAULT_INPUT_SIZE = 1024
DEFAULT_IOU_THRESHOLD = 0.7
DEFAULT_CONF_THRESHOLD = 0.5
DEFAULT_MAX_DET = 500
DEFAULT_CONTOUR_THICKNESS = 1

# --- Utility Functions ---
def calculate_pixel_length(start_point, end_point):
    """Calculates the pixel length of a line."""
    return math.dist((start_point.x(), start_point.y()), (end_point.x(), end_point.y()))

def compute_length_width_feret(contour):
    """
    Computes the Length and Width using Feret diameter (maximum and minimum caliper distances).
    """
    if contour is None or len(contour) < 3:
        logger.warning("Contour too small or None for Feret calculation.")
        return 0, 0

    try:
        # Ensure contour is Nx2
        if contour.shape[-1] != 2:
             contour = contour.reshape(-1, 2)

        # Calculate convex hull - ensure it's also Nx1x2 or Nx2
        hull = cv2.convexHull(contour)
        if hull is None or len(hull) < 3:
            logger.warning("Convex hull could not be computed or is too small.")
            return 0, 0
        if hull.shape[-1] != 2:
             hull = hull.reshape(-1, 2)


        min_rect = cv2.minAreaRect(hull) # ((center_x, center_y), (width, height), angle)
        (w, h) = min_rect[1] # width and height of the minimum area rectangle

        # The longer side is typically considered the length
        length = max(w, h)
        width = min(w, h)

        return length, width

    except cv2.error as e:
        logger.error(f"OpenCV error in compute_length_width_feret: {e}")
        logger.debug(f"Contour shape: {contour.shape}, dtype: {contour.dtype}")
        return 0, 0
    except Exception as e:
        logger.exception(f"Unexpected error in compute_length_width_feret: {e}")
        return 0, 0


def calculate_parameters(annotations, scale_factor, progress_callback=None):
    """
    Calculates morphological parameters for each segmented object using its contour.
    Uses convex area for certain calculations where appropriate.
    Includes progress reporting via signal or direct call. <--- Updated docstring
    """
    total_objects = len(annotations)
    if total_objects == 0:
        return pd.DataFrame(), np.array([], dtype=bool)

    valid_mask = np.ones(total_objects, dtype=bool)
    results = [] # Use a list to build results, then create DataFrame

    logger.info(f"Calculating parameters for {total_objects} annotations with scale {scale_factor:.4f} µm/pixel.")

    # --- Determine how to call the progress callback ---
    report_progress = None
    if progress_callback:
        if hasattr(progress_callback, 'emit') and callable(progress_callback.emit):
            # It's a Qt Signal, use emit
            report_progress = lambda value: progress_callback.emit(value)
            logger.debug("Using progress_callback.emit()")
        elif callable(progress_callback):
            # It's a callable function/slot, call directly
            report_progress = lambda value: progress_callback(value)
            logger.debug("Using direct progress_callback()")
        else:
            logger.warning("progress_callback provided but is not a Signal or callable.")

    # ----------------------------------------------------

    for i, mask_tensor in enumerate(annotations):
        if report_progress: # Use the determined reporter function
            progress_value = int((i / total_objects) * 100) if total_objects > 0 else 0
            report_progress(progress_value) # Emit progress percentage or call function

        try:
            if mask_tensor is None:
                logger.warning(f"Annotation {i} is None, skipping.")
                valid_mask[i] = False
                continue

            # Ensure mask is a numpy array on CPU
            binary_mask = mask_tensor.cpu().numpy().astype(np.uint8)

            # Find the largest external contour
            contours, _ = cv2.findContours(binary_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            if not contours:
                logger.warning(f"No contours found for annotation {i}, skipping.")
                valid_mask[i] = False
                continue

            contour = max(contours, key=cv2.contourArea)

            if len(contour) < 5:
                logger.warning(f"Contour for annotation {i} has < 5 points ({len(contour)}), skipping.")
                valid_mask[i] = False
                continue

            contour = contour.squeeze()
            if len(contour.shape) != 2 or contour.shape[1] != 2:
                 contour = contour.reshape(-1, 2)

            # 1. Centroid
            M = cv2.moments(contour)
            if M["m00"] <= 0:
                logger.warning(f"Invalid moments m00 ({M['m00']}) for annotation {i}, skipping.")
                valid_mask[i] = False
                continue
            center_x = int(M["m10"] / M["m00"])
            center_y = int(M["m01"] / M["m00"])

            # 2. Area (Pixel & Micron) - Use contour area
            area_pixel = cv2.contourArea(contour)
            if area_pixel <= 0:
                 logger.warning(f"Contour area is zero or negative ({area_pixel}) for annotation {i}, skipping.")
                 valid_mask[i] = False
                 continue
            area_micron = area_pixel * (scale_factor ** 2)

            # 3. Perimeter (Pixel & Micron)
            perimeter_pixel = cv2.arcLength(contour, True)
            if perimeter_pixel <= 0:
                 logger.warning(f"Perimeter is zero or negative ({perimeter_pixel}) for annotation {i}, skipping.")
                 valid_mask[i] = False
                 continue
            perimeter_micron = perimeter_pixel * scale_factor

            # 4. Convex Hull and related properties
            hull = cv2.convexHull(contour)
            if hull is None or len(hull) < 3:
                 logger.warning(f"Convex hull computation failed or hull too small for annotation {i}, skipping.")
                 valid_mask[i] = False
                 continue

            convex_area_pixel = cv2.contourArea(hull)
            if convex_area_pixel <= 0:
                 logger.warning(f"Convex area is zero or negative ({convex_area_pixel}) for annotation {i}, skipping.")
                 valid_mask[i] = False
                 continue

            convex_perimeter_pixel = cv2.arcLength(hull, True)
            convex_perimeter_micron = convex_perimeter_pixel * scale_factor

            # 5. Solidity
            solidity = area_pixel / convex_area_pixel if convex_area_pixel != 0 else 0
            if solidity > SOLIDITY_THRESHOLD or solidity <= 0:
                logger.warning(f"Annotation {i} failed solidity check ({solidity:.3f}), skipping.")
                valid_mask[i] = False
                continue

            # 6. Convexity
            convexity = convex_perimeter_micron / perimeter_micron if perimeter_micron != 0 else 0

            # 7. Compactness
            compactness = (4 * np.pi * area_micron) / (perimeter_micron ** 2) if perimeter_micron != 0 else 0

            # 8. Circle-Equivalent Diameter
            equivalent_diameter = np.sqrt(4 * area_micron / np.pi)

            # 9. Length & Width
            length_pixel, width_pixel = compute_length_width_feret(hull)
            if length_pixel <= 0 or width_pixel <= 0:
                 logger.warning(f"Invalid length/width ({length_pixel}, {width_pixel}) for annotation {i}, skipping.")
                 valid_mask[i] = False
                 continue
            length_micron = length_pixel * scale_factor
            width_micron = width_pixel * scale_factor

            if width_micron > length_micron:
                length_micron, width_micron = width_micron, length_micron

            # 10. Elongation
            elongation = width_micron / length_micron if length_micron != 0 else 0

            # 11. Roundness
            roundness = (4 * area_micron) / (np.pi * (length_micron ** 2)) if length_micron != 0 else 0

            # 12. Rectangularity
            min_rect_area_micron = length_micron * width_micron
            rectangularity = area_micron / min_rect_area_micron if min_rect_area_micron != 0 else 0

            # 13. Ellipticity
            ellipticity = (length_micron - width_micron) / length_micron if length_micron != 0 else 0

            # Store results for this valid object
            results.append({
                'Object': f"Object {i}", # Placeholder, will be updated later
                'Center_X (px)': center_x,
                'Center_Y (px)': center_y,
                'Area (µm²)': round(area_micron, 3),
                'Perimeter (µm)': round(perimeter_micron, 3),
                'Compactness': round(compactness, 3),
                'Circle-Equivalent Diameter (µm)': round(equivalent_diameter, 3),
                'Length (µm)': round(length_micron, 3),
                'Width (µm)': round(width_micron, 3),
                'Elongation': round(elongation, 3),
                'Ellipticity': round(ellipticity, 3),
                'Rectangularity': round(rectangularity, 3),
                'Solidity': round(solidity, 3),
                'Convexity': round(convexity, 3),
                'Roundness': round(roundness, 3),
            })

        except Exception as e:
            logger.exception(f"Error calculating parameters for annotation {i}: {e}")
            valid_mask[i] = False
            continue

    # --- Final progress update ---
    if report_progress:
         report_progress(100) # Ensure it finishes at 100%
    # ---------------------------

    if not results:
         logger.warning("No valid objects found after parameter calculation.")
         return pd.DataFrame(), np.array([], dtype=bool)

    df = pd.DataFrame(results)

    # Filter the original valid_mask based on actual successful calculations
    final_valid_indices = df.index
    final_valid_mask = np.zeros(total_objects, dtype=bool)
    original_indices = [j for j, valid in enumerate(valid_mask) if valid]

    df_to_original_map = {}
    valid_counter = 0
    original_indices_in_df = []
    for original_idx, is_initially_valid in enumerate(valid_mask):
        if is_initially_valid:
            if valid_counter < len(df):
                 df_to_original_map[valid_counter] = original_idx
                 original_indices_in_df.append(original_idx)
                 final_valid_mask[original_idx] = True
            valid_counter += 1

    if not df.empty:
        df['Object'] = [f"Object {original_indices_in_df[i]}" for i in range(len(df))]
        df.index = original_indices_in_df

    logger.info(f"Successfully calculated parameters for {len(df)} objects.")

    # Return the mask indicating which of the *original* input annotations are still valid
    return df, final_valid_mask


# --- Segmentation Functions (Modified for clarity) ---

def segment_image(pil_image, model, device, input_size=1024, iou_threshold=0.7, conf_threshold=0.25, max_det=500, points_per_side=32, pred_iou_thresh=0.88, stability_score_thresh=0.95, box_nms_thresh=0.3):
    """Performs segmentation on the input PIL image.
    Supports both FastSAM (YOLO) and MobileSAM (SamAutomaticMaskGenerator) models.
    """
    original_w, original_h = pil_image.size

    # Check model type to determine segmentation approach
    if isinstance(model, YOLO):  # FastSAM model
        logger.info(f"Starting FastSAM segmentation: input_size={input_size}, iou={iou_threshold}, conf={conf_threshold}, max_det={max_det}")
        scale = input_size / max(original_w, original_h)
        new_w = int(original_w * scale)
        new_h = int(original_h * scale)
        resized_image = pil_image.resize((new_w, new_h), Image.Resampling.BILINEAR) # Use BILINEAR

        # Run FastSAM model inference
        results = model(resized_image, retina_masks=True, iou=iou_threshold, conf=conf_threshold,
                       imgsz=input_size, max_det=max_det, verbose=False) # verbose=False

        if not results or results[0].masks is None:
            logger.warning("FastSAM segmentation returned no masks.")
            return [], torch.empty(0) # Return empty list and tensor

        # Get masks (already on the correct device from the model)
        masks_tensor = results[0].masks.data # Shape: (N, H_resized, W_resized)

        # Resize masks back to original image dimensions using torch (potentially faster on GPU)
        masks_resized = torch.nn.functional.interpolate(
            masks_tensor.unsqueeze(1), # Add channel dim: (N, 1, H_r, W_r)
            size=(original_h, original_w),
            mode='nearest' # Use nearest for masks
        ).squeeze(1) # Remove channel dim: (N, H_orig, W_orig)

        # Binarize masks (sometimes models output probabilities)
        masks_resized = (masks_resized > 0.5).byte() # Threshold at 0.5

        logger.info(f"FastSAM segmentation finished. Found {len(masks_resized)} initial masks.")
        return results, masks_resized # Return raw results and resized masks

    else:  # MobileSAM model (SamAutomaticMaskGenerator)
        logger.info(f"Starting MobileSAM segmentation: points_per_side={points_per_side}, pred_iou_thresh={pred_iou_thresh:.2f}, stability_score_thresh={stability_score_thresh:.2f}, box_nms_thresh={box_nms_thresh:.2f}")
        # Convert PIL image to numpy array for MobileSAM
        np_image = np.array(pil_image)

        # Run MobileSAM model inference
        # The generate method returns a list of dictionaries with mask data
        mask_data = model.generate(np_image)

        if not mask_data or len(mask_data) == 0:
            logger.warning("MobileSAM segmentation returned no masks.")
            return [], torch.empty(0) # Return empty list and tensor

        # Convert MobileSAM masks to tensor format compatible with the rest of the pipeline
        masks_list = []
        for mask_dict in mask_data:
            # Get binary mask from segmentation data
            if isinstance(mask_dict['segmentation'], dict):  # RLE format
                # Convert RLE to binary mask if needed
                from pycocotools import mask as mask_utils
                binary_mask = mask_utils.decode(mask_dict['segmentation'])
            else:  # Already binary mask format
                binary_mask = mask_dict['segmentation']

            # Convert to tensor and add to list
            mask_tensor = torch.from_numpy(binary_mask).to(device)
            masks_list.append(mask_tensor)

        # Stack masks into a single tensor if we have any
        if masks_list:
            masks_resized = torch.stack(masks_list)
        else:
            masks_resized = torch.empty(0, device=device)

        logger.info(f"MobileSAM segmentation finished. Found {len(masks_list)} initial masks.")
        return mask_data, masks_resized  # Return mask data and resized masks


def create_segmented_visualization(pil_image, annotations, contour_thickness=1, contour_color=(255, 255, 0)): # Yellow contours default
    """
    Creates a visual representation with ONLY contours drawn on the original image.
    Skips filling masks for clarity and performance.

    Args:
        pil_image (PIL.Image): The original base image.
        annotations (list or torch.Tensor): List/Tensor of mask tensors (binary, uint8).
        contour_thickness (int): Thickness of the contour lines.
        contour_color (tuple): RGB tuple for the contour color (e.g., (255, 255, 0) for yellow).

    Returns:
        PIL.Image: The original image with contours drawn on top (RGBA format).
    """
    if annotations is None or isinstance(annotations, list) and not annotations or hasattr(annotations, 'size') and annotations.size(0) == 0:
        # Return a copy in RGBA format if no annotations
        return pil_image.copy().convert("RGBA")

    # Start with the original image converted to RGBA for compositing
    vis_image = pil_image.copy().convert("RGBA")
    # Create a transparent overlay to draw contours onto
    overlay = Image.new('RGBA', vis_image.size, (0, 0, 0, 0)) # Fully transparent
    draw = ImageDraw.Draw(overlay)

    num_annotations = len(annotations)
    logger.info(f"Creating contour-only visualization for {num_annotations} annotations.")

    # Define contour color with full alpha
    contour_color_rgba = contour_color + (255,) # Ensure full opacity for contours

    for i, mask_tensor in enumerate(annotations):
        try:
            # Ensure mask is numpy uint8 on CPU
            mask_np = mask_tensor.cpu().numpy().astype(np.uint8)
            if np.sum(mask_np) == 0: # Skip empty masks
                continue

            # Find external contours
            contours, _ = cv2.findContours(mask_np, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            if not contours:
                continue

            # --- Draw Contour ---
            # Draw the largest contour found for this mask
            contour = max(contours, key=cv2.contourArea)
            # Squeeze removes unnecessary dimensions, flatten creates a list [x1, y1, x2, y2, ...]
            contour_flat = contour.squeeze().flatten().tolist()
            if len(contour_flat) >= 4: # Need at least 2 points (4 coords) for a line
                # Draw line on the TRANSPARENT overlay
                # Add first point to end to close the polygon contour
                 draw.line(contour_flat + contour_flat[:2], fill=contour_color_rgba, width=contour_thickness)

            # --- MASK FILLING IS SKIPPED ---

        except Exception as e:
            logger.error(f"Error drawing contour for annotation {i}: {e}")
            continue # Skip this annotation if drawing fails

    # Composite the overlay (containing only contours) onto the original image
    # alpha_composite requires both images to be RGBA
    vis_image = Image.alpha_composite(vis_image, overlay)
    logger.info("Contour-only visualization created.")
    return vis_image



# --- Worker Thread for Processing ---
class ProcessingWorker(QObject):
    finished = Signal(object, object, object) # df, annotations, segmented_image_vis
    progress = Signal(int)
    error = Signal(str)
    status = Signal(str)

    def __init__(self, pil_image, model, device, scale_factor, params):
        super().__init__()
        self.pil_image = pil_image
        self.model = model
        self.device = device
        self.scale_factor = scale_factor
        self.params = params
        self._is_running = True

    def stop(self):
        self._is_running = False

    @Slot()
    def run(self):
        """Executes the segmentation and parameter calculation in the worker thread."""
        try:
            if not self._is_running: return
            self.status.emit("Starting segmentation...")
            self.progress.emit(5)

            # 1. Segmentation
            model_type = self.params.get('model_type', None) # Get model type from params
            if model_type is None:
                raise ValueError("Model type not specified in parameters.")

            logger.info(f"Processing with {model_type} model in worker.")

            raw_results = None # Store results from either model
            annotations_full_res = None # Store masks from either model

            # Convert PIL image to numpy array (used by both potentially)
            # Ensure it's in RGB format for consistency if models expect it
            try:
                if self.pil_image.mode != 'RGB':
                    np_image = np.array(self.pil_image.convert('RGB'))
                    logger.debug(f"Converted PIL image from {self.pil_image.mode} to RGB for processing.")
                else:
                    np_image = np.array(self.pil_image)
            except Exception as img_conv_e:
                raise ValueError(f"Failed to convert input image to NumPy array: {img_conv_e}")


            if model_type == "fastsam":
                # --- FastSAM Logic ---
                if not isinstance(self.model, YOLO):
                     # If model is base SAM, this indicates a mismatch
                     # Allow it to proceed but log warning? Or raise error? Let's raise.
                     raise TypeError(f"Model type mismatch: Expected YOLO model for FastSAM, got {type(self.model).__name__}.")

                required_params = ['input_size', 'iou', 'conf', 'max_det']
                if not all(param in self.params for param in required_params):
                    missing = [p for p in required_params if p not in self.params]
                    raise ValueError(f"Missing required FastSAM parameters: {missing}")

                # Move model to GPU if available, just for this inference run
                logger.debug(f"Moving FastSAM model to device: {self.device}")
                yolo_model = self.model.to(self.device)
                yolo_model.eval() # Ensure eval mode
                logger.debug(f"FastSAM model moved successfully.")

                # --- Call simplified segment_image for FastSAM ---
                # Pass the original PIL image, segment_image handles resizing internally now
                pil_image_for_seg = self.pil_image

                results, annotations_full_res = segment_image(
                    pil_image_for_seg, # Pass original PIL image
                    yolo_model,        # Pass model instance (now on device)
                    self.device,       # Pass device
                    input_size=self.params['input_size'],
                    iou_threshold=self.params['iou'],
                    conf_threshold=self.params['conf'],
                    max_det=self.params['max_det']
                )
                raw_results = results # Store raw YOLO results if needed later

                # Move model back to CPU if needed to conserve GPU memory (optional)
                if self.device.type == 'cuda':
                    logger.debug("Moving FastSAM model back to CPU.")
                    self.model.to('cpu')
                    torch.cuda.empty_cache()
                    logger.debug("CUDA cache cleared.")


            elif model_type == "mobilesam":
                # --- MobileSAM Logic ---
                # Check if self.model is the base SAM model (not YOLO, not Generator)
                # A more robust check might be needed if other model types are added
                if isinstance(self.model, YOLO) or isinstance(self.model, SamAutomaticMaskGenerator):
                     raise TypeError(f"Model type mismatch: Expected base SAM model for MobileSAM, got {type(self.model).__name__}.")

                required_params = ['points_per_side', 'pred_iou_thresh', 'stability_score_thresh',
                                   'box_nms_thresh', 'crop_n_layers', 'crop_n_points_downscale_factor']
                if not all(param in self.params for param in required_params):
                    missing = [p for p in required_params if p not in self.params]
                    raise ValueError(f"Missing required MobileSAM parameters: {missing}")

                # --- Instantiate SamAutomaticMaskGenerator HERE ---
                generator_params = {
                    'points_per_side': self.params['points_per_side'],
                    'pred_iou_thresh': self.params['pred_iou_thresh'],
                    'stability_score_thresh': self.params['stability_score_thresh'],
                    'box_nms_thresh': self.params['box_nms_thresh'],
                    'crop_n_layers': self.params['crop_n_layers'],
                    'crop_n_points_downscale_factor': self.params['crop_n_points_downscale_factor'],
                    # Add other generator params like 'min_mask_region_area' if needed/exposed in UI
                    'min_mask_region_area': self.params.get('min_mask_region_area', 0) # Example: Default 0 if not in params
                }
                logger.info(f"Initializing SamAutomaticMaskGenerator with params: {generator_params}")

                # Move base sam model to device and set eval mode
                logger.debug(f"Moving MobileSAM base model to device: {self.device}")
                sam_model = self.model.to(self.device)
                sam_model.eval()
                logger.debug(f"MobileSAM base model moved successfully.")


                # --- IMPORTANT: Create Generator Instance ---
                # The model passed to the generator MUST be the one on the correct device
                mask_generator = SamAutomaticMaskGenerator(model=sam_model, **generator_params)


                # --- Run MobileSAM Segmentation ---
                logger.info(f"Starting MobileSAM segmentation generate...")
                # The generate method expects an image in HWC uint8 format (RGB)
                if np_image.dtype != np.uint8:
                    logger.warning(f"NumPy image dtype is {np_image.dtype}, converting to uint8 for SAM.")
                    np_image = np_image.astype(np.uint8)

                mask_data = mask_generator.generate(np_image) # Pass numpy image (HWC, RGB, uint8)
                logger.info(f"MobileSAM segmentation generate finished. Found {len(mask_data)} potential masks.")

                raw_results = mask_data # Store the raw mask data list

                # --- Process mask_data to create annotations_full_res tensor ---
                if not mask_data:
                    logger.warning("MobileSAM segmentation returned no masks (empty list).")
                    annotations_full_res = torch.empty(0, device=self.device)
                else:
                    masks_list = []
                    for i, mask_dict in enumerate(mask_data):
                        # Check if 'segmentation' key exists and is valid
                        if 'segmentation' not in mask_dict:
                            logger.warning(f"Mask dictionary {i} missing 'segmentation' key. Skipping.")
                            continue

                        seg_data = mask_dict['segmentation']
                        binary_mask = None

                        if isinstance(seg_data, dict):  # RLE format
                            try:
                                from pycocotools import mask as mask_utils # Local import
                                binary_mask = mask_utils.decode(seg_data)
                            except ImportError:
                                logger.error("pycocotools not installed. Cannot decode RLE masks.")
                                # Decide behavior: skip, error out, etc. Skipping for now.
                                continue
                            except Exception as rle_e:
                                logger.error(f"Error decoding RLE mask {i}: {rle_e}")
                                continue
                        elif isinstance(seg_data, np.ndarray) and seg_data.dtype == bool:
                            # Convert boolean array to uint8
                            binary_mask = seg_data.astype(np.uint8)
                        elif isinstance(seg_data, np.ndarray) and seg_data.dtype == np.uint8:
                             binary_mask = seg_data # Already uint8
                        else:
                            logger.warning(f"Unexpected segmentation format in mask {i}: {type(seg_data)}. Skipping.")
                            continue

                        # Ensure mask has the correct dimensions (H, W) matching the original image
                        if binary_mask is None or binary_mask.ndim != 2 or \
                           binary_mask.shape[0] != self.pil_image.height or \
                           binary_mask.shape[1] != self.pil_image.width:
                             logger.warning(f"MobileSAM mask {i} shape mismatch or invalid. Got: {binary_mask.shape if binary_mask is not None else 'None'}, Expected: {(self.pil_image.height, self.pil_image.width)}. Skipping.")
                             continue

                        # Convert the valid binary mask (numpy uint8) to a tensor on the correct device
                        mask_tensor = torch.from_numpy(binary_mask).to(self.device)
                        masks_list.append(mask_tensor)

                    if masks_list:
                        annotations_full_res = torch.stack(masks_list)
                        logger.info(f"Successfully converted {len(masks_list)} MobileSAM masks to tensor.")
                    else:
                        logger.warning("No valid MobileSAM masks were converted.")
                        annotations_full_res = torch.empty(0, device=self.device)

                # Move base model back to CPU if needed (optional)
                if self.device.type == 'cuda':
                    logger.debug("Moving MobileSAM base model back to CPU.")
                    self.model.to('cpu') # Move the original self.model back
                    # sam_model is now potentially out of scope or just a local ref
                    torch.cuda.empty_cache()
                    logger.debug("CUDA cache cleared.")

            else:
                raise ValueError(f"Unknown model_type specified: {model_type}")

            # --- Post-Segmentation Processing (Common) ---
            if not self._is_running: return

            # Ensure annotations_full_res is a tensor, even if empty
            if annotations_full_res is None:
                 logger.warning("annotations_full_res is None after segmentation block. Setting to empty tensor.")
                 annotations_full_res = torch.empty(0, device=self.device)

            if len(annotations_full_res) == 0:
                 # Use error signal if no objects detected
                 self.error.emit(f"No objects detected during {model_type} segmentation.")
                 self.finished.emit(None, None, None) # Signal finished with no results
                 return

            self.status.emit(f"{model_type} segmentation complete ({len(annotations_full_res)} objects). Calculating parameters...")
            self.progress.emit(40)

            # 2. Parameter Calculation (with progress)
            # Ensure annotations are on CPU for calculate_parameters if it uses OpenCV heavily
            # Note: calculate_parameters already does .cpu().numpy() internally
            df, valid_mask = calculate_parameters(annotations_full_res, self.scale_factor, self.progress) # Pass progress signal

            if not self._is_running: return
            if df is None or df.empty:
                self.error.emit("No valid objects found after parameter calculation.")
                self.finished.emit(None, None, None)
                return

            # Filter annotations based on valid_mask from calculate_parameters
            # Ensure annotations_full_res is accessible here (it should be)
            try:
                 # Ensure valid_mask has the same length as the original number of annotations
                 if len(valid_mask) != len(annotations_full_res):
                      logger.error(f"CRITICAL: Mismatch between valid_mask ({len(valid_mask)}) and annotations ({len(annotations_full_res)})")
                      # Handle this error state, maybe signal error and finish
                      raise RuntimeError("Mask count mismatch after parameter calculation.")
                 final_annotations = annotations_full_res[valid_mask]
            except Exception as filter_e:
                 logger.exception("Error filtering annotations based on valid_mask:")
                 self.error.emit(f"Internal error filtering results: {filter_e}")
                 self.finished.emit(None, None, None)
                 return


            self.status.emit(f"Parameter calculation complete ({len(df)} valid objects). Creating visualization...")
            self.progress.emit(90)

            # 3. Create Visualization (using final valid annotations)
            # Ensure final_annotations are on CPU if create_segmented_visualization expects numpy
            # Note: create_segmented_visualization already does .cpu().numpy()
            segmented_image_vis = create_segmented_visualization(
                self.pil_image,
                final_annotations, # Use only the valid annotations
                contour_thickness=self.params.get('contour_thickness', DEFAULT_CONTOUR_THICKNESS) # Use default if missing
            )

            if not self._is_running: return
            self.progress.emit(100)
            self.status.emit("Processing complete.")
            self.finished.emit(df, final_annotations, segmented_image_vis) # Pass final valid annotations back

        except Exception as e:
            # Log the full traceback for debugging
            logger.exception("Error in processing thread:")
            # Emit a user-friendly error message
            self.error.emit(f"Processing error: {e}")
            # Ensure finished signal is emitted even on error, with None results
            self.finished.emit(None, None, None)

# --- Custom QGraphicsView for Zoom and Pan ---
class CustomGraphicsView(QGraphicsView):
    # Signal emitted when scale line drawing is finished
    scale_line_drawn = Signal(QPointF, QPointF)
    # Signal for mouse clicks in selection mode
    scene_clicked = Signal(QPointF)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setRenderHint(QPainter.Antialiasing)
        self.setRenderHint(QPainter.SmoothPixmapTransform)
        self.setRenderHint(QPainter.TextAntialiasing)

        self.setDragMode(QGraphicsView.NoDrag)
        self.setTransformationAnchor(QGraphicsView.AnchorUnderMouse)
        self.setResizeAnchor(QGraphicsView.AnchorUnderMouse)
        self.setInteractive(True) # Make sure interactions are enabled

        self._scale_factor = 1.0  # Internal tracking of scale
        self.zoom_factor_base = 1.15 # Zoom multiplier
        self.min_zoom_level = -10 # Limit zoom out
        self.max_zoom_level = 15  # Limit zoom in
        self._zoom_level = 0      # Current zoom level counter

        self._pan_start_pos = None  # Store the last mouse position for panning (in scene coords)
        self._is_panning = False
        self.mode = 'selection'  # Default mode: 'selection', 'pan', 'zoom', 'scale'

        # Scale Line Drawing
        self.scale_line_start = None
        self.scale_line_end = None
        self.scale_line_item = None # The QGraphicsLineItem

    def wheelEvent(self, event: QtGui.QWheelEvent):
        """Handles mouse wheel events for zooming."""
        # Get the mouse position in scene coordinates before scaling
        old_scene_pos = self.mapToScene(event.position().toPoint())

        # Determine the zoom direction and calculate the factor
        delta = event.angleDelta().y()
        zoom_in = delta > 0

        if zoom_in and self._zoom_level < self.max_zoom_level:
            factor = self.zoom_factor_base
            self._zoom_level += 1
        elif not zoom_in and self._zoom_level > self.min_zoom_level:
            factor = 1.0 / self.zoom_factor_base
            self._zoom_level -= 1
        else:
            return # No zoom if limits are reached

        # Apply scaling
        self.scale(factor, factor)
        self._scale_factor *= factor # Update internal scale tracker

        # Get the mouse position in scene coordinates after scaling
        new_scene_pos = self.mapToScene(event.position().toPoint())

        # Calculate the difference and translate the view to keep the point under the mouse
        delta_pos = new_scene_pos - old_scene_pos
        self.translate(delta_pos.x(), delta_pos.y())

        # Update status bar or log zoom level
        # logger.debug(f"Zoom level: {self._zoom_level}, Scale factor: {self._scale_factor:.3f}")
        # Consider emitting a signal if other parts of the app need the zoom factor
        # self.zoom_changed.emit(self._scale_factor)


    def mousePressEvent(self, event: QtGui.QMouseEvent):
        """Handles mouse press events based on the current mode."""
        scene_pos = self.mapToScene(event.pos())

        if self.mode == 'pan' and event.button() == Qt.LeftButton:
            self._pan_start_pos = scene_pos
            self._is_panning = True
            self.setCursor(Qt.ClosedHandCursor)
            event.accept() # Indicate event was handled
            # logger.debug("Panning started.")

        elif self.mode == 'scale' and event.button() == Qt.LeftButton:
            self.scale_line_start = scene_pos
            # Remove existing temporary line if any
            if self.scale_line_item and self.scale_line_item.scene():
                self.scene().removeItem(self.scale_line_item)
            self.scale_line_item = QGraphicsLineItem()
            self.scale_line_item.setPen(QPen(Qt.red, 2 / self._scale_factor, Qt.DashLine)) # Adjust thickness based on zoom
            self.scene().addItem(self.scale_line_item)
            self.setCursor(Qt.CrossCursor)
            event.accept()
            # logger.debug(f"Scale line started at {self.scale_line_start}")

        elif self.mode == 'selection' and event.button() == Qt.LeftButton:
             # Emit a signal for the main app to handle selection logic
             self.scene_clicked.emit(scene_pos)
             # Don't call super(), let the main app handle selection visualization
             event.accept()

        else:
            # Allow default behavior for other modes or buttons (e.g., context menu)
             super().mousePressEvent(event)


    def mouseMoveEvent(self, event: QtGui.QMouseEvent):
        """Handles mouse move events for panning and drawing."""
        scene_pos = self.mapToScene(event.pos())

        if self.mode == 'pan' and self._is_panning:
            delta = scene_pos - self._pan_start_pos
            # Translate the view (opposite direction of mouse movement)
            self.translate(delta.x(), delta.y())
            # No need to update _pan_start_pos here, delta is from the original press point
            event.accept()
            # logger.debug("Panning...")

        elif self.mode == 'scale' and self.scale_line_start and self.scale_line_item:
            self.scale_line_end = scene_pos
            self.scale_line_item.setLine(self.scale_line_start.x(), self.scale_line_start.y(),
                                         self.scale_line_end.x(), self.scale_line_end.y())
            event.accept()
            # logger.debug(f"Drawing scale line to {self.scale_line_end}")

        else:
            super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event: QtGui.QMouseEvent):
        """Handles mouse release events."""
        scene_pos = self.mapToScene(event.pos())

        if self.mode == 'pan' and event.button() == Qt.LeftButton and self._is_panning:
            self._is_panning = False
            self.setCursor(Qt.OpenHandCursor if self.mode == 'pan' else Qt.ArrowCursor)
            event.accept()
            # logger.debug("Panning stopped.")

        elif self.mode == 'scale' and event.button() == Qt.LeftButton and self.scale_line_start:
            self.scale_line_end = scene_pos
            if self.scale_line_item:
                # Make the line solid and potentially slightly thicker on release
                self.scale_line_item.setPen(QPen(Qt.red, 3 / self._scale_factor, Qt.SolidLine))
                # Emit the signal with start and end points
                self.scale_line_drawn.emit(self.scale_line_start, self.scale_line_end)
                # logger.debug(f"Scale line finished: {self.scale_line_start} -> {self.scale_line_end}")

                # Disable further editing of this scale line
                # We'll keep the points for redisplay when entering scale mode again
                self.mode = 'selection'  # Switch back to selection mode after drawing
                self.setCursor(Qt.ArrowCursor)

            event.accept()

        else:
            super().mouseReleaseEvent(event)


    def set_mode(self, mode):
        """Sets the interaction mode and updates cursor."""
        logger.debug(f"Switching view mode to: {mode}")
        self.mode = mode
        # Clear temporary drawing items when mode changes
        self.clear_scale_line_item()

        # Only clear scale line points if not entering scale mode
        if mode != 'scale':
            self.scale_line_start = None
            self.scale_line_end = None
        elif mode == 'scale' and self.scale_line_start and self.scale_line_end:
            # If entering scale mode and we have previous scale points, redraw the line
            self.scale_line_item = QGraphicsLineItem()
            self.scale_line_item.setPen(QPen(Qt.red, 3 / self._scale_factor, Qt.SolidLine))
            self.scale_line_item.setLine(self.scale_line_start.x(), self.scale_line_start.y(),
                                         self.scale_line_end.x(), self.scale_line_end.y())
            self.scene().addItem(self.scale_line_item)

        if mode == 'pan':
            self.setDragMode(QGraphicsView.NoDrag)
            self.setCursor(Qt.OpenHandCursor)
        elif mode == 'scale':
            self.setDragMode(QGraphicsView.NoDrag)
            self.setCursor(Qt.CrossCursor)
        elif mode == 'zoom': # Zoom is handled by wheel, cursor can be magnifying glass
            self.setDragMode(QGraphicsView.NoDrag)
            # self.setCursor(Qt.CrossCursor) # Or use a zoom cursor icon
            self.setCursor(QIcon(resource_path("icons/zoom.png")).pixmap(QSize(24, 24))) # Example custom cursor
        elif mode == 'selection':
            self.setDragMode(QGraphicsView.NoDrag) # Important for custom click handling
            self.setCursor(Qt.ArrowCursor)
        else: # Fallback or other modes
            self.setDragMode(QGraphicsView.RubberBandDrag) # Example default
            self.setCursor(Qt.ArrowCursor)

    def clear_scale_line_item(self):
        """Removes the temporary scale line from the scene."""
        if self.scale_line_item and self.scale_line_item.scene():
            self.scene().removeItem(self.scale_line_item)
        self.scale_line_item = None

    def reset_view(self):
         """Resets zoom, pan, and clears temporary items."""
         self.resetTransform()
         self._scale_factor = 1.0
         self._zoom_level = 0
         self._is_panning = False
         self.clear_scale_line_item()
         self.scale_line_start = None
         self.scale_line_end = None
         # Fit the content if there is any
         if self.scene() and not self.scene().itemsBoundingRect().isEmpty():
              self.fitInView(self.scene().itemsBoundingRect(), Qt.KeepAspectRatio)
         logger.info("Graphics view reset.")

    def get_current_scale(self):
        """Returns the current view scaling factor."""
        # return self.transform().m11() # This might include rotation/shear, _scale_factor is simpler
        return self._scale_factor

# --- Custom QGraphicsPixmapItem ---
class CustomPixmapItem(QGraphicsPixmapItem):
    # Inherits QGraphicsPixmapItem, currently no modifications needed
    # We might add interaction flags later if needed
    def __init__(self, pixmap, parent=None):
        super().__init__(pixmap, parent)
        self.setTransformationMode(Qt.SmoothTransformation)

# --- Custom Item Delegate for Treeview ---
class CustomItemDelegate(QStyledItemDelegate):
    # Inherits QStyledItemDelegate, currently using default painting logic
    # Kept for potential future custom drawing (e.g., color-coding values)
    pass

# --- Main Application Class ---
class GrainAnalysisApp(QMainWindow):

    # Define signals if needed for cross-component communication not handled by workers
    # e.g., config_changed = Signal()

    def __init__(self):
        super().__init__()
        logger.info("Initializing GrainAnalysisApp...")

        self.setWindowTitle("GrainSight")
        self.setGeometry(50, 50, 1500, 900) # Adjusted size

        # --- Icon ---
        # ... (icon code) ...

        # --- State Variables ---
        self.uploaded_image = None          # Original PIL Image uploaded by user
        self.processed_image_vis = None     # PIL Image with segmentation visualization
        self.annotations = []               # List of valid Torch tensors (masks, original res)
        self.df = None                      # Pandas DataFrame with results
        self.current_scale_factor = None    # µm per pixel (on original image)
        self.original_pixel_length = None   # Pixel length of scale line on original image
        self.image_file_path = None         # Path to the loaded image
        self.image_filename = None          # Filename of the loaded image
        self.last_save_dir = os.path.expanduser("~") # Default save dir
        self.default_save_dir = None        # User-defined default save directory

        self.grain_items = {}               # Mapping: df_index -> {'poly': QGraphicsPolygonItem, 'text': QGraphicsTextItem}
        self.selected_df_indices = set()    # Indices of rows selected (in the DataFrame)

        self.processing_thread = None       # QThread for background tasks
        self.processing_worker = None       # Worker object for background tasks

        self.pixmap_item = None             # QGraphicsPixmapItem displaying the image

        # List to keep track of open plot windows
        self.plot_dialogs = []

        self.model = None # <--- INITIALIZE MODEL ATTRIBUTE HERE (to None initially)
        self.model_type = None # <--- Track the currently loaded model type

        # --- Theme ---
        self.theme_file = os.path.join(application_path, "theme_config.json")
        self.current_theme = self.load_theme_preference()
        self.apply_theme(self.current_theme) # Apply theme early

        # --- UI Setup ---
        self.setup_menu()
        self.setup_toolbar()
        self.setup_widgets() # This now sets the central widget
        self.setup_status_bar()

        # Connect view signals AFTER view is created in setup_widgets
        self.view.scale_line_drawn.connect(self.on_scale_line_drawn)
        self.view.scene_clicked.connect(self.on_scene_clicked)

        # --- Model Loading (Deferred or in Thread) ---
        # self.model is already None here
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.default_box_nms_thresh = 0.3  # Renamed to avoid conflict with UI slider
        logger.info(f"Using device: {self.device}")
        # self.load_model() # Original synchronous load option commented out

        logger.info("GrainAnalysisApp initialization complete.")
        self.load_model_async() # Start loading model in background


    def load_model_async(self):
        """Loads the model in a separate thread to avoid blocking the GUI."""
        self.update_status("Loading model...")
        # Simple threading for model load - QThread might be overkill here
        threading.Thread(target=self._load_model_worker, daemon=True).start()

    def on_model_type_changed(self):
        """Handles model type radio button changes."""
        # Only reload if we already have a model loaded and the type is changing
        if hasattr(self, 'model') and self.model is not None and hasattr(self, 'model_type'):
            # Get current model type from tracking attribute
            current_is_fastsam = (self.model_type == "fastsam")
            # Check if type is changing
            if (current_is_fastsam and self.mobilesam_radio.isChecked()) or \
               (not current_is_fastsam and self.fastsam_radio.isChecked()):
                reply = QMessageBox.question(
                    self, "Change Model Type",
                    "Changing the model type will reload the model. Continue?",
                    QMessageBox.Yes | QMessageBox.Cancel, QMessageBox.Yes
                )
                if reply == QMessageBox.Yes:
                    self.model = None  # Clear current model
                    self.model_type = None  # Clear model type tracking
                    self.load_model_async()  # Reload with new type
                else:
                    # Revert selection if canceled - block signals to prevent recursion
                    self.fastsam_radio.blockSignals(True)
                    self.mobilesam_radio.blockSignals(True)
                    if current_is_fastsam:
                        self.fastsam_radio.setChecked(True)
                        self.mobilesam_radio.setChecked(False)
                    else:
                        self.fastsam_radio.setChecked(False)
                        self.mobilesam_radio.setChecked(True)
                    self.fastsam_radio.blockSignals(False)
                    self.mobilesam_radio.blockSignals(False)

    def _load_model_worker(self):
        """Worker function to load the selected model."""
        try:
            # Determine which model to load based on radio button selection
            use_fastsam = self.fastsam_radio.isChecked()
            # Ensure we have the latest selection
            QMetaObject.invokeMethod(self, "_update_model_radio_buttons", Qt.BlockingQueuedConnection)

            if use_fastsam:
                # Load FastSAM model
                model_path = os.path.join(application_path, "model", "FastSAM-x.pt")
                if not os.path.exists(model_path):
                    # ... (error handling) ...
                    return

                logger.info(f"Loading FastSAM model from: {model_path}")
                self.model = YOLO(model_path)
                self.model_type = "fastsam"  # Update model type tracking
                # self.model.to(self.device) # Keep on CPU initially, move in worker if needed
                logger.info("FastSAM model loaded successfully (on CPU).")
                # Update UI to match loaded model
                QMetaObject.invokeMethod(self, "_update_model_radio_buttons", Qt.QueuedConnection,
                                        Q_ARG(bool, True))
            else:
                # Load MobileSAM model (BASE MODEL ONLY)
                model_path = os.path.join(application_path, "weights", "mobile_sam.pt")
                if not os.path.exists(model_path):
                    # ... (error handling) ...
                    return

                logger.info(f"Loading MobileSAM base model from: {model_path}")
                sam = sam_model_registry["vit_t"](checkpoint=model_path)
                # Keep on CPU initially, move to device in worker thread
                # sam.to(device=self.device) # <<< REMOVE THIS LINE
                # sam.eval()                # <<< REMOVE THIS LINE
                self.model = sam # <<< STORE THE BASE SAM MODEL
                self.model_type = "mobilesam"  # Update model type tracking
                # Update UI to match loaded model
                QMetaObject.invokeMethod(self, "_update_model_radio_buttons", Qt.QueuedConnection,
                                        Q_ARG(bool, False))
                logger.info("MobileSAM base model loaded successfully (on CPU).")

            logger.info(f"Model loaded successfully. Ready for processing.")
            QMetaObject.invokeMethod(self, "update_status", Qt.QueuedConnection,
                                    Q_ARG(str, f"Model loaded. Ready."))
            # --- IMPORTANT: Update action states AFTER model is potentially loaded ---
            QMetaObject.invokeMethod(self, "update_action_states", Qt.QueuedConnection)


        except Exception as e:
            logger.exception("Failed to load model.")
            QMetaObject.invokeMethod(self, "_show_error_message", Qt.QueuedConnection,
                                    Q_ARG(str, "Model Load Error"),
                                    Q_ARG(str, f"Failed to load model: {e}"))
            QMetaObject.invokeMethod(self, "update_status", Qt.QueuedConnection,
                                    Q_ARG(str, f"Error loading model: {e}"))
            # --- Ensure action states are updated even on error ---
            QMetaObject.invokeMethod(self, "update_action_states", Qt.QueuedConnection)

    def _get_model_type(self):
        """Helper method to get model type from UI thread."""
        return self.fastsam_radio.isChecked()

    def _update_model_radio_buttons(self, use_fastsam):
        """Updates the model radio buttons to match the loaded model type.
        This method is called from the worker thread via invokeMethod."""
        # Block signals to prevent triggering on_model_type_changed
        self.fastsam_radio.blockSignals(True)
        self.mobilesam_radio.blockSignals(True)

        # Set radio buttons to match loaded model
        self.fastsam_radio.setChecked(use_fastsam)
        self.mobilesam_radio.setChecked(not use_fastsam)

        # Update stacked widget to show appropriate parameters
        self.model_params_stack.setCurrentIndex(0 if use_fastsam else 1)

        # Unblock signals
        self.fastsam_radio.blockSignals(False)
        self.mobilesam_radio.blockSignals(False)

        logger.info(f"UI updated to match loaded model: {'FastSAM' if use_fastsam else 'MobileSAM'}")

    # --- Theme Methods ---
    def apply_theme(self, theme):
        """Applies the specified theme."""
        if theme == 'light':
            palette = define_light_theme() # Use imported function
            self.setStyleSheet(LIGHT_STYLESHEET) # Use imported stylesheet
        elif theme == 'dark':
            palette = define_dark_theme() # Use imported function
            self.setStyleSheet(DARK_STYLESHEET) # Use imported stylesheet
        else: # Fallback to light
             palette = define_light_theme()
             self.setStyleSheet(LIGHT_STYLESHEET) # Use light stylesheet for fallback

        QApplication.setPalette(palette) # Apply the palette globally
        self.setPalette(palette) # Apply to the main window specifically
        self.update_widget_styles() # Apply to custom elements like the view background

    # Removed local define_light_theme and define_dark_theme methods
    # as they are now imported from src.grainsight_components.gui.utils

    def load_theme_preference(self):
        if os.path.exists(self.theme_file):
            try:
                with open(self.theme_file, 'r') as f:
                    data = json.load(f)
                    theme = data.get('theme', 'light')
                    logger.info(f"Loaded theme preference: {theme}")
                    return theme
            except Exception as e:
                logger.error(f"Failed to load theme file {self.theme_file}: {e}")
                return 'light'
        else:
            logger.info("Theme preference file not found, defaulting to 'light'.")
            return 'light'

    def save_theme_preference(self):
        data = {'theme': self.current_theme}
        try:
            with open(self.theme_file, 'w') as f:
                json.dump(data, f, indent=4)
            logger.info(f"Saved theme preference: {self.current_theme}")
        except Exception as e:
            logger.error(f"Failed to save theme preference to {self.theme_file}: {e}")
            self._show_error_message("Error", f"Failed to save theme preference: {e}")

    def toggle_dark_mode(self):
        if self.current_theme == 'light':
            self.current_theme = 'dark'
        else:
            self.current_theme = 'light'
        logger.info(f"Toggling theme to {self.current_theme}.")
        self.apply_theme(self.current_theme)
        self.save_theme_preference()
        # Force redraw of plots if open? Difficult without tracking them explicitly.
        # Best approach is to recreate plots if theme changes significantly.

    def update_widget_styles(self):
        """Updates specific widget styles after theme change."""
        if hasattr(self, 'view'):
            bg_color = self.palette().color(QPalette.ColorRole.Base) # Use Base color for background
            self.view.setBackgroundBrush(bg_color)
        # Update plot backgrounds if they are open and tracked
        self.update_plot_themes()


    # --- UI Setup Methods ---
    def setup_menu(self):
        menubar = self.menuBar()

        # --- File Menu ---
        file_menu = menubar.addMenu("&File")

        # Upload Action
        upload_action = QAction(QIcon(resource_path("icons/upload.png")), "&Upload Image...", self)
        upload_action.setShortcut("Ctrl+O")
        upload_action.setStatusTip("Load an image file for analysis")
        upload_action.triggered.connect(self.upload_image)
        self.upload_action = upload_action  # Store reference
        file_menu.addAction(self.upload_action)

        file_menu.addSeparator()

        # Crop Action
        crop_action = QAction(QIcon(resource_path("icons/crop.png")), "&Crop Image...", self)
        crop_action.setShortcut("Ctrl+X")
        crop_action.setStatusTip("Crop the currently loaded image")
        crop_action.triggered.connect(self.crop_image)
        self.crop_action = crop_action # Store reference
        file_menu.addAction(self.crop_action)

        file_menu.addSeparator()

        # Save Results Action
        save_results_action = QAction(QIcon(resource_path("icons/save.png")), "&Save Results...", self)
        save_results_action.setShortcut(QKeySequence.Save)
        save_results_action.setStatusTip("Save segmentation data (CSV) and annotated image (PNG)")
        save_results_action.triggered.connect(self.save_results)
        self.save_results_action = save_results_action # Store reference
        file_menu.addAction(self.save_results_action)

        # Save View Action
        save_view_action = QAction(QIcon(resource_path("icons/save_view.png")), "Save Current &View...", self)
        save_view_action.setShortcut("Ctrl+Shift+S")
        save_view_action.setStatusTip("Save the current view in the image panel as a PNG file")
        save_view_action.triggered.connect(self.save_current_view)
        self.save_view_action = save_view_action # Store reference
        file_menu.addAction(self.save_view_action)

        # Export COCO Action
        export_coco_action = QAction(QIcon(resource_path("icons/coco.png")), "Export &COCO Annotations...", self)
        export_coco_action.setStatusTip("Save segmentation masks in COCO JSON format")
        export_coco_action.triggered.connect(self.save_coco_annotations)
        self.export_coco_action = export_coco_action # Store reference
        file_menu.addAction(self.export_coco_action)

        file_menu.addSeparator()

        # Exit Action
        exit_action = QAction(QIcon(resource_path("icons/exit.png")), "E&xit", self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.setStatusTip("Exit the application")
        exit_action.triggered.connect(self.close) # Use self.close() to trigger closeEvent
        file_menu.addAction(exit_action)


        # --- Edit Menu ---
        edit_menu = menubar.addMenu("&Edit")

        # Reset Action
        reset_action = QAction(QIcon(resource_path("icons/reset.png")), "&Reset State", self)
        reset_action.setStatusTip("Reset image, results, and parameters")
        reset_action.triggered.connect(self.reset_app)
        self.reset_action = reset_action # Store reference
        edit_menu.addAction(self.reset_action)


        # --- View Menu ---
        view_menu = menubar.addMenu("&View")

        # Theme Action
        theme_action = QAction("Toggle &Dark/Light Mode", self)
        theme_action.setStatusTip("Switch between dark and light user interface themes")
        theme_action.triggered.connect(self.toggle_dark_mode)
        view_menu.addAction(theme_action)

        view_menu.addSeparator()

        # Zoom In Action
        zoom_in_action = QAction(QIcon(resource_path("icons/zoom_in.png")), "Zoom &In", self)
        zoom_in_action.setShortcut(QKeySequence.ZoomIn) # Ctrl++
        # Connect directly to view's method if view exists, otherwise maybe disable?
        # Safest to connect in __init__ after view is created, or here with a check.
        # For now, assuming view might not exist yet, use lambda with check if necessary
        zoom_in_action.triggered.connect(lambda: self.view.scale(self.view.zoom_factor_base, self.view.zoom_factor_base) if hasattr(self, 'view') else None)
        self.zoom_in_action = zoom_in_action # Store reference
        view_menu.addAction(self.zoom_in_action)

        # Zoom Out Action
        zoom_out_action = QAction(QIcon(resource_path("icons/zoom_out.png")), "Zoom &Out", self)
        zoom_out_action.setShortcut(QKeySequence.ZoomOut) # Ctrl+-
        zoom_out_action.triggered.connect(lambda: self.view.scale(1.0/self.view.zoom_factor_base, 1.0/self.view.zoom_factor_base) if hasattr(self, 'view') else None)
        self.zoom_out_action = zoom_out_action # Store reference
        view_menu.addAction(self.zoom_out_action)

        # Reset Zoom Action
        reset_zoom_action = QAction(QIcon(resource_path("icons/zoom_reset.png")), "Reset &Zoom", self)
        reset_zoom_action.setShortcut(QKeySequence("Ctrl+0"))
        reset_zoom_action.triggered.connect(self.reset_view_zoom)
        self.reset_zoom_action = reset_zoom_action # Store reference
        view_menu.addAction(self.reset_zoom_action)


        # --- Tools Menu ---
        tools_menu = menubar.addMenu("&Tools")

        # Plot Action
        plot_action = QAction(QIcon(resource_path("icons/plot.png")), "&Generate Plots...", self)
        plot_action.setShortcut("Ctrl+P")
        plot_action.setStatusTip("Generate plots based on calculated grain parameters")
        plot_action.triggered.connect(self.show_plot_selection)
        self.plot_action = plot_action # Store reference
        tools_menu.addAction(self.plot_action)


        # --- Settings Menu ---
        settings_menu = menubar.addMenu("&Settings")

        # Set Default Save Directory Action
        set_default_save_dir_action = QAction("Set Default Save &Directory...", self)
        set_default_save_dir_action.triggered.connect(self.set_default_save_directory)
        settings_menu.addAction(set_default_save_dir_action)


        # --- Help Menu ---
        help_menu = menubar.addMenu("&Help")

        # About Action
        about_action = QAction("&About GrainSight", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

        # Initial state for actions
        # It's generally safer to call this *after* all widgets are also created,
        # so call it at the end of __init__ or setup_widgets.
        # self.update_action_states() # Remove if called later



    def setup_toolbar(self):
        toolbar = QToolBar("Main Toolbar")
        toolbar.setIconSize(QSize(24, 24)) # Slightly larger icons
        self.addToolBar(toolbar)

        # --- Standard Actions (Using stored references) ---
        if hasattr(self, 'upload_action'): toolbar.addAction(self.upload_action)
        if hasattr(self, 'crop_action'): toolbar.addAction(self.crop_action)
        toolbar.addSeparator()
        if hasattr(self, 'save_results_action'): toolbar.addAction(self.save_results_action)
        if hasattr(self, 'export_coco_action'): toolbar.addAction(self.export_coco_action)
        toolbar.addSeparator()
        if hasattr(self, 'plot_action'): toolbar.addAction(self.plot_action)
        if hasattr(self, 'reset_action'): toolbar.addAction(self.reset_action) # Full app reset

        # --- Zoom Actions (Using stored references) ---
        toolbar.addSeparator()
        if hasattr(self, 'zoom_in_action'): toolbar.addAction(self.zoom_in_action)
        if hasattr(self, 'zoom_out_action'): toolbar.addAction(self.zoom_out_action)
        if hasattr(self, 'reset_zoom_action'): toolbar.addAction(self.reset_zoom_action)

        # --- Spacer ---
        spacer = QWidget()
        spacer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        toolbar.addWidget(spacer)

        # --- Scale Factor Management ---
        scale_toolbar_group = QGroupBox("Scale Factor")
        scale_toolbar_layout = QHBoxLayout()
        scale_toolbar_group.setLayout(scale_toolbar_layout)

        # Save Scale Action
        save_scale_action = QAction(QIcon(resource_path("icons/save_scale.png")), "Save Scale", self)
        save_scale_action.triggered.connect(self.save_scale_factor)
        save_scale_action.setStatusTip("Save current scale factor settings to a JSON file")
        self.save_scale_action = save_scale_action # Store reference
        save_scale_button = QToolButton()
        save_scale_button.setDefaultAction(save_scale_action)
        scale_toolbar_layout.addWidget(save_scale_button)

        # Load Scale Action
        load_scale_action = QAction(QIcon(resource_path("icons/load_scale.png")), "Load Scale", self)
        # Connect AFTER image is loaded check is performed in load_scale_factor
        load_scale_action.triggered.connect(self.load_scale_factor)
        load_scale_action.setStatusTip("Load scale factor settings from a JSON file")
        self.load_scale_action = load_scale_action # Store reference for enabling/disabling
        load_scale_button = QToolButton()
        load_scale_button.setDefaultAction(load_scale_action)
        scale_toolbar_layout.addWidget(load_scale_button)

        # --- ADD RESET SCALE ACTION AND BUTTON ---
        reset_scale_action = QAction(QIcon(resource_path("icons/reset_scale.png")), "Reset Scale", self) # Use appropriate icon
        reset_scale_action.triggered.connect(self.reset_scale)
        reset_scale_action.setStatusTip("Clear the current scale factor and remove any scale line")
        self.reset_scale_action = reset_scale_action # Store reference
        reset_scale_button = QToolButton()
        reset_scale_button.setDefaultAction(self.reset_scale_action)
        scale_toolbar_layout.addWidget(reset_scale_button)
        # -----------------------------------------

        toolbar.addWidget(scale_toolbar_group)

        # --- Mode Selection ---
        mode_group = QGroupBox("Interaction Mode")
        mode_layout = QHBoxLayout()
        mode_group.setLayout(mode_layout)

        self.mode_buttons = {} # To store radio buttons
        modes = [
            ('selection', "Select", "icons/select.png", "Select individual grains (Click or Ctrl+Click)"),
            ('scale', "Scale", "icons/scale.png", "Draw a line to define the image scale"),
            ('pan', "Pan", "icons/pan.png", "Pan the image by dragging")
        ]

        for key, text, icon_path, tip in modes:
            radio_btn = QRadioButton(text)
            try:
                 icon = QIcon(resource_path(icon_path))
                 if not icon.isNull():
                      radio_btn.setIcon(icon)
                 else:
                      logger.warning(f"Icon not found or invalid for mode '{key}': {icon_path}")
            except Exception as e:
                 logger.error(f"Error loading icon for mode '{key}': {e}")

            radio_btn.setStatusTip(tip)
            radio_btn.toggled.connect(lambda checked, m=key: self.update_mode(m) if checked else None)
            mode_layout.addWidget(radio_btn)
            self.mode_buttons[key] = radio_btn

        if 'selection' in self.mode_buttons:
            self.mode_buttons['selection'].setChecked(True)

        toolbar.addWidget(mode_group)

    @Slot() # Make it a slot
    def reset_scale(self):
        """Clears the current scale factor and related UI elements."""
        if self.current_scale_factor is None:
            # Optionally add a message or just do nothing if already reset
            logger.debug("Scale is already reset.")
            return

        # Optional Confirmation
        reply = QMessageBox.question(
            self, "Confirm Reset Scale",
            "Are you sure you want to clear the current scale factor?",
            QMessageBox.Yes | QMessageBox.Cancel, QMessageBox.Cancel
        )
        if reply == QMessageBox.Cancel:
            return

        logger.info("Resetting scale factor.")

        # Reset internal state
        self.current_scale_factor = None
        self.original_pixel_length = None

        # Clear scale line from the view
        if hasattr(self, 'view'):
            self.view.clear_scale_line_item()
            self.view.scale_line_start = None
            self.view.scale_line_end = None

        # Update UI Labels and Fields
        self.scale_factor_label.setText("<b>Current Scale: Not set</b>")
        self.manual_scale.setText("0.0") # Reset manual input field
        # Keep self.real_world_length as it is user input, don't clear it? Or reset to default? Let's keep it.

        # Update reminder and action states
        self.update_scale_reminder()
        self.update_action_states()
        self.update_status("Scale reset.")


    def setup_widgets(self):
            # Main container (using QSplitter for resizable panels)
            main_container = QSplitter(Qt.Horizontal, self)
            self.setCentralWidget(main_container) # Set splitter as the central widget

            # --- Left Panel (Controls) ---
            left_frame = QFrame()
            left_layout = QVBoxLayout(left_frame)
            left_frame.setMinimumWidth(350) # Adjust as needed
            left_frame.setMaximumWidth(450)
            main_container.addWidget(left_frame)

            # Wrap controls in a scroll area if they might overflow
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            control_widget = QWidget()
            control_layout = QVBoxLayout(control_widget)
            scroll_area.setWidget(control_widget)
            left_layout.addWidget(scroll_area)

            # --- Model Parameters ---
            param_group = QGroupBox("Model Parameters")
            param_layout = QGridLayout(param_group)
            control_layout.addWidget(param_group)

            # Add model type selection
            param_layout.addWidget(QLabel("Segmentation Model:"), 0, 0)
            self.model_type_layout = QHBoxLayout()
            self.fastsam_radio = QRadioButton("FastSAM")
            self.mobilesam_radio = QRadioButton("MobileSAM")
            self.fastsam_radio.setChecked(True)  # Default to FastSAM
            self.model_type_layout.addWidget(self.fastsam_radio)
            self.model_type_layout.addWidget(self.mobilesam_radio)
            param_layout.addLayout(self.model_type_layout, 0, 1, 1, 2)

            # Connect model type change to handler
            self.fastsam_radio.toggled.connect(self.on_model_type_changed)
            self.mobilesam_radio.toggled.connect(self.on_model_type_changed)

            # Create a stacked widget to hold model-specific parameters
            self.model_params_stack = QStackedWidget()
            param_layout.addWidget(self.model_params_stack, 4, 0, 1, 3)

            # FastSAM parameters widget
            fastsam_params_widget = QWidget()
            fastsam_params_layout = QGridLayout(fastsam_params_widget)

            # Add FastSAM specific parameters
            fastsam_params_layout.addWidget(QLabel("Input Size:"), 0, 0)
            self.input_size = QSlider(Qt.Horizontal)
            self.input_size.setRange(256, 2048) # Adjusted range
            self.input_size.setValue(DEFAULT_INPUT_SIZE)
            self.input_size.setTickInterval(256)
            self.input_size.setTickPosition(QSlider.TicksBelow)
            self.input_size.valueChanged.connect(self.update_slider_labels)
            fastsam_params_layout.addWidget(self.input_size, 0, 1)
            self.input_size_label = QLabel(str(DEFAULT_INPUT_SIZE))
            self.input_size_label.setMinimumWidth(40)
            fastsam_params_layout.addWidget(self.input_size_label, 0, 2)

            fastsam_params_layout.addWidget(QLabel("IOU Threshold:"), 1, 0)
            self.iou_threshold = QSlider(Qt.Horizontal)
            self.iou_threshold.setRange(10, 95) # 0.1 to 0.95
            self.iou_threshold.setValue(int(DEFAULT_IOU_THRESHOLD * 100))
            self.iou_threshold.setTickInterval(10)
            self.iou_threshold.setTickPosition(QSlider.TicksBelow)
            self.iou_threshold.valueChanged.connect(self.update_slider_labels)
            fastsam_params_layout.addWidget(self.iou_threshold, 1, 1)
            self.iou_threshold_label = QLabel(f"{DEFAULT_IOU_THRESHOLD:.2f}")
            self.iou_threshold_label.setMinimumWidth(40)
            fastsam_params_layout.addWidget(self.iou_threshold_label, 1, 2)

            fastsam_params_layout.addWidget(QLabel("Conf. Threshold:"), 2, 0)
            self.conf_threshold = QSlider(Qt.Horizontal)
            self.conf_threshold.setRange(5, 95) # 0.05 to 0.95
            self.conf_threshold.setValue(int(DEFAULT_CONF_THRESHOLD * 100))
            self.conf_threshold.setTickInterval(10)
            self.conf_threshold.setTickPosition(QSlider.TicksBelow)
            self.conf_threshold.valueChanged.connect(self.update_slider_labels)
            fastsam_params_layout.addWidget(self.conf_threshold, 2, 1)
            self.conf_threshold_label = QLabel(f"{DEFAULT_CONF_THRESHOLD:.2f}")
            self.conf_threshold_label.setMinimumWidth(40)
            fastsam_params_layout.addWidget(self.conf_threshold_label, 2, 2)

            fastsam_params_layout.addWidget(QLabel("Max Objects:"), 3, 0)
            self.max_det = QLineEdit(str(DEFAULT_MAX_DET))
            self.max_det.setValidator(QtGui.QIntValidator(1, 10000)) # Validate input
            fastsam_params_layout.addWidget(self.max_det, 3, 1, 1, 2) # Span 2 columns

            self.model_params_stack.addWidget(fastsam_params_widget)

            # MobileSAM parameters widget
            mobilesam_params_widget = QWidget()
            mobilesam_params_layout = QGridLayout(mobilesam_params_widget)

            # Initialize row counter for MobileSAM parameters
            current_row_mobilesam = 5  # Start after the existing parameters

            # Add MobileSAM specific parameters
            mobilesam_params_layout.addWidget(QLabel("Points Per Side:"), 0, 0)
            self.points_per_side = QSlider(Qt.Horizontal)
            self.points_per_side.setRange(8, 64) # Range for points per side
            self.points_per_side.setValue(32) # Default value
            self.points_per_side.setTickInterval(8)
            self.points_per_side.setTickPosition(QSlider.TicksBelow)
            self.points_per_side.valueChanged.connect(self.update_slider_labels)
            mobilesam_params_layout.addWidget(self.points_per_side, 0, 1)
            self.points_per_side_label = QLabel("32")
            self.points_per_side_label.setMinimumWidth(40)
            mobilesam_params_layout.addWidget(self.points_per_side_label, 0, 2)

            mobilesam_params_layout.addWidget(QLabel("Pred IOU Thresh:"), 1, 0)
            self.pred_iou_thresh = QSlider(Qt.Horizontal)
            self.pred_iou_thresh.setRange(50, 99) # 0.5 to 0.99
            self.pred_iou_thresh.setValue(95) # Default 0.88
            self.pred_iou_thresh.setTickInterval(10)
            self.pred_iou_thresh.setTickPosition(QSlider.TicksBelow)
            self.pred_iou_thresh.valueChanged.connect(self.update_slider_labels)
            mobilesam_params_layout.addWidget(self.pred_iou_thresh, 1, 1)
            self.pred_iou_thresh_label = QLabel("0.95")
            self.pred_iou_thresh_label.setMinimumWidth(40)
            mobilesam_params_layout.addWidget(self.pred_iou_thresh_label, 1, 2)

            mobilesam_params_layout.addWidget(QLabel("Stability Score Thresh:"), 2, 0)
            self.stability_score_thresh = QSlider(Qt.Horizontal)
            self.stability_score_thresh.setRange(50, 99) # 0.5 to 0.99
            self.stability_score_thresh.setValue(95) # Default 0.95
            self.stability_score_thresh.setTickInterval(10)
            self.stability_score_thresh.setTickPosition(QSlider.TicksBelow)
            self.stability_score_thresh.valueChanged.connect(self.update_slider_labels)
            mobilesam_params_layout.addWidget(self.stability_score_thresh, 2, 1)
            self.stability_score_thresh_label = QLabel("0.95")
            self.stability_score_thresh_label.setMinimumWidth(40)
            mobilesam_params_layout.addWidget(self.stability_score_thresh_label, 2, 2)

            mobilesam_params_layout.addWidget(QLabel("Points Per Batch:"), 3, 0)
            self.points_per_batch = QtWidgets.QSpinBox()
            self.points_per_batch.setRange(16, 256)  # Example range, adjust as needed
            self.points_per_batch.setValue(64)  # Example default, adjust as needed
            self.points_per_batch.setToolTip("Number of points to process in a batch for mask generation.")
            mobilesam_params_layout.addWidget(self.points_per_batch, 3, 1, 1, 2) # Span 2 columns

            mobilesam_params_layout.addWidget(QLabel("Box NMS Thresh:"), 4, 0)
            self.box_nms_thresh = QSlider(Qt.Horizontal)
            self.box_nms_thresh.setRange(10, 99)  # 0.1 to 0.99
            self.box_nms_thresh.setValue(70)  # Default 0.7, adjust as needed
            self.box_nms_thresh.setTickInterval(10)
            self.box_nms_thresh.setTickPosition(QSlider.TicksBelow)
            self.box_nms_thresh.valueChanged.connect(self.update_slider_labels)
            mobilesam_params_layout.addWidget(self.box_nms_thresh, 4, 1)
            self.box_nms_thresh_label = QLabel("0.70")
            self.box_nms_thresh_label.setMinimumWidth(40)
            mobilesam_params_layout.addWidget(self.box_nms_thresh_label, 4, 2)

             # --- ADD NEW PARAMETERS ---
            mobilesam_params_layout.addWidget(QLabel("Crop Layers (0=None):"), current_row_mobilesam, 0)
            self.crop_n_layers = QtWidgets.QSpinBox() # Use QSpinBox
            self.crop_n_layers.setRange(0, 3) # Default=0, Max=3 (Adjust as needed)
            self.crop_n_layers.setValue(0) # Default from SAM
            self.crop_n_layers.setToolTip("Number of layers to run crop integration. 0 disables cropping.")
            mobilesam_params_layout.addWidget(self.crop_n_layers, current_row_mobilesam, 1, 1, 2) # Span 2 columns
            current_row_mobilesam += 1

            mobilesam_params_layout.addWidget(QLabel("Crop Points Downscale:"), current_row_mobilesam, 0)
            self.crop_n_points_downscale_factor = QtWidgets.QSpinBox() # Use QSpinBox
            self.crop_n_points_downscale_factor.setRange(1, 4) # Default=1, Min=1 (Adjust as needed)
            self.crop_n_points_downscale_factor.setValue(1) # Default from SAM
            self.crop_n_points_downscale_factor.setToolTip("Downscaling factor for points grid during cropping.")
            mobilesam_params_layout.addWidget(self.crop_n_points_downscale_factor, current_row_mobilesam, 1, 1, 2) # Span 2 columns
            current_row_mobilesam += 1
            # --- END NEW PARAMETERS ---


            self.model_params_stack.addWidget(mobilesam_params_widget)

            # Connect radio buttons to switch stacked widget
            self.fastsam_radio.toggled.connect(lambda checked: self.model_params_stack.setCurrentIndex(0) if checked else None)
            self.mobilesam_radio.toggled.connect(lambda checked: self.model_params_stack.setCurrentIndex(1) if checked else None)


            # --- Processing Options ---
            processing_group = QGroupBox("Visualization Options")
            processing_layout = QGridLayout(processing_group) # Use Grid for alignment
            control_layout.addWidget(processing_group)

            processing_layout.addWidget(QLabel("Contour Thickness:"), 1, 0)
            self.contour_thickness = QSlider(Qt.Horizontal)
            self.contour_thickness.setRange(1, 10)
            self.contour_thickness.setValue(DEFAULT_CONTOUR_THICKNESS)
            self.contour_thickness.setTickInterval(1)
            self.contour_thickness.setTickPosition(QSlider.TicksBelow)
            self.contour_thickness.valueChanged.connect(self.update_slider_labels)
            processing_layout.addWidget(self.contour_thickness, 1, 1)
            self.contour_thickness_label = QLabel(str(DEFAULT_CONTOUR_THICKNESS))
            self.contour_thickness_label.setMinimumWidth(40)
            processing_layout.addWidget(self.contour_thickness_label, 1, 2)

            # --- Scale Calculation ---
            scale_group = QGroupBox("Image Scale")
            scale_layout = QVBoxLayout(scale_group)
            control_layout.addWidget(scale_group)

            # Instruction Label
            scale_layout.addWidget(QLabel("<b>Method 1: Draw Scale Line</b>"))
            scale_instruct_layout = QHBoxLayout()
            scale_instruct_layout.addWidget(QLabel("Real-world length (µm):"))
            self.real_world_length = QLineEdit("100") # Default value
            self.real_world_length.setValidator(QtGui.QDoubleValidator(0.01, 1000000.0, 3)) # Validate input
            scale_instruct_layout.addWidget(self.real_world_length)
            scale_layout.addLayout(scale_instruct_layout)
            scale_layout.addWidget(QLabel("<i>Then select 'Scale' mode and draw line on image.</i>"))

            scale_layout.addWidget(QLabel("<hr><b>Method 2: Enter Scale Directly</b>"))
            manual_scale_layout = QHBoxLayout()
            manual_scale_layout.addWidget(QLabel("Scale (µm/pixel):"))
            self.manual_scale = QLineEdit("0.0")
            self.manual_scale.setValidator(QtGui.QDoubleValidator(0.000001, 1000.0, 6)) # Validate input
            self.manual_scale.setFixedWidth(120)
            manual_scale_layout.addWidget(self.manual_scale)
            set_scale_button = QPushButton("Set")
            set_scale_button.setObjectName("setManualScaleButton") # Set object name
            set_scale_button.setToolTip("Apply the manually entered scale factor")
            set_scale_button.clicked.connect(self.set_manual_scale)
            manual_scale_layout.addWidget(set_scale_button)
            manual_scale_layout.addStretch()
            scale_layout.addLayout(manual_scale_layout)

            self.scale_factor_label = QLabel("<b>Current Scale: Not set</b>")
            self.scale_factor_label.setStyleSheet("padding-top: 5px;")
            scale_layout.addWidget(self.scale_factor_label)

            # --- Action Buttons ---
            action_layout = QVBoxLayout() # Separate layout for buttons
            control_layout.addLayout(action_layout)

            self.process_button = QPushButton(QIcon(resource_path("icons/process.png")), " Segment Image")
            self.process_button.setToolTip("Start the image segmentation and analysis process")
            self.process_button.clicked.connect(self.start_processing)
            action_layout.addWidget(self.process_button)

            # --- Patches Button ---
            analyze_patches_btn = QPushButton(QIcon(resource_path("icons/grid.png")), " Segment in Patches...")
            analyze_patches_btn.setToolTip("Segment large images by dividing them into patches")
            analyze_patches_btn.clicked.connect(self.analyze_with_patches)
            action_layout.addWidget(analyze_patches_btn)

            # --- ADD RECALCULATE BUTTON ---
            self.recalculate_button = QPushButton(QIcon(resource_path("icons/recalculate.png")), " Recalculate Parameters") # Choose/create an icon
            self.recalculate_button.setToolTip("Recalculate morphological parameters using the current scale factor\n(Requires existing segmentation)")
            self.recalculate_button.clicked.connect(self.recalculate_parameters_with_new_scale)
            self.recalculate_button.setEnabled(False) # Initially disabled
            action_layout.addWidget(self.recalculate_button)
            # -----------------------------

            # Add Stretch to push controls to the top
            control_layout.addStretch()

            # --- Right Panel (Image and Results) ---
            right_frame = QFrame()
            right_layout = QVBoxLayout(right_frame)
            main_container.addWidget(right_frame)

            # Image Display (Graphics View)
            self.view = CustomGraphicsView(self)
            self.scene = QGraphicsScene(self)
            self.view.setScene(self.scene)
            bg_color = self.palette().color(QPalette.ColorRole.Base) # Use theme base color
            self.view.setBackgroundBrush(bg_color)
            right_layout.addWidget(self.view, 5) # Give more stretch factor to view

            # Scale reminder label
            self.scale_reminder_label = QLabel("Load an image and set the scale (draw line or enter value).")
            # Initial style, will be updated by update_scale_reminder
            self.scale_reminder_label.setStyleSheet("color: #ffb74d; font-weight: bold; padding: 8px; background-color: #2a2a2d; border-radius: 4px; margin: 2px;")
            self.scale_reminder_label.setAlignment(Qt.AlignCenter)
            right_layout.addWidget(self.scale_reminder_label)
            self.update_scale_reminder() # Call to set initial state correctly

            # Results Display (Treeview)
            results_group = QGroupBox("Analysis Results")
            results_layout = QVBoxLayout(results_group)
            right_layout.addWidget(results_group, 3) # Smaller stretch factor for results

            # Add a delete button near the results table
            delete_button = QPushButton(QIcon(resource_path("icons/delete.png")), " Delete Selected Grains")
            delete_button.setToolTip("Remove the selected grains from the results and visualization")
            delete_button.clicked.connect(self.delete_selected_grains)
            self.delete_grains_button = delete_button # Store reference
            results_layout.addWidget(delete_button)

            self.result_tree = QTreeView()
            self.result_tree.setSortingEnabled(True)
            self.result_tree.setAlternatingRowColors(True)
            self.result_tree.setSelectionMode(QAbstractItemView.ExtendedSelection) # Allow multiple rows
            self.result_tree.setSelectionBehavior(QAbstractItemView.SelectRows) # Select whole rows
            self.result_tree.setItemDelegate(CustomItemDelegate(self.result_tree)) # Use custom delegate
            results_layout.addWidget(self.result_tree)

            self.tree_model = QStandardItemModel() # Initialize the model HERE
            self.result_tree.setModel(self.tree_model)
            self.setup_result_tree_headers() # Setup headers

            # Connect treeview selection change to update canvas
            self.result_tree.selectionModel().selectionChanged.connect(self.on_treeview_selection_changed)

            # --- Splitter Sizes ---
            # Try to give more space to the right panel initially
            # Setting sizes after window is shown might be more reliable
            QTimer.singleShot(100, lambda: main_container.setSizes([int(self.width() * 0.25), int(self.width() * 0.75)]))

            self.update_action_states() # Initial state




    @Slot()
    def recalculate_parameters_with_new_scale(self):
        """
        Recalculates morphological parameters using existing annotations
        and the currently set scale factor. Updates the results table.
        """
        logger.info("Recalculate Parameters requested.")

        # --- Pre-conditions ---
        if self.processing_thread and self.processing_thread.isRunning():
            QMessageBox.warning(self, "Busy", "Cannot recalculate while processing is running.")
            return
        if not self.annotations:
            self._show_error_message("No Segmentation", "Cannot recalculate. No segmentation results exist.\nPlease run 'Segment Image' first.")
            return
        if self.current_scale_factor is None:
            self._show_error_message("No Scale", "Cannot recalculate. Please set a valid scale factor.")
            return

        # --- Confirmation ---
        reply = QMessageBox.question(
            self, "Confirm Recalculation",
            f"Recalculate all parameters using the current scale factor ({self.current_scale_factor:.4f} µm/pixel)?\nThis will overwrite existing parameter values.",
            QMessageBox.Yes | QMessageBox.Cancel, QMessageBox.Yes
        )
        if reply == QMessageBox.Cancel:
            return

        logger.info(f"Starting recalculation with scale: {self.current_scale_factor:.4f} µm/pixel for {len(self.annotations)} annotations.")
        self.update_status("Recalculating parameters...")
        QApplication.setOverrideCursor(Qt.WaitCursor)
        self.update_progress(1) # Show progress bar start

        try:
            # --- Perform Recalculation ---
            # Use the existing calculate_parameters function
            new_df, new_valid_mask = calculate_parameters(
                self.annotations,
                self.current_scale_factor,
                progress_callback=self.update_progress # Pass progress signal
            )

            if new_df is None or new_df.empty:
                # This might happen if the new scale makes all previous annotations invalid (unlikely but possible)
                logger.warning("Recalculation resulted in no valid objects.")
                self._show_error_message("Recalculation Error", "No valid objects found after recalculating parameters with the new scale.")
                # Clear existing results
                self.df = pd.DataFrame() # Empty DataFrame
                # Keep the original annotations? Or clear them too? Let's keep them but clear df.
                # self.annotations = []
                self.clear_scene_items(clear_pixmap=False) # Remove highlights
            else:
                logger.info(f"Recalculation successful. Found {len(new_df)} valid objects.")
                # Update the main DataFrame
                self.df = new_df

                # --- Important: Update annotations if the valid mask changed ---
                # Check if the number of valid items after recalculation matches the original number of annotations used
                # This assumes self.annotations contained only valid items *before* this call.
                if new_valid_mask.size != len(self.annotations):
                     logger.error(f"CRITICAL: Size mismatch between new valid mask ({new_valid_mask.size}) and existing annotations ({len(self.annotations)}).")
                     raise RuntimeError("Annotation count mismatch during recalculation.")

                current_valid_indices = np.where(new_valid_mask)[0]
                if len(current_valid_indices) != len(self.annotations):
                     logger.warning(f"Number of valid annotations changed during recalculation ({len(self.annotations)} -> {len(current_valid_indices)}). Filtering annotations.")
                     original_annotations = self.annotations # Keep a reference
                     self.annotations = [original_annotations[i] for i in current_valid_indices]
                     # Sanity check - df length should now match filtered annotations length
                     if len(self.df) != len(self.annotations):
                          logger.error("CRITICAL: Mismatch between recalculated df and filtered annotations count!")
                          # Handle this potentially catastrophic state? Maybe reset?
                          raise RuntimeError("Mismatch between df and annotations after recalculation filter.")

            # --- Update UI ---
            self.populate_result_tree() # Refresh the table with new data
            # Redraw highlights only if annotations changed or if df is not empty
            if len(current_valid_indices) != new_valid_mask.size or not self.df.empty:
                self.draw_grain_highlights()
            else: # If df became empty, ensure highlights are cleared
                 self.clear_scene_items(clear_pixmap=False)

            self.update_status(f"Parameters recalculated: {len(self.df)} valid objects.")

        except Exception as e:
            logger.exception("Error during parameter recalculation:")
            self._show_error_message("Recalculation Error", f"An error occurred during recalculation: {e}")
            self.update_status("Recalculation failed.")
        finally:
            QApplication.restoreOverrideCursor()
            self.update_progress(0) # Hide progress bar
            self.update_action_states() # Update button states


    def setup_result_tree_headers(self):
        """Configures the columns and headers for the result tree."""
        if self.tree_model is None: return

        columns = [
            'Object', 'Area (µm²)', 'Length (µm)', 'Width (µm)', 'Perimeter (µm)',
            'Elongation', 'Compactness', 'Roundness', 'Solidity', 'Convexity',
            'Rectangularity', 'Circle-Equivalent Diameter (µm)',
            'Center_X (px)', 'Center_Y (px)'
             # Add/remove/reorder as needed
        ]
        self.tree_model.setColumnCount(len(columns))
        self.tree_model.setHorizontalHeaderLabels(columns)

        # Adjust column widths (example)
        header = self.result_tree.header()
        header.setSectionResizeMode(QHeaderView.ResizeToContents) # Start with resize to contents
        # Optionally set specific columns to interactive or fixed later
        # header.setSectionResizeMode(0, QHeaderView.Interactive)
        # header.setColumnWidth(0, 80)

        # Enable sorting on the header
        self.result_tree.setSortingEnabled(True)
        # self.result_tree.sortByColumn(0, Qt.AscendingOrder) # Initial sort?

    def populate_result_tree(self):
        """Populates the result treeview with data from the DataFrame."""
        logger.debug(f"Populating result tree. DataFrame is None: {self.df is None}")
        if hasattr(self, 'tree_model'):
            self.tree_model.removeRows(0, self.tree_model.rowCount()) # Clear existing items
            self.setup_result_tree_headers() # Ensure headers are reset/correct
        else:
             logger.error("Tree model not initialized.")
             return

        if self.df is None or self.df.empty:
            logger.info("No data to display in result tree.")
            # Optionally clear headers if you prefer an empty table
            # self.tree_model.setColumnCount(0)
            return

        logger.info(f"Displaying {len(self.df)} results in tree view.")
        headers = [self.tree_model.horizontalHeaderItem(i).text() for i in range(self.tree_model.columnCount())]

        # Block signals during population for performance
        self.result_tree.selectionModel().blockSignals(True)
        self.tree_model.blockSignals(True)

        # Make sure 'Object' column exists and is correctly formatted
        if 'Object' not in self.df.columns:
            # Create 'Object' column based on index if it doesn't exist
            self.df['Object'] = [f"Object {idx}" for idx in self.df.index]
            # Re-fetch headers if 'Object' was just added (though setup_result_tree_headers should handle it)
            headers = [self.tree_model.horizontalHeaderItem(i).text() for i in range(self.tree_model.columnCount())]
        elif not all(self.df['Object'] == [f"Object {idx}" for idx in self.df.index]):
            # If 'Object' exists but doesn't match index, overwrite for consistency
             logger.warning("Overwriting 'Object' column to match DataFrame index.")
             self.df['Object'] = [f"Object {idx}" for idx in self.df.index]


        for df_index, row in self.df.iterrows():
            items = []
            for header in headers:
                if header in row:
                    value = row[header]
                    try:
                        if isinstance(value, (float, np.floating)):
                            item = QStandardItem(locale.format_string("%.3f", value, grouping=True))
                        elif isinstance(value, (int, np.integer)):
                             item = QStandardItem(locale.format_string("%d", value, grouping=True))
                        else:
                            item = QStandardItem(str(value))
                    except Exception:
                         item = QStandardItem(str(value)) # Fallback to string

                    item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter if isinstance(value, (float, int, np.number)) and header != 'Object' else Qt.AlignLeft | Qt.AlignVCenter)
                    item.setData(df_index, Qt.UserRole) # Store the original DataFrame index
                    item.setEditable(False)
                    items.append(item)
                else:
                     items.append(QStandardItem("")) # Add empty item if column missing

            self.tree_model.appendRow(items)

        # Unblock signals
        self.tree_model.blockSignals(False)
        self.result_tree.selectionModel().blockSignals(False) # Ensure selection model is unblocked too

        # --- MODIFICATION START ---
        # Resize columns after populating - Try resizing all columns forcefully
        logger.debug("Resizing all tree view columns to contents.")
        for i in range(self.tree_model.columnCount()):
            self.result_tree.resizeColumnToContents(i)
        # Optional: Try adjusting stretch on last section, might help layout
        self.result_tree.header().setStretchLastSection(False) # Turn off stretch first
        self.result_tree.header().setStretchLastSection(True)  # Then turn it back on to recalculate
        # --- MODIFICATION END ---


        # Optional: If resizing doesn't work, force an explicit repaint (usually not needed)
        # self.result_tree.update()

        logger.debug("Finished populating result tree.")

    def setup_status_bar(self):
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_progress = QProgressBar()
        self.status_progress.setMaximumSize(150, 15)
        self.status_progress.setTextVisible(False)
        self.status_bar.addPermanentWidget(self.status_progress)
        self.status_progress.hide() # Hide initially
        self.update_status("Ready. Load an image to start.")

    @Slot(str)
    def update_status(self, message):
        """Updates the status bar message and triggers an update of action states."""
        self.status_bar.showMessage(message)
        logger.info(f"Status: {message}")
        # Call update_action_states whenever status changes, as it might affect button states,
        # especially after model loading or processing finishes.
        self.update_action_states() # Ensure button states reflect the new status

    @Slot(int)
    def update_progress(self, value):
        if value > 0 and value < 100:
            self.status_progress.show()
            self.status_progress.setValue(value)
        else:
            self.status_progress.hide()
            self.status_progress.setValue(0)


    # --- Core Logic Methods ---

    def reset_app_state(self, clear_image=True):
         """Resets analysis data and optionally the loaded image."""
         logger.info(f"Resetting application state (clear_image={clear_image}).")
         # Stop any ongoing processing
         self.stop_processing_thread()

         if clear_image:
              self.uploaded_image = None
              self.image_file_path = None
              self.image_filename = None
              self.scene.clear()
              self.pixmap_item = None
              self.view.reset_view() # Reset zoom/pan

         self.processed_image_vis = None
         self.annotations = []
         self.df = None
         self.grain_items = {}
         self.selected_df_indices = set()

         # Reset scale
         self.current_scale_factor = None
         self.original_pixel_length = None
         self.scale_factor_label.setText("<b>Current Scale: Not set</b>")
         self.view.clear_scale_line_item()
         self.view.scale_line_start = None
         self.view.scale_line_end = None
         self.update_scale_reminder()

         # Clear results table
         if hasattr(self, 'tree_model'):
              self.tree_model.removeRows(0, self.tree_model.rowCount())
         self.update_action_states()
         self.update_status("State reset." + (" Load an image." if clear_image else ""))

    def reset_app(self):
        """Resets the application to its initial state, asking for confirmation."""
        if self.df is not None or self.uploaded_image is not None:
            reply = QMessageBox.question(
                self, "Reset Confirmation",
                "Are you sure you want to reset?\nAll unsaved data (image, results, scale) will be lost.",
                QMessageBox.Yes | QMessageBox.Cancel, QMessageBox.Cancel
            )
            if reply == QMessageBox.Cancel:
                return

        logger.info("Performing full application reset.")
        self.reset_app_state(clear_image=True)
        # Reset UI defaults (sliders, line edits)
        self.input_size.setValue(DEFAULT_INPUT_SIZE)
        self.iou_threshold.setValue(int(DEFAULT_IOU_THRESHOLD * 100))
        self.conf_threshold.setValue(int(DEFAULT_CONF_THRESHOLD * 100))
        self.max_det.setText(str(DEFAULT_MAX_DET))
        self.contour_thickness.setValue(DEFAULT_CONTOUR_THICKNESS)
        self.real_world_length.setText("100")
        self.manual_scale.setText("0.0")

        # Reset MobileSAM params (including new ones)
        self.points_per_side.setValue(32)
        self.pred_iou_thresh.setValue(88)
        self.stability_score_thresh.setValue(95)
        self.crop_n_layers.setValue(0) # <-- Reset new param
        self.crop_n_points_downscale_factor.setValue(1) # <-- Reset new param

        self.update_slider_labels()
        self.update_status("Application reset. Load an image to start.")


    def upload_image(self):
        """Opens a file dialog for the user to select an image."""
        # Stop any processing first
        if self.processing_thread and self.processing_thread.isRunning():
             if QMessageBox.question(self, "Confirm", "Processing is ongoing. Stop and load new image?",
                                     QMessageBox.Yes | QMessageBox.Cancel) == QMessageBox.Cancel:
                  return
             self.stop_processing_thread()

        options = QFileDialog.Options()
        # options |= QFileDialog.ReadOnly # Not necessary usually
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Open Image File",
            self.last_save_dir, # Start in last used directory
            "Image Files (*.png *.jpg *.jpeg *.tif *.tiff *.bmp);;All Files (*)",
            options=options,
        )

        if file_path:
            try:
                self.update_status(f"Loading image: {os.path.basename(file_path)}...")
                QApplication.setOverrideCursor(Qt.WaitCursor)

                # Load the image using PIL
                new_image = Image.open(file_path)
                # Ensure image is in a displayable format (RGB/RGBA)
                if new_image.mode not in ['RGB', 'RGBA', 'L']:
                     logger.warning(f"Image mode {new_image.mode} not standard, converting to RGB.")
                     new_image = new_image.convert('RGB')
                elif new_image.mode == 'L':
                     new_image = new_image.convert('RGB') # Convert grayscale for consistency

                # Reset state BEFORE assigning new image
                self.reset_app_state(clear_image=True)

                self.uploaded_image = new_image
                self.image_file_path = file_path
                self.image_filename = os.path.basename(file_path)
                self.last_save_dir = os.path.dirname(file_path) # Update last directory

                logger.info(f"Image loaded: {self.image_filename} ({self.uploaded_image.width}x{self.uploaded_image.height})")

                # Display the image
                self.display_image_on_scene(self.uploaded_image)

                # Update UI
                self.update_action_states()
                self.update_scale_reminder()
                self.update_status(f"Image '{self.image_filename}' loaded. Set scale.")
                self.setWindowTitle(f"GrainSight - {self.image_filename}")

                # Optional: Suggest input size based on image dimensions
                min_dim = int(min(self.uploaded_image.width, self.uploaded_image.height))
                rec_input_size = max(256, min(2048, int(min_dim * 0.99))) # Suggest slightly smaller than min dim
                # Find nearest multiple of 64 or 32 if needed
                rec_input_size = max(256, (rec_input_size // 64) * 64)
                self.input_size.setValue(rec_input_size)
                self.update_slider_labels()
                logger.debug(f"Suggested input size: {rec_input_size}")


            except FileNotFoundError:
                self._show_error_message("Error", f"Image file not found: {file_path}")
                self.update_status("Error: Image file not found.")
            except Exception as e:
                 logger.exception(f"Failed to load image: {file_path}")
                 self._show_error_message("Image Load Error", f"Could not load image file.\nError: {e}")
                 self.reset_app_state(clear_image=True) # Reset if load failed
                 self.update_status("Error loading image.")
            finally:
                QApplication.restoreOverrideCursor()


    def pil_image_to_qimage(self, pil_image):
        """
        Converts a PIL Image to a QImage, correctly handling channel order
        for common modes (RGB, RGBA, L) and Qt formats.
        Attempts to display original RGB images correctly without swapping.
        Handles RGBA conversion to BGRA for ARGB32 format.
        """
        try:
            img_width, img_height = pil_image.size
            logger.debug(f"PIL Image Info: Mode={pil_image.mode}, Size=({img_width}x{img_height})")

            if pil_image.mode == "RGB":
                # PIL RGB is R,G,B. QImage.Format_RGB888 expects R,G,B.
                # Pass the bytes directly. QPixmap should handle display.
                byte_string = pil_image.tobytes("raw", "RGB")
                bytes_per_line = img_width * 3
                qimage = QImage(byte_string, img_width, img_height, bytes_per_line, QImage.Format_RGB888)
                # --- REMOVED THE SWAP ---
                # qimage = qimage.rgbSwapped()
                logger.debug("Converted PIL RGB -> QImage Format_RGB888 (NO swap)")

            elif pil_image.mode == "RGBA":
                # PIL RGBA is R,G,B,A. QImage.Format_ARGB32 expects B,G,R,A (on little-endian).
                # Convert RGBA -> BGRA. This part worked correctly before.
                logger.debug("Converting PIL RGBA -> QImage Format_ARGB32 (BGRA byte order)")
                try:
                    rgba_bytes = pil_image.tobytes("raw", "RGBA")
                    # Ensure NumPy is available
                    try:
                        import numpy as np
                    except ImportError:
                         logger.error("NumPy not found, required for RGBA conversion. Install NumPy (`pip install numpy`).")
                         # Fallback or return None? Fallback is less reliable for color.
                         return None # Indicate failure if NumPy is missing

                    arr_rgba = np.frombuffer(rgba_bytes, dtype=np.uint8).reshape((img_height, img_width, 4))
                    # Create BGRA array by selecting channels B, G, R, A
                    arr_bgra = arr_rgba[..., [2, 1, 0, 3]] # B=2, G=1, R=0, A=3
                    bgra_byte_string = arr_bgra.tobytes()
                    bytes_per_line = img_width * 4
                    qimage = QImage(bgra_byte_string, img_width, img_height, bytes_per_line, QImage.Format_ARGB32)
                except Exception as e:
                    logger.error(f"Error during RGBA->BGRA conversion: {e}", exc_info=True)
                    # Attempt a less reliable fallback if conversion fails
                    logger.warning("Falling back to Format_RGBA8888 due to conversion error. Colors might be incorrect.")
                    byte_string = pil_image.tobytes("raw", "RGBA")
                    bytes_per_line = img_width * 4
                    qimage = QImage(byte_string, img_width, img_height, bytes_per_line, QImage.Format_RGBA8888)


            elif pil_image.mode == "L": # Grayscale
                # Convert to RGB first for display
                logger.debug("Converting PIL L -> RGB -> QImage Format_RGB888 (NO swap)")
                rgb_image = pil_image.convert("RGB")
                byte_string = rgb_image.tobytes("raw", "RGB")
                bytes_per_line = img_width * 3
                qimage = QImage(byte_string, img_width, img_height, bytes_per_line, QImage.Format_RGB888)
                # --- REMOVED THE SWAP ---
                # qimage = qimage.rgbSwapped()

            else: # Handle other modes by converting to RGBA first (preferred for alpha handling)
                logger.warning(f"Unsupported PIL mode {pil_image.mode}, attempting conversion via RGBA.")
                try:
                    rgba_image = pil_image.convert("RGBA")
                    img_width, img_height = rgba_image.size # Update size if conversion changed it
                    # Now apply the same RGBA -> BGRA conversion logic
                    rgba_bytes = rgba_image.tobytes("raw", "RGBA")

                    try:
                        import numpy as np
                    except ImportError:
                         logger.error("NumPy not found, required for RGBA conversion. Install NumPy (`pip install numpy`).")
                         return None

                    arr_rgba = np.frombuffer(rgba_bytes, dtype=np.uint8).reshape((img_height, img_width, 4))
                    arr_bgra = arr_rgba[..., [2, 1, 0, 3]] # Swap R and B
                    bgra_byte_string = arr_bgra.tobytes()
                    bytes_per_line = img_width * 4
                    qimage = QImage(bgra_byte_string, img_width, img_height, bytes_per_line, QImage.Format_ARGB32)
                    logger.debug(f"Converted PIL {pil_image.mode} -> RGBA -> QImage Format_ARGB32")

                except Exception as conv_e:
                    logger.error(f"Could not convert PIL image mode {pil_image.mode} to RGBA/ARGB32: {conv_e}", exc_info=True)
                    return None

            # Final check for null QImage
            if qimage is None or qimage.isNull():
                logger.error("Created QImage is null after conversion attempt!")
                return None

            logger.debug(f"QImage created successfully: Size=({qimage.width()}x{qimage.height()}), Format={qimage.format()}")
            return qimage

        except Exception as e:
            # Catch potential memory errors or other issues during conversion
            logger.exception(f"General error converting PIL image to QImage: {e}")
            return None

    def display_image_on_scene(self, pil_image_to_display):
        """Clears the scene and displays the given PIL image."""
        if pil_image_to_display is None:
            self.scene.clear()
            self.pixmap_item = None
            logger.warning("Attempted to display a None image.")
            return

        logger.debug("Attempting to convert PIL to QImage...")
        qimage = self.pil_image_to_qimage(pil_image_to_display)
        if qimage is None or qimage.isNull(): # Check if conversion failed or resulted in null
            logger.error("Failed to convert PIL to QImage or QImage is null.")
            self._show_error_message("Display Error", "Could not convert image for display.")
            # Optionally clear scene or show placeholder
            self.scene.clear()
            self.pixmap_item = None
            return
        logger.debug("PIL to QImage conversion successful. Creating QPixmap...")

        try:
            # --- Potential Crash Point ---
            pixmap = QPixmap.fromImage(qimage)
            # ---------------------------
            logger.debug("QPixmap created successfully.")
            if pixmap.isNull():
                logger.error("Created QPixmap is null!")
                self._show_error_message("Display Error", "Failed to create pixmap from image data.")
                self.scene.clear()
                self.pixmap_item = None
                return

        except Exception as e:
            # Catch potential errors during QPixmap creation (less common but possible)
            logger.exception("Error creating QPixmap from QImage!")
            self._show_error_message("Display Error", f"Failed to create pixmap for display.\nError: {e}")
            self.scene.clear()
            self.pixmap_item = None
            return

        logger.debug("Clearing previous scene items (keeping pixmap=False initially)...")
        self.clear_scene_items(clear_pixmap=True) # Clear previous pixmap before adding new one

        logger.debug("Adding new pixmap item to scene...")
        self.pixmap_item = CustomPixmapItem(pixmap)
        self.scene.addItem(self.pixmap_item)
        logger.debug("Setting scene rect...")
        self.scene.setSceneRect(self.pixmap_item.boundingRect())

        logger.debug("Fitting view...")
        self.view.fitInView(self.pixmap_item, Qt.KeepAspectRatio)
        logger.debug("Resetting view...")
        self.view.reset_view()

        logger.debug(f"Displayed image ({pil_image_to_display.width}x{pil_image_to_display.height}) on scene complete.")
    def clear_scene_items(self, clear_pixmap=True):
         """Removes grain polygons, text, and optionally the base pixmap."""
         items_to_remove = []
         for item in self.scene.items():
              if isinstance(item, (QGraphicsPolygonItem, QGraphicsTextItem, QGraphicsLineItem)):
                   items_to_remove.append(item)
              elif clear_pixmap and item == self.pixmap_item:
                   items_to_remove.append(item)

         for item in items_to_remove:
              self.scene.removeItem(item)

         if clear_pixmap:
             self.pixmap_item = None
         self.grain_items = {} # Clear grain item tracking


    @Slot(QPointF, QPointF)
    def on_scale_line_drawn(self, start_point_scene, end_point_scene):
        """Handles the scale line drawn signal from the view."""
        if self.uploaded_image is None:
            logger.warning("Scale line drawn but no image loaded.")
            return

        # The points are in scene coordinates, which correspond to the displayed pixmap
        # We need the pixel length on the *original* image.

        if not self.pixmap_item or self.pixmap_item.pixmap().isNull():
             logger.error("Pixmap item is missing or invalid for scale calculation.")
             return

        display_width = self.pixmap_item.pixmap().width()
        display_height = self.pixmap_item.pixmap().height()
        original_width = self.uploaded_image.width
        original_height = self.uploaded_image.height

        if display_width == 0 or display_height == 0:
            logger.error("Displayed image has zero dimension.")
            return

        # Calculate ratios to map scene coordinates back to original image coordinates
        ratio_x = original_width / display_width
        ratio_y = original_height / display_height

        # Convert scene points to original image pixel coordinates
        start_orig_x = start_point_scene.x() * ratio_x
        start_orig_y = start_point_scene.y() * ratio_y
        end_orig_x = end_point_scene.x() * ratio_x
        end_orig_y = end_point_scene.y() * ratio_y

        # Calculate pixel length in original image space
        pixel_length = math.dist((start_orig_x, start_orig_y), (end_orig_x, end_orig_y))
        self.original_pixel_length = pixel_length # Store the length on the original image
        logger.info(f"Scale line drawn. Scene length: {calculate_pixel_length(start_point_scene, end_point_scene):.2f}, Original image pixel length: {pixel_length:.2f}")

        if pixel_length < 1.0: # Check for very small lines
            self._show_error_message("Scale Error", "Scale line is too short. Please draw a longer line.")
            self.view.clear_scale_line_item() # Remove the drawn line
            self.original_pixel_length = None
            return

        try:
            real_length_str = self.real_world_length.text().replace(locale.localeconv()['decimal_point'], '.') # Handle locale decimal sep
            real_length = float(real_length_str)
            if real_length <= 0:
                raise ValueError("Real-world length must be positive.")

            # Calculate scale factor (µm per original image pixel)
            self.current_scale_factor = real_length / pixel_length
            self.scale_factor_label.setText(f"<b>Current Scale: {self.current_scale_factor:.4f} µm/pixel</b>")
            self.update_scale_reminder(is_set=True)
            logger.info(f"Scale factor calculated: {self.current_scale_factor:.4f} µm/pixel")

            # Optional: Show a confirmation dialog
            # QMessageBox.information(self, "Scale Set", f"Scale factor set to: {self.current_scale_factor:.4f} µm/pixel")

            # Enable processing now that scale is set
            self.update_action_states()

        except ValueError as e:
            self._show_error_message("Input Error", f"Invalid real-world length.\nPlease enter a positive number.\nError: {e}")
            self.view.clear_scale_line_item() # Remove the drawn line
            self.current_scale_factor = None
            self.original_pixel_length = None
            self.update_scale_reminder()
        except Exception as e:
            logger.exception("Error calculating scale factor:")
            self._show_error_message("Scale Error", f"An unexpected error occurred during scale calculation: {e}")
            self.view.clear_scale_line_item() # Remove the drawn line
            self.current_scale_factor = None
            self.original_pixel_length = None
            self.update_scale_reminder()

    def set_manual_scale(self):
        """Sets the scale factor manually from the line edit."""
        if self.uploaded_image is None:
            self._show_error_message("Warning", "Please load an image before setting the scale.")
            return

        try:
            scale_str = self.manual_scale.text().replace(locale.localeconv()['decimal_point'], '.') # Handle locale decimal sep
            scale_factor = float(scale_str)
            if scale_factor <= 0:
                raise ValueError("Scale factor must be positive.")

            self.current_scale_factor = scale_factor
            self.original_pixel_length = None # Manual scale doesn't have a pixel length
            self.scale_factor_label.setText(f"<b>Current Scale: {self.current_scale_factor:.4f} µm/pixel</b>")
            self.update_scale_reminder(is_set=True)
            logger.info(f"Scale factor set manually to {self.current_scale_factor:.4f} µm/pixel")

            # Clear any previously drawn scale line
            self.view.clear_scale_line_item()
            self.view.scale_line_start = None
            self.view.scale_line_end = None

            self.update_action_states()

            # Optionally confirm
            # QMessageBox.information(self, "Scale Set", f"Scale factor set manually to: {self.current_scale_factor:.4f} µm/pixel")

        except ValueError as e:
            self._show_error_message("Input Error", f"Invalid scale factor value.\nPlease enter a positive number.\nError: {e}")
            self.current_scale_factor = None
            self.update_scale_reminder()
        except Exception as e:
            logger.exception("Error setting manual scale factor:")
            self._show_error_message("Scale Error", f"An unexpected error occurred: {e}")
            self.current_scale_factor = None
            self.update_scale_reminder()


    def update_scale_reminder(self, is_set=None):
         """Updates the scale reminder label based on current state."""
         if is_set is None: # Auto-detect if not specified
              is_set = self.current_scale_factor is not None

         # Determine if dark theme is active
         is_dark = self.is_dark_theme if hasattr(self, 'is_dark_theme') else False
         
         # Define colors based on theme
         if is_dark:
             # Dark theme colors
             warning_color = "#FFA500"  # Orange
             warning_bg = "#404040"
             error_color = "#FF6347"    # Tomato Red
             error_bg = "#403030"
             success_color = "#90EE90"  # Light Green
             success_bg = "#305030"     # Dark Green
         else:
             # Light theme colors
             warning_color = "#FF8C00"  # Dark Orange
             warning_bg = "#FFF8E1"     # Light Yellow
             error_color = "#D32F2F"    # Red
             error_bg = "#FFEBEE"       # Light Red
             success_color = "#388E3C"  # Green
             success_bg = "#E8F5E9"     # Light Green

         # Common style properties
         base_style = "font-weight: bold; padding: 8px; border-radius: 4px; margin: 2px;"
         
         if self.uploaded_image is None:
              self.scale_reminder_label.setText("Load an image to start.")
              self.scale_reminder_label.setStyleSheet(f"color: {warning_color}; background-color: {warning_bg}; {base_style}")
              self.scale_reminder_label.show()
         elif not is_set:
              self.scale_reminder_label.setText("Scale not set. Draw line or enter value.")
              self.scale_reminder_label.setStyleSheet(f"color: {error_color}; background-color: {error_bg}; {base_style}")
              self.scale_reminder_label.show()
         else:
              # Scale is set, hide or change the message
              self.scale_reminder_label.setText("Scale is set. Ready to process.")
              self.scale_reminder_label.setStyleSheet(f"color: {success_color}; background-color: {success_bg}; {base_style}")
              # Optionally hide it completely after a delay
              QTimer.singleShot(3000, lambda: self.scale_reminder_label.hide() if self.current_scale_factor is not None else None)


    def start_processing(self):
        """Initiates the image processing in a background thread."""
        logger.info("Processing requested.")
        if self.model is None:
             self._show_error_message("Model Error", "Model is not loaded yet. Please wait or check logs.")
             return
        if self.uploaded_image is None:
            self._show_error_message("Warning", "Please upload an image first.")
            return
        if self.current_scale_factor is None:
            self._show_error_message("Scale Error", "Image scale is not set.\nPlease draw a scale line or enter the scale factor manually.")
            return
        if self.processing_thread and self.processing_thread.isRunning():
             logger.warning("Processing is already running.")
             QMessageBox.warning(self, "Busy", "Image processing is already in progress.")
             return

        # --- Get Parameters ---
        try:
            # Determine which model is being used and which model is loaded
            use_fastsam = self.fastsam_radio.isChecked()
            current_model_is_fastsam = (self.model_type == "fastsam")

            # Check if selected model type matches loaded model type
            if use_fastsam != current_model_is_fastsam:
                model_name = "FastSAM" if use_fastsam else "MobileSAM"
                loaded_model_name = "FastSAM" if current_model_is_fastsam else "MobileSAM"
                self._show_error_message("Model Mismatch",
                                        f"Selected model ({model_name}) doesn't match loaded model ({loaded_model_name}).\n"
                                        f"Please change selection or reload the model.")
                return

            # Common parameters
            contour_thickness = self.contour_thickness.value()

            # Model-specific parameters
            if current_model_is_fastsam:  # Using FastSAM model
                # FastSAM parameters
                input_size = self.input_size.value()
                iou = self.iou_threshold.value() / 100.0
                conf = self.conf_threshold.value() / 100.0
                max_det = int(self.max_det.text())

                logger.info(f"Processing with FastSAM: input_size={input_size}, iou={iou:.2f}, "
                           f"conf={conf:.2f}, max_det={max_det}, contour_thickness={contour_thickness}")

                # Setup parameters for FastSAM
                params = {
                    'input_size': input_size,
                    'iou': iou,
                    'conf': conf,
                    'max_det': max_det,
                    'contour_thickness': contour_thickness,
                    'model_type': 'fastsam'
                }
            else:  # Using MobileSAM model
                points_per_side = self.points_per_side.value()
                pred_iou_thresh = self.pred_iou_thresh.value() / 100.0
                stability_score_thresh = self.stability_score_thresh.value() / 100.0
                box_nms_thresh = 0.3
                crop_n_layers = self.crop_n_layers.value()
                crop_n_points_downscale_factor = self.crop_n_points_downscale_factor.value()

                logger.info(f"Processing with MobileSAM: points_per_side={points_per_side}, "
                           f"pred_iou_thresh={pred_iou_thresh:.2f}, stability_score_thresh={stability_score_thresh:.2f}, "
                           f"box_nms_thresh={box_nms_thresh:.2f}, crop_n_layers={crop_n_layers}, " # Log new params
                           f"crop_points_downscale={crop_n_points_downscale_factor}, contour_thickness={contour_thickness}") # Log new params
                # Setup parameters for MobileSAM
                params = {
                    'points_per_side': points_per_side,
                    'pred_iou_thresh': pred_iou_thresh,
                    'stability_score_thresh': stability_score_thresh,
                    'box_nms_thresh': box_nms_thresh,
                    'crop_n_layers': crop_n_layers,
                    'crop_n_points_downscale_factor': crop_n_points_downscale_factor,
                    'contour_thickness': contour_thickness,
                    'model_type': 'mobilesam'
                }

        except ValueError as e:
            self._show_error_message("Parameter Error", f"Invalid parameter value: {e}")
            return

        # --- Reset previous results ---
        # self.reset_app_state(clear_image=False) # Keep image, clear results
        self.annotations = []
        self.df = None
        self.processed_image_vis = None
        self.clear_scene_items(clear_pixmap=False) # Keep base image
        if hasattr(self, 'tree_model'):
            self.tree_model.removeRows(0, self.tree_model.rowCount())
        self.selected_df_indices = set()
        logger.debug("Cleared previous analysis results.")

        # --- Setup Worker and Thread ---
        self.processing_worker = ProcessingWorker(
            self.uploaded_image,
            self.model,
            self.device,
            self.current_scale_factor,
            params
        )
        self.processing_thread = QThread()
        self.processing_worker.moveToThread(self.processing_thread)

        # --- Connections ---
        # Worker signals to main thread slots
        self.processing_worker.finished.connect(self.on_processing_finished)
        self.processing_worker.error.connect(self.on_processing_error)
        self.processing_worker.progress.connect(self.update_progress)
        self.processing_worker.status.connect(self.update_status)

        # Thread control
        self.processing_thread.started.connect(self.processing_worker.run)
        self.processing_worker.finished.connect(self.processing_thread.quit)
        self.processing_worker.finished.connect(self.processing_worker.deleteLater)
        self.processing_thread.finished.connect(self.processing_thread.deleteLater)
        self.processing_thread.finished.connect(self._on_thread_finished) # Clean up reference

        # --- Start Thread ---
        self.processing_thread.start()
        logger.info("Processing thread started.")

        # Update UI
        self.update_action_states(processing=True)
        self.update_status("Processing started...")
        self.update_progress(1) # Show progress bar start


    def stop_processing_thread(self):
        """Requests the processing thread to stop."""
        if self.processing_thread and self.processing_thread.isRunning():
            logger.info("Attempting to stop processing thread.")
            if self.processing_worker:
                 self.processing_worker.stop() # Signal worker to stop gracefully
            # self.processing_thread.quit() # Request thread quit
            # self.processing_thread.wait(1000) # Wait briefly
            # if self.processing_thread.isRunning():
            #      logger.warning("Processing thread did not stop gracefully, terminating.")
            #      self.processing_thread.terminate() # Force terminate if needed (use with caution)
            #      self.processing_thread.wait()
            self._on_thread_finished() # Ensure cleanup happens


    @Slot(object, object, object)
    def on_processing_finished(self, df, annotations, segmented_image_vis):
        """Handles the results when the processing thread finishes successfully."""
        logger.info("Processing finished signal received.")
        self.update_progress(0) # Hide progress bar

        if df is not None and annotations is not None and segmented_image_vis is not None:
            self.df = df
            self.annotations = annotations # Store the final valid annotations
            self.processed_image_vis = segmented_image_vis

            logger.info(f"Processing successful. Received {len(self.df)} results.")

            # Update UI with results
            self.display_image_on_scene(self.processed_image_vis) # Show the visualization
            self.draw_grain_highlights() # Draw interactive elements
            self.populate_result_tree()
            self.update_status(f"Processing complete: {len(self.df)} grains found.")
        else:
             # This case should ideally be handled by on_processing_error
             logger.warning("Processing finished but results are None.")
             self.update_status("Processing finished, but no valid results were generated.")
             # Display the original image again if visualization failed
             if self.uploaded_image:
                  self.display_image_on_scene(self.uploaded_image)

        self.update_action_states(processing=False)

    @Slot(str)
    def on_processing_error(self, error_message):
        """Handles errors reported by the processing thread."""
        logger.error(f"Processing error signal received: {error_message}")
        self.update_progress(0)
        self._show_error_message("Processing Error", error_message)
        self.update_status(f"Processing failed: {error_message}")
        self.update_action_states(processing=False)
        # Display original image if processing failed partway through visualization
        if self.uploaded_image and self.pixmap_item is None:
            self.display_image_on_scene(self.uploaded_image)

    @Slot()
    def _on_thread_finished(self):
         """Cleans up thread-related variables when the QThread finishes."""
         logger.debug("Processing QThread finished signal received.")
         self.processing_thread = None
         self.processing_worker = None
         self.update_action_states(processing=False) # Ensure UI is updated


    def draw_grain_highlights(self):
        """
        Draws interactive polygons (outlines only) and text labels for each valid grain
        on top of the current scene pixmap. Designed for a contours-only background.
        """
        if self.df is None or self.annotations is None or len(self.df) != len(self.annotations):
            logger.warning("Mismatch between DataFrame and annotations, cannot draw highlights.")
            return
        if self.pixmap_item is None:
            logger.warning("No base image pixmap item to draw highlights on.")
            return

        logger.info(f"Drawing interactive highlights for {len(self.df)} grains.")
        # Clear only previous highlights and text, keep the base pixmap
        self.clear_scene_items(clear_pixmap=False)

        # --- Scaling Logic (assumes uploaded_image and pixmap_item exist) ---
        try:
            display_width = self.pixmap_item.pixmap().width()
            display_height = self.pixmap_item.pixmap().height()
            original_width = self.uploaded_image.width
            original_height = self.uploaded_image.height
            if display_width == 0 or original_width == 0:
                logger.error("Cannot draw highlights: Image dimensions are zero.")
                return
            scale_x = display_width / original_width
            scale_y = display_height / original_height
        except Exception as e:
            logger.error(f"Error getting dimensions for highlight scaling: {e}")
            return


        # --- Define Pens for INTERACTIVE Highlights ---
        # Visually distinct from the background contours (e.g., if background is yellow)
        default_pen = QPen(QColor(0, 255, 255, 200), 1.5) # Cyan, slightly thick, semi-transparent
        default_pen.setCosmetic(True) # Keep thickness constant on zoom
        selected_pen = QPen(QColor(255, 0, 0, 255), 2.5) # Bright Red, thicker, opaque
        selected_pen.setCosmetic(True)

        # --- Font Settings ---
        base_font_size = 10
        min_font_size = 6
        max_font_size = 18

        # --- Reset Tracking Dictionary ---
        self.grain_items = {}

        # --- Iterate and Draw ---
        for df_index, mask_tensor in zip(self.df.index, self.annotations):
            try:
                # Get contour from original resolution mask
                mask_np = mask_tensor.cpu().numpy().astype(np.uint8)
                contours, _ = cv2.findContours(mask_np, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                if not contours: continue
                contour = max(contours, key=cv2.contourArea)

                # Scale contour points to *display* coordinates
                scaled_contour = contour.astype(np.float32)
                scaled_contour[:, :, 0] *= scale_x
                scaled_contour[:, :, 1] *= scale_y
                scaled_contour = scaled_contour.astype(np.int32) # Convert to int for QPolygon

                # Create QPolygonF for potentially smoother rendering
                qpolygon = QtGui.QPolygonF()
                # Squeeze contour to remove single dimension if present
                contour_points = scaled_contour.squeeze()
                # Handle cases where squeeze might result in a single point (invalid contour)
                if contour_points.ndim == 1 and contour_points.shape[0] >= 2:
                    # If it's flattened somehow, try reshaping if possible, else skip
                    # This case shouldn't happen often with valid contours
                    logger.warning(f"Unexpected contour shape for index {df_index}, skipping highlight.")
                    continue
                elif contour_points.ndim == 2:
                    for point in contour_points:
                        if len(point) == 2:
                            qpolygon.append(QPointF(point[0], point[1]))
                        else:
                            logger.warning(f"Invalid point format in contour {df_index}, skipping point.")
                else:
                    # Handle single point contour or other unexpected shapes
                    logger.warning(f"Skipping highlight for contour {df_index} due to invalid shape: {contour_points.shape}")
                    continue

                if qpolygon.count() < 3: # Need at least 3 points for a polygon
                    logger.warning(f"Skipping highlight for contour {df_index}, not enough points ({qpolygon.count()}).")
                    continue

                # --- Create Polygon Item ---
                polygon_item = QGraphicsPolygonItem(qpolygon)
                polygon_item.setFlag(QGraphicsPolygonItem.ItemIsSelectable)
                polygon_item.setAcceptHoverEvents(True)
                polygon_item.setData(Qt.UserRole, df_index) # Store DataFrame index

                # --- Style: Use distinct default pen, NO BRUSH ---
                polygon_item.setPen(default_pen) # Default interactive is Cyan
                polygon_item.setBrush(Qt.NoBrush) # IMPORTANT: No fill for highlights
                polygon_item.setZValue(0) # Default Z order

                self.scene.addItem(polygon_item)

                # --- Create Text Item (Grain ID) ---
                # Calculate centroid on *scaled* contour for positioning
                M = cv2.moments(scaled_contour)
                cX, cY = 0, 0
                if M["m00"] != 0:
                    cX = int(M["m10"] / M["m00"])
                    cY = int(M["m01"] / M["m00"])
                elif not qpolygon.isEmpty(): # Fallback to first point
                    pt = qpolygon.first()
                    cX, cY = int(pt.x()), int(pt.y())

                text_item = QGraphicsTextItem(str(df_index)) # Use df_index as label
                text_item.setDefaultTextColor(QColor(255, 100, 100, 220)) # Default: Slightly less intense red

                # --- Font Size (Example based on bounding box diagonal) ---
                bbox = qpolygon.boundingRect()
                diag = math.sqrt(bbox.width()**2 + bbox.height()**2)
                # Adjust the divisor (50) based on typical grain size in pixels for good scaling
                font_size = np.clip(base_font_size * diag / 50, min_font_size, max_font_size)

                font = QFont("Arial", int(font_size))
                text_item.setFont(font)

                # Center text item over centroid
                text_rect = text_item.boundingRect()
                text_item.setPos(cX - text_rect.width() / 2, cY - text_rect.height() / 2)
                # Make text scale with view zoom or stay constant size?
                # False = scales with zoom; True = constant screen size
                text_item.setFlag(QGraphicsTextItem.ItemIgnoresTransformations, False)
                text_item.setData(Qt.UserRole, df_index) # Also store index here if needed
                text_item.setZValue(polygon_item.zValue() + 0.1) # Ensure text is slightly above polygon

                self.scene.addItem(text_item)

                # Store references
                self.grain_items[df_index] = {'poly': polygon_item, 'text': text_item}

            except Exception as e:
                logger.exception(f"Error drawing highlight for grain index {df_index}: {e}")
                continue

        logger.info(f"Finished drawing {len(self.grain_items)} interactive grain highlights.")
        # Update selection visuals based on current state after drawing all
        self.update_grain_visual_selection()



    @Slot(QPointF)
    def on_scene_clicked(self, scene_pos):
        """
        Handles clicks on the scene when in selection mode.
        Prioritizes selecting the smallest grain if multiple grains overlap
        at the click position.
        """
        if self.mode != 'selection': return
        if not self.grain_items: return # No items to select

        logger.debug(f"Scene clicked at: {scene_pos.x():.1f}, {scene_pos.y():.1f}")

        # Find items at the click position
        items_at_pos = self.scene.items(scene_pos) # Gets all items overlapping the point

        candidate_grains = [] # List to store (area, df_index, item) tuples

        # 1. Find all candidate polygons containing the click point
        for item in items_at_pos:
            # Check if it's one of our interactive polygon highlights
            if isinstance(item, QGraphicsPolygonItem):
                df_index = item.data(Qt.UserRole)
                # Ensure it has our user data and check containment precisely
                if df_index is not None and item.contains(scene_pos):
                    # Calculate area for sorting (use bounding box for speed)
                    area = item.polygon().boundingRect().width() * item.polygon().boundingRect().height()
                    # Could use item.polygon().area() for exact polygon area, but slower
                    candidate_grains.append((area, df_index, item))
                    logger.debug(f"  -> Candidate: Index {df_index}, Area ~{area:.1f}")

        # 2. Determine the best candidate (smallest area)
        clicked_grain_index = None
        if not candidate_grains:
            logger.debug("  -> No grain polygon contains the click point.")
            # Clicked outside any actual grain polygon shape
            pass # Handled later by multi-select logic
        elif len(candidate_grains) == 1:
            # Only one grain contains the point
            _, clicked_grain_index, _ = candidate_grains[0]
            logger.debug(f"  -> Single candidate selected: Index {clicked_grain_index}")
        else:
            # Multiple overlapping grains contain the point, find the smallest
            candidate_grains.sort(key=lambda x: x[0]) # Sort by area (ascending)
            smallest_area, clicked_grain_index, _ = candidate_grains[0]
            logger.debug(f"  -> Multiple candidates, selected smallest: Index {clicked_grain_index} (Area ~{smallest_area:.1f})")


        # 3. Handle selection logic based on modifiers (Ctrl for multi-select)
        modifiers = QApplication.keyboardModifiers()
        is_multi_select = modifiers == Qt.ControlModifier

        new_selection = set() # Holds the potentially new set of selected indices

        if clicked_grain_index is not None:
            # Clicked inside at least one grain
            if is_multi_select:
                # Toggle selection for the clicked grain in multi-select mode
                new_selection = self.selected_df_indices.copy()
                if clicked_grain_index in new_selection:
                    new_selection.remove(clicked_grain_index) # Toggle off
                else:
                    new_selection.add(clicked_grain_index) # Toggle on
            else:
                # Single selection mode:
                # If the clicked grain is not already the *only* selected item, select it.
                # If it *is* the only selected item, deselect it (toggle).
                if clicked_grain_index not in self.selected_df_indices or len(self.selected_df_indices) != 1:
                    new_selection = {clicked_grain_index} # Select only this one
                else:
                    new_selection = set() # Deselect if already single selected

        else:
            # Clicked outside any grain polygon shape
            if not is_multi_select:
                new_selection = set() # Clear selection on empty click (single select mode)
            else:
                # In multi-select mode, clicking empty space does nothing to the selection
                new_selection = self.selected_df_indices # Keep current selection


        # 4. Update internal selection state and UI (only if changed)
        if new_selection != self.selected_df_indices:
            self.set_selected_grains(new_selection)
        else:
            logger.debug("Selection unchanged.")

    def set_selected_grains(self, new_selected_indices):
        """Updates the internal selection set and refreshes UI highlights and table selection."""
        if not isinstance(new_selected_indices, set):
             logger.error("set_selected_grains expects a set.")
             return

        # Only update if the selection actually changed
        if new_selected_indices == self.selected_df_indices:
             logger.debug("Selection unchanged, skipping update.")
             return # Avoid redundant updates if selection is the same

        logger.debug(f"Setting selected grains to indices: {new_selected_indices}")
        self.selected_df_indices = new_selected_indices

        # Update visual highlights on the canvas
        self.update_grain_visual_selection()

        # Update selection in the TreeView
        self.update_treeview_selection()

        # --- ADD THIS LINE ---
        # Refresh the enabled/disabled state of buttons based on the new selection
        self.update_action_states()

    def update_grain_visual_selection(self):
        """
        Updates the appearance of interactive grain highlight items on the scene
        based on the current selection state (self.selected_df_indices).
        Uses NoBrush and distinct pen colors.
        """
        if not hasattr(self, 'grain_items') or not self.grain_items:
            # logger.debug("No grain highlight items to update.")
            return

        # --- Define Pens (match those in draw_grain_highlights) ---
        default_pen = QPen(QColor(0, 255, 255, 200), 1.5) # Cyan, semi-transparent
        default_pen.setCosmetic(True)
        selected_pen = QPen(QColor(255, 0, 0, 255), 2.5) # Bright Red, opaque
        selected_pen.setCosmetic(True)

        # --- Iterate through tracked items ---
        for df_index, items_dict in self.grain_items.items():
            poly_item = items_dict.get('poly')
            text_item = items_dict.get('text')

            # Should always exist if tracked correctly, but check anyway
            if not poly_item:
                continue

            is_selected = df_index in self.selected_df_indices

            # --- Apply Style ---
            poly_item.setBrush(Qt.NoBrush) # Ensure no brush fill

            if is_selected:
                poly_item.setPen(selected_pen) # Red when selected
                poly_item.setZValue(1) # Bring selected items visually to front
                if text_item:
                    text_item.setDefaultTextColor(QColor("white")) # White text when selected
                    text_item.setZValue(1.1) # Ensure text is above selected polygon
            else:
                poly_item.setPen(default_pen) # Cyan when not selected
                poly_item.setZValue(0) # Default Z order
                if text_item:
                    text_item.setDefaultTextColor(QColor(255, 100, 100, 220)) # Default text color
                    text_item.setZValue(0.1) # Ensure text is above non-selected polygon

        # Request a repaint of the scene to show changes
        # This is often necessary if ZValues or complex properties changed
        if hasattr(self, 'scene'):
            self.scene.update()



    @Slot(QItemSelection, QItemSelection)
    def on_treeview_selection_changed(self, selected, deselected):
        """Handles selection changes in the result tree view."""
        # Prevent infinite loops by blocking signals temporarily if needed,
        # but often it's okay if set_selected_grains handles the logic correctly.
        logger.debug("Tree view selection changed.")

        current_selection_model = self.result_tree.selectionModel()
        selected_indices_in_view = current_selection_model.selectedRows() # QModelIndex list

        new_selection = set()
        for model_index in selected_indices_in_view:
             # Get the original DataFrame index stored in the item's data
             item = self.tree_model.itemFromIndex(model_index)
             if item:
                  df_index = item.data(Qt.UserRole)
                  if df_index is not None:
                       new_selection.add(df_index)

        # Update the internal selection state and canvas visuals
        # Check if the selection actually changed to avoid redundant updates
        if new_selection != self.selected_df_indices:
             self.set_selected_grains(new_selection)


    def update_treeview_selection(self):
         """Selects rows in the treeview corresponding to selected_df_indices."""
         if self.tree_model is None: return

         selection_model = self.result_tree.selectionModel()
         if not selection_model: return

         # Block signals from the selection model to prevent feedback loop
         selection_model.blockSignals(True)

         selection_model.clearSelection() # Clear existing selection first

         items_to_select = []
         # Iterate through the model to find items matching the selected df indices
         for row in range(self.tree_model.rowCount()):
              item = self.tree_model.item(row, 0) # Check first column item
              if item:
                   df_index = item.data(Qt.UserRole)
                   if df_index is not None and df_index in self.selected_df_indices:
                        model_index = self.tree_model.index(row, 0)
                        # Create a QItemSelection for the entire row
                        row_selection = QItemSelection(model_index, model_index.siblingAtColumn(self.tree_model.columnCount() - 1))
                        items_to_select.append(row_selection)

         # Apply the new selection
         for selection in items_to_select:
              selection_model.select(selection, QItemSelectionModel.Select | QItemSelectionModel.Rows)

         # Ensure the first selected item is visible (scroll to it)
         if items_to_select:
              first_index = items_to_select[0].indexes()[0]
              self.result_tree.scrollTo(first_index, QAbstractItemView.PositionAtCenter)

         # Unblock signals
         selection_model.blockSignals(False)
         logger.debug(f"Updated treeview selection for indices: {self.selected_df_indices}")


    def delete_selected_grains(self):
        """
        Deletes selected grains from data (df, annotations) and visualization
        by regenerating the faster contours-only background image and redrawing highlights.
        """
        if not self.selected_df_indices:
            QMessageBox.information(self, "No Selection", "No grains are selected for deletion.")
            return
        # Need original image to regenerate vis
        if self.df is None or self.annotations is None or self.uploaded_image is None:
            logger.error("Cannot delete grains, data structures or original image missing.")
            self._show_error_message("Deletion Error", "Cannot delete grains: Data or original image missing.")
            return

        num_to_delete = len(self.selected_df_indices)
        reply = QMessageBox.question(
            self, "Confirm Deletion",
            f"Are you sure you want to delete {num_to_delete} selected grain(s)?\nVisualization will be updated.",
            QMessageBox.Yes | QMessageBox.Cancel, QMessageBox.Cancel
        )
        if reply == QMessageBox.Cancel:
            return

        logger.info(f"Deleting {num_to_delete} selected grains (regenerating contours) with indices: {self.selected_df_indices}")
        QApplication.setOverrideCursor(Qt.WaitCursor) # Indicate busyness

        try:
            # --- Find valid indices in current DataFrame ---
            valid_indices_to_delete = self.selected_df_indices.intersection(set(self.df.index))
            if len(valid_indices_to_delete) != len(self.selected_df_indices):
                logger.warning(f"Some selected indices were not found in the DataFrame ({len(self.selected_df_indices) - len(valid_indices_to_delete)} missing). Deleting only found indices.")
                num_to_delete = len(valid_indices_to_delete) # Update count

            if not valid_indices_to_delete:
                logger.info("No valid selected grains found in the current results to delete.")
                self.selected_df_indices = set() # Clear selection
                self.update_action_states()
                # Release cursor here as we are returning early
                QApplication.restoreOverrideCursor()
                return

            # --- Map df indices to current annotation list indices ---
            df_keep_mask = ~self.df.index.isin(valid_indices_to_delete)
            indices_to_keep_in_annotations = [
                i for i, df_idx in enumerate(self.df.index) if df_idx not in valid_indices_to_delete
            ]
            # Sanity check
            if len(indices_to_keep_in_annotations) != df_keep_mask.sum():
                # This indicates a critical state mismatch, raise error
                raise RuntimeError("Inconsistent state mapping DataFrame indices to annotation indices during deletion.")

            # --- Filter DataFrame ---
            self.df = self.df[df_keep_mask]

            # --- Filter Annotations ---
            if isinstance(self.annotations, torch.Tensor):
                # Ensure indices are suitable for tensor slicing (e.g., a list or tensor of ints)
                if indices_to_keep_in_annotations:
                    keep_indices_tensor = torch.tensor(indices_to_keep_in_annotations, dtype=torch.long, device=self.annotations.device)
                    self.annotations = self.annotations[keep_indices_tensor]
                else: # Handle case where all annotations are deleted
                    self.annotations = torch.empty((0,) + self.annotations.shape[1:], dtype=self.annotations.dtype, device=self.annotations.device)

            elif isinstance(self.annotations, list):
                self.annotations = [self.annotations[i] for i in indices_to_keep_in_annotations]
            else:
                # Handle unexpected annotation type
                logger.error(f"Unsupported annotation type during deletion: {type(self.annotations)}")
                raise TypeError("Annotations are not in an expected format (list or tensor).")

            # --- Clear Selection State ---
            self.selected_df_indices = set()

            # --- Update Status Bar ---
            self.update_status(f"Deleted {num_to_delete} grains. Regenerating contour visualization...")
            QApplication.processEvents() # Force UI update to show status

            # --- *** Regenerate CONTOURS-ONLY Visualization *** ---
            logger.debug("Regenerating contour-only visualization...")
            # Get current contour thickness from UI or use default
            contour_thickness = self.contour_thickness.value() if hasattr(self, 'contour_thickness') else DEFAULT_CONTOUR_THICKNESS
            # Define the color for background contours (e.g., Yellow)
            background_contour_color = (255, 255, 0)

            vis_regen_success = False
            try:
                # This should be reasonably fast as it only draws lines
                self.processed_image_vis = create_segmented_visualization(
                    self.uploaded_image, # Base image
                    self.annotations,    # Use the *filtered* annotations
                    contour_thickness=contour_thickness,
                    contour_color=background_contour_color
                )
                logger.debug("Contour-only visualization regenerated successfully.")
                vis_regen_success = True
            except Exception as vis_e:
                # Log the error, maybe fallback to base image
                logger.exception("Error regenerating contour-only visualization after deletion:")
                self.processed_image_vis = self.uploaded_image # Fallback to original if regen fails
                self._show_error_message("Visualization Error", f"Could not update visualization after deletion: {vis_e}\nDisplaying original image.")
                # Continue without the updated visualization if fallback is acceptable

            # --- Update Display with New Visualization (or fallback) ---
            # display_image_on_scene handles putting the pixmap into the view
            self.display_image_on_scene(self.processed_image_vis)

            # --- Redraw Interactive Highlights ---
            # This needs to run AFTER display_image_on_scene has potentially reset the scene
            # and AFTER self.df and self.annotations are filtered.
            self.draw_grain_highlights() # This redraws based on the filtered self.df / self.annotations

            # Repopulate the results table
            self.populate_result_tree()
            # Update enabled/disabled state of buttons/actions
            self.update_action_states()

            # Final status update
            status_msg = f"Deleted {num_to_delete} grains."
            if vis_regen_success:
                status_msg += " Visualization updated."
            else:
                status_msg += " Visualization update failed."
            self.update_status(status_msg)
            logger.info("Deletion complete.")

        except Exception as e:
            # Catch errors during data filtering or UI updates
            logger.exception("Error during grain deletion process:")
            self._show_error_message("Deletion Error", f"An error occurred while deleting grains: {e}")
            self.update_status("Error during deletion.")
            # Consider resetting state or advising user to reload if state might be corrupt
        finally:
            # ALWAYS restore the cursor
            QApplication.restoreOverrideCursor()


    def update_action_states(self, processing=None):
            """Enable/disable actions and controls based on application state."""
            # Determine current state
            image_loaded = self.uploaded_image is not None
            results_exist = self.df is not None and not self.df.empty
            # Check if annotations exist using tensor-specific methods to avoid ambiguous boolean error
            annotations_exist = self.annotations is not None and (isinstance(self.annotations, list) and len(self.annotations) > 0 or
                                                              hasattr(self.annotations, 'numel') and self.annotations.numel() > 0)
            scale_is_set = self.current_scale_factor is not None
            model_is_loaded = self.model is not None

            if processing is None:
                processing = self.processing_thread is not None and self.processing_thread.isRunning()

            # --- Pre-calculate specific enable states ---
            can_process = image_loaded and scale_is_set and model_is_loaded and not processing
            can_recalculate = annotations_exist and scale_is_set and not processing
            something_selected = bool(self.selected_df_indices)
            can_delete = results_exist and something_selected and not processing

            # --- Enable/disable Menu & Toolbar Actions ---
            if hasattr(self, 'crop_action'): self.crop_action.setEnabled(image_loaded and not processing)
            if hasattr(self, 'save_results_action'): self.save_results_action.setEnabled(results_exist and not processing)
            if hasattr(self, 'save_view_action'): self.save_view_action.setEnabled(self.pixmap_item is not None and not processing)
            # Enable COCO export if annotations exist (even if df is empty after deletion/recalc?) - Yes, use annotations_exist
            if hasattr(self, 'export_coco_action'): self.export_coco_action.setEnabled(annotations_exist and not processing)
            if hasattr(self, 'plot_action'): self.plot_action.setEnabled(results_exist and not processing)
            if hasattr(self, 'save_scale_action'): self.save_scale_action.setEnabled(scale_is_set and not processing)
            if hasattr(self, 'load_scale_action'): self.load_scale_action.setEnabled(image_loaded and not processing)
            if hasattr(self, 'reset_scale_action'): self.reset_scale_action.setEnabled(scale_is_set and not processing)
            if hasattr(self, 'reset_action'): self.reset_action.setEnabled(not processing)

            # Zoom actions
            can_zoom = hasattr(self, 'view')
            if hasattr(self, 'zoom_in_action'): self.zoom_in_action.setEnabled(can_zoom)
            if hasattr(self, 'zoom_out_action'): self.zoom_out_action.setEnabled(can_zoom)
            if hasattr(self, 'reset_zoom_action'): self.reset_zoom_action.setEnabled(can_zoom)

            # --- Enable/disable Buttons and Controls (Main Panel) ---
            # Process Button
            if hasattr(self, 'process_button'):
                self.process_button.setEnabled(can_process)
                tooltip = "Start segmentation and analysis"
                if not image_loaded: tooltip += "\n(Requires loaded image)"
                if not scale_is_set: tooltip += "\n(Requires scale to be set)"
                if not model_is_loaded: tooltip += "\n(Requires model to be loaded)"
                if processing: tooltip = "Processing in progress..."
                self.process_button.setToolTip(tooltip)

            # Recalculate Button
            if hasattr(self, 'recalculate_button'):
                 self.recalculate_button.setEnabled(can_recalculate)
                 tooltip = "Recalculate parameters using current scale"
                 if not annotations_exist: tooltip += "\n(Requires existing segmentation results)"
                 if not scale_is_set: tooltip += "\n(Requires scale to be set)"
                 if processing: tooltip = "Cannot recalculate during processing"
                 self.recalculate_button.setToolTip(tooltip)

            # Delete Grains Button (in results panel)
            if hasattr(self, 'delete_grains_button'):
                self.delete_grains_button.setEnabled(can_delete)
                tooltip = "Remove selected grains from results"
                if not results_exist: tooltip += "\n(Requires analysis results)"
                if not something_selected: tooltip += "\n(Requires grain selection)"
                if processing: tooltip = "Cannot delete during processing"
                self.delete_grains_button.setToolTip(tooltip)

            # --- Disable parameter/mode controls during processing ---
            controls_to_disable_during_processing = [
                self.input_size, self.iou_threshold, self.conf_threshold, self.max_det,
                self.contour_thickness, self.real_world_length, self.manual_scale
            ]
            # Find the manual scale "Set" button
            set_scale_button = self.findChild(QPushButton, "setManualScaleButton")
            if set_scale_button: controls_to_disable_during_processing.append(set_scale_button)
            # Add mode radio buttons
            if hasattr(self, 'mode_buttons'):
                controls_to_disable_during_processing.extend(self.mode_buttons.values())
            # Add scale toolbar buttons (via their actions)
            if hasattr(self, 'save_scale_action'): controls_to_disable_during_processing.append(self.save_scale_action)
            if hasattr(self, 'load_scale_action'): controls_to_disable_during_processing.append(self.load_scale_action)
            if hasattr(self, 'reset_scale_action'): controls_to_disable_during_processing.append(self.reset_scale_action)
            # Add main action buttons from control panel
            if hasattr(self, 'process_button'): controls_to_disable_during_processing.append(self.process_button)
            if hasattr(self, 'recalculate_button'): controls_to_disable_during_processing.append(self.recalculate_button)
            # Add menu actions that should be disabled during processing
            if hasattr(self, 'upload_action'): controls_to_disable_during_processing.append(self.upload_action)
            if hasattr(self, 'crop_action'): controls_to_disable_during_processing.append(self.crop_action)
            if hasattr(self, 'reset_action'): controls_to_disable_during_processing.append(self.reset_action)
            # Add actions from other menus if necessary (e.g., save results, export, plot?) - Generally OK to leave these active, but disable if needed.


            for control in controls_to_disable_during_processing:
                if control and hasattr(control, 'setEnabled'):
                    # The desired state is *enabled* if NOT processing,
                    # UNLESS the control is one whose state depends on other factors (process, recalc, delete, scale save/reset)
                    desired_state = not processing

                    # Check specific conditions that override the basic 'not processing' rule
                    if control is self.process_button:
                        desired_state = can_process
                    elif control is self.recalculate_button:
                        desired_state = can_recalculate
                    elif control is self.save_scale_action or control is self.reset_scale_action:
                         desired_state = scale_is_set and not processing
                    elif control is self.load_scale_action:
                         desired_state = image_loaded and not processing
                    # Add others here if needed

                    # Only call setEnabled if the state needs changing
                    if control.isEnabled() != desired_state:
                        control.setEnabled(desired_state)
                elif control:
                    logger.warning(f"Control {control} lacks setEnabled method or is None.")


            logger.debug(
                f"Action states updated. "
                f"Processing: {processing}, "
                f"Model Loaded: {model_is_loaded}, "
                f"Image Loaded: {image_loaded}, "
                f"Annotations Exist: {annotations_exist}, "
                f"Scale Set: {scale_is_set}, "
                f"Results Exist: {results_exist}, "
                f"Selected Grains: {len(self.selected_df_indices)}"
            )


    @Slot() # Make it a slot if called via invokeMethod
    def _show_error_message(self, title, message):
        """Helper to show QMessageBox critical error."""
        QMessageBox.critical(self, title, message)

    @Slot() # Make it a slot
    def update_slider_labels(self):
        """Updates labels next to sliders."""
        # FastSAM parameters
        if hasattr(self, 'input_size') and hasattr(self, 'input_size_label'):
            # Input Size (integer)
            self.input_size_label.setText(f"{self.input_size.value()}")
        if hasattr(self, 'iou_threshold') and hasattr(self, 'iou_threshold_label'):
            # IOU Threshold (float 0-1)
            iou_val = self.iou_threshold.value() / 100.0
            self.iou_threshold_label.setText(f"{iou_val:.2f}")
        if hasattr(self, 'conf_threshold') and hasattr(self, 'conf_threshold_label'):
            # Confidence Threshold (float 0-1)
            conf_val = self.conf_threshold.value() / 100.0
            self.conf_threshold_label.setText(f"{conf_val:.2f}")
        if hasattr(self, 'contour_thickness') and hasattr(self, 'contour_thickness_label'):
            # Contour Thickness (integer)
            self.contour_thickness_label.setText(f"{self.contour_thickness.value()}")

        # MobileSAM parameters
        if hasattr(self, 'points_per_side') and hasattr(self, 'points_per_side_label'):
            # Points Per Side (integer)
            self.points_per_side_label.setText(str(self.points_per_side.value()))
        if hasattr(self, 'pred_iou_thresh') and hasattr(self, 'pred_iou_thresh_label'):
            # Pred IOU Threshold (float 0-1)
            pred_iou_val = self.pred_iou_thresh.value() / 100.0
            self.pred_iou_thresh_label.setText(f"{pred_iou_val:.2f}")
        if hasattr(self, 'stability_score_thresh') and hasattr(self, 'stability_score_thresh_label'):
            # Stability Score Threshold (float 0-1)
            stability_val = self.stability_score_thresh.value() / 100.0
            self.stability_score_thresh_label.setText(f"{stability_val:.2f}")
        if hasattr(self, 'box_nms_thresh') and hasattr(self, 'box_nms_thresh_label'):
            # Box NMS Threshold (float 0-1)
            box_nms_val = self.box_nms_thresh.value() / 100.0
            self.box_nms_thresh_label.setText(f"{box_nms_val:.2f}")



    # --- Plotting Methods ---

    def show_plot_selection(self):
        """Shows a dialog to select parameters for plotting."""
        if self.df is None or self.df.empty:
            self._show_error_message("No Data", "No analysis results available to plot.")
            return

        dialog = QDialog(self)
        dialog.setWindowTitle("Select Parameters to Plot")
        layout = QVBoxLayout(dialog)
        dialog.setStyleSheet(self.styleSheet()) # Apply theme

        plot_vars = {}
        grid_layout = QGridLayout()
        layout.addLayout(grid_layout)

        # Filter columns to only include numeric types suitable for plotting
        numeric_cols = self.df.select_dtypes(include=np.number).columns.tolist()
        # Exclude coordinate columns if desired
        cols_to_plot = [col for col in numeric_cols if col not in ['Center_X (px)', 'Center_Y (px)', 'Object']]


        num_cols = 3 # Arrange checkboxes in columns
        row, col = 0, 0
        for column in cols_to_plot:
            var = QCheckBox(column)
            var.setChecked(False)
            grid_layout.addWidget(var, row, col)
            plot_vars[column] = var
            col += 1
            if col >= num_cols:
                col = 0
                row += 1

        button_box = QtWidgets.QDialogButtonBox(QtWidgets.QDialogButtonBox.Ok | QtWidgets.QDialogButtonBox.Cancel)
        button_box.accepted.connect(lambda: self.generate_selected_plots(plot_vars, dialog))
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        dialog.setLayout(layout)
        dialog.exec()

    def generate_selected_plots(self, plot_vars, selection_dialog):
        """Gets selected parameters and calls display_plots."""
        selected_params = [
            param for param, checkbox in plot_vars.items() if checkbox.isChecked()
        ]
        selection_dialog.accept() # Close the selection dialog

        if not selected_params:
            QMessageBox.information(self, "No Selection", "No parameters were selected for plotting.")
            return

        logger.info(f"Generating plots for: {selected_params}")
        self.display_plots(selected_params)


    def display_plots(self, selected_params):
        """Displays plots in a new modeless window with tabs."""
        # Create a new dialog window for plots
        plot_dialog = QDialog(self)
        plot_dialog.setWindowTitle("Analysis Plots")
        plot_dialog.resize(900, 700) # Good default size
        plot_dialog.setStyleSheet(self.styleSheet()) # Apply theme

        layout = QVBoxLayout(plot_dialog)
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)

        # --- Add Plots ---
        # Create plots and add them as tabs
        # Keep track of figures and canvases created
        plot_widgets = [] # Store tuples: (widget, canvas, figure)

        try:
            # Distribution Plot
            dist_widget, canvas, fig = self.create_distribution_plot_widget(selected_params)
            if dist_widget:
                 tab_widget.addTab(dist_widget, "Histograms")
                 plot_widgets.append((dist_widget, canvas, fig))

            # Cumulative Frequency Plot
            cf_widget, canvas, fig = self.create_cumulative_frequency_plot_widget(selected_params)
            if cf_widget:
                 tab_widget.addTab(cf_widget, "Cumulative Frequency")
                 plot_widgets.append((cf_widget, canvas, fig))

            # Box Plot
            box_widget, canvas, fig = self.create_box_plot_widget(selected_params)
            if box_widget:
                 tab_widget.addTab(box_widget, "Box Plots")
                 plot_widgets.append((box_widget, canvas, fig))

            # Scatter Plot (Example: Area vs Elongation) - Add more options if needed
            scatter_widget, canvas, fig = self.create_scatter_plot_widget('Area (µm²)', 'Elongation')
            if scatter_widget:
                if 'Area (µm²)' in selected_params and 'Elongation' in selected_params:
                     tab_widget.addTab(scatter_widget, "Area vs Elongation")
                     plot_widgets.append((scatter_widget, canvas, fig))

        except Exception as e:
             logger.exception("Error creating plot widgets:")
             self._show_error_message("Plot Error", f"Failed to create plots: {e}")
             plot_dialog.close()
             return


        # --- Save Button ---
        button_layout = QHBoxLayout()
        save_button = QPushButton(QIcon(resource_path("icons/save.png")), " Save All Plots")
        save_button.clicked.connect(lambda: self.save_plots(plot_widgets))
        button_layout.addStretch()
        button_layout.addWidget(save_button)
        layout.addLayout(button_layout)

        plot_dialog.setLayout(layout)

        # --- Cleanup on Close ---
        # Connect the dialog's finished signal to a cleanup lambda
        plot_dialog.finished.connect(lambda result, pw=plot_widgets: self.cleanup_plot_resources(pw))

        self.plot_dialogs.append(plot_dialog) # Keep track of open dialogs
        plot_dialog.show() # Show modelessly

    def create_plot_widget_base(self):
         """Creates the basic structure (widget, layout, figure, canvas) for a plot tab."""
         widget = QWidget()
         layout = QVBoxLayout(widget)
         fig, ax = plt.subplots(figsize=(8, 6)) # Create figure and axes

         # Apply theme colors
         face_color = self.palette().color(QPalette.ColorRole.Window).name() # Match window background
         text_color = self.palette().color(QPalette.ColorRole.WindowText).name()
         fig.patch.set_facecolor(face_color)
         ax.set_facecolor(face_color)
         ax.tick_params(colors=text_color)
         ax.xaxis.label.set_color(text_color)
         ax.yaxis.label.set_color(text_color)
         ax.title.set_color(text_color)
         # Set spine colors
         for spine in ax.spines.values():
              spine.set_edgecolor(text_color)


         canvas = FigureCanvas(fig)
         layout.addWidget(canvas)
         widget.setLayout(layout)
         return widget, canvas, fig, ax


    def create_distribution_plot_widget(self, params):
        """Creates the distribution plot tab widget."""
        if self.df is None or self.df.empty: return None, None, None
        widget, canvas, fig, ax = self.create_plot_widget_base()

        num_plots = len(params)
        colors = plt.cm.viridis(np.linspace(0, 1, num_plots))

        max_bins = 30 # Limit bins for clarity

        for i, column in enumerate(params):
            try:
                numeric_data = pd.to_numeric(self.df[column], errors='coerce').dropna()
                if not numeric_data.empty:
                     # Calculate reasonable number of bins (e.g., Freedman-Diaconis or Sturges)
                     iqr = numeric_data.quantile(0.75) - numeric_data.quantile(0.25)
                     bin_width = 2 * iqr / (len(numeric_data) ** (1/3)) if iqr > 0 else 0
                     num_bins = int(np.ceil((numeric_data.max() - numeric_data.min()) / bin_width)) if bin_width > 0 else 10
                     num_bins = min(num_bins, max_bins) # Cap the number of bins
                     if num_bins <= 1: num_bins = 10 # Fallback

                     ax.hist(numeric_data, bins=num_bins, alpha=0.7, label=column, color=colors[i])
                else:
                     logger.warning(f"No numeric data for histogram: {column}")
            except Exception as e:
                logger.error(f"Error plotting histogram for {column}: {e}")

        ax.set_xlabel('Value')
        ax.set_ylabel('Frequency')
        ax.set_title('Parameter Distributions')
        if num_plots <= 10: # Avoid overly crowded legends
            ax.legend()
        else:
             ax.legend(ncol=2, fontsize='small') # Multi-column legend for many params
        fig.tight_layout() # Adjust layout
        canvas.draw()
        return widget, canvas, fig

    def create_cumulative_frequency_plot_widget(self, params):
        """Creates the cumulative frequency plot tab widget."""
        if self.df is None or self.df.empty: return None, None, None
        widget, canvas, fig, ax = self.create_plot_widget_base()

        num_plots = len(params)
        colors = plt.cm.viridis(np.linspace(0, 1, num_plots))

        for i, column in enumerate(params):
            try:
                numeric_data = pd.to_numeric(self.df[column], errors='coerce').dropna()
                if not numeric_data.empty:
                    sorted_data = np.sort(numeric_data)
                    yvals = np.arange(1, len(sorted_data) + 1) / float(len(sorted_data)) # Start from 1/N
                    ax.plot(sorted_data, yvals, label=column, color=colors[i], marker='.', markersize=3, linestyle='-')
                else:
                     logger.warning(f"No numeric data for cumulative plot: {column}")
            except Exception as e:
                logger.error(f"Error plotting cumulative frequency for {column}: {e}")

        ax.set_xlabel('Value')
        ax.set_ylabel('Cumulative Frequency')
        ax.set_title('Cumulative Frequency Distributions')
        ax.grid(True, linestyle=':', alpha=0.6)
        if num_plots <= 10:
             ax.legend()
        else:
             ax.legend(ncol=2, fontsize='small')
        fig.tight_layout()
        canvas.draw()
        return widget, canvas, fig

    def create_box_plot_widget(self, params):
        """Creates box plots for selected parameters."""
        if self.df is None or self.df.empty: return None, None, None
        widget, canvas, fig, ax = self.create_plot_widget_base()

        data_to_plot = []
        labels = []
        for column in params:
            try:
                numeric_data = pd.to_numeric(self.df[column], errors='coerce').dropna()
                if not numeric_data.empty:
                    data_to_plot.append(numeric_data)
                    labels.append(column)
                else:
                    logger.warning(f"No numeric data for box plot: {column}")
            except Exception as e:
                logger.error(f"Error preparing data for box plot {column}: {e}")

        if not data_to_plot:
             return None, None, None # No data to plot

        boxplot = ax.boxplot(data_to_plot, labels=labels, patch_artist=True, vert=True, showfliers=True) # Show outliers

        # Apply theme colors to boxes
        num_boxes = len(data_to_plot)
        colors = plt.cm.viridis(np.linspace(0, 1, num_boxes))
        text_color = self.palette().color(QPalette.ColorRole.WindowText).name()

        for patch, color in zip(boxplot['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)
        for median in boxplot['medians']:
            median.set_color('red') # Make medians stand out
            median.set_linewidth(1.5)
        for whisker in boxplot['whiskers']:
            whisker.set_color(text_color)
        for cap in boxplot['caps']:
             cap.set_color(text_color)
        for flier in boxplot['fliers']:
             flier.set_markerfacecolor(text_color)
             flier.set_markeredgecolor(text_color)
             flier.set_alpha(0.5)


        ax.set_ylabel('Value')
        ax.set_title('Parameter Box Plots')
        ax.yaxis.grid(True, linestyle=':', alpha=0.6)
        plt.xticks(rotation=45, ha='right') # Rotate labels if many params
        fig.tight_layout()
        canvas.draw()
        return widget, canvas, fig


    def create_scatter_plot_widget(self, param_x, param_y):
        """Creates a scatter plot for two parameters."""
        if self.df is None or self.df.empty or param_x not in self.df.columns or param_y not in self.df.columns:
            return None, None, None
        widget, canvas, fig, ax = self.create_plot_widget_base()

        try:
            data_x = pd.to_numeric(self.df[param_x], errors='coerce')
            data_y = pd.to_numeric(self.df[param_y], errors='coerce')
            valid_mask = data_x.notna() & data_y.notna()
            data_x = data_x[valid_mask]
            data_y = data_y[valid_mask]

            if data_x.empty or data_y.empty:
                 logger.warning(f"No valid numeric data pair for scatter plot: {param_x} vs {param_y}")
                 return None, None, None

            # Optional: Color points by a third parameter (e.g., Area)
            color_param = 'Area (µm²)'
            colors = None
            if color_param in self.df.columns:
                 color_data = pd.to_numeric(self.df.loc[valid_mask, color_param], errors='coerce').fillna(0)
                 colors = plt.cm.viridis( (color_data - color_data.min()) / (color_data.max() - color_data.min() + 1e-9) ) # Normalize

            ax.scatter(data_x, data_y, alpha=0.6, s=15, c=colors) # Adjust size 's'

            ax.set_xlabel(param_x)
            ax.set_ylabel(param_y)
            ax.set_title(f'Scatter Plot: {param_y} vs {param_x}')
            ax.grid(True, linestyle=':', alpha=0.6)

            # Add colorbar if colors are used
            # if colors is not None:
            #      cbar = fig.colorbar(plt.cm.ScalarMappable(cmap='viridis'), ax=ax)
            #      cbar.set_label(color_param)


            fig.tight_layout()
            canvas.draw()
            return widget, canvas, fig

        except Exception as e:
            logger.error(f"Error creating scatter plot for {param_x} vs {param_y}: {e}")
            return None, None, None


    def save_plots(self, plot_widgets):
        """Saves all currently displayed plots."""
        if not plot_widgets:
            QMessageBox.warning(self, "No Plots", "There are no plots to save.")
            return

        directory = QFileDialog.getExistingDirectory(
            self, "Select Directory to Save Plots", self.last_save_dir
        )
        if not directory:
            return # User cancelled

        self.last_save_dir = directory # Remember directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = os.path.splitext(self.image_filename or "plot")[0]
        saved_files = []
        errors = []

        for i, (widget, canvas, fig) in enumerate(plot_widgets):
            try:
                 # Try to get tab title, otherwise use generic name
                 tab_widget = widget.parentWidget().parentWidget() # Assumes structure TabWidget -> StackedWidget -> PlotWidget
                 plot_title = "Plot"
                 if isinstance(tab_widget, QTabWidget):
                     idx = tab_widget.indexOf(widget.parentWidget())
                     if idx != -1:
                          plot_title = tab_widget.tabText(idx).replace(" ", "_")

                 filename = f"{base_filename}_{plot_title}_{timestamp}.png"
                 file_path = os.path.join(directory, filename)

                 # Use figure's facecolor when saving
                 fig.savefig(file_path, dpi=300, bbox_inches='tight', facecolor=fig.get_facecolor())
                 saved_files.append(filename)
                 logger.info(f"Saved plot: {file_path}")

            except Exception as e:
                error_msg = f"Failed to save plot {i+1}: {e}"
                logger.error(error_msg)
                errors.append(error_msg)

        if saved_files:
            QMessageBox.information(
                self, "Plots Saved",
                f"{len(saved_files)} plot(s) saved successfully to:\n{directory}" +
                (f"\n\nErrors encountered:\n" + "\n".join(errors) if errors else "")
            )
            # Optionally open directory
            try:
                if sys.platform == 'win32':
                    os.startfile(directory)
                elif sys.platform == 'darwin':
                    os.system(f'open "{directory}"')
                else: # Linux
                    os.system(f'xdg-open "{directory}"')
            except Exception as e:
                 logger.warning(f"Could not open directory automatically: {e}")
        elif errors:
            self._show_error_message("Save Error", "Failed to save any plots.\n" + "\n".join(errors))
        else:
            QMessageBox.warning(self, "Save Error", "Unknown error, no plots were saved.")


    def cleanup_plot_resources(self, plot_widgets):
        """Closes matplotlib figures associated with the closed plot dialog."""
        logger.debug(f"Cleaning up resources for {len(plot_widgets)} plots.")
        for widget, canvas, fig in plot_widgets:
            try:
                plt.close(fig) # Close the matplotlib figure
                # Qt widgets (widget, canvas) should be garbage collected
                # as the dialog closes, but explicit deletion can be added if needed.
                # canvas.deleteLater()
                # widget.deleteLater()
            except Exception as e:
                logger.error(f"Error closing plot figure: {e}")
        # Remove dialog from tracking list
        # This requires identifying which dialog finished - need to pass dialog ref


    def update_plot_themes(self):
         """Attempts to update the theme of existing plots."""
         # This is difficult as matplotlib styles are set at creation time.
         # The best approach is usually to close and recreate plots on theme change.
         # For now, just log that theme changed.
         if self.plot_dialogs: # If any plot dialogs are open
              logger.warning("Theme changed. For plots to fully reflect the new theme, please close and regenerate them.")
              # Optionally, force close existing plot dialogs:
              # for dialog in self.plot_dialogs[:]: # Iterate copy
              #      dialog.close()
              # self.plot_dialogs.clear()


    # --- Saving Methods ---

    def save_results(self):
        """Saves the analysis results (DataFrame CSV and annotated image PNG)."""
        if self.df is None or self.df.empty:
            self._show_error_message("No Results", "No analysis results to save.")
            return
        if self.processed_image_vis is None:
             logger.warning("DataFrame exists, but processed visualization image is missing.")
             # Ask user if they want to save only CSV?
             reply = QMessageBox.question(self, "Missing Image",
                                          "The annotated image is not available.\nSave only the results data (CSV)?",
                                          QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes)
             if reply == QMessageBox.No:
                  return
             save_image = False
        else:
             save_image = True

        base_filename = os.path.splitext(self.image_filename or "analysis")[0]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Suggest filenames
        suggested_csv = f"{base_filename}_results_{timestamp}.csv"
        suggested_img = f"{base_filename}_segmented_{timestamp}.png"

        # Get directory using user-defined default or last used
        target_dir = self.default_save_dir or self.last_save_dir
        save_dir = QFileDialog.getExistingDirectory(
            self, "Select Directory to Save Results", target_dir
        )
        if not save_dir:
            return # User cancelled

        self.last_save_dir = save_dir # Update last used directory

        saved_files = []
        errors = []

        try:
            # --- Save DataFrame to CSV ---
            df_file_path = os.path.join(save_dir, suggested_csv)
            # Use locale-aware decimal separator
            decimal_sep = locale.localeconv()['decimal_point']
            self.df.to_csv(df_file_path, index=False, decimal=decimal_sep)
            saved_files.append(f"Data: {suggested_csv}")
            logger.info(f"Results saved to: {df_file_path}")

            # --- Save Segmented Image ---
            if save_image and self.processed_image_vis:
                img_file_path = os.path.join(save_dir, suggested_img)
                self.processed_image_vis.save(img_file_path, "PNG")
                saved_files.append(f"Image: {suggested_img}")
                logger.info(f"Segmented image saved to: {img_file_path}")

        except Exception as e:
            logger.exception("Failed to save results.")
            errors.append(f"Error saving results: {e}")

        # --- Report Outcome ---
        if saved_files:
            QMessageBox.information(
                self, "Save Successful",
                f"Results saved successfully in:\n{save_dir}\n\n" + "\n".join(saved_files) +
                (f"\n\nErrors encountered:\n" + "\n".join(errors) if errors else "")
            )
        elif errors:
             self._show_error_message("Save Error", "Failed to save results.\n" + "\n".join(errors))
        else:
             self._show_error_message("Save Error", "Unknown error, failed to save results.")


    def save_current_view(self):
        """Saves the current QGraphicsView content as an image file."""
        if not self.scene or not self.scene.items():
            self._show_error_message("Nothing to Save", "The image view is empty.")
            return

        base_filename = os.path.splitext(self.image_filename or "view")[0]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        suggested_filename = f"{base_filename}_view_{timestamp}.png"

        target_dir = self.default_save_dir or self.last_save_dir
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Current View As", os.path.join(target_dir, suggested_filename),
            "PNG Image (*.png);;JPEG Image (*.jpg *.jpeg);;All Files (*)"
        )

        if not file_path:
            return # User cancelled

        self.last_save_dir = os.path.dirname(file_path) # Update last used

        try:
            # Get the bounding rectangle of all items in the scene
            rect = self.scene.itemsBoundingRect()
            # Create a QImage to render onto
            image = QImage(rect.size().toSize(), QImage.Format_ARGB32_Premultiplied) # Use ARGB for transparency
            image.fill(Qt.transparent) # Start with transparent background

            painter = QPainter(image)
            painter.setRenderHint(QPainter.Antialiasing)
            painter.setRenderHint(QPainter.SmoothPixmapTransform)
            # Render the scene onto the image, specifying the source rectangle
            self.scene.render(painter, QRectF(image.rect()), rect)
            painter.end()

            # Save the QImage
            if not image.save(file_path):
                 raise IOError(f"Failed to save image to {file_path}. Check permissions or path.")

            QMessageBox.information(self, "View Saved", f"Current view saved successfully to:\n{file_path}")
            logger.info(f"Current view saved to: {file_path}")

        except Exception as e:
            logger.exception("Failed to save current view.")
            self._show_error_message("Save Error", f"Failed to save current view: {e}")

    def save_scale_factor(self):
        """Saves the current scale factor settings to a JSON file."""
        if self.current_scale_factor is None:
            self._show_error_message("No Scale Set", "No scale factor is currently set to save.")
            return

        target_dir = self.default_save_dir or self.last_save_dir
        suggested_filename = f"{os.path.splitext(self.image_filename or 'scale')[0]}_scale.json"

        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Scale Factor", os.path.join(target_dir, suggested_filename),
            "JSON files (*.json)"
        )
        if not file_path: return

        self.last_save_dir = os.path.dirname(file_path)

        scale_data = {
            'scale_factor_um_per_pixel': self.current_scale_factor,
            'real_world_length_um': float(self.real_world_length.text().replace(locale.localeconv()['decimal_point'], '.')) if self.real_world_length.text() else None,
            'original_image_pixel_length': self.original_pixel_length, # Length on original image
            'source_image': self.image_file_path,
            'timestamp': datetime.now().isoformat()
        }

        try:
            with open(file_path, 'w') as f:
                json.dump(scale_data, f, indent=4)
            self.update_status(f"Scale factor saved to {os.path.basename(file_path)}")
            logger.info(f"Scale factor saved to {file_path}")
            QMessageBox.information(self, "Scale Saved", f"Scale factor settings saved to:\n{file_path}")
        except Exception as e:
            logger.exception(f"Failed to save scale factor to {file_path}")
            self._show_error_message("Save Error", f"Failed to save scale factor: {e}")

    def load_scale_factor(self):
        """Loads scale factor settings from a JSON file."""
        if self.uploaded_image is None:
             self._show_error_message("Load Image First", "Please load an image before loading a scale factor.")
             return

        target_dir = self.default_save_dir or self.last_save_dir
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Load Scale Factor", target_dir, "JSON files (*.json)"
        )
        if not file_path: return

        self.last_save_dir = os.path.dirname(file_path)

        try:
            with open(file_path, 'r') as f:
                scale_data = json.load(f)

            loaded_scale = scale_data.get('scale_factor_um_per_pixel')
            if loaded_scale is None or loaded_scale <= 0:
                raise ValueError("Invalid 'scale_factor_um_per_pixel' found in file.")

            self.current_scale_factor = loaded_scale
            # Optionally update UI fields based on loaded data
            rwl = scale_data.get('real_world_length_um')
            opl = scale_data.get('original_image_pixel_length')
            self.original_pixel_length = opl # Store loaded pixel length

            # Update UI labels
            self.scale_factor_label.setText(f"<b>Current Scale: {self.current_scale_factor:.4f} µm/pixel</b>")
            if rwl: self.real_world_length.setText(locale.format_string("%.2f", rwl))
            self.manual_scale.setText(locale.format_string("%.6f", self.current_scale_factor)) # Update manual field too
            self.update_scale_reminder(is_set=True)

            # Clear any existing scale line on the view
            self.view.clear_scale_line_item()

            self.update_action_states()
            self.update_status(f"Scale factor loaded from {os.path.basename(file_path)}")
            logger.info(f"Scale factor {self.current_scale_factor:.4f} loaded from {file_path}")
            QMessageBox.information(self, "Scale Loaded", f"Scale factor loaded: {self.current_scale_factor:.4f} µm/pixel")

        except FileNotFoundError:
            logger.error(f"Scale file not found: {file_path}")
            self._show_error_message("File Not Found", f"Scale factor file not found:\n{file_path}")
        except (json.JSONDecodeError, KeyError, ValueError) as e:
             logger.error(f"Failed to parse scale file {file_path}: {e}")
             self._show_error_message("Load Error", f"Failed to load or parse scale factor file.\nError: {e}")
             self.current_scale_factor = None # Reset if load failed
             self.update_scale_reminder()
        except Exception as e:
            logger.exception(f"Unexpected error loading scale factor from {file_path}")
            self._show_error_message("Load Error", f"An unexpected error occurred: {e}")
            self.current_scale_factor = None # Reset if load failed
            self.update_scale_reminder()


    def save_coco_annotations(self):
        """Saves the current annotations in COCO JSON format."""
        if self.df is None or self.annotations is None or self.df.empty or not self.annotations:
            self._show_error_message("No Annotations", "No annotations available to save.")
            return
        if len(self.df) != len(self.annotations):
             logger.warning(f"Mismatch between DataFrame ({len(self.df)}) and annotations ({len(self.annotations)}) count. COCO export might be incomplete.")
             # Decide how to handle: Proceed with available annotations, or abort?
             # Let's proceed but warn user.
             if QMessageBox.warning(self, "Data Mismatch",
                                    "The number of results in the table doesn't match the number of segmentation masks.\nCOCO export might be incomplete or inaccurate. Continue anyway?",
                                    QMessageBox.Yes | QMessageBox.Cancel, QMessageBox.Cancel) == QMessageBox.Cancel:
                  return


        base_filename = os.path.splitext(self.image_filename or "coco_export")[0]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        suggested_filename = f"{base_filename}_coco_{timestamp}.json"

        target_dir = self.default_save_dir or self.last_save_dir
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save COCO Annotations", os.path.join(target_dir, suggested_filename),
            "JSON files (*.json)"
        )
        if not file_path: return

        self.last_save_dir = os.path.dirname(file_path)

        try:
            # Ensure image dimensions are available
            if self.uploaded_image is None:
                 raise ValueError("Original image is not loaded, cannot determine dimensions for COCO.")
            img_width = self.uploaded_image.width
            img_height = self.uploaded_image.height

            coco_output = {
                "info": {
                    "description": "GrainSight Segmentation Export",
                    "version": "1.0",
                    "year": datetime.now().year,
                    "contributor": "GrainSight User",
                    "date_created": datetime.now().isoformat()
                },
                "licenses": [], # Add license info if applicable
                "images": [{
                    "id": 1, # Assuming single image context
                    "width": img_width,
                    "height": img_height,
                    "file_name": self.image_filename or "unknown_image.png", # Use relative filename
                    "license": 0,
                    "flickr_url": "",
                    "coco_url": "",
                    "date_captured": ""
                }],
                "annotations": [],
                "categories": [{ # Define category - adjust as needed
                    "id": 1,
                    "name": "grain", # Generic category
                    "supercategory": ""
                }]
            }

            annotation_id_counter = 1
            # Iterate through the ANNOTATIONS list (mask tensors)
            # Use the DataFrame index as the grain identifier if possible
            for df_index, mask_tensor in zip(self.df.index, self.annotations):
                 # Ensure mask is uint8 numpy array on CPU
                 binary_mask = mask_tensor.cpu().numpy().astype(np.uint8)

                 # Find contours - use CHAIN_APPROX_SIMPLE for polygons
                 contours, hierarchy = cv2.findContours(binary_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                 if not contours: continue

                 # Process each contour as a separate annotation or merge? COCO expects one segmentation per object instance.
                 # If a mask generates multiple external contours, it might represent fragments.
                 # Let's assume the largest contour represents the main object.
                 contour = max(contours, key=cv2.contourArea)

                 # Check contour validity
                 if len(contour) < 3: continue # Need at least 3 points for a polygon

                 # Convert contour to COCO segmentation format [x1,y1,x2,y2,...]
                 segmentation = contour.flatten().tolist()

                 # Calculate bounding box [x, y, width, height] from contour
                 x, y, w, h = cv2.boundingRect(contour)

                 # Calculate area
                 area = cv2.contourArea(contour)
                 if area <= 0: continue # Skip zero-area contours

                 coco_annotation = {
                      "id": annotation_id_counter, # Unique ID for each annotation object
                      "image_id": 1, # Corresponds to the image ID defined above
                      "category_id": 1, # Corresponds to the category ID ('grain')
                      "segmentation": [segmentation], # List of polygon lists
                      "area": float(area),
                      "bbox": [float(x), float(y), float(w), float(h)],
                      "iscrowd": 0 # 0 for polygon segmentation, 1 for RLE
                 }
                 coco_output["annotations"].append(coco_annotation)
                 annotation_id_counter += 1

            # Save to JSON file
            with open(file_path, 'w') as f:
                json.dump(coco_output, f, indent=2) # Use indent for readability

            QMessageBox.information(self, "Export Successful", f"COCO annotations saved to:\n{file_path}")
            logger.info(f"COCO annotations saved to {file_path}")

        except Exception as e:
            logger.exception("Failed to save COCO annotations.")
            self._show_error_message("Export Error", f"Failed to save COCO annotations: {e}")


    def set_default_save_directory(self):
        """Allows the user to set a persistent default directory for saving files."""
        directory = QFileDialog.getExistingDirectory(
             self, "Select Default Save Directory",
             self.default_save_dir or self.last_save_dir # Start from current default or last used
        )
        if directory:
            self.default_save_dir = directory
            self.update_status(f"Default save directory set to: {directory}")
            logger.info(f"Default save directory set to: {directory}")
            # Persist this setting? Could save it in the theme_config.json or a separate config file.
            # For simplicity now, it only lasts the session.


    # --- Other UI Methods ---

    def reset_view_zoom(self):
         """Resets the view's zoom and centers the image."""
         if self.pixmap_item:
              self.view.fitInView(self.pixmap_item, Qt.KeepAspectRatio)
         self.view.reset_view() # Ensure internal state is also reset


    def update_mode(self, mode):
        """Updates the current interaction mode."""
        self.mode = mode
        if hasattr(self, 'view'):
            self.view.set_mode(mode)
        # Ensure the correct radio button is checked (in case called programmatically)
        if mode in self.mode_buttons:
             self.mode_buttons[mode].setChecked(True)
        logger.debug(f"Interaction mode set to: {mode}")


    def show_about(self):
      """Displays the About dialog."""
      about_text = """
      <h2>GrainSight v1.0</h2>
      <p>Developed by Fares AZZAM</p>
      <p>This application performs grain segmentation and morphometric analysis using the FastSAM model (or other compatible YOLO models).</p>
      <p>For questions or support, contact: <a href="mailto:<EMAIL>"><EMAIL></a></p>
      <hr>
      <p><b>License:</b> Creative Commons Attribution 4.0 International (CC BY 4.0)</p>
      <p>This license allows re-distribution and re-use of this software on the condition that the creator is appropriately credited. For more details, visit:<br>
      <a href="https://creativecommons.org/licenses/by/4.0/">https://creativecommons.org/licenses/by/4.0/</a></p>
      <hr>
      <p><b>How to Cite:</b><br>
      Azzam, F., Blaise, T., & Brigaud, B. (2024).<br>
      <i>Automated petrographic image analysis by supervised and unsupervised machine learning methods.</i><br>
      Sedimentologika, 2(2).<br>
      <a href="https://doi.org/10.57035/journals/sdk.2024.e22.1594">https://doi.org/10.57035/journals/sdk.2024.e22.1594</a>
      </p>
      """
      QMessageBox.about(self, "About GrainSight", about_text)
      logger.info("Displayed About dialog.")

    # --- Cropping Methods ---

    def crop_image(self):
        """Opens a dialog to allow the user to crop the loaded image."""
        if self.uploaded_image is None:
            self._show_error_message("No Image", "Please load an image before cropping.")
            return
        if self.processing_thread and self.processing_thread.isRunning():
             QMessageBox.warning(self, "Busy", "Cannot crop while processing is running.")
             return

        # Create a modeless dialog for cropping
        crop_dialog = QDialog(self)
        crop_dialog.setWindowTitle("Crop Image - Draw Rectangle")
        crop_dialog.resize(800, 600) # Adjust size
        layout = QVBoxLayout(crop_dialog)

        # --- Cropping Graphics View ---
        crop_scene = QGraphicsScene()
        crop_view = QGraphicsView(crop_scene)
        crop_view.setRenderHint(QPainter.Antialiasing)
        crop_view.setRenderHint(QPainter.SmoothPixmapTransform)
        layout.addWidget(crop_view)

        # Display the image in the crop view
        crop_qimage = self.pil_image_to_qimage(self.uploaded_image)
        if crop_qimage is None:
            crop_dialog.close()
            return
        crop_pixmap_item = QGraphicsPixmapItem(QPixmap.fromImage(crop_qimage))
        crop_scene.addItem(crop_pixmap_item)
        crop_view.fitInView(crop_pixmap_item, Qt.KeepAspectRatio) # Fit initially

        # --- Cropping Rectangle ---
        crop_rect_item = QGraphicsRectItem()
        crop_rect_item.setPen(QPen(Qt.red, 2, Qt.DashLine))
        crop_rect_item.setBrush(QColor(255, 0, 0, 30)) # Semi-transparent fill
        crop_scene.addItem(crop_rect_item)
        crop_rect_item.hide() # Hide initially

        # --- Mouse Handling for Crop View ---
        self.crop_start_pos = None # Store start position in scene coords

        def crop_mouse_press(event):
            if event.button() == Qt.LeftButton:
                self.crop_start_pos = crop_view.mapToScene(event.pos())
                crop_rect_item.setRect(QRectF(self.crop_start_pos, self.crop_start_pos)) # Start rect
                crop_rect_item.show()

        def crop_mouse_move(event):
            if self.crop_start_pos:
                current_pos = crop_view.mapToScene(event.pos())
                crop_rect_item.setRect(QRectF(self.crop_start_pos, current_pos).normalized()) # Keep updating

        def crop_mouse_release(event):
            if event.button() == Qt.LeftButton and self.crop_start_pos:
                final_rect_scene = crop_rect_item.rect()
                self.crop_start_pos = None # Reset start pos
                # Ask confirmation before applying crop
                apply_crop(final_rect_scene, crop_dialog)

        crop_view.mousePressEvent = crop_mouse_press
        crop_view.mouseMoveEvent = crop_mouse_move
        crop_view.mouseReleaseEvent = crop_mouse_release

        # --- Apply Crop Logic ---
        def apply_crop(rect_scene, dialog_to_close):
            # Convert scene rectangle (based on displayed pixmap) to original image coordinates
            display_pixmap = crop_pixmap_item.pixmap()
            display_width = display_pixmap.width()
            display_height = display_pixmap.height()
            original_width = self.uploaded_image.width
            original_height = self.uploaded_image.height

            if display_width == 0 or display_height == 0: return

            ratio_x = original_width / display_width
            ratio_y = original_height / display_height

            # Ensure rect is within bounds of displayed pixmap first
            scene_bounds = crop_pixmap_item.boundingRect()
            clipped_rect = rect_scene.intersected(scene_bounds)

            # Convert clipped scene coordinates to original image pixel coordinates
            orig_x0 = max(0, int(clipped_rect.left() * ratio_x))
            orig_y0 = max(0, int(clipped_rect.top() * ratio_y))
            orig_x1 = min(original_width, int(clipped_rect.right() * ratio_x))
            orig_y1 = min(original_height, int(clipped_rect.bottom() * ratio_y))

            # Check if the resulting crop area is valid
            if (orig_x1 - orig_x0) < 1 or (orig_y1 - orig_y0) < 1:
                QMessageBox.warning(crop_dialog, "Crop Error", "Invalid crop area selected (too small or outside image).")
                crop_rect_item.hide() # Hide invalid rect
                return

            # Confirmation dialog
            reply = QMessageBox.question(crop_dialog, "Confirm Crop",
                                         "Apply this crop?\nThis will replace the current image.",
                                         QMessageBox.Apply | QMessageBox.Cancel, QMessageBox.Apply)

            if reply == QMessageBox.Apply:
                try:
                    # --- Apply Crop to Original Image ---
                    logger.info(f"Applying crop: Box({orig_x0}, {orig_y0}, {orig_x1}, {orig_y1})")
                    cropped_pil_image = self.uploaded_image.crop((orig_x0, orig_y0, orig_x1, orig_y1))

                    # --- Reset State and Update Display ---
                    self.reset_app_state(clear_image=True) # Full reset for cropped image
                    self.uploaded_image = cropped_pil_image
                    # Keep filename, maybe append "_cropped"?
                    if self.image_filename:
                        base, ext = os.path.splitext(self.image_filename)
                        self.image_filename = f"{base}_cropped{ext}"
                    # self.image_file_path remains the original for reference, or set to None?

                    self.display_image_on_scene(self.uploaded_image)
                    self.update_action_states()
                    self.update_scale_reminder() # Scale needs resetting after crop
                    self.update_status(f"Image cropped. Original filename: {os.path.basename(self.image_file_path or '')}")
                    self.setWindowTitle(f"GrainSight - {self.image_filename}")

                    dialog_to_close.accept() # Close the crop dialog

                except Exception as e:
                    logger.exception("Error applying crop:")
                    QMessageBox.critical(crop_dialog, "Crop Error", f"Failed to apply crop: {e}")
            else:
                # User cancelled, just hide the rectangle
                crop_rect_item.hide()


        # --- Dialog Buttons ---
        button_box = QtWidgets.QDialogButtonBox(QtWidgets.QDialogButtonBox.Cancel)
        # Apply happens on mouse release + confirmation
        button_box.rejected.connect(crop_dialog.reject)
        layout.addWidget(button_box)

        crop_dialog.setLayout(layout)
        crop_dialog.show() # Show modelessly


    # --- Patch Processing --- (Conceptual - requires more robust merging)

    def analyze_with_patches(self):
        """Initiates patch-based segmentation using a dialog for configuration."""
        logger.info("Patch segmentation requested.")
        if self.model is None: return self._show_error_message("Model Error", "Model not loaded.")
        if self.uploaded_image is None: return self._show_error_message("Warning", "Upload image first.")
        if self.current_scale_factor is None: return self._show_error_message("Scale Error", "Set image scale first.")
        if hasattr(self, 'patch_processing_thread') and self.patch_processing_thread and self.patch_processing_thread.isRunning():
            return QMessageBox.warning(self, "Busy", "Patch processing already running.")

        # Import the dialog function
        try:
            from src.grainsight_components.gui.dialogs import show_patch_config_dialog
        except ImportError:
            logger.error("Could not import patch configuration dialog.")
            QMessageBox.critical(self, "Import Error", "Could not load patch configuration dialog.")
            return

        # Get patch configuration from dialog
        patch_config = show_patch_config_dialog(
            self.uploaded_image.width,
            self.uploaded_image.height,
            self
        )

        if not patch_config:
            logger.info("Patch configuration canceled by user.")
            return

        # Get parameters based on the current model type
        try:
            if self.fastsam_radio.isChecked():
                model_type = 'fastsam'
                params = {
                    'model_type': 'fastsam',
                    'input_size': self.input_size.value(),
                    'iou': self.iou_threshold.value() / 100.0,  # Convert from slider value to float
                    'conf': self.conf_threshold.value() / 100.0,  # Convert from slider value to float
                    'max_det': int(self.max_det.text()),
                    'contour_thickness': self.contour_thickness.value(),
                    'use_intelligent_patch_merge': True,
                    'artifact_sensitivity': 0.5,
                    'duplicate_sensitivity': 0.7
                }
            elif self.mobilesam_radio.isChecked():
                model_type = 'mobilesam'
                params = {
                    'model_type': 'mobilesam',
                    'points_per_side': self.points_per_side.value(),
                    'points_per_batch': self.points_per_batch.value(),
                    'pred_iou_thresh': self.pred_iou_thresh.value() / 100.0,
                    'stability_score_thresh': self.stability_score_thresh.value() / 100.0,
                    'box_nms_thresh': self.box_nms_thresh.value() / 100.0,
                    'crop_n_layers': self.crop_n_layers.value(),
                    'crop_n_points_downscale_factor': self.crop_n_points_downscale_factor.value(),
                    'contour_thickness': self.contour_thickness.value(),
                    'use_intelligent_patch_merge': True,
                    'artifact_sensitivity': 0.5,
                    'duplicate_sensitivity': 0.7
                }
            else:
                raise ValueError("No segmentation model selected.")
        except Exception as e:
            self._show_error_message("Parameter Error", f"Invalid parameters: {e}")
            return

        # Determine target device based on model type
        if model_type == 'fastsam':
            target_device = torch.device("cpu") # Force CPU for FastSAM
            logger.info("FastSAM selected: Forcing CPU processing.")
        elif model_type == 'mobilesam':
            # Prioritize GPU if available, otherwise use CPU
            if torch.cuda.is_available():
                target_device = torch.device("cuda")
                logger.info("MobileSAM selected: Using CUDA device.")
            else:
                target_device = torch.device("cpu")
                logger.info("MobileSAM selected: CUDA not available, using CPU.")
        else:
            logger.error(f"Unknown model type '{model_type}' during device selection.")
            self._show_error_message("Internal Error", f"Unknown model type '{model_type}'.")
            return

        # Reset previous results
        self.annotations = None
        self.df = None
        self.processed_image_vis = None
        self.clear_scene_items(clear_pixmap=False) # Keep base image, clear overlays
        if hasattr(self, 'result_tree'):
            self.tree_model.removeRows(0, self.tree_model.rowCount())
        logger.debug("Cleared previous analysis results.")

        # Import the worker class
        try:
            from src.grainsight_components.gui.workers import PatchProcessingWorker
        except ImportError:
            logger.error("Could not import PatchProcessingWorker.")
            QMessageBox.critical(self, "Import Error", "Could not load patch processing worker.")
            return

        # Setup worker and thread
        self.patch_processing_worker = PatchProcessingWorker(
            self.uploaded_image, # Pass original PIL image
            self.model,          # Pass loaded model object
            target_device,       # Pass target device
            self.current_scale_factor,
            params,              # Pass collected parameters
            patch_config         # Pass patch configuration
        )
        self.patch_processing_thread = QThread()
        self.patch_processing_worker.moveToThread(self.patch_processing_thread)

        # Connections
        self.patch_processing_worker.finished.connect(self.on_processing_finished) # Reuse same handler
        self.patch_processing_worker.error.connect(self._show_error_message)
        self.patch_processing_worker.progress.connect(self.update_progress)
        self.patch_processing_worker.status.connect(self.update_status)
        self.patch_processing_thread.started.connect(self.patch_processing_worker.run)
        
        # Cleanup connections
        self.patch_processing_worker.finished.connect(self.patch_processing_thread.quit)
        self.patch_processing_worker.finished.connect(self.patch_processing_worker.deleteLater)
        self.patch_processing_thread.finished.connect(self.patch_processing_thread.deleteLater)
        self.patch_processing_thread.finished.connect(lambda: setattr(self, 'patch_processing_thread', None))

        # Start thread
        self.patch_processing_thread.start()
        self.update_action_states()
        self.update_status("Starting patch-based segmentation...")
        logger.info(f"Patch processing started with config: {patch_config}")
        
        # Show progress bar
        self.update_progress(5)


    def merge_patch_annotations(self, patch_results, image_size, iou_threshold=0.5):
        """
        Merges annotations from patches, handling overlaps using IoU.
        (This is a complex task and this is a simplified conceptual version)

        Args:
            patch_results: List of tuples [(mask_tensor, (offset_x, offset_y)), ...]
                           where mask_tensor is relative to the patch.
            image_size: Tuple (width, height) of the original image.
            iou_threshold: IoU threshold to consider detections as overlapping.

        Returns:
            List of merged annotation tensors relative to the full image.
        """
        logger.info(f"Merging annotations from {len(patch_results)} patches.")
        full_image_width, full_image_height = image_size
        all_masks_full = [] # Store masks relative to the full image
        all_bboxes_full = [] # Store bounding boxes [x1, y1, x2, y2]

        for mask_patch, (offset_x, offset_y) in patch_results:
            try:
                 mask_np = mask_patch.cpu().numpy().astype(np.uint8)
                 patch_h, patch_w = mask_np.shape

                 # Find contour on patch mask to get bbox relative to patch
                 contours, _ = cv2.findContours(mask_np, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                 if not contours: continue
                 contour = max(contours, key=cv2.contourArea)
                 x, y, w, h = cv2.boundingRect(contour)

                 # Calculate bbox relative to full image
                 x1_full = offset_x + x
                 y1_full = offset_y + y
                 x2_full = x1_full + w
                 y2_full = y1_full + h

                 # --- Create full-size mask ---
                 # This is memory intensive if done for all patches upfront.
                 # A better approach might involve operating primarily on bounding boxes first.
                 full_mask_np = np.zeros((full_image_height, full_image_width), dtype=np.uint8)
                 # Calculate slice indices, ensuring they are within bounds
                 y_start, y_end = offset_y, min(offset_y + patch_h, full_image_height)
                 x_start, x_end = offset_x, min(offset_x + patch_w, full_image_width)
                 patch_y_end = y_end - y_start
                 patch_x_end = x_end - x_start

                 # Place the patch mask onto the full mask, handling boundary clipping
                 full_mask_np[y_start:y_end, x_start:x_end] = mask_np[:patch_y_end, :patch_x_end]

                 all_masks_full.append(torch.from_numpy(full_mask_np)) # Convert back to tensor if needed downstream
                 all_bboxes_full.append([x1_full, y1_full, x2_full, y2_full])

            except Exception as e:
                 logger.error(f"Error processing patch result at offset ({offset_x}, {offset_y}): {e}")
                 continue

        if not all_masks_full:
             return []

        # --- Non-Maximum Suppression (NMS) based on BBoxes ---
        # This is a common way to handle overlaps. We need scores (confidence) if available.
        # If scores aren't directly available from segment_everything results, use area or other proxy.
        # For simplicity, let's use area as score proxy here.

        bboxes_np = np.array(all_bboxes_full)
        scores = np.array([torch.sum(mask).item() for mask in all_masks_full]) # Use mask area as score

        # --- Basic NMS Implementation ---
        # (Note: torchvision.ops.nms is more efficient if available)
        keep_indices = []
        order = scores.argsort()[::-1] # Sort by score descending

        while order.size > 0:
            i = order[0]
            keep_indices.append(i)

            # Calculate IoU between the current box and remaining boxes
            xx1 = np.maximum(bboxes_np[i, 0], bboxes_np[order[1:], 0])
            yy1 = np.maximum(bboxes_np[i, 1], bboxes_np[order[1:], 1])
            xx2 = np.minimum(bboxes_np[i, 2], bboxes_np[order[1:], 2])
            yy2 = np.minimum(bboxes_np[i, 3], bboxes_np[order[1:], 3])

            w = np.maximum(0.0, xx2 - xx1 + 1)
            h = np.maximum(0.0, yy2 - yy1 + 1)
            intersection = w * h

            area_i = (bboxes_np[i, 2] - bboxes_np[i, 0] + 1) * (bboxes_np[i, 3] - bboxes_np[i, 1] + 1)
            area_order = (bboxes_np[order[1:], 2] - bboxes_np[order[1:], 0] + 1) * (bboxes_np[order[1:], 3] - bboxes_np[order[1:], 1] + 1)

            iou = intersection / (area_i + area_order - intersection + 1e-6) # Add epsilon

            # Keep boxes with IoU below the threshold
            inds = np.where(iou <= iou_threshold)[0]
            order = order[inds + 1] # +1 because order[0] was the current box 'i'

        merged_annotations = [all_masks_full[idx].to(self.device) for idx in keep_indices] # Move final masks to device
        logger.info(f"Merged annotations down to {len(merged_annotations)} using NMS.")
        return merged_annotations


    # --- Event Handlers ---

    def keyPressEvent(self, event: QtGui.QKeyEvent):
        """Handles global keyboard shortcuts."""
        key = event.key()
        modifiers = event.modifiers()

        # --- Shortcuts that work regardless of focus ---
        if key == Qt.Key_Delete or key == Qt.Key_Backspace:
             if self.selected_df_indices and self.delete_grains_button.isEnabled():
                  self.delete_selected_grains()
                  event.accept()
                  return # Stop further processing
        elif key == Qt.Key_Escape:
             # Deselect all? Or cancel current operation?
             if self.selected_df_indices:
                  self.set_selected_grains(set()) # Deselect all
                  event.accept()
                  return
             # If in scale mode, cancel drawing?
             if self.mode == 'scale' and self.view.scale_line_start:
                  self.view.clear_scale_line_item()
                  self.view.scale_line_start = None
                  self.view.scale_line_end = None
                  self.view.setCursor(Qt.CrossCursor) # Reset cursor
                  event.accept()
                  return


        # Let the base class handle other keys or forward to focused widget
        super().keyPressEvent(event)


    def closeEvent(self, event: QtGui.QCloseEvent):
        """Handles the window close event, asking for confirmation."""
        logger.info("Close event triggered.")
        # Stop any processing
        self.stop_processing_thread()

        # Check if there's unsaved data
        unsaved_data = self.df is not None # Add other checks if needed

        if unsaved_data:
            reply = QMessageBox.question(
                self, "Exit Confirmation",
                "Are you sure you want to exit?\nUnsaved results will be lost.",
                QMessageBox.Yes | QMessageBox.Cancel, QMessageBox.Cancel
            )
            if reply == QMessageBox.Cancel:
                event.ignore() # Prevent closing
                logger.debug("Close cancelled by user.")
                return

        logger.info("Proceeding with application close.")
        self.cleanup_before_exit()
        event.accept() # Allow closing

    def cleanup_before_exit(self):
        """Performs cleanup operations before the application exits."""
        logger.info("Performing cleanup before exit...")
        # Save theme preference
        self.save_theme_preference()

        # Close plot dialogs and figures
        for dialog in self.plot_dialogs[:]: # Iterate copy
             if dialog: dialog.close()
        self.plot_dialogs.clear()
        try:
            plt.close('all') # Close all matplotlib figures
        except Exception as e:
            logger.error(f"Error closing matplotlib figures: {e}")

        # Release model resources? Usually handled by Python GC, but explicit del can help
        if hasattr(self, 'model'):
             del self.model
             self.model = None
             if self.device.type == 'cuda':
                  logger.debug("Clearing CUDA cache.")
                  torch.cuda.empty_cache()

        # Close logging handlers
        logger.info("Closing logging handlers.")
        for handler in logging.getLogger().handlers[:]:
            try:
                handler.close()
                logging.getLogger().removeHandler(handler)
            except Exception as e:
                 print(f"Error closing handler {handler}: {e}") # Use print as logging might be closed

        # Log file deletion - Optional, maybe keep logs?
        # try:
        #      log_file = "grainsight_analysis.log"
        #      if os.path.exists(log_file):
        #           os.remove(log_file)
        # except Exception as e:
        #      print(f"Error deleting log file: {e}")

        logger.info("Cleanup complete. Exiting.")
        # Note: QApplication.quit() is called automatically after event.accept() in closeEvent


# --- Font Handling ---
def find_system_font(font_name):
    """ Tries to find a font path using matplotlib's font manager. """
    try:
        import matplotlib.font_manager
        # Use findfont with fallback to default sans-serif if specific font not found
        prop = matplotlib.font_manager.FontProperties(family=font_name)
        font_path = matplotlib.font_manager.findfont(prop, fallback_to_default=True)
        if font_path:
            logger.debug(f"Found font '{font_name}' (or fallback) at: {font_path}")
            return font_path
        else:
             logger.warning(f"Font '{font_name}' not found using matplotlib.")
             return None
    except ImportError:
        logger.warning("Matplotlib font manager not available. Cannot reliably find system fonts.")
        # Minimal fallback for Windows
        if sys.platform == 'win32':
            fonts_dir = os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'Fonts')
            common_fonts = [f"{font_name}.ttf", f"{font_name.lower()}.ttf", "arial.ttf", "verdana.ttf", "tahoma.ttf"]
            for fname in common_fonts:
                 fpath = os.path.join(fonts_dir, fname)
                 if os.path.exists(fpath):
                      logger.debug(f"Found fallback font: {fpath}")
                      return fpath
        return None
    except Exception as e:
        logger.error(f"Error finding font '{font_name}': {e}")
        return None

def load_font(font_path):
    """Loads a font using QFontDatabase."""
    if not font_path or not os.path.exists(font_path):
         logger.error(f"Font path invalid or file not found: {font_path}")
         return False
    font_id = QFontDatabase.addApplicationFont(font_path)
    if font_id == -1:
        logger.warning(f"Failed to load font from {font_path}")
        return False
    font_families = QFontDatabase.applicationFontFamilies(font_id)
    if not font_families:
        logger.warning(f"Loaded font from {font_path}, but couldn't retrieve families.")
        return False
    logger.info(f"Successfully loaded font: {font_families[0]} from {font_path}")
    return True

# --- Main Execution ---
def main():
    # Set Application details for styling and identification
    QApplication.setApplicationName("GrainSight")
    QApplication.setOrganizationName("FaresAzzam") # Optional
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True) # Enable High DPI scaling
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)   # Use high DPI pixmaps

    app = QApplication(sys.argv)

    # --- Font Setup ---
    # Try loading preferred fonts, fallback to system default
    preferred_fonts = ['Arial', 'Helvetica', 'Verdana', 'Sans Serif'] # Add preferred fonts
    font_loaded = False
    for font_name in preferred_fonts:
        font_path = find_system_font(font_name)
        if font_path and load_font(font_path):
            app.setFont(QFont(font_name, 9)) # Set default app font size
            font_loaded = True
            logger.info(f"Using font: {font_name}")
            break
    if not font_loaded:
         logger.warning("Could not load preferred fonts, using system default.")
         # System default font will be used

    # --- Initialize and Show Window ---
    try:
        window = GrainAnalysisApp()
        window.show()
        sys.exit(app.exec())
    except Exception as e:
         logger.critical(f"Unhandled exception during application startup or execution: {e}", exc_info=True)
         # Show error message box before exiting
         msgBox = QMessageBox()
         msgBox.setIcon(QMessageBox.Critical)
         msgBox.setWindowTitle("Fatal Error")
         msgBox.setText(f"A critical error occurred:\n\n{e}\n\nPlease check the log file 'grainsight_analysis.log' for details.")
         msgBox.setStandardButtons(QMessageBox.Ok)
         msgBox.exec()
         sys.exit(1)

if __name__ == "__main__":
    main()
