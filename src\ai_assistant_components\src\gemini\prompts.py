# src/gemini/prompts.py
# Contains prompt templates and formatting logic for Gemini requests

DEFAULT_PROMPTS = {
    "Sedimentologist Analysis": """
Analyze the provided thin section photomicrograph from a sedimentologist's perspective. Describe the following:
1.  **Overall Texture:** Grain size distribution (sorting), grain shape (roundness, sphericity), grain packing, nature of grain contacts (point, long, concavo-convex, sutured).
2.  **Framework Grains:** Identify major detrital minerals (e.g., quartz types, feldspars, lithic fragments, heavy minerals). Describe their characteristics and relative abundance. Note any signs of alteration or weathering.
3.  **Matrix/Cement:** Describe the interstitial material. Is it primary depositional matrix (clay, silt) or secondary cement (e.g., carbonate, silica, clay minerals, iron oxides)? Describe cement types, textures (e.g., pore-lining, pore-filling, syntaxial overgrowths), and paragenesis if possible.
4.  **Porosity:** Describe the type (primary intergranular, secondary dissolution, fracture), abundance, and distribution of visible porosity (mention if blue epoxy indicates porosity).
5.  **Diagenetic Features:** Note any evidence of compaction, dissolution, replacement, or cementation events.
6.  **Interpretation:** Provide a likely rock classification (e.g., quartz arenite, lithic arkose) and suggest a possible depositional environment or diagenetic history based on the observations.
""",

    "Petrographer Analysis": """
Provide a detailed petrographic description of the provided thin section photomicrograph. Focus on:
1.  **Mineral Identification:** Identify all visible minerals precisely. For major minerals (quartz, feldspar), specify varieties if possible (e.g., monocrystalline vs. polycrystalline quartz, plagioclase vs. K-feldspar, twinning types, alteration products like sericite or kaolinite). Identify accessory minerals.
2.  **Texture & Fabric:** Describe the crystallographic relationships between grains, grain boundary shapes, and any preferred orientation or fabric. Detail the nature of cements (crystal size, habit, relationship to pores and grains).
3.  **Microstructures:** Describe any deformation features (e.g., undulose extinction in quartz, microfractures, pressure solution seams), exsolution lamellae, zoning in minerals, or reaction rims.
4.  **Opaque Minerals:** Describe any opaque minerals present (shape, size, estimated abundance, possible identification based on morphology).
5.  **Porosity:** Characterize visible porosity types and morphology in detail.
6.  **Paragenesis:** If possible, infer the sequence of mineral formation and alteration events (diagenetic sequence).
""",

    "Structured Analysis (JSON)": """
**Objective:**  
Analyze the provided image (thin section photomicrograph or BSE image) and generate a structured JSON output detailing identified geological features (minerals, lithics, cement, pores).

**Input:**  
A single image.

**Output Requirements:**

1. **Format:**  
   - Your response **must** be a single, valid JSON list (`[...]`) containing one or more feature objects.

2. **Content Rules:**  
   - Each object within the list **must** represent one identified feature and include the following keys:  
     - `box_2d`: A list of **normalized coordinates** `[ymin, xmin, ymax, xmax]` (floats from 0.0 to 1.0) **tightly enclosing the feature**.  
     - `label`: A concise string describing the feature type (e.g., `"quartz"`, `"plagioclase"`, `"porosity"`).  
     - `attributes`: A dictionary of characteristics, including (at minimum):  
       - `shape`: Morphology descriptor (e.g., `"angular"`, `"rounded"`, `"euhedral"`).  
       - `description`: A brief petrographic summary including **key identification features**.


5. **Strict JSON Constraints:**  
   - The entire response **must consist only of the valid JSON list**.  
   - Do **not** include any introductory text, closing remarks, explanations, or code fences.  
   - Do **not** add characters before the opening `[` or after the closing `]`.

6. **Handling Unidentifiable Images:**  
   - If no features can be confidently identified, return an empty list:
     ```json
     []
     ```

---

#### Example of a Valid Output Object

```json
{
  "box_2d": [0.15, 0.22, 0.31, 0.45],
  "label": "plagioclase",
  "attributes": {
    "shape": "subhedral",
    "description": "Plagioclase feldspar showing polysynthetic twinning and partial alteration to sericite along cleavage planes.",
    "twinning": "polysynthetic (albite)",
    "alteration": "sericite (minor)"
  }
}
```
"""
}