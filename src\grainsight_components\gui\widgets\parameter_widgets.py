# gui/widgets/parameter_widgets.py

import logging
from PySide6 import Qt<PERSON>ore, QtGui, QtWidgets
from PySide6.QtCore import Qt, Signal, Slot
from PySide6.QtGui import QIntValidator, QDoubleValidator
from PySide6.QtWidgets import (QW<PERSON>t, Q<PERSON>abel, QSlider, QLineEdit, QGridLayout,
                               QSpinBox, QHBoxLayout) # Added QSpinBox, QHBoxLayout

logger = logging.getLogger(__name__)

# Define defaults directly here or import from a config/constants file
DEFAULT_INPUT_SIZE = 1024
DEFAULT_IOU_THRESHOLD = 0.7
DEFAULT_CONF_THRESHOLD = 0.5
DEFAULT_MAX_DET = 500
DEFAULT_MOBILE_SAM_POINTS_PER_SIDE = 32
DEFAULT_MOBILE_SAM_PRED_IOU_THRESH = 0.88
DEFAULT_MOBILE_SAM_STABILITY_SCORE_THRESH = 0.95
DEFAULT_MOBILE_SAM_CROP_LAYERS = 0
DEFAULT_MOBILE_SAM_CROP_DOWNSCALE = 1
DEFAULT_CONTOUR_THICKNESS = 1

class BaseParameterWidget(QWidget):
    """Base class for parameter widgets to handle common functionality like signals."""
    # Signal emitted when any parameter value changes
    parameters_changed = Signal()

    def connect_signals(self):
        """Connects changed signals of input widgets to the parameters_changed signal."""
        for child in self.findChildren(QSlider) + self.findChildren(QLineEdit) + self.findChildren(QSpinBox):
            if isinstance(child, QSlider):
                child.valueChanged.connect(lambda *args: self.parameters_changed.emit())
            elif isinstance(child, QLineEdit):
                child.textChanged.connect(lambda *args: self.parameters_changed.emit())
            elif isinstance(child, QSpinBox):
                 child.valueChanged.connect(lambda *args: self.parameters_changed.emit())


class FastSAMParameterWidget(BaseParameterWidget):
    """Widget containing input controls for FastSAM parameters."""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.connect_signals() # Connect signals after UI is created

    def setup_ui(self):
        layout = QGridLayout(self)
        layout.setContentsMargins(5,5,5,5) # Add some padding

        # Input Size
        layout.addWidget(QLabel("Input Size:"), 0, 0)
        self.input_size_slider = QSlider(Qt.Horizontal)
        self.input_size_slider.setRange(256, 2048) # Adjusted range
        self.input_size_slider.setValue(DEFAULT_INPUT_SIZE)
        self.input_size_slider.setTickInterval(256)
        self.input_size_slider.setTickPosition(QSlider.TicksBelow)
        self.input_size_slider.valueChanged.connect(self._update_labels)
        layout.addWidget(self.input_size_slider, 0, 1)
        self.input_size_label = QLabel(str(DEFAULT_INPUT_SIZE))
        self.input_size_label.setMinimumWidth(40)
        layout.addWidget(self.input_size_label, 0, 2)

        # IOU Threshold
        layout.addWidget(QLabel("IOU Threshold:"), 1, 0)
        self.iou_threshold_slider = QSlider(Qt.Horizontal)
        self.iou_threshold_slider.setRange(10, 95) # 0.1 to 0.95
        self.iou_threshold_slider.setValue(int(DEFAULT_IOU_THRESHOLD * 100))
        self.iou_threshold_slider.setTickInterval(10)
        self.iou_threshold_slider.setTickPosition(QSlider.TicksBelow)
        self.iou_threshold_slider.valueChanged.connect(self._update_labels)
        layout.addWidget(self.iou_threshold_slider, 1, 1)
        self.iou_threshold_label = QLabel(f"{DEFAULT_IOU_THRESHOLD:.2f}")
        self.iou_threshold_label.setMinimumWidth(40)
        layout.addWidget(self.iou_threshold_label, 1, 2)

        # Confidence Threshold
        layout.addWidget(QLabel("Conf. Threshold:"), 2, 0)
        self.conf_threshold_slider = QSlider(Qt.Horizontal)
        self.conf_threshold_slider.setRange(5, 95) # 0.05 to 0.95
        self.conf_threshold_slider.setValue(int(DEFAULT_CONF_THRESHOLD * 100))
        self.conf_threshold_slider.setTickInterval(10)
        self.conf_threshold_slider.setTickPosition(QSlider.TicksBelow)
        self.conf_threshold_slider.valueChanged.connect(self._update_labels)
        layout.addWidget(self.conf_threshold_slider, 2, 1)
        self.conf_threshold_label = QLabel(f"{DEFAULT_CONF_THRESHOLD:.2f}")
        self.conf_threshold_label.setMinimumWidth(40)
        layout.addWidget(self.conf_threshold_label, 2, 2)

        # Max Objects
        layout.addWidget(QLabel("Max Objects:"), 3, 0)
        self.max_det_edit = QLineEdit(str(DEFAULT_MAX_DET))
        self.max_det_edit.setValidator(QIntValidator(1, 10000)) # Validate input
        layout.addWidget(self.max_det_edit, 3, 1, 1, 2) # Span 2 columns

        # Visualization Options (Moved here from main window for grouping)
        layout.addWidget(QLabel("Contour Thickness:"), 4, 0)
        self.contour_thickness_slider = QSlider(Qt.Horizontal)
        self.contour_thickness_slider.setRange(1, 10)
        self.contour_thickness_slider.setValue(DEFAULT_CONTOUR_THICKNESS)
        self.contour_thickness_slider.setTickInterval(1)
        self.contour_thickness_slider.setTickPosition(QSlider.TicksBelow)
        self.contour_thickness_slider.valueChanged.connect(self._update_labels)
        layout.addWidget(self.contour_thickness_slider, 4, 1)
        self.contour_thickness_label = QLabel(str(DEFAULT_CONTOUR_THICKNESS))
        self.contour_thickness_label.setMinimumWidth(40)
        layout.addWidget(self.contour_thickness_label, 4, 2)


        self._update_labels() # Set initial labels correctly

    @Slot()
    def _update_labels(self):
        """Updates the labels next to sliders."""
        self.input_size_label.setText(f"{self.input_size_slider.value()}")
        self.iou_threshold_label.setText(f"{self.iou_threshold_slider.value() / 100.0:.2f}")
        self.conf_threshold_label.setText(f"{self.conf_threshold_slider.value() / 100.0:.2f}")
        self.contour_thickness_label.setText(f"{self.contour_thickness_slider.value()}")

    def get_parameters(self) -> dict:
        """Returns a dictionary of the current parameter values."""
        try:
            return {
                'model_type': 'fastsam',
                'input_size': self.input_size_slider.value(),
                'iou': self.iou_threshold_slider.value() / 100.0,
                'conf': self.conf_threshold_slider.value() / 100.0,
                'max_det': int(self.max_det_edit.text()),
                'contour_thickness': self.contour_thickness_slider.value()
            }
        except ValueError as e:
            logger.error(f"Invalid parameter value encountered: {e}")
            # Handle error appropriately, maybe raise or return default/None
            raise ValueError(f"Invalid input parameter: {e}")

    def reset_defaults(self):
         """Resets parameters to their default values."""
         self.input_size_slider.setValue(DEFAULT_INPUT_SIZE)
         self.iou_threshold_slider.setValue(int(DEFAULT_IOU_THRESHOLD * 100))
         self.conf_threshold_slider.setValue(int(DEFAULT_CONF_THRESHOLD * 100))
         self.max_det_edit.setText(str(DEFAULT_MAX_DET))
         self.contour_thickness_slider.setValue(DEFAULT_CONTOUR_THICKNESS)
         self._update_labels()


class MobileSAMParameterWidget(BaseParameterWidget):
    """Widget containing input controls for MobileSAM parameters."""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.connect_signals() # Connect signals after UI is created

    def setup_ui(self):
        layout = QGridLayout(self)
        layout.setContentsMargins(5,5,5,5) # Add some padding
        current_row = 0

        # Points Per Side
        layout.addWidget(QLabel("Points Per Side:"), current_row, 0)
        self.points_per_side_slider = QSlider(Qt.Horizontal)
        self.points_per_side_slider.setRange(8, 64)
        self.points_per_side_slider.setValue(DEFAULT_MOBILE_SAM_POINTS_PER_SIDE)
        self.points_per_side_slider.setTickInterval(8)
        self.points_per_side_slider.setTickPosition(QSlider.TicksBelow)
        self.points_per_side_slider.valueChanged.connect(self._update_labels)
        layout.addWidget(self.points_per_side_slider, current_row, 1)
        self.points_per_side_label = QLabel(str(DEFAULT_MOBILE_SAM_POINTS_PER_SIDE))
        self.points_per_side_label.setMinimumWidth(40)
        layout.addWidget(self.points_per_side_label, current_row, 2)
        current_row += 1

        # Prediction IOU Threshold
        layout.addWidget(QLabel("Pred IOU Thresh:"), current_row, 0)
        self.pred_iou_thresh_slider = QSlider(Qt.Horizontal)
        self.pred_iou_thresh_slider.setRange(50, 99) # 0.50 to 0.99
        self.pred_iou_thresh_slider.setValue(int(DEFAULT_MOBILE_SAM_PRED_IOU_THRESH * 100))
        self.pred_iou_thresh_slider.setTickInterval(10)
        self.pred_iou_thresh_slider.setTickPosition(QSlider.TicksBelow)
        self.pred_iou_thresh_slider.valueChanged.connect(self._update_labels)
        layout.addWidget(self.pred_iou_thresh_slider, current_row, 1)
        self.pred_iou_thresh_label = QLabel(f"{DEFAULT_MOBILE_SAM_PRED_IOU_THRESH:.2f}")
        self.pred_iou_thresh_label.setMinimumWidth(40)
        layout.addWidget(self.pred_iou_thresh_label, current_row, 2)
        current_row += 1

        # Stability Score Threshold
        layout.addWidget(QLabel("Stability Score Thresh:"), current_row, 0)
        self.stability_score_thresh_slider = QSlider(Qt.Horizontal)
        self.stability_score_thresh_slider.setRange(50, 99) # 0.50 to 0.99
        self.stability_score_thresh_slider.setValue(int(DEFAULT_MOBILE_SAM_STABILITY_SCORE_THRESH * 100))
        self.stability_score_thresh_slider.setTickInterval(10)
        self.stability_score_thresh_slider.setTickPosition(QSlider.TicksBelow)
        self.stability_score_thresh_slider.valueChanged.connect(self._update_labels)
        layout.addWidget(self.stability_score_thresh_slider, current_row, 1)
        self.stability_score_thresh_label = QLabel(f"{DEFAULT_MOBILE_SAM_STABILITY_SCORE_THRESH:.2f}")
        self.stability_score_thresh_label.setMinimumWidth(40)
        layout.addWidget(self.stability_score_thresh_label, current_row, 2)
        current_row += 1

        # Crop Layers
        layout.addWidget(QLabel("Crop Layers (0=None):"), current_row, 0)
        self.crop_n_layers_spinbox = QSpinBox()
        self.crop_n_layers_spinbox.setRange(0, 3)
        self.crop_n_layers_spinbox.setValue(DEFAULT_MOBILE_SAM_CROP_LAYERS)
        self.crop_n_layers_spinbox.setToolTip("Number of layers for automatic mask generation cropping.")
        layout.addWidget(self.crop_n_layers_spinbox, current_row, 1, 1, 2) # Span 2 columns
        current_row += 1

        # Crop Points Downscale Factor
        layout.addWidget(QLabel("Crop Points Downscale:"), current_row, 0)
        self.crop_n_points_downscale_factor_spinbox = QSpinBox()
        self.crop_n_points_downscale_factor_spinbox.setRange(1, 4)
        self.crop_n_points_downscale_factor_spinbox.setValue(DEFAULT_MOBILE_SAM_CROP_DOWNSCALE)
        self.crop_n_points_downscale_factor_spinbox.setToolTip("Downscaling factor for points grid during cropping.")
        layout.addWidget(self.crop_n_points_downscale_factor_spinbox, current_row, 1, 1, 2) # Span 2 columns
        current_row += 1

        # Visualization Options (Shared - could be a separate widget)
        layout.addWidget(QLabel("Contour Thickness:"), current_row, 0)
        self.contour_thickness_slider = QSlider(Qt.Horizontal)
        self.contour_thickness_slider.setRange(1, 10)
        self.contour_thickness_slider.setValue(DEFAULT_CONTOUR_THICKNESS)
        self.contour_thickness_slider.setTickInterval(1)
        self.contour_thickness_slider.setTickPosition(QSlider.TicksBelow)
        self.contour_thickness_slider.valueChanged.connect(self._update_labels)
        layout.addWidget(self.contour_thickness_slider, current_row, 1)
        self.contour_thickness_label = QLabel(str(DEFAULT_CONTOUR_THICKNESS))
        self.contour_thickness_label.setMinimumWidth(40)
        layout.addWidget(self.contour_thickness_label, current_row, 2)

        self._update_labels() # Set initial labels correctly


    @Slot()
    def _update_labels(self):
        """Updates the labels next to sliders."""
        self.points_per_side_label.setText(f"{self.points_per_side_slider.value()}")
        self.pred_iou_thresh_label.setText(f"{self.pred_iou_thresh_slider.value() / 100.0:.2f}")
        self.stability_score_thresh_label.setText(f"{self.stability_score_thresh_slider.value() / 100.0:.2f}")
        self.contour_thickness_label.setText(f"{self.contour_thickness_slider.value()}")


    def get_parameters(self) -> dict:
        """Returns a dictionary of the current parameter values."""
        return {
            'model_type': 'mobilesam',
            'points_per_side': self.points_per_side_slider.value(),
            'pred_iou_thresh': self.pred_iou_thresh_slider.value() / 100.0,
            'stability_score_thresh': self.stability_score_thresh_slider.value() / 100.0,
            'box_nms_thresh': 0.3, # Not currently configurable in UI, using default
            'crop_n_layers': self.crop_n_layers_spinbox.value(),
            'crop_n_points_downscale_factor': self.crop_n_points_downscale_factor_spinbox.value(),
            'min_mask_region_area': 0, # Not currently configurable, using default
            'contour_thickness': self.contour_thickness_slider.value()
        }

    def reset_defaults(self):
        """Resets parameters to their default values."""
        self.points_per_side_slider.setValue(DEFAULT_MOBILE_SAM_POINTS_PER_SIDE)
        self.pred_iou_thresh_slider.setValue(int(DEFAULT_MOBILE_SAM_PRED_IOU_THRESH * 100))
        self.stability_score_thresh_slider.setValue(int(DEFAULT_MOBILE_SAM_STABILITY_SCORE_THRESH * 100))
        self.crop_n_layers_spinbox.setValue(DEFAULT_MOBILE_SAM_CROP_LAYERS)
        self.crop_n_points_downscale_factor_spinbox.setValue(DEFAULT_MOBILE_SAM_CROP_DOWNSCALE)
        self.contour_thickness_slider.setValue(DEFAULT_CONTOUR_THICKNESS)
        self._update_labels()