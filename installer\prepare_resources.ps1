# VisionLab Ai Installer Resource Preparation Script
# This script prepares all necessary resources for the installer

Write-Host "===============================================" -ForegroundColor Green
Write-Host "VisionLab Ai Installer Resource Preparation" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green
Write-Host ""

# Create resources directory if it doesn't exist
if (!(Test-Path "resources")) {
    New-Item -ItemType Directory -Path "resources" -Force
    Write-Host "Created resources directory" -ForegroundColor Yellow
}

# Function to convert PNG to ICO using .NET
function Convert-PngToIco {
    param(
        [string]$InputPath,
        [string]$OutputPath,
        [int]$Size = 32
    )
    
    try {
        Add-Type -AssemblyName System.Drawing
        
        # Load the PNG image
        $png = [System.Drawing.Image]::FromFile($InputPath)
        
        # Create a new bitmap with the desired size
        $bitmap = New-Object System.Drawing.Bitmap($Size, $Size)
        $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
        $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
        $graphics.DrawImage($png, 0, 0, $Size, $Size)
        
        # Save as ICO
        $bitmap.Save($OutputPath, [System.Drawing.Imaging.ImageFormat]::Icon)
        
        # Cleanup
        $graphics.Dispose()
        $bitmap.Dispose()
        $png.Dispose()
        
        Write-Host "Successfully converted $InputPath to $OutputPath" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "Error converting PNG to ICO: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to create a simple colored bitmap
function Create-SimpleBitmap {
    param(
        [string]$OutputPath,
        [int]$Width,
        [int]$Height,
        [string]$Text = "",
        [string]$BackgroundColor = "LightBlue",
        [string]$TextColor = "DarkBlue"
    )
    
    try {
        Add-Type -AssemblyName System.Drawing
        
        # Create bitmap
        $bitmap = New-Object System.Drawing.Bitmap($Width, $Height)
        $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
        
        # Fill background
        $bgBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::$BackgroundColor)
        $graphics.FillRectangle($bgBrush, 0, 0, $Width, $Height)
        
        # Add text if provided
        if ($Text) {
            $font = New-Object System.Drawing.Font("Arial", 12, [System.Drawing.FontStyle]::Bold)
            $textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::$TextColor)
            $textSize = $graphics.MeasureString($Text, $font)
            $x = ($Width - $textSize.Width) / 2
            $y = ($Height - $textSize.Height) / 2
            $graphics.DrawString($Text, $font, $textBrush, $x, $y)
            $font.Dispose()
            $textBrush.Dispose()
        }
        
        # Save as BMP
        $bitmap.Save($OutputPath, [System.Drawing.Imaging.ImageFormat]::Bmp)
        
        # Cleanup
        $bgBrush.Dispose()
        $graphics.Dispose()
        $bitmap.Dispose()
        
        Write-Host "Created bitmap: $OutputPath ($Width x $Height)" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "Error creating bitmap: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Check if logo.png exists and convert to ICO
$logoPath = "resources\logo.png"
$iconPath = "resources\app_icon.ico"

if (Test-Path $logoPath) {
    Write-Host "Converting application logo to ICO format..." -ForegroundColor Yellow
    $success = Convert-PngToIco -InputPath $logoPath -OutputPath $iconPath -Size 32
    if (!$success) {
        Write-Host "Failed to convert PNG to ICO. You may need to manually convert the logo." -ForegroundColor Red
        Write-Host "Alternative: Use an online converter or ImageMagick" -ForegroundColor Yellow
    }
} else {
    Write-Host "Logo not found at $logoPath" -ForegroundColor Red
    Write-Host "Creating a placeholder icon..." -ForegroundColor Yellow
    
    # Create a simple placeholder icon
    try {
        Add-Type -AssemblyName System.Drawing
        $bitmap = New-Object System.Drawing.Bitmap(32, 32)
        $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
        $graphics.FillRectangle([System.Drawing.Brushes]::Blue, 0, 0, 32, 32)
        $graphics.DrawString("VL", (New-Object System.Drawing.Font("Arial", 10, [System.Drawing.FontStyle]::Bold)), [System.Drawing.Brushes]::White, 5, 8)
        $bitmap.Save($iconPath, [System.Drawing.Imaging.ImageFormat]::Icon)
        $graphics.Dispose()
        $bitmap.Dispose()
        Write-Host "Created placeholder icon" -ForegroundColor Green
    }
    catch {
        Write-Host "Could not create placeholder icon: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Create wizard images
Write-Host "Creating installer wizard images..." -ForegroundColor Yellow

# Large wizard image (164x314)
$wizardImagePath = "resources\wizard_image.bmp"
if (!(Test-Path $wizardImagePath)) {
    Create-SimpleBitmap -OutputPath $wizardImagePath -Width 164 -Height 314 -Text "VisionLab Ai" -BackgroundColor "White" -TextColor "DarkBlue"
}

# Small wizard image (55x55)
$wizardSmallPath = "resources\wizard_small.bmp"
if (!(Test-Path $wizardSmallPath)) {
    Create-SimpleBitmap -OutputPath $wizardSmallPath -Width 55 -Height 55 -Text "VL" -BackgroundColor "LightBlue" -TextColor "DarkBlue"
}

Write-Host ""
Write-Host "Resource preparation completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Generated files:" -ForegroundColor Yellow
Get-ChildItem "resources" | ForEach-Object {
    Write-Host "  - $($_.Name)" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Review the generated resource files" -ForegroundColor White
Write-Host "2. Replace placeholder images with professional graphics if desired" -ForegroundColor White
Write-Host "3. Run build_installer.bat to create the installer" -ForegroundColor White
Write-Host ""

# Check for Inno Setup installation
$innoSetupPaths = @(
    "C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
    "C:\Program Files\Inno Setup 6\ISCC.exe"
)

$innoSetupFound = $false
foreach ($path in $innoSetupPaths) {
    if (Test-Path $path) {
        Write-Host "Inno Setup found at: $path" -ForegroundColor Green
        $innoSetupFound = $true
        break
    }
}

if (!$innoSetupFound) {
    Write-Host "WARNING: Inno Setup 6 not found!" -ForegroundColor Red
    Write-Host "Please download and install from: https://jrsoftware.org/isinfo.php" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
