@echo off
echo ===============================================
echo VisionLab Ai Professional Installer Builder
echo ===============================================
echo.

:: Check if Inno Setup is installed
set "INNO_SETUP_PATH=C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
if not exist "%INNO_SETUP_PATH%" (
    echo ERROR: Inno Setup 6 not found at expected location.
    echo Please install Inno Setup 6 from: https://jrsoftware.org/isinfo.php
    echo.
    echo Alternative locations to check:
    echo - C:\Program Files\Inno Setup 6\ISCC.exe
    echo - C:\Program Files (x86)\Inno Setup 6\ISCC.exe
    echo.
    pause
    exit /b 1
)

:: Check if the application dist folder exists
if not exist "..\dist\VisionLab_Ai_Simple" (
    echo ERROR: Application distribution folder not found.
    echo Expected location: ..\dist\VisionLab_Ai_Simple
    echo.
    echo Please ensure you have built the application using PyInstaller first.
    echo Run the build script in the main directory to create the distribution.
    echo.
    pause
    exit /b 1
)

:: Create necessary directories
echo Creating installer directories...
if not exist "resources" mkdir resources
if not exist "output" mkdir output

:: Try to create application icon
echo Preparing application icon...
if exist "resources\logo.png" (
    echo Attempting to create ICO file from PNG...
    call create_icon.bat
) else (
    echo Warning: Logo file not found at resources\logo.png
    echo Installer will use default Windows icon.
)

:: Check if custom images exist, if not installer will use defaults
if not exist "resources\wizard_image.bmp" (
    echo Note: Custom wizard image not found, using default appearance
)

if not exist "resources\wizard_small.bmp" (
    echo Note: Custom small wizard image not found, using default appearance
)

:: Build the installer
echo.
echo Building VisionLab Ai installer...
echo.

"%INNO_SETUP_PATH%" "VisionLab_Ai_Setup.iss"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ===============================================
    echo Installer built successfully!
    echo ===============================================
    echo.
    echo Output location: installer\output\
    echo Installer file: VisionLab_Ai_v4.0.0_Setup.exe
    echo.
    echo The installer includes:
    echo - Professional installation wizard
    echo - Start Menu shortcuts
    echo - Optional desktop shortcut
    echo - Proper uninstaller
    echo - Registry entries
    echo - License agreement
    echo.
    echo You can now distribute this installer to end users.
    echo.
) else (
    echo.
    echo ===============================================
    echo ERROR: Installer build failed!
    echo ===============================================
    echo.
    echo Please check the error messages above and ensure:
    echo 1. Inno Setup 6 is properly installed
    echo 2. All required resource files are present
    echo 3. The application distribution folder exists
    echo 4. You have write permissions to the output directory
    echo.
)

echo Press any key to continue...
pause >nul
