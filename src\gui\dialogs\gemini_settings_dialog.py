# src/gui/dialogs/gemini_settings_dialog.py

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QLabel, QLineEdit, QDialogButtonBox, QMessageBox,
    QComboBox, QHBoxLayout, QGroupBox, QPushButton
)
from PySide6.QtCore import QSettings, QThread, Signal
import google.generativeai as genai
import logging

logger = logging.getLogger(__name__)

class GeminiSettingsDialog(QDialog):
    """Dialog to configure Gemini API settings."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Gemini API Settings")
        self.setMinimumWidth(400)

        self.settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")

        layout = QVBoxLayout(self)

        # API Key
        api_key_group = QGroupBox("API Key")
        api_key_layout = QVBoxLayout(api_key_group)

        api_key_layout.addWidget(QLabel("Google Gemini API Key:"))
        self.api_key_input = QLineEdit()
        self.api_key_input.setEchoMode(QLineEdit.EchoMode.Password)
        # Load saved key
        saved_key = self.settings.value("gemini/api_key", "")
        self.api_key_input.setText(saved_key)
        api_key_layout.addWidget(self.api_key_input)

        layout.addWidget(api_key_group)

        # Model Selection
        model_group = QGroupBox("Model Selection")
        model_layout = QVBoxLayout(model_group)

        model_layout.addWidget(QLabel("Select Gemini Model:"))
        
        # Model selector and refresh button layout
        model_selector_layout = QHBoxLayout()
        self.model_selector = QComboBox()
        self.refresh_models_button = QPushButton("Refresh Models")
        self.refresh_models_button.clicked.connect(self.refresh_available_models)
        
        model_selector_layout.addWidget(self.model_selector)
        model_selector_layout.addWidget(self.refresh_models_button)
        model_layout.addLayout(model_selector_layout)
        
        # Try to automatically fetch models if API key is available, otherwise use fallback
        saved_key = self.settings.value("gemini/api_key", "")
        if saved_key:
            self._fetch_models_silently(saved_key)
        else:
            self._populate_fallback_models()
        
        # Load saved model
        saved_model = self.settings.value("gemini/model", "gemini-2.0-flash")
        index = self.model_selector.findText(saved_model)
        if index >= 0:
            self.model_selector.setCurrentIndex(index)

        layout.addWidget(model_group)

        # Instructions
        instructions = QLabel(
            "To use the AI Assistant, you need a Google Gemini API key.\n"
            "1. Go to https://makersuite.google.com/app/apikey\n"
            "2. Create a new API key\n"
            "3. Copy and paste it here"
        )
        instructions.setWordWrap(True)
        layout.addWidget(instructions)

        # Buttons
        self.button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        layout.addWidget(self.button_box)

    def _populate_fallback_models(self):
        """Populate the model selector with fallback models."""
        fallback_models = [
            "gemini-2.0-flash",
            "gemini-2.0-pro", 
            "gemini-1.5-flash",
            "gemini-1.5-pro",
            "gemini-1.5-flash-8b"
        ]
        self.model_selector.clear()
        self.model_selector.addItems(fallback_models)
        logger.info(f"Populated fallback models: {fallback_models}")

    def _fetch_models_silently(self, api_key):
        """Fetch available models silently without showing success messages."""
        try:
            # Configure the API with the provided key
            genai.configure(api_key=api_key)
            
            # Fetch available models
            models = genai.list_models()
            available_models = []
            
            for model in models:
                # Filter for generative models that support vision
                if hasattr(model, 'supported_generation_methods') and 'generateContent' in model.supported_generation_methods:
                    # Clean up the model name (remove 'models/' prefix if present)
                    model_name = model.name
                    if model_name.startswith('models/'):
                        model_name = model_name.replace('models/', '')
                    available_models.append(model_name)
            
            if available_models:
                # Update the combo box
                self.model_selector.clear()
                available_models.sort()  # Sort alphabetically
                self.model_selector.addItems(available_models)
                logger.info(f"Silently fetched {len(available_models)} models")
            else:
                logger.warning("No suitable models found, using fallback models")
                self._populate_fallback_models()
                
        except Exception as e:
            logger.warning(f"Error fetching models silently: {e}, using fallback models")
            self._populate_fallback_models()

    def refresh_available_models(self):
        """Refresh the list of available models from the Gemini API."""
        api_key = self.api_key_input.text().strip()
        if not api_key:
            QMessageBox.warning(self, "API Key Required", 
                              "Please enter your Gemini API key first to fetch available models.")
            return
            
        # Disable the refresh button during the operation
        self.refresh_models_button.setEnabled(False)
        self.refresh_models_button.setText("Fetching...")
        
        try:
            # Configure the API with the current key
            genai.configure(api_key=api_key)
            
            # Fetch available models
            models = genai.list_models()
            available_models = []
            
            for model in models:
                # Filter for generative models that support vision
                if hasattr(model, 'supported_generation_methods') and 'generateContent' in model.supported_generation_methods:
                    # Clean up the model name (remove 'models/' prefix if present)
                    model_name = model.name
                    if model_name.startswith('models/'):
                        model_name = model_name.replace('models/', '')
                    available_models.append(model_name)
            
            if available_models:
                # Remember the currently selected model
                current_model = self.model_selector.currentText()
                
                # Update the combo box
                self.model_selector.clear()
                available_models.sort()  # Sort alphabetically
                self.model_selector.addItems(available_models)
                
                # Try to restore the previously selected model
                index = self.model_selector.findText(current_model)
                if index >= 0:
                    self.model_selector.setCurrentIndex(index)
                
                logger.info(f"Successfully fetched {len(available_models)} models")
            else:
                logger.warning("No suitable models found")
                QMessageBox.warning(self, "No Models Found", 
                                  "No suitable generative models found. Using fallback models.")
                self._populate_fallback_models()
                
        except Exception as e:
            logger.error(f"Error fetching models: {e}")
            QMessageBox.critical(self, "Error Fetching Models", 
                               f"Failed to fetch available models: {str(e)}\n\nUsing fallback models.")
            self._populate_fallback_models()
        
        finally:
            # Re-enable the refresh button
            self.refresh_models_button.setEnabled(True)
            self.refresh_models_button.setText("Refresh Models")

    def accept(self):
        """Save settings when OK is clicked."""
        # Save API key
        new_key = self.api_key_input.text().strip()
        self.settings.setValue("gemini/api_key", new_key)
        self.settings.sync()  # Force settings to be written to disk immediately

        # Save model selection
        model = self.model_selector.currentText()
        self.settings.setValue("gemini/model", model)
        self.settings.sync()  # Force settings to be written to disk immediately

        # No message boxes here - they will be shown by the caller
        super().accept()

    @staticmethod
    def get_api_key():
        """Static method to get the API key from settings."""
        settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")
        settings.sync()  # Ensure we're reading the latest values
        api_key = settings.value("gemini/api_key", "")
        return api_key

    @staticmethod
    def get_model_name():
        """Static method to get the model name from settings."""
        settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")
        settings.sync()  # Ensure we're reading the latest values
        model_name = settings.value("gemini/model", "gemini-2.0-flash")

        # Remove the "models/" prefix if it exists
        if model_name and model_name.startswith("models/"):
            model_name = model_name.replace("models/", "")

        return model_name
