# core/io.py

import logging
import json
from datetime import datetime
import cv2
import numpy as np
import torch
from typing import List, Tuple, Union, Optional, Dict, Any

logger = logging.getLogger(__name__)

def generate_coco_dict(
    annotations: Union[List[torch.Tensor], torch.Tensor],
    image_height: int,
    image_width: int,
    image_filename: Optional[str] = "image.png",
    category_name: str = "grain",
    category_id: int = 1
    ) -> Optional[Dict[str, Any]]:
    """
    Generates a dictionary representing annotations in COCO format.

    Args:
        annotations (Union[List[torch.Tensor], torch.Tensor]):
            List or Tensor of binary mask tensors (H, W, uint8 or bool).
            Expected on CPU or GPU, will be moved to CPU.
        image_height (int): Height of the original image.
        image_width (int): Width of the original image.
        image_filename (Optional[str]): Filename to include in the COCO 'images' section.
        category_name (str): Name for the annotation category (e.g., 'grain').
        category_id (int): ID for the annotation category.

    Returns:
        Optional[Dict[str, Any]]: A dictionary structured according to COCO format,
                                  or None if annotations are empty or an error occurs.
    """
    if isinstance(annotations, torch.Tensor):
        num_annotations = annotations.shape[0]
    elif isinstance(annotations, list):
        num_annotations = len(annotations)
    else:
        num_annotations = 0

    if num_annotations == 0:
        logger.warning("No annotations provided for COCO export.")
        return None
    if image_height <= 0 or image_width <= 0:
         logger.error("Invalid image dimensions provided for COCO export.")
         return None

    logger.info(f"Generating COCO dictionary for {num_annotations} annotations.")

    try:
        coco_output = {
            "info": {
                "description": "GrainSight Segmentation Export",
                "version": "1.0",
                "year": datetime.now().year,
                "contributor": "GrainSight Core Module",
                "date_created": datetime.now().isoformat()
            },
            "licenses": [],
            "images": [{
                "id": 1, # Assuming single image context
                "width": image_width,
                "height": image_height,
                "file_name": image_filename or "unknown_image.png",
                "license": 0, "flickr_url": "", "coco_url": "", "date_captured": ""
            }],
            "annotations": [],
            "categories": [{
                "id": category_id,
                "name": category_name,
                "supercategory": ""
            }]
        }

        annotation_id_counter = 1
        processed_count = 0

        for i in range(num_annotations):
            # Get mask tensor
            if isinstance(annotations, torch.Tensor):
                mask_tensor = annotations[i]
            else:
                mask_tensor = annotations[i]

            if mask_tensor is None: continue

            # --- Ensure mask is numpy uint8 on CPU ---
            if not isinstance(mask_tensor, torch.Tensor) or mask_tensor.ndim != 2:
                 logger.warning(f"Skipping COCO annotation for invalid item {i} (not 2D tensor).")
                 continue
            try:
                if mask_tensor.dtype == torch.bool:
                    binary_mask_np = mask_tensor.cpu().numpy().astype(np.uint8)
                elif mask_tensor.dtype.is_floating_point:
                    binary_mask_np = (mask_tensor.cpu().numpy() > 0.5).astype(np.uint8)
                elif mask_tensor.dtype == torch.uint8:
                    binary_mask_np = mask_tensor.cpu().numpy()
                else:
                    logger.warning(f"Skipping COCO annotation {i} due to unsupported dtype {mask_tensor.dtype}.")
                    continue
            except Exception as conv_e:
                 logger.error(f"Error converting mask {i} to numpy for COCO export: {conv_e}")
                 continue
            # --- End Mask Conversion ---

            # Check shape consistency
            if binary_mask_np.shape[0] != image_height or binary_mask_np.shape[1] != image_width:
                 logger.warning(f"Mask {i} shape ({binary_mask_np.shape}) mismatch with image dimensions ({image_height}x{image_width}). Skipping COCO annotation.")
                 continue

            # Find contours - use CHAIN_APPROX_SIMPLE for polygons
            contours, hierarchy = cv2.findContours(binary_mask_np, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            if not contours: continue

            # Process each major contour (external) as a separate COCO annotation instance
            # This handles cases where one mask tensor might contain multiple disconnected objects.
            for contour in contours:
                # Check contour validity
                if len(contour) < 3: continue # Need at least 3 points for a polygon

                # Calculate area
                area = cv2.contourArea(contour)
                if area <= 1e-6: continue # Skip zero-area contours

                # Convert contour to COCO segmentation format [x1,y1,x2,y2,...]
                segmentation = contour.flatten().tolist()

                # Calculate bounding box [x, y, width, height] from contour
                x, y, w, h = cv2.boundingRect(contour)

                coco_annotation = {
                    "id": annotation_id_counter,
                    "image_id": 1, # Corresponds to the image ID
                    "category_id": category_id, # Corresponds to the category ID
                    "segmentation": [segmentation], # List of polygon lists [[x1,y1,...]]
                    "area": float(area),
                    "bbox": [float(x), float(y), float(w), float(h)], # COCO format [xmin, ymin, width, height]
                    "iscrowd": 0 # 0 for polygon segmentation
                }
                coco_output["annotations"].append(coco_annotation)
                annotation_id_counter += 1
                processed_count += 1


        if processed_count == 0:
             logger.warning("COCO export generated, but no valid annotation data was found.")
             # Return the structure anyway, but it will have an empty "annotations" list
             # return None # Or return None if empty annotations list is undesirable

        logger.info(f"COCO dictionary generated with {processed_count} annotation entries.")
        return coco_output

    except Exception as e:
        logger.exception("Failed to generate COCO dictionary.")
        return None

# Example: Function to save the dictionary (can be called by GUI after getting dict)
# def save_coco_json(coco_dict: Dict[str, Any], file_path: str):
#     """Saves a COCO dictionary to a JSON file."""
#     if not coco_dict:
#         logger.error("Cannot save COCO JSON: Dictionary is empty or None.")
#         return False
#     try:
#         with open(file_path, 'w') as f:
#             json.dump(coco_dict, f, indent=2) # Use indent for readability
#         logger.info(f"COCO data successfully saved to {file_path}")
#         return True
#     except Exception as e:
#         logger.exception(f"Failed to save COCO dictionary to {file_path}")
#         return False