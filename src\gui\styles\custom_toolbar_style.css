/* Custom toolbar styling for VisionLab Ai V4 */

/* Main toolbar background */
QToolBar {
    background-color: #1e1e1e;
    spacing: 0px;
    padding: 0px;
    border: none;
    border-bottom: 1px solid #333333;
}

/* All toolbar buttons */
QToolBar QToolButton {
    background-color: #1e1e1e;
    color: #cccccc;
    border: none;
    padding: 10px 18px;
    font-size: 14px;
    font-weight: 500;
    margin: 0px 2px;
    border-radius: 0px;
}

/* Hover state for toolbar buttons */
QToolBar QToolButton:hover {
    background-color: #333333;
    color: white;
}

/* Pressed state for toolbar buttons */
QToolBar QToolButton:pressed {
    background-color: #444444;
}

/* Active tab styling */
QToolBar QToolButton[active="true"] {
    background-color: #0078d4;
    color: white;
    font-weight: bold;
    border-bottom: 3px solid #007acc;
}

/* Menu buttons (Files, Analyze, Window, Help) */
QToolBar QToolButton[popupMode="1"] {
    padding: 10px 16px;
    margin-right: 5px;
    background-color: #1e1e1e;
    border-radius: 4px;
    border: 1px solid transparent;
}

/* Hover state for menu buttons */
QToolBar QToolButton[popupMode="1"]:hover {
    background-color: #333333;
    border: 1px solid #444444;
}

/* Hide menu button indicators */
QToolBar QToolButton::menu-button {
    width: 0px;
}

QToolBar QToolButton::menu-indicator {
    image: none;
}

/* Menu styling */
QMenu {
    background-color: #252525;
    border: 1px solid #333333;
    padding: 5px;
}

QMenu::item {
    background-color: transparent;
    color: #cccccc;
    padding: 8px 20px;
    margin: 2px;
    border-radius: 3px;
}

QMenu::item:selected {
    background-color: #0078d4;
    color: white;
}

QMenu::separator {
    height: 1px;
    background-color: #333333;
    margin: 5px 10px;
}
