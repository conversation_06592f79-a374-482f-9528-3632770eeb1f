# src/gui/dialogs/unsaved_annotations_dialog.py

from PySide6.QtWidgets import <PERSON><PERSON><PERSON>og, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QMessageBox
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon, QPixmap
import logging

logger = logging.getLogger(__name__)

class UnsavedAnnotationsDialog(QDialog):
    """Dialog to warn users about unsaved annotations when closing the application."""
    
    def __init__(self, parent=None, pages_with_unsaved_data=None):
        super().__init__(parent)
        self.pages_with_unsaved_data = pages_with_unsaved_data or []
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the dialog UI."""
        self.setWindowTitle("Unsaved Annotations")
        self.setModal(True)
        self.setFixedSize(400, 200)
        
        # Main layout
        layout = QVBoxLayout()
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Warning icon and message
        message_layout = QHBoxLayout()
        
        # Warning icon
        icon_label = QLabel()
        warning_icon = self.style().standardIcon(self.style().StandardPixmap.SP_MessageBoxWarning)
        icon_label.setPixmap(warning_icon.pixmap(48, 48))
        icon_label.setAlignment(Qt.AlignmentFlag.AlignTop)
        message_layout.addWidget(icon_label)
        
        # Message text
        message_text = self._create_message_text()
        message_label = QLabel(message_text)
        message_label.setWordWrap(True)
        message_label.setAlignment(Qt.AlignmentFlag.AlignTop)
        message_layout.addWidget(message_label, 1)
        
        layout.addLayout(message_layout)
        
        # Spacer
        layout.addStretch()
        
        # Buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.return_button = QPushButton("Return to App")
        self.return_button.setDefault(True)
        self.return_button.clicked.connect(self.reject)
        button_layout.addWidget(self.return_button)
        
        self.continue_button = QPushButton("Continue Closing")
        self.continue_button.clicked.connect(self.accept)
        button_layout.addWidget(self.continue_button)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
    def _create_message_text(self):
        """Create the appropriate message text based on which pages have unsaved data."""
        if not self.pages_with_unsaved_data:
            return "There are unsaved annotations that will be lost if you continue."
        
        if len(self.pages_with_unsaved_data) == 1:
            page_name = self.pages_with_unsaved_data[0]
            return f"You have unsaved annotations in the {page_name} page.\n\nThese annotations will be lost if you continue closing the application."
        else:
            pages_text = ", ".join(self.pages_with_unsaved_data[:-1]) + f" and {self.pages_with_unsaved_data[-1]}"
            return f"You have unsaved annotations in the {pages_text} pages.\n\nThese annotations will be lost if you continue closing the application."