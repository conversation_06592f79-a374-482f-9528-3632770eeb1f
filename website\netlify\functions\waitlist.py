#!/usr/bin/env python3
"""
Netlify Function for Vision Lab Waitlist API
Serverless function to handle waitlist submissions securely.
"""

import json
import os
import re
import hashlib
import secrets
import time
from datetime import datetime
from typing import Dict, Any, Optional

# Simple in-memory storage for demo (use external database in production)
# In production, use services like:
# - Netlify Blobs
# - Supabase
# - Firebase
# - FaunaDB
# - Airtable API

class NetlifyWaitlistAPI:
    """Simplified waitlist API for Netlify Functions"""
    
    def __init__(self):
        self.rate_limits = {}
        self.waitlist_count = 0
    
    def validate_email(self, email: str) -> bool:
        """Validate email format"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    def sanitize_input(self, text: str) -> str:
        """Sanitize user input"""
        if not text:
            return ""
        sanitized = re.sub(r'[<>"\'\/\\]', '', text)
        return sanitized.strip()[:255]
    
    def hash_email(self, email: str) -> str:
        """Hash email for privacy"""
        salt = os.environ.get('EMAIL_SALT', 'netlify_default_salt')
        return hashlib.sha256(f"{email}{salt}".encode()).hexdigest()
    
    def check_rate_limit(self, ip_address: str) -> bool:
        """Simple rate limiting"""
        current_time = time.time()
        window = 3600  # 1 hour
        max_requests = 5
        
        if ip_address not in self.rate_limits:
            self.rate_limits[ip_address] = []
        
        # Remove old requests
        self.rate_limits[ip_address] = [
            req_time for req_time in self.rate_limits[ip_address]
            if current_time - req_time < window
        ]
        
        # Check limit
        if len(self.rate_limits[ip_address]) >= max_requests:
            return False
        
        # Add current request
        self.rate_limits[ip_address].append(current_time)
        return True
    
    def add_to_waitlist(self, data: Dict[str, Any], ip_address: str) -> Dict[str, Any]:
        """Add user to waitlist"""
        
        # Rate limiting
        if not self.check_rate_limit(ip_address):
            return {
                'success': False,
                'error': 'Too many requests. Please try again later.',
                'code': 'RATE_LIMIT_EXCEEDED'
            }
        
        # Validate required fields
        required_fields = ['fullName', 'email', 'privacy']
        for field in required_fields:
            if not data.get(field):
                return {
                    'success': False,
                    'error': f'Missing required field: {field}',
                    'code': 'VALIDATION_ERROR'
                }
        
        # Validate email
        email = data['email'].lower().strip()
        if not self.validate_email(email):
            return {
                'success': False,
                'error': 'Invalid email format',
                'code': 'INVALID_EMAIL'
            }
        
        # Sanitize inputs
        full_name = self.sanitize_input(data['fullName'])
        organization = self.sanitize_input(data.get('organization', ''))
        
        # Validate enum fields
        valid_use_cases = ['research', 'industrial', 'medical', 'geological', 'other', '']
        valid_experience = ['beginner', 'intermediate', 'advanced', 'expert', '']
        
        use_case = data.get('useCase', '') if data.get('useCase') in valid_use_cases else ''
        experience = data.get('experience', '') if data.get('experience') in valid_experience else ''
        
        # In production, save to external database
        # For demo, we'll just increment counter and log
        self.waitlist_count += 1
        
        # Log the signup (in production, save to database)
        email_hash = self.hash_email(email)
        signup_data = {
            'email_hash': email_hash,
            'full_name': full_name,
            'organization': organization,
            'use_case': use_case,
            'experience': experience,
            'newsletter': bool(data.get('newsletter', False)),
            'timestamp': datetime.now().isoformat(),
            'ip_hash': hashlib.sha256(ip_address.encode()).hexdigest()
        }
        
        # In production, you would:
        # 1. Save to database (Supabase, Firebase, etc.)
        # 2. Send welcome email
        # 3. Add to mailing list if opted in
        
        print(f"New waitlist signup: {json.dumps(signup_data, indent=2)}")
        
        return {
            'success': True,
            'message': 'Successfully added to waitlist',
            'waitlist_position': self.waitlist_count,
            'verification_sent': True
        }

# Initialize API
api = NetlifyWaitlistAPI()

def handler(event, context):
    """Netlify Function handler"""
    
    # CORS headers
    headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Content-Type': 'application/json'
    }
    
    # Handle preflight requests
    if event['httpMethod'] == 'OPTIONS':
        return {
            'statusCode': 200,
            'headers': headers,
            'body': ''
        }
    
    # Only allow POST requests
    if event['httpMethod'] != 'POST':
        return {
            'statusCode': 405,
            'headers': headers,
            'body': json.dumps({
                'success': False,
                'error': 'Method not allowed'
            })
        }
    
    try:
        # Parse request body
        if not event.get('body'):
            return {
                'statusCode': 400,
                'headers': headers,
                'body': json.dumps({
                    'success': False,
                    'error': 'No data provided'
                })
            }
        
        data = json.loads(event['body'])
        
        # Get client IP
        ip_address = event.get('headers', {}).get('x-forwarded-for', '').split(',')[0].strip()
        if not ip_address:
            ip_address = event.get('headers', {}).get('x-real-ip', 'unknown')
        
        # Process the request
        result = api.add_to_waitlist(data, ip_address)
        
        # Return response
        status_code = 200 if result['success'] else 400
        return {
            'statusCode': status_code,
            'headers': headers,
            'body': json.dumps(result)
        }
        
    except json.JSONDecodeError:
        return {
            'statusCode': 400,
            'headers': headers,
            'body': json.dumps({
                'success': False,
                'error': 'Invalid JSON data'
            })
        }
    except Exception as e:
        print(f"Error in waitlist function: {e}")
        return {
            'statusCode': 500,
            'headers': headers,
            'body': json.dumps({
                'success': False,
                'error': 'Internal server error',
                'code': 'SERVER_ERROR'
            })
        }

# For local testing
if __name__ == '__main__':
    # Test the function
    test_event = {
        'httpMethod': 'POST',
        'headers': {
            'x-forwarded-for': '127.0.0.1'
        },
        'body': json.dumps({
            'fullName': 'John Doe',
            'email': '<EMAIL>',
            'organization': 'Test University',
            'useCase': 'research',
            'experience': 'intermediate',
            'newsletter': True,
            'privacy': True
        })
    }
    
    result = handler(test_event, {})
    print(json.dumps(result, indent=2))