"""
Project Repair Utility

This module provides utilities to repair corrupted VisionLab projects that have
performance issues due to large uncompressed annotation files.
"""

import os
import shutil
import tempfile
import zipfile
import numpy as np
import logging
from pathlib import Path
from typing import Optional, Tuple

logger = logging.getLogger(__name__)


class ProjectRepairTool:
    """Tool to repair corrupted VisionLab projects with large annotation files."""
    
    def __init__(self):
        self.temp_dir = None
        
    def analyze_project(self, project_path: str) -> dict:
        """Analyze a project file to identify performance issues.
        
        Args:
            project_path: Path to the .vlp project file
            
        Returns:
            Dictionary with analysis results
        """
        if not os.path.exists(project_path):
            return {"error": "Project file not found"}
            
        analysis = {
            "project_path": project_path,
            "total_size_mb": os.path.getsize(project_path) / (1024 * 1024),
            "large_files": [],
            "compression_savings": 0,
            "needs_repair": False
        }
        
        try:
            with zipfile.ZipFile(project_path, 'r') as zipf:
                for file_info in zipf.filelist:
                    size_mb = file_info.file_size / (1024 * 1024)
                    
                    # Check for large annotation files
                    if file_info.filename.endswith('annotations.npz') and size_mb > 50:
                        analysis["large_files"].append({
                            "filename": file_info.filename,
                            "size_mb": size_mb,
                            "compressed_size_mb": file_info.compress_size / (1024 * 1024),
                            "compression_ratio": file_info.compress_size / file_info.file_size if file_info.file_size > 0 else 0
                        })
                        analysis["needs_repair"] = True
                        
                        # Estimate potential savings (typical compression ratio for annotation data is 10-20%)
                        estimated_compressed_size = size_mb * 0.15  # Conservative estimate
                        analysis["compression_savings"] += size_mb - estimated_compressed_size
                        
        except Exception as e:
            analysis["error"] = f"Failed to analyze project: {e}"
            
        return analysis
    
    def repair_project(self, project_path: str, backup: bool = True) -> Tuple[bool, str]:
        """Repair a corrupted project by compressing large annotation files.
        
        Args:
            project_path: Path to the .vlp project file
            backup: Whether to create a backup before repair
            
        Returns:
            Tuple of (success, message)
        """
        if not os.path.exists(project_path):
            return False, "Project file not found"
            
        try:
            # Create backup if requested
            if backup:
                backup_path = project_path + ".backup"
                shutil.copy2(project_path, backup_path)
                logger.info(f"Created backup at {backup_path}")
            
            # Create temporary directory for extraction
            with tempfile.TemporaryDirectory() as temp_dir:
                self.temp_dir = temp_dir
                
                # Extract project
                with zipfile.ZipFile(project_path, 'r') as zipf:
                    zipf.extractall(temp_dir)
                
                # Find and compress large annotation files
                repaired_files = []
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        if file == "annotations.npz":
                            file_path = os.path.join(root, file)
                            original_size = os.path.getsize(file_path)
                            
                            if original_size > 50 * 1024 * 1024:  # 50MB threshold
                                success, new_size = self._compress_annotation_file(file_path)
                                if success:
                                    savings_mb = (original_size - new_size) / (1024 * 1024)
                                    repaired_files.append({
                                        "file": file_path,
                                        "original_size_mb": original_size / (1024 * 1024),
                                        "new_size_mb": new_size / (1024 * 1024),
                                        "savings_mb": savings_mb
                                    })
                
                # Recreate the project file
                temp_project_path = project_path + ".tmp"
                with zipfile.ZipFile(temp_project_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for root, dirs, files in os.walk(temp_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            rel_path = os.path.relpath(file_path, temp_dir)
                            zipf.write(file_path, rel_path)
                
                # Replace original with repaired version
                if os.path.exists(project_path):
                    os.remove(project_path)
                os.rename(temp_project_path, project_path)
                
                total_savings = sum(f["savings_mb"] for f in repaired_files)
                message = f"Successfully repaired project. Compressed {len(repaired_files)} annotation files, saved {total_savings:.1f} MB"
                logger.info(message)
                
                return True, message
                
        except Exception as e:
            error_msg = f"Failed to repair project: {e}"
            logger.exception(error_msg)
            return False, error_msg
    
    def _compress_annotation_file(self, file_path: str) -> Tuple[bool, int]:
        """Compress a single annotation file.
        
        Args:
            file_path: Path to the annotations.npz file
            
        Returns:
            Tuple of (success, new_file_size)
        """
        try:
            # Load the annotation data
            with np.load(file_path, allow_pickle=True) as data:
                # Extract all data from the file
                file_data = {key: data[key] for key in data.keys()}
            
            # Save with compression
            np.savez_compressed(file_path, **file_data)
            
            new_size = os.path.getsize(file_path)
            logger.info(f"Compressed {file_path}, new size: {new_size / (1024 * 1024):.1f} MB")
            
            return True, new_size
            
        except Exception as e:
            logger.error(f"Failed to compress {file_path}: {e}")
            return False, 0


def repair_project_cli(project_path: str, backup: bool = True) -> None:
    """Command-line interface for project repair."""
    repair_tool = ProjectRepairTool()
    
    print(f"Analyzing project: {project_path}")
    analysis = repair_tool.analyze_project(project_path)
    
    if "error" in analysis:
        print(f"Error: {analysis['error']}")
        return
    
    print(f"Project size: {analysis['total_size_mb']:.1f} MB")
    print(f"Large annotation files found: {len(analysis['large_files'])}")
    
    if analysis["needs_repair"]:
        print(f"Estimated savings: {analysis['compression_savings']:.1f} MB")
        
        response = input("Proceed with repair? (y/N): ")
        if response.lower() == 'y':
            success, message = repair_tool.repair_project(project_path, backup)
            print(message)
        else:
            print("Repair cancelled")
    else:
        print("Project does not need repair")


if __name__ == "__main__":
    import sys
    if len(sys.argv) < 2:
        print("Usage: python project_repair.py <project_path> [--no-backup]")
        sys.exit(1)
    
    project_path = sys.argv[1]
    backup = "--no-backup" not in sys.argv
    
    repair_project_cli(project_path, backup)
