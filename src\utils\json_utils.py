"""
JSON utility module that provides a unified interface for JSON operations.
Uses orjson for better performance when available, with fallback to standard json.
"""

try:
    import orjson
    ORJSON_AVAILABLE = True
except ImportError:
    import json
    ORJSON_AVAILABLE = False

import json as std_json  # Always import standard json for access to JSONDecodeError

# Re-export JSONDecodeError for consistent error handling
JSONDecodeError = std_json.JSONDecodeError

def loads(s):
    """Parse JSON string or bytes.

    Args:
        s: JSON string or bytes to parse

    Returns:
        Parsed Python object
    """
    if ORJSON_AVAILABLE:
        # orjson.loads accepts bytes or str
        return orjson.loads(s)
    return std_json.loads(s)

def dumps(obj, indent=None, sort_keys=False):
    """Serialize object to JSON string.

    Args:
        obj: Python object to serialize
        indent: Number of spaces for indentation (orjson only supports 2-space indentation)
        sort_keys: Whether to sort dictionary keys

    Returns:
        JSON string
    """
    if ORJSON_AVAILABLE:
        # Handle indentation and sorting options
        option = 0
        if indent is not None:
            option |= orjson.OPT_INDENT_2
        if sort_keys:
            option |= orjson.OPT_SORT_KEYS

        # orjson.dumps returns bytes, convert to str
        return orjson.dumps(obj, option=option).decode('utf-8')

    return std_json.dumps(obj, indent=indent, sort_keys=sort_keys)

def load(fp):
    """Load JSON from file object.

    Args:
        fp: File-like object to read from

    Returns:
        Parsed Python object
    """
    if ORJSON_AVAILABLE:
        return orjson.loads(fp.read())
    return std_json.load(fp)

def dump(obj, fp, indent=None, sort_keys=False):
    """Write JSON to file object.

    Args:
        obj: Python object to serialize
        fp: File-like object to write to
        indent: Number of spaces for indentation (orjson only supports 2-space indentation)
        sort_keys: Whether to sort dictionary keys
    """
    if ORJSON_AVAILABLE:
        # Handle indentation and sorting options
        option = 0
        if indent is not None:
            option |= orjson.OPT_INDENT_2
        if sort_keys:
            option |= orjson.OPT_SORT_KEYS

        # Write bytes to file - handle both text and binary modes
        json_bytes = orjson.dumps(obj, option=option)
        if hasattr(fp, 'mode') and 'b' in fp.mode:
            # Binary mode
            fp.write(json_bytes)
        else:
            # Text mode
            fp.write(json_bytes.decode('utf-8'))
    else:
        std_json.dump(obj, fp, indent=indent, sort_keys=sort_keys)
