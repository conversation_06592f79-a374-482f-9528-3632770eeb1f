# src/utils/coco_utils.py

import os
import datetime
import cv2
import numpy as np

# Use our custom JSON utility module
from src.utils import json_utils

def generate_coco_json(segmented_image, original_image_size, image_filename, image_full_path, save_dir, selected_labels):
    """Generates and saves COCO JSON annotations."""
    if segmented_image is None or original_image_size is None:
        return

    original_height, original_width = original_image_size
    image_height, image_width = segmented_image.shape[:2]

    base_filename, _ = os.path.splitext(image_filename) if image_filename else "segmented_image"
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    coco_filename = f"{base_filename}coco{timestamp}.json"

    coco_dict = {
        "images": [{
            "id": 1,
            "width": original_width,
            "height": original_height,
            "file_name": image_full_path if image_full_path else "segmented_image.png"
        }],
        "annotations": [],
        "categories": []
    }

    categories = {tuple(label): idx + 1 for idx, label in enumerate(selected_labels)}
    for label, category_id in categories.items():
        coco_dict["categories"].append({"id": category_id, "name": f"Label {category_id}"})

    annotation_id = 0
    for label in selected_labels:
        mask = np.all(segmented_image == label, axis=-1)
        if np.any(mask):
            mask = mask.astype(np.uint8)
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            for contour in contours:
                segmentation = contour.flatten().tolist()

                x_scale = original_width / image_width
                y_scale = original_height / image_height
                scaled_segmentation = [coord * (x_scale if i % 2 == 0 else y_scale)
                                        for i, coord in enumerate(segmentation)]

                x, y, w, h = cv2.boundingRect(contour)
                x = int(x * x_scale)
                y = int(y * y_scale)
                w = int(w * x_scale)
                h = int(h * y_scale)

                area = cv2.contourArea(contour)

                annotation = {
                    "id": annotation_id,
                    "image_id": 1,
                    "category_id": categories[tuple(label)],
                    "segmentation": [scaled_segmentation],
                    "area": float(area * x_scale * y_scale),  # Scaled area
                    "bbox": [x, y, w, h],
                    "iscrowd": 0
                }
                coco_dict["annotations"].append(annotation)
                annotation_id += 1

    file_path = os.path.join(save_dir, coco_filename)
    with open(file_path, 'w') as f:
        json_utils.dump(coco_dict, f, indent=4)