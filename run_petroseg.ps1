# PetroSEG V4 PowerShell Launcher
# This script ensures the application runs with the correct virtual environment

Write-Host "Starting PetroSEG V4..." -ForegroundColor Green
Write-Host ""

# Check if virtual environment exists
$venvPython = Join-Path $PSScriptRoot "venv\Scripts\python.exe"
if (-not (Test-Path $venvPython)) {
    Write-Host "ERROR: Virtual environment not found!" -ForegroundColor Red
    Write-Host "Please make sure the 'venv' folder exists in the current directory." -ForegroundColor Red
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

# Run the application with the virtual environment Python
Write-Host "Using virtual environment Python: $venvPython" -ForegroundColor Cyan
Write-Host ""

try {
    & $venvPython "main.py"
}
catch {
    Write-Host ""
    Write-Host "Application exited with an error: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
}
