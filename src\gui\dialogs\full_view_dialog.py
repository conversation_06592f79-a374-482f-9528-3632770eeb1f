# full_view_dialog.py
# Full View dialog for AI Assistant results with export functionality

import os
import logging
from datetime import datetime
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTextEdit, QPushButton, 
    QLabel, QFileDialog, QMessageBox, QSizePolicy, QFrame
)
from PySide6.QtGui import QFont, QIcon, QTextDocument
from PySide6.QtCore import Qt, QSize
from PySide6.QtPrintSupport import QPrinter, QPrintDialog

logger = logging.getLogger(__name__)

class FullViewDialog(QDialog):
    """Full view dialog for displaying AI Assistant results with export capabilities."""
    
    def __init__(self, content, title="AI Analysis Results", parent=None):
        super().__init__(parent)
        self.content = content
        self.title = title
        self.setWindowTitle(f"Full View - {title}")
        self.setModal(True)
        self.resize(900, 700)
        
        # Set window icon if available
        try:
            self.setWindowIcon(QIcon(":/icons/ai_icon.png"))
        except:
            pass
            
        self._init_ui()
        self._connect_signals()
        
    def _init_ui(self):
        """Initialize the user interface."""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # Header section
        header_layout = QHBoxLayout()
        
        # Title label
        title_label = QLabel(self.title)
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        self.title_label = title_label  # Store reference for theme styling
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Export buttons
        export_layout = QHBoxLayout()
        export_layout.setSpacing(5)
        
        self.export_html_btn = QPushButton("Export HTML")
        self.export_html_btn.setIcon(QIcon(":/icons/html_icon.png") if hasattr(QIcon, ":/icons/html_icon.png") else QIcon())
        self.export_html_btn.setToolTip("Export content as HTML file")
        self.export_html_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        export_layout.addWidget(self.export_html_btn)
        
        self.export_txt_btn = QPushButton("Export Text")
        self.export_txt_btn.setIcon(QIcon(":/icons/text_icon.png") if hasattr(QIcon, ":/icons/text_icon.png") else QIcon())
        self.export_txt_btn.setToolTip("Export content as plain text file")
        self.export_txt_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        export_layout.addWidget(self.export_txt_btn)
        
        self.print_btn = QPushButton("Print")
        self.print_btn.setIcon(QIcon(":/icons/print_icon.png") if hasattr(QIcon, ":/icons/print_icon.png") else QIcon())
        self.print_btn.setToolTip("Print the content")
        self.print_btn.setStyleSheet("""
            QPushButton {
                background-color: #8e44ad;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7d3c98;
            }
            QPushButton:pressed {
                background-color: #6c3483;
            }
        """)
        export_layout.addWidget(self.print_btn)
        
        header_layout.addLayout(export_layout)
        layout.addLayout(header_layout)
        
        # Separator line
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        self.separator = separator  # Store reference for theme styling
        layout.addWidget(separator)
        
        # Content display area
        self.content_display = QTextEdit()
        self.content_display.setReadOnly(True)
        self.content_display.setPlainText(self.content)
        
        # Set font for better readability
        content_font = QFont("Segoe UI", 11)
        self.content_display.setFont(content_font)
        
        # Detect theme and apply appropriate styling
        self._apply_theme_aware_styling()
        
        layout.addWidget(self.content_display, 1)  # Give it stretch factor
        
        # Bottom button layout
        bottom_layout = QHBoxLayout()
        bottom_layout.addStretch()
        
        self.close_btn = QPushButton("Close")
        self.close_btn.setDefault(True)
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
            QPushButton:pressed {
                background-color: #6c7b7d;
            }
        """)
        bottom_layout.addWidget(self.close_btn)
        
        layout.addLayout(bottom_layout)
        
    def _apply_theme_aware_styling(self):
        """Apply theme-aware styling to the content display area."""
        # Get the application's palette to detect theme
        from PySide6.QtWidgets import QApplication
        palette = QApplication.palette()
        
        # Check if we're in dark mode by comparing window background color
        window_color = palette.color(palette.ColorRole.Window)
        is_dark_theme = window_color.lightness() < 128
        
        if is_dark_theme:
            # Dark theme styling
            self.content_display.setStyleSheet("""
                QTextEdit {
                    border: 1px solid #555555;
                    border-radius: 6px;
                    padding: 15px;
                    background-color: #2b2b2b;
                    color: #ffffff;
                    selection-background-color: #3498db;
                    selection-color: white;
                }
            """)
            
            # Style title label for dark theme
            self.title_label.setStyleSheet("color: #ffffff; margin-bottom: 5px;")
            
            # Style separator for dark theme
            self.separator.setStyleSheet("color: #555555;")
        else:
            # Light theme styling
            self.content_display.setStyleSheet("""
                QTextEdit {
                    border: 1px solid #bdc3c7;
                    border-radius: 6px;
                    padding: 15px;
                    background-color: #ffffff;
                    color: #000000;
                    selection-background-color: #3498db;
                    selection-color: white;
                }
            """)
            
            # Style title label for light theme
            self.title_label.setStyleSheet("color: #2c3e50; margin-bottom: 5px;")
            
            # Style separator for light theme
            self.separator.setStyleSheet("color: #bdc3c7;")
        
    def _connect_signals(self):
        """Connect button signals to their handlers."""
        self.export_html_btn.clicked.connect(self._export_html)
        self.export_txt_btn.clicked.connect(self._export_text)
        self.print_btn.clicked.connect(self._print_content)
        self.close_btn.clicked.connect(self.accept)
        
    def _export_html(self):
        """Export content as HTML file."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"ai_analysis_results_{timestamp}.html"
            
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Export as HTML",
                default_filename,
                "HTML Files (*.html);;All Files (*)"
            )
            
            if file_path:
                # Create HTML content with proper formatting
                html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{self.title}</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }}
        .header {{
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }}
        .content {{
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            white-space: pre-wrap;
            word-wrap: break-word;
        }}
        .timestamp {{
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 20px;
            text-align: center;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>{self.title}</h1>
    </div>
    <div class="content">
{self.content}
    </div>
    <div class="timestamp">
        Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
    </div>
</body>
</html>
                """
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                    
                QMessageBox.information(
                    self,
                    "Export Successful",
                    f"Content exported successfully to:\n{file_path}"
                )
                logger.info(f"AI analysis results exported as HTML to: {file_path}")
                
        except Exception as e:
            logger.error(f"Error exporting HTML: {e}")
            QMessageBox.critical(
                self,
                "Export Error",
                f"Failed to export HTML file:\n{str(e)}"
            )
            
    def _export_text(self):
        """Export content as plain text file."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"ai_analysis_results_{timestamp}.txt"
            
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Export as Text",
                default_filename,
                "Text Files (*.txt);;All Files (*)"
            )
            
            if file_path:
                # Create formatted text content
                text_content = f"""
{self.title}
{'=' * len(self.title)}

Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

{'-' * 50}

{self.content}

{'-' * 50}

End of Analysis Results
                """
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(text_content)
                    
                QMessageBox.information(
                    self,
                    "Export Successful",
                    f"Content exported successfully to:\n{file_path}"
                )
                logger.info(f"AI analysis results exported as text to: {file_path}")
                
        except Exception as e:
            logger.error(f"Error exporting text: {e}")
            QMessageBox.critical(
                self,
                "Export Error",
                f"Failed to export text file:\n{str(e)}"
            )
            
    def _print_content(self):
        """Print the content."""
        try:
            printer = QPrinter()
            print_dialog = QPrintDialog(printer, self)
            
            if print_dialog.exec() == QPrintDialog.DialogCode.Accepted:
                # Create a document for printing
                document = QTextDocument()
                
                # Format content for printing
                formatted_content = f"""
<h1 style="color: #2c3e50; text-align: center;">{self.title}</h1>
<hr>
<p style="color: #7f8c8d; text-align: center; font-size: 0.9em;">
Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
</p>
<hr>
<div style="white-space: pre-wrap; font-family: 'Segoe UI', sans-serif; line-height: 1.6;">
{self.content}
</div>
                """
                
                document.setHtml(formatted_content)
                document.print(printer)
                
                logger.info("AI analysis results sent to printer")
                
        except Exception as e:
            logger.error(f"Error printing content: {e}")
            QMessageBox.critical(
                self,
                "Print Error",
                f"Failed to print content:\n{str(e)}"
            )
            
    def set_content(self, content):
        """Update the content displayed in the dialog."""
        self.content = content
        self.content_display.setPlainText(content)
        
    def get_content(self):
        """Get the current content."""
        return self.content