# Python
__pycache__
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.history

# Virtual Environment
venv/
env/
ENV/
.env
.venv
*.pt
*.pth
*.onnx

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Qt/PySide
*.qmlc
*.jsc
*.qrc
*.qm
*.ts
*.pro.user
*.pro.user.*
*.qbs.user
*.qbs.user.*
moc_*.cpp
qrc_*.cpp
ui_*.h
*.autosave

# Project specific
*.png
*.jpg
*.jpeg
*.bmp
*.tiff
*.gif
*.json
!requirements.txt

# Logs and databases
*.log
*.sqlite
*.db

# Coverage and testing
.coverage
.tox/
.pytest_cache/
htmlcov/
coverage.xml