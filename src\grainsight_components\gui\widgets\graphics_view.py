# gui/widgets/graphics_view.py

import logging
import os # Keep os for resource_path temporarily if icons loaded here
import sys # Keep sys for resource_path temporarily if icons loaded here
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtCore import Qt, QSize, QPoint, QRect, QPointF, QRectF, Signal, Slot
from PySide6.QtGui import QPixmap, QImage, QIcon, QPainter, QPen, QColor
from PySide6.QtWidgets import (QGraphicsView, QGraphicsScene, QGraphicsPixmapItem,
                               QGraphicsLineItem, QGraphicsPolygonItem, QGraphicsTextItem,
                               QGraphicsRectItem)


logger = logging.getLogger(__name__)

# Temporary resource_path if needed directly here (better in gui.utils)
def resource_path(relative_path):
    """ Get absolute path to resource, works for dev and for PyInstaller """
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        # Use location of this file if not frozen
        base_path = os.path.dirname(os.path.abspath(__file__))
        # Go up one level to get to the 'gui' directory base for assets
        base_path = os.path.join(base_path, '..', 'assets') # Adjust if assets path is different

    # Ensure the path exists before joining
    if not os.path.isdir(base_path):
         # Fallback if _MEIPASS is not defined and relative path doesn't work
         # This might happen if run directly without proper packaging setup
         alt_base_path = os.path.abspath(".") # Try project root relative
         if os.path.exists(os.path.join(alt_base_path, 'gui', 'assets', relative_path)):
              base_path = os.path.join(alt_base_path, 'gui', 'assets')
         else:
              # Last resort: assume assets relative to current working dir
              base_path = os.path.join(os.getcwd(), 'gui', 'assets')


    final_path = os.path.join(base_path, relative_path)
    # print(f"Requesting resource: {relative_path}, Base path: {base_path}, Final path: {final_path}") # Debugging
    return final_path


class CustomPixmapItem(QGraphicsPixmapItem):
    """Custom QGraphicsPixmapItem for smooth transformations."""
    def __init__(self, pixmap, parent=None):
        super().__init__(pixmap, parent)
        self.setTransformationMode(Qt.SmoothTransformation)

from typing import Optional

class CustomGraphicsView(QGraphicsView):
    """
    Custom QGraphicsView supporting zoom, pan, scale line drawing, and item selection.
    """
    # Signal emitted when scale line drawing is finished (start_scene_pos, end_scene_pos)
    scale_line_drawn = Signal(QPointF, QPointF)
    # Signal for mouse clicks in selection mode (scene_pos)
    scene_clicked = Signal(QPointF)
    # Signal emitted when zoom level changes (current_scale_factor)
    zoom_changed = Signal(float)
    # Signal emitted when view transform changes (e.g., zoom, pan)
    transform_changed = Signal()

    MODE_SELECTION = 'selection'
    MODE_PAN = 'pan'
    MODE_ZOOM = 'zoom' # Conceptually, zoom is via wheel, but mode can set cursor
    MODE_SCALE = 'scale'

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setRenderHint(QPainter.Antialiasing)
        self.setRenderHint(QPainter.SmoothPixmapTransform)
        self.setRenderHint(QPainter.TextAntialiasing)

        self.setDragMode(QGraphicsView.NoDrag)
        self.setTransformationAnchor(QGraphicsView.AnchorUnderMouse)
        self.setResizeAnchor(QGraphicsView.AnchorUnderMouse)
        self.setInteractive(True) # Make sure interactions are enabled

        self._scale_factor = 1.0  # Internal tracking of view scale relative to base
        self.zoom_factor_base = 1.15 # Zoom multiplier
        self.min_zoom_level_steps = -10 # Limit zoom out steps
        self.max_zoom_level_steps = 15  # Limit zoom in steps
        self._zoom_level_steps = 0      # Current zoom level counter

        self._pan_start_pos = None  # Store the last mouse position for panning (in scene coords)
        self._is_panning = False
        self.mode = self.MODE_SELECTION # Default mode

        # Scale Line Drawing state
        self.scale_line_start = None
        self.scale_line_end = None
        self.scale_line_item = None # The QGraphicsLineItem for drawing/display

        # Load cursors once
        try:
            self._zoom_cursor = QIcon(resource_path("icons/zoom.png")).pixmap(QSize(24, 24))
            self._open_hand_cursor = Qt.OpenHandCursor
            self._closed_hand_cursor = Qt.ClosedHandCursor
            self._cross_cursor = Qt.CrossCursor
            self._arrow_cursor = Qt.ArrowCursor
        except Exception as e:
             logger.error(f"Failed to load cursors: {e}. Using default cursors.")
             # Fallback cursors
             self._zoom_cursor = Qt.CrossCursor # Fallback zoom cursor
             self._open_hand_cursor = Qt.OpenHandCursor
             self._closed_hand_cursor = Qt.ClosedHandCursor
             self._cross_cursor = Qt.CrossCursor
             self._arrow_cursor = Qt.ArrowCursor

        self.set_mode(self.MODE_SELECTION) # Set initial cursor

    def wheelEvent(self, event: QtGui.QWheelEvent):
        """Handles mouse wheel events for zooming."""
        old_scene_pos = self.mapToScene(event.position().toPoint())

        delta = event.angleDelta().y()
        zoom_in = delta > 0

        if zoom_in and self._zoom_level_steps < self.max_zoom_level_steps:
            factor = self.zoom_factor_base
            self._zoom_level_steps += 1
        elif not zoom_in and self._zoom_level_steps > self.min_zoom_level_steps:
            factor = 1.0 / self.zoom_factor_base
            self._zoom_level_steps -= 1
        else:
            event.accept() # Consume event even if no zoom happens
            return # No zoom if limits are reached

        # Apply scaling
        self.scale(factor, factor)
        self._scale_factor *= factor # Update internal scale tracker

        # Get the mouse position in scene coordinates after scaling
        new_scene_pos = self.mapToScene(event.position().toPoint())

        # Calculate the difference and translate the view to keep the point under the mouse
        delta_pos = new_scene_pos - old_scene_pos
        # self.translate(delta_pos.x(), delta_pos.y()) # Translate seems redundant with AnchorUnderMouse

        logger.debug(f"Zoom level steps: {self._zoom_level_steps}, Scale factor: {self._scale_factor:.3f}")
        self.zoom_changed.emit(self._scale_factor)
        self.transform_changed.emit()  # Emit transform changed signal
        self.update_scale_line_thickness() # Adjust line thickness on zoom
        event.accept() # Indicate event was handled


    def mousePressEvent(self, event: QtGui.QMouseEvent):
        """Handles mouse press events based on the current mode."""
        scene_pos = self.mapToScene(event.pos())

        if self.mode == self.MODE_PAN and event.button() == Qt.LeftButton:
            self._pan_start_pos = scene_pos
            self._is_panning = True
            self.setCursor(self._closed_hand_cursor)
            event.accept()
            logger.debug("Panning started.")

        elif self.mode == self.MODE_SCALE and event.button() == Qt.LeftButton:
            self.scale_line_start = scene_pos
            self.scale_line_end = scene_pos # Initialize end point
            # Remove existing temporary line if any
            self.clear_scale_line_item(temporary_only=True)
            # Create new temporary line
            pen = QPen(Qt.red, self.get_dynamic_pen_width(2), Qt.DashLine)
            pen.setCosmetic(True)
            self.scale_line_item = QGraphicsLineItem()
            self.scale_line_item.setPen(pen)
            self.scale_line_item.setLine(self.scale_line_start.x(), self.scale_line_start.y(),
                                         self.scale_line_end.x(), self.scale_line_end.y())
            if self.scene():
                 self.scene().addItem(self.scale_line_item)
            self.setCursor(self._cross_cursor)
            event.accept()
            logger.debug(f"Scale line started at {self.scale_line_start}")

        elif self.mode == self.MODE_SELECTION and event.button() == Qt.LeftButton:
             # Emit a signal for the main app to handle selection logic
             self.scene_clicked.emit(scene_pos)
             # Don't call super(), let the main app handle selection visualization
             event.accept()

        else:
            # Allow default behavior for other modes or buttons (e.g., context menu)
             super().mousePressEvent(event)


    def mouseMoveEvent(self, event: QtGui.QMouseEvent):
        """Handles mouse move events for panning and drawing."""
        scene_pos = self.mapToScene(event.pos())

        if self.mode == self.MODE_PAN and self._is_panning:
            if self._pan_start_pos is None: return # Should not happen if is_panning is True
            delta = scene_pos - self._pan_start_pos
            # Translate the view (opposite direction of mouse movement relative to viewport)
            # Getting the translation right for AnchorUnderMouse can be tricky.
            # A simpler way is often to use ScrollHandDrag mode.
            # For manual panning with NoDrag:
            vsb = self.verticalScrollBar()
            hsb = self.horizontalScrollBar()
            vsb.setValue(vsb.value() - int(delta.y())) # Invert Y delta for scroll bar
            hsb.setValue(hsb.value() - int(delta.x())) # Invert X delta for scroll bar
            # We need to update the start pos to the *new* scene pos corresponding
            # to the *same* viewport pixel, otherwise panning accelerates.
            # Alternatively, calculate delta based on viewport coords:
            # viewport_delta = event.pos() - self.mapFromScene(self._pan_start_pos)
            # self.translate(viewport_delta.x(), viewport_delta.y()) # This is likely wrong too

            # Let's stick to the scrollbar method which is usually more reliable for NoDrag manual pan
            # Update pan_start_pos to the current position for continuous panning feel
            self._pan_start_pos = scene_pos
            # Emit transform changed signal when panning
            self.transform_changed.emit()
            event.accept()
            # logger.debug("Panning...") # Can be very verbose

        elif self.mode == self.MODE_SCALE and self.scale_line_start is not None and self.scale_line_item:
            self.scale_line_end = scene_pos
            self.scale_line_item.setLine(self.scale_line_start.x(), self.scale_line_start.y(),
                                         self.scale_line_end.x(), self.scale_line_end.y())
            event.accept()
            # logger.debug(f"Drawing scale line to {self.scale_line_end}")

        else:
            super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event: QtGui.QMouseEvent):
        """Handles mouse release events."""
        scene_pos = self.mapToScene(event.pos())

        if self.mode == self.MODE_PAN and event.button() == Qt.LeftButton and self._is_panning:
            self._is_panning = False
            self._pan_start_pos = None
            self.setCursor(self._open_hand_cursor if self.mode == self.MODE_PAN else self._arrow_cursor)
            event.accept()
            logger.debug("Panning stopped.")

        elif self.mode == self.MODE_SCALE and event.button() == Qt.LeftButton and self.scale_line_start:
            # Finalize scale line end point
            self.scale_line_end = scene_pos
            if self.scale_line_item:
                # Make the line solid and potentially slightly thicker on release
                pen = QPen(Qt.red, self.get_dynamic_pen_width(3), Qt.SolidLine)
                pen.setCosmetic(True)
                self.scale_line_item.setPen(pen)
                # Emit the signal with start and end points
                self.scale_line_drawn.emit(self.scale_line_start, self.scale_line_end)
                logger.debug(f"Scale line finished: {self.scale_line_start} -> {self.scale_line_end}")

                # Set scale_line_start to None to prevent further updates in mouseMoveEvent
                self.scale_line_start = None

                # Optional: Switch back to selection mode automatically? (Current code does this in main_window)
                # self.set_mode(self.MODE_SELECTION)

            self.setCursor(self._cross_cursor) # Keep cross cursor while in scale mode
            event.accept()

        else:
            super().mouseReleaseEvent(event)

    def get_dynamic_pen_width(self, base_width: int) -> float:
        """Calculates pen width based on zoom (simple version)."""
        # This is often handled better by cosmetic pens, but can be used for non-cosmetic.
        # return max(1, base_width / self._scale_factor)
        return float(base_width) # Rely on cosmetic pen property

    def update_scale_line_thickness(self):
        """Updates the pen thickness of the existing scale line if needed (for cosmetic pens)."""
        if self.scale_line_item:
             # For cosmetic pens, thickness is in device-independent pixels,
             # but if we want appearance to scale slightly, we might adjust base width.
             # Or just ensure the pen is cosmetic.
             pen = self.scale_line_item.pen()
             # pen.setWidthF(self.get_dynamic_pen_width(int(pen.widthF()))) # Example if not cosmetic
             pen.setCosmetic(True) # Ensure it stays cosmetic
             self.scale_line_item.setPen(pen)


    def set_mode(self, mode: str):
        """Sets the interaction mode and updates cursor."""
        logger.debug(f"Switching view mode to: {mode}")

        # Store previous mode for debugging
        previous_mode = self.mode
        self.mode = mode

        # Clear temporary drawing line if leaving scale mode
        if mode != self.MODE_SCALE and self.scale_line_item and self.scale_line_item.pen().style() == Qt.DashLine:
             self.clear_scale_line_item(temporary_only=True) # Remove dashed line used for drawing
             # Reset scale line start to prevent issues when switching back to scale mode
             self.scale_line_start = None

        # Set cursor and drag mode based on the new mode
        if mode == self.MODE_PAN:
            self.setDragMode(QGraphicsView.NoDrag) # Use manual panning
            self.setCursor(self._open_hand_cursor)
            logger.debug(f"Mode changed from {previous_mode} to {mode}, cursor set to open hand")
        elif mode == self.MODE_SCALE:
            self.setDragMode(QGraphicsView.NoDrag)
            self.setCursor(self._cross_cursor)
            logger.debug(f"Mode changed from {previous_mode} to {mode}, cursor set to cross")
            # If entering scale mode and we have previous points, optionally redraw solid line
            self.redraw_persistent_scale_line()
        elif mode == self.MODE_ZOOM: # Zoom is handled by wheel, cursor can be magnifying glass
            self.setDragMode(QGraphicsView.NoDrag)
            self.setCursor(QtGui.QCursor(self._zoom_cursor)) # Use loaded zoom cursor
            logger.debug(f"Mode changed from {previous_mode} to {mode}, cursor set to zoom")
        elif mode == self.MODE_SELECTION:
            self.setDragMode(QGraphicsView.NoDrag) # Important for custom click handling
            self.setCursor(self._arrow_cursor)
            logger.debug(f"Mode changed from {previous_mode} to {mode}, cursor set to arrow")
        else: # Fallback
            self.setDragMode(QGraphicsView.NoDrag)
            self.setCursor(self._arrow_cursor)
            logger.debug(f"Mode changed from {previous_mode} to unknown mode {mode}, cursor set to arrow")

        # Force update to ensure cursor changes take effect
        self.viewport().update()

    def redraw_persistent_scale_line(self):
        """Redraws the solid scale line if start/end points exist."""
        self.clear_scale_line_item() # Clear any existing line first
        if self.scale_line_start is not None and self.scale_line_end is not None:
            pen = QPen(Qt.red, self.get_dynamic_pen_width(3), Qt.SolidLine)
            pen.setCosmetic(True)
            self.scale_line_item = QGraphicsLineItem()
            self.scale_line_item.setPen(pen)
            self.scale_line_item.setLine(self.scale_line_start.x(), self.scale_line_start.y(),
                                         self.scale_line_end.x(), self.scale_line_end.y())
            if self.scene():
                self.scene().addItem(self.scale_line_item)
            logger.debug("Redrew persistent scale line.")

    def set_scale_line_points(self, start_point: Optional[QPointF], end_point: Optional[QPointF]):
        """Externally set the start and end points for the scale line (e.g., when loading)."""
        self.scale_line_start = start_point
        self.scale_line_end = end_point
        # If currently in scale mode, redraw the line immediately
        if self.mode == self.MODE_SCALE:
            self.redraw_persistent_scale_line()
        else:
            self.clear_scale_line_item() # Ensure no line is shown if not in scale mode

    def clear_scale_line_item(self, temporary_only=False):
        """Removes the scale line item from the scene."""
        if self.scale_line_item:
            # Only remove if it's temporary or if not temporary_only
            is_temporary = self.scale_line_item.pen().style() == Qt.DashLine
            if not temporary_only or is_temporary:
                 if self.scale_line_item.scene():
                     self.scene().removeItem(self.scale_line_item)
                 self.scale_line_item = None
                 # logger.debug("Cleared scale line item.")


    def reset_view(self):
         """Resets zoom, pan, and potentially clears temporary items."""
         logger.info("Resetting graphics view.")
         # Check if scene and items exist before fitting
         if self.scene() and self.scene().items():
              original_rect = self.scene().itemsBoundingRect()
              self.fitInView(original_rect, Qt.KeepAspectRatio)
              # Store the initial scale factor after fitInView
              self._scale_factor = self.transform().m11()
         else:
             # If no items, just reset transform
             self.resetTransform()
             self._scale_factor = 1.0

         self._zoom_level_steps = 0 # Reset zoom steps count
         self._is_panning = False
         self.set_mode(self.MODE_SELECTION) # Reset to default mode

         # Clear scale line completely on full view reset? User expectation dependent.
         # Let's keep the points but clear the visual item for now.
         self.clear_scale_line_item()
         # self.scale_line_start = None
         # self.scale_line_end = None

         self.zoom_changed.emit(self._scale_factor)


    def get_current_scale(self) -> float:
        """Returns the current view scaling factor."""
        # Use transform matrix m11 for horizontal scale factor
        # return self.transform().m11()
        # Or use internal tracker (might be slightly off if resetTransform used)
        return self._scale_factor