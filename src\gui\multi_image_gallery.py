# src/gui/multi_image_gallery.py

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QGridLayout, QLabel,
                              QPushButton, QScrollArea, QWidget, QFileDialog,
                              QHBoxLayout, QFrame, QSplitter, QMessageBox,
                              QProgressBar)
from PySide6.QtCore import Qt, Signal, QSize
from PySide6.QtGui import QPixmap, QIcon
from src.utils.image_utils import resize_image, convert_cvimage_to_qpixmap
from src.gui.preprocessing_dialog import PreprocessingDialog
import cv2
import os

class ImageThumbnail(QFrame):
    """Enhanced thumbnail widget for displaying image in gallery."""
    clicked = Signal(object, str)

    def __init__(self, image, name, parent=None):
        super().__init__(parent)
        self.image = image
        self.name = name
        self.setFixedSize(200, 220)
        self.setFrameShape(QFrame.StyledPanel)
        self.setFrameShadow(QFrame.Raised)

        # Create layout for thumbnail and label
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # Image thumbnail
        self.img_label = QLabel()
        self.img_label.setFixedSize(180, 180)
        self.img_label.setAlignment(Qt.AlignCenter)
        self.update_thumbnail()
        layout.addWidget(self.img_label)

        # Image name label
        self.name_label = QLabel(name)
        self.name_label.setAlignment(Qt.AlignCenter)
        self.name_label.setWordWrap(True)
        self.name_label.setStyleSheet("font-size: 9pt;")
        layout.addWidget(self.name_label)

        self.setStyleSheet("""
            ImageThumbnail {
                border: 2px solid #ccc;
                border-radius: 5px;
                margin: 5px;
                background: #f5f5f5;
            }
            ImageThumbnail:hover {
                border-color: #3498db;
                background: #e0e0e0;
            }
        """)

    def update_thumbnail(self):
        """Updates the thumbnail with the current image."""
        thumbnail = resize_image(self.image.copy(), (170, 170))
        self.img_label.setPixmap(convert_cvimage_to_qpixmap(thumbnail))

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.clicked.emit(self.image, self.name)
            self.setStyleSheet("""
                ImageThumbnail {
                    border: 2px solid #3498db;
                    border-radius: 5px;
                    margin: 5px;
                    background: #e0e0e0;
                }
            """)

class MultiImageGallery(QDialog):
    """Enhanced gallery dialog for handling multiple images."""
    images_selected = Signal(list, list)  # Signal emitting images and filenames

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Image Gallery")
        self.resize(900, 600)
        self.images = []
        self.filenames = []
        self.thumbnails = []
        self.selected_index = -1

        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(10)

        # Top toolbar
        toolbar = QHBoxLayout()
        toolbar.setContentsMargins(0, 0, 0, 10)

        # Toolbar starts with Clear button

        # Clear button
        clear_button = QPushButton("Clear All")
        clear_button.setStyleSheet("padding: 8px 15px;")
        clear_button.clicked.connect(self.clear_gallery)
        toolbar.addWidget(clear_button)

        # Preprocessing button
        preprocess_button = QPushButton("Preprocess Selected")
        preprocess_button.setStyleSheet("padding: 8px 15px;")
        preprocess_button.clicked.connect(self.preprocess_selected)
        toolbar.addWidget(preprocess_button)

        # Batch preprocess button
        batch_button = QPushButton("Batch Preprocess")
        batch_button.setStyleSheet("padding: 8px 15px;")
        batch_button.clicked.connect(self.batch_preprocess)
        toolbar.addWidget(batch_button)

        # Apply button
        apply_button = QPushButton("Apply Selected")
        apply_button.setStyleSheet("padding: 8px 15px; background-color: #2ecc71; color: white;")
        apply_button.clicked.connect(self.apply_selected)
        toolbar.addWidget(apply_button)

        main_layout.addLayout(toolbar)

        # Splitter for gallery and preview
        splitter = QSplitter(Qt.Horizontal)

        # Left side - Gallery
        gallery_widget = QWidget()
        gallery_layout = QVBoxLayout(gallery_widget)
        gallery_layout.setContentsMargins(0, 0, 0, 0)

        # Gallery label
        gallery_label = QLabel("Image Gallery")
        gallery_label.setStyleSheet("font-size: 12pt; font-weight: bold; margin-bottom: 5px;")
        gallery_layout.addWidget(gallery_label)

        # Scroll area for thumbnails
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setStyleSheet("QScrollArea { border: 1px solid #ccc; border-radius: 5px; }")

        # Container for thumbnails
        self.gallery_container = QWidget()
        self.gallery_grid = QGridLayout(self.gallery_container)
        self.gallery_grid.setSpacing(10)
        self.gallery_grid.setContentsMargins(10, 10, 10, 10)
        scroll.setWidget(self.gallery_container)
        gallery_layout.addWidget(scroll)

        # Right side - Preview
        preview_widget = QWidget()
        preview_layout = QVBoxLayout(preview_widget)
        preview_layout.setContentsMargins(10, 0, 0, 0)

        # Preview label
        preview_label = QLabel("Image Preview")
        preview_label.setStyleSheet("font-size: 12pt; font-weight: bold; margin-bottom: 5px;")
        preview_layout.addWidget(preview_label)

        # Preview image
        self.preview_frame = QFrame()
        self.preview_frame.setFrameShape(QFrame.StyledPanel)
        self.preview_frame.setFrameShadow(QFrame.Sunken)
        self.preview_frame.setStyleSheet("background-color: #f0f0f0; border-radius: 5px;")
        preview_frame_layout = QVBoxLayout(self.preview_frame)

        self.preview_image = QLabel("No image selected")
        self.preview_image.setAlignment(Qt.AlignCenter)
        self.preview_image.setMinimumSize(400, 350)
        preview_frame_layout.addWidget(self.preview_image)

        preview_layout.addWidget(self.preview_frame)

        # Image info
        self.image_info = QLabel("")
        self.image_info.setStyleSheet("font-family: monospace; margin-top: 10px;")
        preview_layout.addWidget(self.image_info)

        # Add widgets to splitter
        splitter.addWidget(gallery_widget)
        splitter.addWidget(preview_widget)
        splitter.setSizes([300, 600])

        main_layout.addWidget(splitter)

        # Status bar
        status_bar = QHBoxLayout()
        self.status_label = QLabel("Ready")
        status_bar.addWidget(self.status_label)

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_bar.addWidget(self.progress_bar)

        main_layout.addLayout(status_bar)

    # upload_images method removed - images are now only loaded from project hub

    def add_image(self, image, name, file_path=None):
        """Adds an image to the gallery."""
        self.images.append(image)
        self.filenames.append(file_path if file_path else name)

        thumbnail = ImageThumbnail(image, name)
        thumbnail.clicked.connect(self.show_preview)
        self.thumbnails.append(thumbnail)

        # Calculate position in grid
        count = len(self.images) - 1
        row = count // 4
        col = count % 4

        self.gallery_grid.addWidget(thumbnail, row, col)

    def show_preview(self, image, name):
        """Shows a preview of the selected image."""
        # Find the index of the image
        try:
            self.selected_index = self.images.index(image)

            # Reset all thumbnail styles
            for thumbnail in self.thumbnails:
                thumbnail.setStyleSheet("""
                    ImageThumbnail {
                        border: 2px solid #ccc;
                        border-radius: 5px;
                        margin: 5px;
                        background: #f5f5f5;
                    }
                    ImageThumbnail:hover {
                        border-color: #3498db;
                        background: #e0e0e0;
                    }
                """)

            # Highlight selected thumbnail
            self.thumbnails[self.selected_index].setStyleSheet("""
                ImageThumbnail {
                    border: 2px solid #3498db;
                    border-radius: 5px;
                    margin: 5px;
                    background: #e0e0e0;
                }
            """)

            # Display preview
            preview = resize_image(image.copy(), (400, 350))
            self.preview_image.setPixmap(convert_cvimage_to_qpixmap(preview))

            # Update info
            height, width = image.shape[:2]
            channels = 1 if len(image.shape) == 2 else image.shape[2]
            size_kb = image.nbytes / 1024

            info_text = f"Name: {name}\n"
            info_text += f"Dimensions: {width} × {height} pixels\n"
            info_text += f"Channels: {channels}\n"
            info_text += f"Size: {size_kb:.1f} KB"

            self.image_info.setText(info_text)

        except ValueError:
            pass

    def preprocess_selected(self):
        """Opens preprocessing dialog for the selected image."""
        if self.selected_index < 0 or self.selected_index >= len(self.images):
            QMessageBox.warning(self, "Warning", "Please select an image first.")
            return

        image = self.images[self.selected_index]
        dialog = PreprocessingDialog(image, self)
        dialog.setup_variables()  # Initialize variables before preview

        if dialog.exec() == QDialog.Accepted:
            processed_image = dialog.get_result_image()
            if processed_image is not None:
                # Update the image in our list
                self.images[self.selected_index] = processed_image

                # Update the thumbnail
                self.thumbnails[self.selected_index].image = processed_image
                self.thumbnails[self.selected_index].update_thumbnail()

                # Update the preview
                self.show_preview(processed_image, self.thumbnails[self.selected_index].name)

                QMessageBox.information(self, "Success", "Image preprocessing applied successfully!")

    def batch_preprocess(self):
        """Applies the same preprocessing to all images."""
        if not self.images:
            QMessageBox.warning(self, "Warning", "No images in gallery.")
            return

        # Use the first image to set up preprocessing parameters
        dialog = PreprocessingDialog(self.images[0], self)
        if dialog.exec() == QDialog.Accepted:
            # Get the preprocessing parameters
            brightness = dialog.brightness_var.value()
            contrast = dialog.contrast_var.value()
            saturation = dialog.saturation_var.value()
            gamma = dialog.gamma_var.value()
            sharpness = dialog.sharpness_var.value()
            blur = dialog.blur_var.value()

            # Show progress bar
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, len(self.images))
            self.progress_bar.setValue(0)

            # Apply to all images
            for i, image in enumerate(self.images):
                from src.utils.image_utils import apply_preprocessing_advanced
                processed = apply_preprocessing_advanced(
                    image.copy(),
                    brightness, contrast, saturation,
                    gamma, sharpness, blur
                )
                self.images[i] = processed
                self.thumbnails[i].image = processed
                self.thumbnails[i].update_thumbnail()
                self.progress_bar.setValue(i + 1)

            # Hide progress bar and update status
            self.progress_bar.setVisible(False)
            self.status_label.setText(f"Batch preprocessing applied to {len(self.images)} images")
            QMessageBox.information(self, "Success", "Batch preprocessing completed successfully!")

    def clear_gallery(self):
        """Clears all images from the gallery."""
        if not self.images:
            return

        reply = QMessageBox.question(
            self, "Confirm Clear", "Are you sure you want to clear all images?",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Clear data
            self.images = []
            self.filenames = []
            self.thumbnails = []
            self.selected_index = -1

            # Clear UI
            for i in reversed(range(self.gallery_grid.count())):
                widget = self.gallery_grid.itemAt(i).widget()
                if widget is not None:
                    widget.setParent(None)

            # Reset preview
            self.preview_image.setPixmap(QPixmap())
            self.preview_image.setText("No image selected")
            self.image_info.setText("")

            # Update status
            self.status_label.setText("Gallery cleared")

    def apply_selected(self):
        """Applies the selected image to the main application."""
        if self.selected_index < 0 or self.selected_index >= len(self.images):
            QMessageBox.warning(self, "Warning", "Please select an image first.")
            return

        # Emit signal with selected image
        selected_image = self.images[self.selected_index]
        selected_filename = self.filenames[self.selected_index]

        # Emit signal with the selected image and filename
        self.images_selected.emit([selected_image], [selected_filename])
        self.accept()  # Close dialog