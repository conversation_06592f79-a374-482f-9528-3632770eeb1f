# src/gemini/gemini_worker.py
# Handles interaction with Gemini API or local Gemini models

import google.generativeai as genai
import os
import logging
import time

# Use our custom JSON utility module
from src.utils import json_utils
from PySide6.QtCore import QThread, Signal
# from PySide6.QtGui import QImage # QImage seems unused here, can be removed if not needed elsewhere
from PIL import Image
import io

logger = logging.getLogger(__name__)

class GeminiWorker(QThread):
    """Handles communication with the Gemini API in a separate thread."""
    # Signals
    result_ready = Signal(str)
    structured_data_ready = Signal(list)
    bounding_boxes_ready = Signal(list)
    error_occurred = Signal(str)
    status_update = Signal(str)
    finished = Signal()

    def __init__(self, api_key, model_name="gemini-2.0-flash", parent=None):
        super().__init__(parent)
        self.api_key = api_key # Initial API key
        self.model_name = model_name
        self.image_path = None
        self.prompt = None
        self.request_json = False
        self._is_cancelled = False
        self._timeout = 60  # Default timeout in seconds

    def set_api_key(self, api_key):
        """Updates the API key to be used for the next request."""
        if not api_key or len(api_key.strip()) == 0:
            logger.warning("Empty API key provided to GeminiWorker")
        else:
            logger.info(f"GeminiWorker API key updated: {api_key[:5]}...{api_key[-5:] if len(api_key) > 10 else ''}")
        self.api_key = api_key

    def set_task(self, image_path, prompt, request_json=False, timeout=60):
        """Sets the image path and prompt for the analysis.

        Args:
            image_path: Path to the image file
            prompt: Text prompt for the analysis
            request_json: Whether to request JSON output
            timeout: Timeout in seconds (default: 60)
        """
        logger.info(f"Setting task - Image path: {image_path}, Prompt length: {len(prompt)}, JSON: {request_json}, Timeout: {timeout}s")
        self.image_path = image_path
        self.prompt = prompt
        self.request_json = request_json
        self._timeout = timeout
        self._is_cancelled = False # Reset cancellation flag for new task
        logger.info("Task set successfully")

    def cancel(self):
        """Cancels the current task."""
        self._is_cancelled = True
        self.status_update.emit("Cancellation requested...")

    def reset(self):
        """Resets the worker to a clean state with optimized timeout."""
        logger.info("Resetting worker")
        self._is_cancelled = True
        if self.isRunning():
            logger.info("Worker is running, waiting for it to finish")
            self.wait(500)  # Wait for 0.5 second (reduced from 1)
            if self.isRunning():
                logger.warning("Worker is still running after wait, terminating")
                self.terminate()
                self.wait(300)  # Wait for termination (reduced from default)
        logger.info("Worker reset complete")

    def run(self):
        """Executes the Gemini API call."""
        logger.info(f"GeminiWorker thread started with model: {self.model_name}")

        # --- API Key Check ---
        # Use the potentially updated self.api_key
        if not self.api_key:
            logger.error("Gemini API key is not configured")
            self.error_occurred.emit("Gemini API key is not configured.")
            self.finished.emit()
            return

        # --- Input Validation ---
        logger.info("Validating input parameters")
        if not self.image_path or not os.path.exists(self.image_path):
            logger.error(f"Image path is invalid or file not found: {self.image_path}")
            self.error_occurred.emit(f"Image path is invalid or file not found: {self.image_path}")
            self.finished.emit()
            return

        if not self.prompt:
            logger.error("Prompt cannot be empty")
            self.error_occurred.emit("Prompt cannot be empty.")
            self.finished.emit()
            return

        logger.info("Input validation successful")

        # --- Cancellation Check (Early) ---
        if self._is_cancelled:
             self.status_update.emit("Analysis cancelled before starting.")
             self.finished.emit()
             return

        try:
            # --- API Configuration ---
            self.status_update.emit("Configuring Gemini client...")
            logger.info(f"Configuring Gemini API with key: {self.api_key[:5]}...{self.api_key[-5:] if len(self.api_key) > 10 else ''}")
            # Ensure configuration uses the current self.api_key
            try:
                if not self.api_key or len(self.api_key.strip()) == 0:
                    raise ValueError("API key is empty or not set")
                genai.configure(api_key=self.api_key)
                logger.info("Gemini API configured successfully")

                # List available models
                try:
                    models = genai.list_models()
                    available_models = [model.name for model in models]
                    logger.info(f"Available models: {available_models}")

                    # Check if our model is available
                    model_found = False
                    for model in models:
                        if self.model_name in model.name:
                            model_found = True
                            self.model_name = model.name  # Use the full model name
                            logger.info(f"Using model: {self.model_name}")
                            break

                    if not model_found:
                        logger.warning(f"Model {self.model_name} not found in available models")
                        # Try to find a suitable alternative
                        for model in models:
                            if "gemini" in model.name.lower() and ("flash" in model.name.lower() or "vision" in model.name.lower()):
                                self.model_name = model.name
                                logger.info(f"Using alternative model: {self.model_name}")
                                break
                except Exception as e:
                    logger.warning(f"Error listing models: {e}")
                    # Continue with the default model
            except Exception as e:
                logger.error(f"Error configuring Gemini API: {e}")
                self.error_occurred.emit(f"Error configuring Gemini API: {e}")
                self.finished.emit()
                return

            # --- Image Loading ---
            self.status_update.emit(f"Loading image: {os.path.basename(self.image_path)}")
            logger.info(f"Loading image from path: {self.image_path}")
            try:
                img = Image.open(self.image_path)
                img.load()
                img_format = img.format.lower() if img.format else '' # Handle cases where format might be None
                logger.info(f"Image loaded successfully: {img.width}x{img.height}, format: {img_format}")
                # Supported MIME types directly by Gemini API (common ones)
                supported_formats = {
                    'png': 'image/png',
                    'jpeg': 'image/jpeg',
                    'jpg': 'image/jpeg', # Alias for jpeg
                    'webp': 'image/webp',
                    'heic': 'image/heic',
                    'heif': 'image/heif'
                 }

                if img_format in supported_formats:
                     mime_type = supported_formats[img_format]
                     # Get bytes in the original supported format
                     byte_arr = io.BytesIO()
                     # Pillow needs 'JPEG' not 'jpeg' for saving format string
                     save_format = 'JPEG' if img_format == 'jpg' else img_format.upper()
                     img.save(byte_arr, format=save_format)
                     img_bytes = byte_arr.getvalue()
                     logger.debug(f"Using original image format: {mime_type}")
                else:
                    # If format is not directly supported or unknown, convert to PNG
                    logger.warning(f"Unsupported/Unknown image format '{img_format}'. Converting to PNG.")
                    mime_type = 'image/png'
                    byte_arr = io.BytesIO()
                    img.save(byte_arr, format='PNG')
                    img_bytes = byte_arr.getvalue()

                # Create the image part using the dictionary format (works with all versions)
                logger.info("Using dictionary format for image part")
                image_part = {"mime_type": mime_type, "data": img_bytes}
                logger.info(f"Successfully created image part with mime type: {mime_type}")
                logger.info(f"Image bytes size: {len(img_bytes)} bytes")

                img.close() # Close the PIL image object

            except Exception as e:
                logger.exception("Failed to load or process image:") # Log traceback
                self.error_occurred.emit(f"Failed to load or process image: {e}")
                self.finished.emit()
                return

            # --- Cancellation Check ---
            if self._is_cancelled:
                self.status_update.emit("Analysis cancelled before sending request.")
                self.finished.emit()
                return

            # --- Request Preparation ---
            self.status_update.emit("Preparing request...")
            logger.info(f"Creating GenerativeModel with model name: {self.model_name}")
            try:
                # Try to create the model with the specified name
                try:
                    # Remove the "models/" prefix if it exists
                    model_name = self.model_name
                    if model_name.startswith("models/"):
                        model_name = model_name.replace("models/", "")
                        self.model_name = model_name

                    logger.info(f"Trying to create model with name: {self.model_name}")
                    model = genai.GenerativeModel(self.model_name)
                    logger.info(f"Successfully created model: {self.model_name}")
                except Exception as model_error:
                    logger.error(f"Error creating model with name {self.model_name}: {model_error}")

                    # Try fallback models
                    fallback_models = ["gemini-2.5-pro-preview-06-05", "gemini-2.5-flash-preview-05-20", "gemini-2.0-flash", "gemini-1.5-flash", "gemini-1.5-pro"]
                    model_created = False

                    for fallback_model in fallback_models:
                        if fallback_model == self.model_name:
                            continue  # Skip if it's the same as the one we just tried

                        try:
                            logger.info(f"Trying fallback model: {fallback_model}")
                            model = genai.GenerativeModel(fallback_model)
                            self.model_name = fallback_model
                            logger.info(f"Successfully created model with fallback name: {fallback_model}")
                            model_created = True
                            break
                        except Exception as fallback_error:
                            logger.error(f"Error creating model with fallback name {fallback_model}: {fallback_error}")

                    if not model_created:
                        error_msg = f"Failed to create model with any name. Original error: {model_error}"
                        logger.error(error_msg)
                        self.error_occurred.emit(error_msg)
                        self.finished.emit()
                        return

                # Set up generation config
                generation_config = genai.types.GenerationConfig(
                    response_mime_type="application/json"
                ) if self.request_json else None
                logger.info(f"Using JSON response format: {self.request_json}")

                # Prepare content using the list format (works with all versions)
                logger.info("Using list format for content")
                contents = [image_part, self.prompt]
                logger.info(f"Content structure: image part + prompt (length: {len(self.prompt)})")
                logger.info("Request prepared successfully")
            except Exception as e:
                logger.error(f"Error preparing request: {e}")
                self.error_occurred.emit(f"Error preparing request: {e}")
                self.finished.emit()
                return

            # --- API Call ---
            self.status_update.emit("Sending request to Gemini API...")
            logger.info(f"Sending request to Gemini API with timeout: {self._timeout}s")
            start_time = time.time()
            try:
                # Import threading for timeout mechanism
                import threading
                import queue

                # Create a queue for the response
                response_queue = queue.Queue()

                # Define a function to run the API call in a separate thread
                def api_call():
                    try:
                        logger.info(f"Generating content with model: {self.model_name}")
                        logger.info(f"Content type: {type(contents)}")
                        logger.info(f"Prompt: {self.prompt[:50]}..." if len(self.prompt) > 50 else f"Prompt: {self.prompt}")
                        resp = model.generate_content(contents, generation_config=generation_config)
                        logger.info("Content generated successfully")
                        response_queue.put((True, resp))
                    except Exception as e:
                        logger.error(f"Error generating content: {e}")
                        response_queue.put((False, e))

                # Start the API call in a separate thread
                thread = threading.Thread(target=api_call)
                thread.daemon = True
                thread.start()

                # Wait for the response with timeout
                try:
                    success, result = response_queue.get(timeout=self._timeout)
                    if success:
                        response = result
                        end_time = time.time()
                        logger.info(f"Gemini response received in {end_time - start_time:.2f} seconds")
                    else:
                        raise result  # Re-raise the exception from the thread
                except queue.Empty:
                    # Timeout occurred
                    logger.error(f"Timeout after {self._timeout} seconds")
                    self.error_occurred.emit(f"Request timed out after {self._timeout} seconds. Please try again or use a shorter prompt.")
                    self.finished.emit()
                    return
            except Exception as e:
                logger.error(f"Error generating content: {e}")
                self.error_occurred.emit(f"Error generating content: {e}")
                self.finished.emit()
                return

            # --- Cancellation Check (After Receiving) ---
            if self._is_cancelled:
                self.status_update.emit("Analysis cancelled after receiving response.")
                self.finished.emit()
                return

            # --- Response Processing ---
            self.status_update.emit("Processing response...")
            logger.info("Processing Gemini API response")
            full_text = ""
            try:
                 # Safely extract text (handles different response structures)
                 if hasattr(response, 'text'):
                     logger.info("Response has 'text' attribute")
                     full_text = response.text
                 elif response.parts:
                     logger.info("Response has 'parts' attribute")
                     full_text = "".join(part.text for part in response.parts if hasattr(part, 'text'))
                 else:
                     logger.warning(f"Unexpected response structure, converting to string: {response}")
                     full_text = str(response) # Fallback

                 logger.info(f"Extracted text length: {len(full_text)}")
                 if not full_text:
                     logger.warning("No text extracted from response")

                 logger.info(f"Emitting result_ready signal with text length: {len(full_text)}")
                 logger.info(f"Text preview: {full_text[:100]}..." if len(full_text) > 100 else f"Text: {full_text}")
                 self.result_ready.emit(full_text) # Emit the full text regardless
                 logger.info("Text result emitted")

                 # --- JSON Parsing (if applicable) ---
                 parsed_data = None
                 if self.request_json:
                     logger.info("JSON response requested, attempting to parse")
                     if not full_text:
                          logger.warning("Requested JSON but received empty text response.")
                          self.structured_data_ready.emit([])
                          self.bounding_boxes_ready.emit([])
                     else:
                         try:
                             # Clean potential markdown fences (more robustly)
                             json_str = full_text.strip()
                             logger.info(f"Original JSON string length: {len(json_str)}")

                             if json_str.startswith("```json"):
                                 logger.info("Removing ```json prefix")
                                 json_str = json_str[len("```json"):].strip()
                             elif json_str.startswith("```"):
                                 logger.info("Removing ``` prefix")
                                 json_str = json_str[len("```"):].strip()
                             if json_str.endswith("```"):
                                 logger.info("Removing ``` suffix")
                                 json_str = json_str[:-len("```")].strip()

                             logger.info(f"Cleaned JSON string length: {len(json_str)}")
                             logger.info(f"JSON string preview: {json_str[:100]}..." if len(json_str) > 100 else f"JSON string: {json_str}")

                             parsed_data = json_utils.loads(json_str)
                             logger.info("JSON parsed successfully")
                         except json_utils.JSONDecodeError as json_e:
                             logger.error(f"Failed to decode JSON from response: {json_e}\nResponse Text:\n{full_text}")
                             self.error_occurred.emit(f"AI returned invalid JSON: {json_e}") # Report error
                             self.structured_data_ready.emit([])
                             self.bounding_boxes_ready.emit([])
                         except Exception as e: # Catch other parsing errors
                             logger.error(f"Error processing JSON data: {e}")
                             self.error_occurred.emit(f"Error processing JSON data: {e}")
                             self.structured_data_ready.emit([])
                             self.bounding_boxes_ready.emit([])

                 # Emit structured data/bboxes if parsing was successful
                 if parsed_data is not None:
                     logger.info(f"Processing parsed data of type: {type(parsed_data)}")

                     if isinstance(parsed_data, list):
                         logger.info(f"Parsed data is a list with {len(parsed_data)} items")
                         self.structured_data_ready.emit(parsed_data)
                         logger.info("Structured data emitted")

                         # Extract bounding boxes (keep full objects with labels)
                         bboxes = [item for item in parsed_data
                                  if isinstance(item, dict) and "box_2d" in item and "label" in item]

                         if bboxes:
                             logger.info(f"Found {len(bboxes)} valid bounding boxes")
                             self.bounding_boxes_ready.emit(bboxes)
                             logger.info("Bounding boxes emitted")
                         else:
                             logger.warning("JSON list parsed, but no valid bounding box items found.")
                             self.bounding_boxes_ready.emit([])
                     elif isinstance(parsed_data, dict):
                         logger.info("Parsed data is a dictionary")

                         # Check if it's a standard format with 'objects' list
                         if 'objects' in parsed_data and isinstance(parsed_data['objects'], list):
                             logger.info(f"Found 'objects' list with {len(parsed_data['objects'])} items")
                             self.structured_data_ready.emit(parsed_data['objects'])

                             # Extract bounding boxes (keep full objects with labels)
                             bboxes = [item for item in parsed_data['objects']
                                      if isinstance(item, dict) and "box_2d" in item and "label" in item]

                             if bboxes:
                                 logger.info(f"Found {len(bboxes)} valid bounding boxes")
                                 self.bounding_boxes_ready.emit(bboxes)
                             else:
                                 logger.warning("No valid bounding boxes found in 'objects' list")
                                 self.bounding_boxes_ready.emit([])
                         else:
                             logger.info("Emitting dictionary as a single-item list")
                             self.structured_data_ready.emit([parsed_data])
                             self.bounding_boxes_ready.emit([]) # No list = no boxes expected
                     else:
                         logger.warning(f"Parsed JSON is not a list or dict: {type(parsed_data)}")
                         # Emit empty lists for unexpected data types
                         self.structured_data_ready.emit([])
                         self.bounding_boxes_ready.emit([]) # No list = no boxes expected

            except Exception as e:
                logger.exception("Error processing Gemini response content:")
                self.error_occurred.emit(f"Error processing response content: {e}")
                if self.request_json: # Ensure empty lists if JSON failed mid-processing
                    self.structured_data_ready.emit([])
                    self.bounding_boxes_ready.emit([])

        # --- API Error Handling ---
        except genai.types.generation_types.BlockedPromptException as bpe:
             logger.error(f"Gemini request blocked: {bpe}")
             self.error_occurred.emit(f"Request blocked by safety filters. Please modify the prompt or image.")
        except Exception as e:
            logger.exception("An error occurred during Gemini API call/setup:")
            self.error_occurred.emit(f"An unexpected error occurred: {e}")
            if self.request_json: # Ensure empty lists on general error if JSON expected
                self.structured_data_ready.emit([])
                self.bounding_boxes_ready.emit([])
        finally:
            self.status_update.emit("Analysis complete.")
            self.finished.emit() # Signal completion regardless of success/failure