from PySide6.QtWidgets import QWidget, QScrollArea, QHBoxLayout, QLabel
from PySide6.QtCore import Signal, Qt
from src.utils.image_utils import resize_image, convert_cvimage_to_qpixmap

class ThumbnailGallery(QScrollArea):
    image_selected = Signal(int)  # Signal emitted when thumbnail is clicked

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedHeight(120)
        self.setWidgetResizable(True)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOn)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        container = QWidget()
        self.layout = QHBoxLayout(container)
        self.layout.setContentsMargins(5, 5, 5, 5)
        self.layout.setSpacing(5)
        self.layout.setAlignment(Qt.AlignLeft)
        self.setWidget(container)
        
        self.thumbnails = []
        self.current_index = -1

    def clear_thumbnails(self):
        for thumb in self.thumbnails:
            self.layout.removeWidget(thumb)
            thumb.deleteLater()
        self.thumbnails.clear()
        self.current_index = -1

    def add_thumbnail(self, image, index):
        thumb_label = QLabel()
        thumb_label.setFixedSize(100, 100)
        thumbnail = resize_image(image, (90, 90))
        thumb_label.setPixmap(convert_cvimage_to_qpixmap(thumbnail))
        thumb_label.mousePressEvent = lambda e, idx=index: self.image_selected.emit(idx)
        thumb_label.setStyleSheet("""
            QLabel {
                border: 2px solid #ccc;
                border-radius: 5px;
                padding: 3px;
                background: #2a2a2a;
            }
            QLabel:hover {
                border-color: #3498db;
            }
        """)
        self.thumbnails.append(thumb_label)
        self.layout.addWidget(thumb_label)

    def set_selected(self, index):
        for i, thumb in enumerate(self.thumbnails):
            thumb.setStyleSheet(thumb.styleSheet().replace(
                "border: 2px solid #3498db",
                "border: 2px solid #ccc"
            ))
        if 0 <= index < len(self.thumbnails):
            self.thumbnails[index].setStyleSheet(
                self.thumbnails[index].styleSheet().replace(
                    "border: 2px solid #ccc",
                    "border: 2px solid #3498db"
                )
            )
            self.current_index = index
