import logging
from PySide6.QtCore import Qt, QPoint
from PySide6.QtGui import QP<PERSON>ter, QPen, QColor
from PySide6.QtWidgets import QPushButton, QComboBox, QSlider, QSpinBox, QCheckBox
from src.segmentation.sam_segmentation_handler import SAMSegmentationHandler

logger = logging.getLogger(__name__)

class TrainableSegmentationPageHandler:
    def __init__(self, ui):
        self.ui = ui
        self.sam_handler = SAMSegmentationHandler()
        self.current_image = None
        self.current_mask = None
        self.drawing_sam_bbox = False
        self.start_point = None
        self.end_point = None
        self.setup_handlers()

    def setup_handlers(self):
        """Set up event handlers for the UI components."""
        # SAM Magic Wand handlers
        self.ui.sam_magic_wand_button.clicked.connect(self.toggle_sam_magic_wand)
        self.ui.sam_magic_wand_point_button.clicked.connect(self.toggle_sam_point_prompt)

        # Connect negative point prompt button if it exists
        if hasattr(self.ui, 'sam_magic_wand_neg_point_button'):
            print("DEBUG: Connecting negative point prompt button")
            self.ui.sam_magic_wand_neg_point_button.clicked.connect(self.toggle_sam_neg_point_prompt)
        else:
            print("DEBUG: Negative point prompt button not found in UI")

        self.ui.accept_sam_button.clicked.connect(self.accept_sam_prediction)
        self.ui.reject_sam_button.clicked.connect(self.reject_sam_prediction)

        # Connect manage labels button if it exists
        if hasattr(self.ui, 'manage_labels_button'):
            print("DEBUG: Connecting manage labels button")
            # This will be handled by the TrainableSegmentationHandlers class
            # We just need to make sure the button is connected
        else:
            print("DEBUG: Manage labels button not found in UI")

        # Connect load exported annotations button if it exists
        if hasattr(self.ui, 'load_exported_annotations_button'):
            print("DEBUG: Connecting load exported annotations button")
            self.ui.load_exported_annotations_button.clicked.connect(self.load_exported_annotations)
        else:
            print("DEBUG: Load exported annotations button not found in UI")

        # Annotation loading/saving functionality has been removed

    def toggle_sam_magic_wand(self):
        """Toggle the SAM magic wand (box) tool."""
        if self.ui.sam_magic_wand_point_button.isChecked():
            self.ui.sam_magic_wand_point_button.setChecked(False)

        if self.ui.sam_magic_wand_button.isChecked():
            self.activate_sam_magic_wand()
        else:
            self.deactivate_sam_magic_wand()

    def toggle_sam_point_prompt(self):
        """Toggle the SAM point prompt tool."""
        if self.ui.sam_magic_wand_button.isChecked():
            self.ui.sam_magic_wand_button.setChecked(False)

        # Deactivate negative point prompt if active
        if hasattr(self.ui, 'sam_magic_wand_neg_point_button') and self.ui.sam_magic_wand_neg_point_button.isChecked():
            self.ui.sam_magic_wand_neg_point_button.setChecked(False)
            self.deactivate_sam_neg_point_prompt()

        if self.ui.sam_magic_wand_point_button.isChecked():
            self.activate_sam_point_prompt()
        else:
            self.deactivate_sam_point_prompt()

    def toggle_sam_neg_point_prompt(self):
        """Toggle the SAM negative point prompt tool."""
        print("toggle_sam_neg_point_prompt called in page handler")

        if self.ui.sam_magic_wand_button.isChecked():
            self.ui.sam_magic_wand_button.setChecked(False)

        # Deactivate positive point prompt if active
        if self.ui.sam_magic_wand_point_button.isChecked():
            self.ui.sam_magic_wand_point_button.setChecked(False)
            self.deactivate_sam_point_prompt()

        if hasattr(self.ui, 'sam_magic_wand_neg_point_button') and self.ui.sam_magic_wand_neg_point_button.isChecked():
            self.activate_sam_neg_point_prompt()
        else:
            self.deactivate_sam_neg_point_prompt()

    def activate_sam_magic_wand(self):
        """Activate the SAM magic wand (box) tool."""
        self.ui.trainable_original_view.current_tool = "sam_magic_wand"
        self.ui.trainable_original_view.setCursor(Qt.CrossCursor)

    def deactivate_sam_magic_wand(self):
        """Deactivate the SAM magic wand (box) tool."""
        self.ui.trainable_original_view.current_tool = None
        self.ui.trainable_original_view.setCursor(Qt.ArrowCursor)
        self.ui.sam_magic_wand_button.setChecked(False)

    def activate_sam_point_prompt(self):
        """Activate the SAM point prompt tool."""
        self.ui.trainable_original_view.current_tool = "sam_point_prompt"
        self.ui.trainable_original_view.drawing_enabled = False
        self.ui.trainable_original_view.erasing_enabled = False
        self.ui.trainable_original_view.setCursor(Qt.CrossCursor)

    def deactivate_sam_point_prompt(self):
        """Deactivate the SAM point prompt tool."""
        self.ui.trainable_original_view.current_tool = None
        self.ui.trainable_original_view.setCursor(Qt.ArrowCursor)
        self.ui.sam_magic_wand_point_button.setChecked(False)

    def activate_sam_neg_point_prompt(self):
        """Activate the SAM negative point prompt tool."""
        print("activate_sam_neg_point_prompt called in page handler")
        self.ui.trainable_original_view.current_tool = "sam_neg_point_prompt"
        self.ui.trainable_original_view.drawing_enabled = False
        self.ui.trainable_original_view.erasing_enabled = False
        self.ui.trainable_original_view.setCursor(Qt.CrossCursor)

    def deactivate_sam_neg_point_prompt(self):
        """Deactivate the SAM negative point prompt tool."""
        print("deactivate_sam_neg_point_prompt called in page handler")
        self.ui.trainable_original_view.current_tool = None
        self.ui.trainable_original_view.setCursor(Qt.ArrowCursor)
        if hasattr(self.ui, 'sam_magic_wand_neg_point_button'):
            self.ui.sam_magic_wand_neg_point_button.setChecked(False)

    def predict_sam_mask(self, input_type='box'):
        """Predict mask using SAM with either box or point input."""
        if self.current_image is None:
            return

        if self.ui.trainable_original_view.current_tool == 'sam_point_prompt':
            input_type = 'point'
            point = self.ui.trainable_original_view.sam_bbox
            if point is None:
                return
            mask, score = self.sam_handler.predict_mask(point, input_type=input_type)
        else:
            bbox = self.ui.trainable_original_view.sam_bbox
            if bbox is None:
                return
            mask, score = self.sam_handler.predict_mask(bbox, input_type='box')

        if mask is not None:
            self.current_mask = mask
            self.update_mask_display()

    def accept_sam_prediction(self):
        """Accept the current SAM prediction."""
        if self.current_mask is not None:
            # Add the mask to the current label
            # Implementation depends on how you want to handle the accepted mask
            pass
        self.reject_sam_prediction()

    def reject_sam_prediction(self):
        """Reject the current SAM prediction."""
        self.current_mask = None
        self.update_mask_display()

    def update_mask_display(self):
        """Update the display with the current mask."""
        # Implementation depends on how you want to display the mask
        pass

    def load_exported_annotations(self):
        """Load annotations exported from the unsupervised segmentation page."""
        # The UI (self.ui) is actually the main app instance which inherits from TrainableSegmentationHandlers
        # So we can directly call the method on the UI
        if hasattr(self.ui, 'load_exported_annotations'):
            # Call the handler's method directly
            self.ui.load_exported_annotations()
        else:
            print("ERROR: load_exported_annotations method not found in UI")

    # Annotation loading/saving functionality has been removed

    def apply_theme(self, theme_name="dark", style_params=None):
        """Apply the current theme to the trainable segmentation page components.

        Args:
            theme_name (str): The name of the theme to apply
            style_params (dict, optional): Custom style parameters to use
        """
        try:
            # Try to use the new theme system
            from src.gui.styles.theme_config import apply_theme, get_palette
            from PySide6.QtWidgets import QApplication

            # Get the current application instance
            app = QApplication.instance()
            if not app:
                logger.warning("No QApplication instance found when applying theme to trainable segmentation page")
                return

            # Get the current theme settings
            settings = app.property("settings")
            if settings:
                theme_base = settings.value("app/theme", "Dark Theme").lower().replace(" theme", "")
                color_scheme = settings.value("app/color_scheme", "Default").lower()
                font_family = settings.value("app/font_family", "Segoe UI")
                font_size_name = settings.value("app/font_size", "normal")
                theme_name = f"{color_scheme}-{theme_base}"
            else:
                # Use the provided theme name as fallback
                theme_name = theme_name.lower()
                font_family = "Segoe UI"
                font_size_name = "normal"

            # Get the font size from the font size name
            from src.gui.styles.theme_config import FONT_SIZES
            font_size = FONT_SIZES.get(font_size_name, 10)

            # Get the palette for the current theme
            palette = get_palette(theme_name)

            # Apply theme to specific buttons that need special attention
            special_buttons = [
                'draw_label_button',
                'erase_label_button',
                'clear_all_labels_button',
                'sam_magic_wand_button',
                'sam_magic_wand_point_button',
                'sam_magic_wand_neg_point_button',
                'accept_sam_button',
                'reject_sam_button'
            ]

            for button_name in special_buttons:
                if hasattr(self.ui, button_name):
                    button = getattr(self.ui, button_name)
                    # First, clear any existing style
                    button.setStyleSheet("")
                    # Then apply the theme
                    apply_theme(button, theme_name, font_family, font_size, style_params)
                    # Force update
                    button.update()
                    logger.debug(f"Applied theme to special button: {button_name}")

            # Check for any buttons with custom styles in the trainable segmentation page
            from PySide6.QtWidgets import QGroupBox, QFrame

            # Apply theme to all group boxes and frames to fix background colors
            for group_box in self.ui.trainable_segmentation_page.findChildren(QGroupBox):
                apply_theme(group_box, theme_name, font_family, font_size, style_params)
                # Remove any custom background color
                current_style = group_box.styleSheet()
                if "background-color" in current_style:
                    new_style = ""
                    for line in current_style.split(";"):
                        if "background-color" not in line:
                            new_style += line + ";"
                    group_box.setStyleSheet(new_style)

            # Apply theme to all frames
            for frame in self.ui.trainable_segmentation_page.findChildren(QFrame):
                apply_theme(frame, theme_name, font_family, font_size, style_params)
                # Remove any custom background color
                current_style = frame.styleSheet()
                if "background-color" in current_style:
                    new_style = ""
                    for line in current_style.split(";"):
                        if "background-color" not in line:
                            new_style += line + ";"
                    frame.setStyleSheet(new_style)

            # Apply theme to all buttons in the trainable segmentation page
            for button in self.ui.trainable_segmentation_page.findChildren(QPushButton):
                apply_theme(button, theme_name, font_family, font_size, style_params)

            # Apply theme to all comboboxes
            for combo in self.ui.trainable_segmentation_page.findChildren(QComboBox):
                apply_theme(combo, theme_name, font_family, font_size, style_params)

            # Apply theme to all sliders
            for slider in self.ui.trainable_segmentation_page.findChildren(QSlider):
                apply_theme(slider, theme_name, font_family, font_size, style_params)

            # Apply theme to all spinboxes
            for spinbox in self.ui.trainable_segmentation_page.findChildren(QSpinBox):
                apply_theme(spinbox, theme_name, font_family, font_size, style_params)

            # Apply theme to all checkboxes
            for checkbox in self.ui.trainable_segmentation_page.findChildren(QCheckBox):
                apply_theme(checkbox, theme_name, font_family, font_size, style_params)

            logger.info(f"Applied theme {theme_name} to trainable segmentation page components")

        except ImportError:
            logger.warning("Could not import theme_config module, using fallback theme")
            # Fallback to basic theme application
            if theme_name == "dark":
                # Apply dark theme styles
                pass
            else:
                # Apply light theme styles
                pass
        except Exception as e:
            logger.error(f"Error applying theme to trainable segmentation page: {e}")