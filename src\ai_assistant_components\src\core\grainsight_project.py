# src/core/grainsight_project.py
import os
import json
import logging
from pathlib import Path
import shutil # Import shutil for file copying

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GrainSightProject:
    """Manages project data, including images and AI analysis results."""

    def __init__(self, project_path="."):
        self.project_path = Path(project_path).resolve()
        self.results_dir = self.project_path / "results" / "ai_assistant"
        self.state_dir = self.project_path / "state" / "ai_assistant"
        # Use a dedicated directory for images managed by the project
        self.project_images_dir = self.project_path / "project_images"
        self.images = {} # In a real app, load this from project file

        self.results_dir.mkdir(parents=True, exist_ok=True)
        self.state_dir.mkdir(parents=True, exist_ok=True)
        self.project_images_dir.mkdir(parents=True, exist_ok=True) # Create image dir

        # --- Load existing project images instead of placeholder ---
        self._load_existing_project_images()
        # ---------------------------------------------------------

    def _load_existing_project_images(self):
        """Loads images found in the project_images directory."""
        self.images = {}
        logger.info(f"Scanning for images in: {self.project_images_dir}")
        for item in self.project_images_dir.iterdir():
            if item.is_file() and item.suffix.lower() in ['.png', '.jpg', '.jpeg', '.tif', '.tiff', '.bmp', '.webp']:
                image_id = item.name # Use filename as ID
                self.images[image_id] = {"path": str(item)}
        logger.info(f"Loaded project images: {list(self.images.keys())}")

    def add_image(self, source_image_path):
        """Copies an image into the project and adds it to the list."""
        source_path = Path(source_image_path)
        if not source_path.is_file():
            logger.error(f"Source path is not a valid file: {source_image_path}")
            return None, "Source path is not a valid file."

        dest_filename = source_path.name
        dest_path = self.project_images_dir / dest_filename

        # Handle potential filename collisions (simple approach: add suffix)
        counter = 1
        while dest_path.exists():
            name_part, ext_part = source_path.stem, source_path.suffix
            dest_filename = f"{name_part}_{counter}{ext_part}"
            dest_path = self.project_images_dir / dest_filename
            counter += 1

        try:
            shutil.copy2(source_path, dest_path) # copy2 preserves metadata
            image_id = dest_filename # Use the (potentially renamed) dest filename as ID
            self.images[image_id] = {"path": str(dest_path)}
            logger.info(f"Added image '{image_id}' to project from '{source_image_path}'")
            return image_id, None # Return the new ID and no error
        except Exception as e:
            logger.error(f"Failed to copy image from {source_image_path} to {dest_path}: {e}")
            return None, f"Failed to copy image: {e}"

    # --- Keep other methods the same ---
    # get_image_ids, get_image_path, _get_ai_state_path, _get_ai_results_path,
    # save_ai_assistant_state, load_ai_assistant_state, save_ai_analysis_results,
    # load_ai_analysis_results, has_ai_analysis_results, get_ai_assistant_bounding_boxes
    # ... (rest of the class methods from previous version) ...

    def get_image_ids(self):
        """Returns a list of image IDs (e.g., filenames) in the project."""
        return list(self.images.keys())

    def get_image_path(self, image_id):
        """Returns the full path to an image given its ID."""
        return self.images.get(image_id, {}).get("path")

    def _get_ai_state_path(self, image_id):
        """Gets the path to the AI state file for a given image ID."""
        safe_image_id = image_id.replace("..", "_").replace("/", "_") # Basic sanitization
        return self.state_dir / f"{safe_image_id}.json"

    def _get_ai_results_path(self, image_id):
        """Gets the path to the AI results file for a given image ID."""
        safe_image_id = image_id.replace("..", "_").replace("/", "_") # Basic sanitization
        return self.results_dir / f"{safe_image_id}_analysis.json"

    def save_ai_assistant_state(self, image_id, state):
        """Saves the UI state (e.g., last prompt) for the AI Assistant page."""
        if not image_id: return
        path = self._get_ai_state_path(image_id)
        try:
            with open(path, 'w') as f:
                json.dump(state, f, indent=2)
            logger.info(f"AI state saved for {image_id}")
        except Exception as e:
            logger.error(f"Error saving AI state for {image_id}: {e}")

    def load_ai_assistant_state(self, image_id):
        """Loads the UI state for the AI Assistant page."""
        if not image_id: return {}
        path = self._get_ai_state_path(image_id)
        if path.exists():
            try:
                with open(path, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading AI state for {image_id}: {e}")
        return {} # Return default empty dict if not found or error

    def save_ai_analysis_results(self, image_id, results):
        """Saves the analysis results (text, structured data) from Gemini."""
        if not image_id: return
        path = self._get_ai_results_path(image_id)
        try:
            with open(path, 'w') as f:
                json.dump(results, f, indent=2)
            logger.info(f"AI analysis results saved for {image_id}")
        except Exception as e:
            logger.error(f"Error saving AI analysis for {image_id}: {e}")

    def load_ai_analysis_results(self, image_id):
        """Loads previously saved analysis results for an image."""
        if not image_id: return None
        path = self._get_ai_results_path(image_id)
        if path.exists():
            try:
                with open(path, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading AI analysis for {image_id}: {e}")
        return None # Return None if not found or error

    def has_ai_analysis_results(self, image_id):
        """Checks if saved analysis results exist for an image."""
        if not image_id: return False
        return self._get_ai_results_path(image_id).exists()

    def get_ai_assistant_bounding_boxes(self, image_id):
        """Extracts bounding boxes from the saved analysis results."""
        results = self.load_ai_analysis_results(image_id)
        if results and isinstance(results.get("structured_data"), list):
            # Ensure items are dicts before checking for 'box_2d'
            return [item for item in results["structured_data"] if isinstance(item, dict) and "box_2d" in item]
        return []