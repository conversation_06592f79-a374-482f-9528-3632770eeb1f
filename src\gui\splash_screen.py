"""Enhanced splash screen for the VisionLab Ai application with modern design (animations removed)."""

from PySide6.QtWidgets import (QSplashScreen, QProgressBar, QVBoxLayout, QLabel,
                              QWidget, QGraphicsOpacityEffect, QHBoxLayout, QPushButton, QApplication)
from PySide6.QtGui import QPixmap, QColor, QPainter, QLinearGradient, QBrush, QPen, QPalette
from PySide6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, QSettings
from pathlib import Path

class StaticLabel(QLabel):
    """A simple label without animations for better performance."""

    def __init__(self, text, parent=None, font_size=32, color="#0078d4", bold=True, delay=0):
        super().__init__(text, parent)

        # Set font and color using a single stylesheet for consistency
        style_parts = [f"color: {color};", f"font-size: {font_size}px;"]
        if bold:
            style_parts.append("font-weight: bold;")
        else:
            style_parts.append("font-weight: normal;")
        self.setStyleSheet(" ".join(style_parts))

    def disable_animation(self):
        """Placeholder method for compatibility."""
        pass


class StaticProgressBar(QProgressBar):
    """A simple progress bar without animations for better performance."""

    def __init__(self, parent=None, is_dark_theme=True):
        super().__init__(parent)
        self.setTextVisible(False)
        self.setRange(0, 100)
        self.setValue(0)
        self.setFixedHeight(8)

        # Theme-aware styling
        if is_dark_theme:
            self.setStyleSheet("""
                QProgressBar {
                    background-color: rgba(255, 255, 255, 0.1);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    border-radius: 6px;
                }
                QProgressBar::chunk {
                    background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0,
                                                     stop:0 #00d4ff, stop:0.5 #0099cc, stop:1 #006699);
                    border-radius: 6px;
                    border: none;
                }
            """)
        else:
            self.setStyleSheet("""
                QProgressBar {
                    background-color: rgba(0, 0, 0, 0.1);
                    border: 1px solid rgba(0, 0, 0, 0.2);
                    border-radius: 6px;
                }
                QProgressBar::chunk {
                    background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0,
                                                     stop:0 #0066cc, stop:0.5 #004499, stop:1 #003366);
                    border-radius: 6px;
                    border: none;
                }
            """)


class StaticLogoWidget(QWidget):
    """A simple widget that displays an image logo without animations."""

    def __init__(self, image_path, parent=None):
        super().__init__(parent)

        self.image_label = QLabel(self)
        pixmap = QPixmap(image_path)
        if not pixmap.isNull():
            # Scale pixmap to fit the widget size (80x80 as set later)
            scaled_pixmap = pixmap.scaled(80, 80, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.image_label.setPixmap(scaled_pixmap)
            self.image_label.setAlignment(Qt.AlignCenter)
        else:
            self.image_label.setText("Logo not found") # Fallback text
            self.image_label.setAlignment(Qt.AlignCenter)
            self.image_label.setStyleSheet("color: red;")

        # Layout to center the image label
        layout = QVBoxLayout(self)
        layout.addWidget(self.image_label)
        layout.setContentsMargins(0, 0, 0, 0)
        self.setLayout(layout)

class VisionLabAiSplashScreen(QSplashScreen):
    """Modern splash screen without animations for VisionLab Ai application."""

    def __init__(self):
        # Detect current theme
        self.is_dark_theme = self._detect_theme()
        # Get theme-appropriate colors
        colors = self._get_theme_colors()

        # Create a larger, more modern splash screen
        pixmap = QPixmap(700, 450)
        # Create a theme-appropriate background
        pixmap.fill(QColor(colors['background']))

        super().__init__(pixmap)

        # Create a widget to hold the layout
        self.splash_widget = QWidget(self)
        self.splash_widget.setGeometry(0, 0, 700, 450)

        # Variables for dragging the window
        self.dragging = False
        self.drag_position = None

        # Create a layout for the splash screen with feature list positioned much higher
        main_layout = QVBoxLayout(self.splash_widget)
        main_layout.setContentsMargins(50, 25, 50, 30)  # Significantly reduced top margin to move content higher
        main_layout.setSpacing(8)  # Further reduced overall spacing

        # Add logo/header section
        header_layout = QHBoxLayout()

        # Create a simple logo widget without animations
        logo_image_path = Path(__file__).parent / "icons" / "logo.png"
        self.logo_widget = StaticLogoWidget(str(logo_image_path))
        self.logo_widget.setFixedSize(100, 100)

        # Add a subtle glow effect to the logo container
        self.logo_widget.setStyleSheet("""
            QWidget {
                background-color: rgba(0, 212, 255, 0.1);
                border: 2px solid rgba(0, 212, 255, 0.3);
                border-radius: 50px;
            }
        """)

        header_layout.addWidget(self.logo_widget)
        header_layout.addSpacing(20)  # Add spacing between logo and title

        # Add title section
        title_layout = QVBoxLayout()
        title_layout.setSpacing(0)

        # Add title label with theme-aware colors and larger font
        colors = self._get_theme_colors()
        self.title_label = StaticLabel("VisionLab Ai", font_size=38, color=colors['title_primary'], delay=200)
        self.title_label.setStyleSheet(f"""
            color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0,
                                  stop:0 {colors['title_primary']}, stop:1 {colors['title_secondary']});
            font-size: 38px;
            font-weight: bold;
            font-family: 'Segoe UI', Arial, sans-serif;
        """)
        title_layout.addWidget(self.title_label)

        # Add subtitle label with theme-aware colors and larger font
        self.subtitle_label = StaticLabel(
            "Advanced Image Analysis & AI-Powered Segmentation",
            font_size=18,
            color=colors['subtitle'],
            bold=False,
            delay=400
        )
        self.subtitle_label.setStyleSheet(f"""
            color: {colors['subtitle']};
            font-size: 18px;
            font-weight: 400;
            font-family: 'Segoe UI', Arial, sans-serif;
            letter-spacing: 0.5px;
        """)
        title_layout.addWidget(self.subtitle_label)

        header_layout.addLayout(title_layout)
        header_layout.addStretch()

        main_layout.addLayout(header_layout)

        # Minimal spacer to move feature list much higher up
        main_layout.addSpacing(2)

        # Add features section with increased spacing between items
        features_layout = QVBoxLayout()
        features_layout.setSpacing(22)  # Further increased spacing between items for better separation
        features_layout.setContentsMargins(0, 8, 0, 8)  # Slightly reduced margins to compensate

        feature_items = [
            "✨ Advanced Image Analysis & Processing",
            "🤖 AI-Powered Intelligent Segmentation",
            "🎯 Precision Unsupervised Learning",
            "🔍 Real-time Object Detection",
            "📊 Comprehensive Data Analytics"
        ]

        self.feature_labels = []
        colors = self._get_theme_colors()
        for i, feature in enumerate(feature_items):
            label = StaticLabel(feature, font_size=16, color=colors['feature_text'],
                                 bold=False, delay=800 + i*200)
            # Enhanced styling with transparent background for natural blending
            label.setStyleSheet(f"""
                color: {colors['feature_text']};
                font-size: 16px;
                font-weight: 400;
                font-family: 'Segoe UI', Arial, sans-serif;
                padding: 14px 16px;
                margin: 6px 0;
                min-height: 28px;
                max-height: 52px;
                background-color: transparent;
                border-left: 3px solid {colors['accent']};
                border-radius: 6px;
            """)
            label.setWordWrap(True)  # Enable word wrapping
            label.setMinimumHeight(52)  # Increased minimum height for better separation
            features_layout.addWidget(label)
            self.feature_labels.append(label)

        main_layout.addLayout(features_layout)

        # Reduced spacer to accommodate increased feature spacing while staying within bounds
        main_layout.addSpacing(20)

        # Add status section with improved spacing
        status_layout = QVBoxLayout()
        status_layout.setSpacing(8)

        # Add enhanced status label with theme-aware colors and larger font
        colors = self._get_theme_colors()
        self.status_label = QLabel("Initializing...")
        self.status_label.setStyleSheet(f"""
            color: {colors['status_text']};
            font-size: 16px;
            font-weight: 500;
            font-family: 'Segoe UI', Arial, sans-serif;
            padding: 8px 0;
        """)
        status_layout.addWidget(self.status_label)

        # Add progress bar with theme awareness
        self.progress_bar = StaticProgressBar(is_dark_theme=self.is_dark_theme)
        status_layout.addWidget(self.progress_bar)

        main_layout.addLayout(status_layout)

        # Add version label and control buttons
        bottom_layout = QHBoxLayout()

        # Add version label with theme-aware colors and larger font
        colors = self._get_theme_colors()
        self.version_label = QLabel("Version 1.0")
        self.version_label.setStyleSheet(f"""
            color: {colors['version_text']};
            font-size: 12px;
            font-weight: 400;
            font-family: 'Segoe UI', Arial, sans-serif;
        """)
        bottom_layout.addWidget(self.version_label)

        # Add spacer to push buttons to the right
        bottom_layout.addStretch()

        # Add minimize button
        self.minimize_button = QPushButton("_")
        self.minimize_button.setFixedSize(24, 24)
        self.minimize_button.setStyleSheet("""
            QPushButton {
                background-color: #2d2d2d;
                color: #a0a0a0;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3d3d3d;
                color: #ffffff;
            }
            QPushButton:pressed {
                background-color: #0078d4;
            }
        """)
        self.minimize_button.clicked.connect(self.showMinimized)
        bottom_layout.addWidget(self.minimize_button)

        # Add close button
        self.close_button = QPushButton("×")
        self.close_button.setFixedSize(24, 24)
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: #2d2d2d;
                color: #a0a0a0;
                border: none;
                border-radius: 3px;
                font-weight: bold;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #e81123;
                color: #ffffff;
            }
            QPushButton:pressed {
                background-color: #f1707a;
            }
        """)
        self.close_button.clicked.connect(self.handle_close)
        bottom_layout.addWidget(self.close_button)

        main_layout.addLayout(bottom_layout)

        # Set window flags - remove WindowStaysOnTopHint to prevent blocking
        self.setWindowFlags(Qt.FramelessWindowHint)

        # Add enhanced styling with theme-aware modern effects
        colors = self._get_theme_colors()
        self.setStyleSheet(f"""
            QSplashScreen {{
                background: qlineargradient(spread:pad, x1:0, y1:0, x2:0, y2:1,
                                          stop:0 {colors['background_gradient_start']},
                                          stop:0.5 {colors['background_gradient_mid']},
                                          stop:1 {colors['background_gradient_end']});
                border: 2px solid {colors['border']};
                border-radius: 15px;
            }}
        """)

        # Initialize without animations
        self.animations_active = False
        self.window_creation_mode = False

    def _detect_theme(self):
        """Detect if the current theme is dark or light."""
        try:
            # Try to get theme from application settings
            settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")
            theme = settings.value("app/theme", "Dark Theme").lower()
            return "dark" in theme
        except:
            # Fallback: detect from system palette
            app = QApplication.instance()
            if app:
                palette = app.palette()
                window_color = palette.color(QPalette.Window)
                # If window background is dark, assume dark theme
                return window_color.lightness() < 128
            return True  # Default to dark theme

    def _get_theme_colors(self):
        """Get theme-appropriate colors."""
        if self.is_dark_theme:
            return {
                'background': '#0f0f14',
                'background_gradient_start': '#0f0f14',
                'background_gradient_mid': '#1a1a22',
                'background_gradient_end': '#0f0f14',
                'title_primary': '#00d4ff',
                'title_secondary': '#0099cc',
                'subtitle': '#d0d0d0',
                'feature_text': '#e8e8e8',
                'status_text': '#d0d0d0',
                'version_text': '#a0a0a0',
                'accent': '#00d4ff',
                'border': 'rgba(0, 212, 255, 0.3)',
                'feature_bg': 'rgba(255, 255, 255, 0.08)'
            }
        else:
            return {
                'background': '#f8f9fa',
                'background_gradient_start': '#ffffff',
                'background_gradient_mid': '#f8f9fa',
                'background_gradient_end': '#e9ecef',
                'title_primary': '#0066cc',
                'title_secondary': '#004499',
                'subtitle': '#495057',
                'feature_text': '#212529',
                'status_text': '#495057',
                'version_text': '#6c757d',
                'accent': '#0066cc',
                'border': 'rgba(0, 102, 204, 0.3)',
                'feature_bg': 'rgba(0, 102, 204, 0.08)'
            }

    def start_animations(self):
        """Placeholder method - animations disabled."""
        # Set initial progress without animation
        self.progress_bar.setValue(10)
    
    def disable_animations(self):
        """Disable all animations for faster performance during critical operations."""
        self.animations_active = False
        self.window_creation_mode = True
        
        # Stop logo pulse timer
        if hasattr(self, 'logo_pulse_timer') and self.logo_pulse_timer:
            self.logo_pulse_timer.stop()
            
        # Disable animations for all feature labels
        for label in self.feature_labels:
            if hasattr(label, 'disable_animation'):
                label.disable_animation()
                
        # Disable animations for title and subtitle
        if hasattr(self.title_label, 'disable_animation'):
            self.title_label.disable_animation()
        if hasattr(self.subtitle_label, 'disable_animation'):
            self.subtitle_label.disable_animation()
    
    def enable_animations(self):
        """Re-enable animations after critical operations."""
        self.animations_active = True
        self.window_creation_mode = False
        
        # Restart logo pulse timer if needed
        if hasattr(self, 'logo_pulse_timer') and self.logo_pulse_timer and not self.logo_pulse_timer.isActive():
            self.logo_pulse_timer.start(3000)
    
    def prepare_for_window_creation(self):
        """Prepare splash screen for main window creation by disabling animations."""
        self.disable_animations()
        
        # Ensure all text is immediately visible
        self.status_label.show()
        for label in self.feature_labels:
            label.show()
        self.title_label.show()
        self.subtitle_label.show()
        
        # Force immediate update
        self.update()
        QApplication.processEvents()



    def update_progress(self, value, message):
        """Update the progress bar and status message with improved reliability."""
        # Update progress bar directly without animation
        self.progress_bar.setValue(value)

        # Ensure status label is visible and properly styled
        colors = self._get_theme_colors()
        
        # Use solid color instead of gradient for better text visibility during updates
        self.status_label.setStyleSheet(f"""
            color: {colors['title_primary']};
            font-size: 16px;
            font-weight: 500;
            font-family: 'Segoe UI', Arial, sans-serif;
            padding: 8px 0;
            background-color: transparent;
        """)
        
        # Update text and ensure immediate visibility
        self.status_label.setText(message)
        self.status_label.show()  # Ensure label is visible
        
        # Force immediate update of the splash screen
        self.update()
        QApplication.processEvents()  # Process pending events for immediate update

    def handle_close(self):
        """Handle the close button click."""
        # Hide the splash screen but don't destroy it
        # This allows the application to continue loading
        self.hide()

        # Reset dragging state to prevent issues
        self.dragging = False
        self.drag_position = None
    
    def finish(self, main_window):
        """Finish the splash screen and show the main window."""
        # Disable all animations for clean finish
        self.disable_animations()
        
        # Update to final state
        self.update_progress(100, "Application ready!")
        
        # Clean finish without fade effect for better performance
        self.hide()
        
        # Ensure main window is properly shown
        if main_window:
            main_window.show()
            main_window.raise_()
            main_window.activateWindow()
        
        # Clean up resources
        self._cleanup_resources()
    
    def _cleanup_resources(self):
        """Clean up animation resources and timers."""
        # Stop and clean up logo pulse timer
        if hasattr(self, 'logo_pulse_timer') and self.logo_pulse_timer:
            self.logo_pulse_timer.stop()
            self.logo_pulse_timer = None
            
        # Clean up feature label animations
        for label in self.feature_labels:
            if hasattr(label, 'disable_animation'):
                label.disable_animation()
                
        # Clean up title and subtitle animations
        if hasattr(self.title_label, 'disable_animation'):
            self.title_label.disable_animation()
        if hasattr(self.subtitle_label, 'disable_animation'):
            self.subtitle_label.disable_animation()

    def paintEvent(self, event):
        """Override paint event to add custom drawing with enhanced effects."""
        super().paintEvent(event)

        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Draw a sophisticated gradient overlay
        gradient = QLinearGradient(0, 0, self.width(), self.height())
        gradient.setColorAt(0, QColor(0, 212, 255, 15))
        gradient.setColorAt(0.3, QColor(0, 153, 204, 25))
        gradient.setColorAt(0.7, QColor(0, 102, 153, 20))
        gradient.setColorAt(1, QColor(0, 51, 102, 10))
        painter.fillRect(self.rect(), QBrush(gradient))

        # Draw multiple highlight lines for depth
        # Top highlight
        pen = QPen(QColor(0, 212, 255, 150))
        pen.setWidth(2)
        painter.setPen(pen)
        painter.drawLine(0, 0, self.width(), 0)

        # Subtle inner glow
        pen = QPen(QColor(0, 212, 255, 80))
        pen.setWidth(1)
        painter.setPen(pen)
        painter.drawLine(2, 2, self.width()-2, 2)

        # Bottom accent
        pen = QPen(QColor(0, 153, 204, 100))
        pen.setWidth(1)
        painter.setPen(pen)
        painter.drawLine(0, self.height()-1, self.width(), self.height()-1)

    def mousePressEvent(self, event):
        """Handle mouse press events for dragging."""
        # Call the parent class implementation first to maintain normal behavior
        super().mousePressEvent(event)

        # Then handle our custom dragging logic
        if event.button() == Qt.LeftButton:
            # Only enable dragging if we're not clicking on a button
            widget_at_pos = self.childAt(event.position().toPoint())
            if not isinstance(widget_at_pos, QPushButton):
                self.dragging = True
                self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
                event.accept()

    def mouseMoveEvent(self, event):
        """Handle mouse move events for dragging."""
        # Call the parent class implementation first
        super().mouseMoveEvent(event)

        # Then handle our custom dragging logic
        if self.dragging and event.buttons() & Qt.LeftButton:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()

    def mouseReleaseEvent(self, event):
        """Handle mouse release events for dragging."""
        # Call the parent class implementation first
        super().mouseReleaseEvent(event)

        # Then handle our custom dragging logic
        if event.button() == Qt.LeftButton:
            self.dragging = False
            event.accept()
