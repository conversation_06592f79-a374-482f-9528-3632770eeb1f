/* Custom styling for navigation tabs in VisionLab Ai V4 */

/* Main window styling to remove top margin */
QMainWindow {
    margin: 0;
    padding: 0;
}

/* Central widget styling to remove margins */
QMainWindow > QWidget {
    margin: 0;
    padding: 0;
}

/* Main window tab widget styling */
QMainWindow > QWidget > QTabWidget::pane {
    border: none;
    top: 0px;
    margin-top: 0px;
    padding-top: 0px;
}

/* Main window tab bar styling */
QMainWindow > QWidget > QTabWidget > QTabBar {
    background-color: #1e1e1e;
    border-bottom: 1px solid #333333;
    margin-top: 0px;
    padding-top: 0px;
}

/* Main window individual tabs */
QMainWindow > QWidget > QTabWidget > QTabBar::tab {
    background-color: #1e1e1e;
    color: #cccccc;
    border: none;
    padding: 10px 18px;
    font-size: 14px;
    font-weight: 500;
    margin: 0px 2px;
    margin-top: 0px;
    border-radius: 0px;
    min-width: 120px;
    min-height: 20px;
}

/* Main window hover state for tabs */
QMainWindow > QWidget > QTabWidget > QTabBar::tab:hover {
    background-color: #333333;
    color: white;
}

/* Main window selected tab */
QMainWindow > QWidget > QTabWidget > QTabBar::tab:selected {
    background-color: #007acc;
    color: white;
    font-weight: bold;
    border-bottom: 3px solid #007acc;
}

/* Main window unselected tabs */
