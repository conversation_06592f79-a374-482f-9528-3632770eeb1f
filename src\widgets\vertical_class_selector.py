"""
Vertical Class Selector Widget for quick class selection in point counting.
This version stacks classes vertically for a more compact display.
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QPushButton, QLabel,
                              QFrame, QHBoxLayout, QToolButton, QSizePolicy)
from PySide6.QtCore import Qt, Signal, QSize
from PySide6.QtGui import QColor

class VerticalClassButton(QPushButton):
    """A custom button for class selection in vertical layout with modern styling."""

    def __init__(self, class_name, color, index, parent=None):
        super().__init__(parent)
        self.class_name = class_name
        self.color = color
        self.index = index
        self.setup_ui()

    def setup_ui(self):
        """Set up the button UI with modern styling."""
        # Set button text to class name with index
        self.setText(f"{self.index + 1}. {self.class_name}")

        # Set button color
        r, g, b = self.color.red(), self.color.green(), self.color.blue()

        # Calculate text color based on background brightness
        brightness = (0.299 * r + 0.587 * g + 0.114 * b) / 255
        text_color = "white" if brightness < 0.5 else "black"
        
        # Create color variations for modern effects
        hover_r = min(r + 40, 255)
        hover_g = min(g + 40, 255) 
        hover_b = min(b + 40, 255)
        
        pressed_r = max(r - 40, 0)
        pressed_g = max(g - 40, 0)
        pressed_b = max(b - 40, 0)
        
        # Create shadow color (darker version)
        shadow_r = max(r - 60, 0)
        shadow_g = max(g - 60, 0)
        shadow_b = max(b - 60, 0)

        # Set modern button style with gradient and shadow effects
        self.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgb({min(r+20, 255)}, {min(g+20, 255)}, {min(b+20, 255)}),
                    stop:1 rgb({r}, {g}, {b}));
                color: {text_color};
                border: 2px solid rgba({shadow_r}, {shadow_g}, {shadow_b}, 180);
                border-radius: 8px;
                padding: 8px 12px;
                font-weight: 600;
                font-size: 12pt;
                text-align: left;
                margin: 1px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgb({min(hover_r+20, 255)}, {min(hover_g+20, 255)}, {min(hover_b+20, 255)}),
                    stop:1 rgb({hover_r}, {hover_g}, {hover_b}));
                border: 2px solid rgba(255, 255, 255, 200);
                padding: 7px 12px 9px 12px;
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgb({pressed_r}, {pressed_g}, {pressed_b}),
                    stop:1 rgb({max(pressed_r-20, 0)}, {max(pressed_g-20, 0)}, {max(pressed_b-20, 0)}));
                border: 2px solid rgba({shadow_r}, {shadow_g}, {shadow_b}, 220);
                padding: 9px 11px 7px 13px;
            }}
        """)

        # Set fixed height but allow width to expand
        self.setFixedHeight(42)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        
        # Enable cursor change on hover
        self.setCursor(Qt.PointingHandCursor)

        # Set enhanced tooltip
        self.setToolTip(f"🎯 Select '{self.class_name}' class\n⌨️ Keyboard shortcut: {self.index + 1}\n🖱️ Click to assign this class to points")

class VerticalClassSelector(QWidget):
    """A vertical class selector widget for quick class selection."""

    class_selected = Signal(int)  # Signal emitted when a class is selected

    def __init__(self, parent=None):
        super().__init__(parent)
        self.classes = []  # List of (class_name, color) tuples
        self.dragging = False
        self.drag_start_position = None
        self.setup_ui()

    def setup_ui(self):
        """Set up the UI components with modern styling."""
        # Set window flags for floating behavior within the application
        self.setWindowFlags(Qt.Widget)  # Regular widget, not a separate window

        # Main layout
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(8, 8, 8, 8)
        self.main_layout.setSpacing(6)

        # Title bar with drag handle
        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(4, 4, 4, 4)

        # Drag handle with icon
        self.drag_handle = QLabel("⋮⋮ Quick Class Selector")
        self.drag_handle.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-weight: 600;
                font-size: 11pt;
                padding: 4px 8px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(70, 130, 180, 180),
                    stop:1 rgba(100, 149, 237, 180));
                border-radius: 4px;
            }
        """)
        self.drag_handle.setCursor(Qt.OpenHandCursor)
        self.drag_handle.setToolTip("🖱️ Click and drag to move this panel")
        title_layout.addWidget(self.drag_handle)

        title_layout.addStretch()

        # Close button with modern styling
        self.close_button = QToolButton()
        self.close_button.setText("✕")
        self.close_button.setFixedSize(24, 24)
        self.close_button.setStyleSheet("""
            QToolButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ff6b6b,
                    stop:1 #ee5a52);
                color: white;
                border-radius: 12px;
                font-weight: bold;
                font-size: 10pt;
                border: 1px solid rgba(255, 255, 255, 100);
            }
            QToolButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ff8e8e,
                    stop:1 #ff6b6b);
                border: 1px solid rgba(255, 255, 255, 150);
            }
            QToolButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ee5a52,
                    stop:1 #dc3545);
            }
        """)
        self.close_button.setCursor(Qt.PointingHandCursor)
        self.close_button.setToolTip("❌ Hide quick selector")
        self.close_button.clicked.connect(self.hide)
        title_layout.addWidget(self.close_button)

        self.main_layout.addLayout(title_layout)

        # Modern separator with gradient
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFixedHeight(2)
        separator.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0),
                    stop:0.5 rgba(255, 255, 255, 100),
                    stop:1 rgba(255, 255, 255, 0));
                border: none;
            }
        """)
        self.main_layout.addWidget(separator)

        # Container for class buttons with modern styling
        self.buttons_container = QWidget()
        self.buttons_container.setStyleSheet("""
            QWidget {
                background: rgba(0, 0, 0, 30);
                border-radius: 6px;
                margin: 2px;
            }
        """)
        self.buttons_layout = QVBoxLayout(self.buttons_container)
        self.buttons_layout.setContentsMargins(6, 6, 6, 6)
        self.buttons_layout.setSpacing(4)

        self.main_layout.addWidget(self.buttons_container)

        # Set modern widget style with glassmorphism effect
        self.setStyleSheet("""
            VerticalClassSelector {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(45, 45, 45, 240),
                    stop:0.5 rgba(35, 35, 35, 220),
                    stop:1 rgba(25, 25, 25, 240));
                border-radius: 12px;
                border: 2px solid rgba(255, 255, 255, 80);
            }
        """)

        # Set size policy
        self.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Minimum)

        # Enable mouse tracking for drag and drop
        self.setMouseTracking(True)

    def update_classes(self, classes):
        """Update the class buttons.

        Args:
            classes: List of (class_name, color) tuples
        """
        self.classes = classes

        # Clear existing buttons
        while self.buttons_layout.count():
            item = self.buttons_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        # Add new buttons
        for i, (class_name, color) in enumerate(classes):
            button = VerticalClassButton(class_name, color, i)
            # Use a helper function to avoid lambda closure issues
            self.connect_button_to_index(button, i)
            self.buttons_layout.addWidget(button)

        # Adjust size based on number of buttons
        self.adjustSize()

    def mousePressEvent(self, event):
        """Handle mouse press events for dragging."""
        if event.button() == Qt.LeftButton and self.drag_handle.geometry().contains(event.pos()):
            self.dragging = True
            self.drag_start_position = event.pos()
            self.drag_handle.setCursor(Qt.ClosedHandCursor)
            event.accept()
        else:
            super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """Handle mouse move events for dragging."""
        if self.dragging and (event.buttons() & Qt.LeftButton):
            # Calculate the new position
            delta = event.pos() - self.drag_start_position
            new_pos = self.pos() + delta

            # Ensure the widget stays within the parent widget
            if self.parent():
                parent_rect = self.parent().rect()
                new_pos.setX(max(0, min(new_pos.x(), parent_rect.width() - self.width())))
                new_pos.setY(max(0, min(new_pos.y(), parent_rect.height() - self.height())))

            # Move the widget
            self.move(new_pos)
            event.accept()
        else:
            super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """Handle mouse release events for dragging."""
        if event.button() == Qt.LeftButton and self.dragging:
            self.dragging = False
            self.drag_handle.setCursor(Qt.OpenHandCursor)
            event.accept()
        else:
            super().mouseReleaseEvent(event)

    def connect_button_to_index(self, button, index):
        """Connect a button to emit a specific index when clicked.
        This avoids the lambda closure issue where all buttons would use the last index.
        """
        button.clicked.connect(lambda checked=False, idx=index: self.class_selected.emit(idx))

    def sizeHint(self):
        """Suggest a size for the widget based on modern styling."""
        # Calculate height based on new button height (42px) plus spacing and margins
        button_height = 42
        spacing = 4
        margins = 16  # top + bottom margins
        title_height = 40  # title bar height
        separator_height = 6
        
        total_height = (len(self.classes) * (button_height + spacing) + 
                       margins + title_height + separator_height + 20)
        
        return QSize(180, max(total_height, 100))  # Minimum height of 100px
