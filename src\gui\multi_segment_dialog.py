# src/gui/multi_segment_dialog.py

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QListWidget, QListWidgetItem, QFrame,
                              QSizePolicy, QCheckBox, QRadioButton, QButtonGroup)
from PySide6.QtGui import QColor, QBrush
from PySide6.QtCore import Qt, Signal

class MultiSegmentDialog(QDialog):
    """Dialog for selecting multiple segments to display in a grid or main preview."""
    
    segments_selected = Signal(list)  # Signal emitting the list of selected segment colors
    segments_selected_for_preview = Signal(list)  # Signal for displaying in main preview
    
    def __init__(self, segment_colors, segment_names, parent=None):
        """Initialize the dialog.
        
        Args:
            segment_colors: Dictionary of segment colors (RGB tuples)
            segment_names: Dictionary mapping colors to segment names
            parent: Parent widget
        """
        super().__init__(parent)
        self.setWindowTitle("Select Segments to Display")
        self.setMinimumWidth(350)
        self.setMinimumHeight(450)
        
        self.segment_colors = segment_colors
        self.segment_names = segment_names
        self.selected_segments = []
        
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout(self)
        
        # Instructions
        instructions = QLabel("Select segments to display:")
        instructions.setStyleSheet("font-weight: bold; font-size: 12pt;")
        layout.addWidget(instructions)
        
        # Display mode selection
        display_mode_layout = QHBoxLayout()
        display_mode_label = QLabel("Display mode:")
        display_mode_layout.addWidget(display_mode_label)
        
        self.display_mode_group = QButtonGroup()
        self.grid_mode_radio = QRadioButton("Grid layout")
        self.preview_mode_radio = QRadioButton("Main preview")
        self.grid_mode_radio.setChecked(True)  # Default to grid mode
        
        self.display_mode_group.addButton(self.grid_mode_radio, 0)
        self.display_mode_group.addButton(self.preview_mode_radio, 1)
        
        display_mode_layout.addWidget(self.grid_mode_radio)
        display_mode_layout.addWidget(self.preview_mode_radio)
        display_mode_layout.addStretch()
        
        layout.addLayout(display_mode_layout)
        
        # Description
        self.description = QLabel("Check the segments you want to display in the grid layout. "
                                 "You can select multiple segments.")
        self.description.setWordWrap(True)
        layout.addWidget(self.description)
        
        # Connect radio button signals to update description
        self.grid_mode_radio.toggled.connect(self.update_description)
        self.preview_mode_radio.toggled.connect(self.update_description)
        
        # Segment list
        self.segment_list = QListWidget()
        self.segment_list.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.segment_list.setMinimumHeight(250)  # Set minimum height
        layout.addWidget(self.segment_list, 1)  # Give it a stretch factor
        
        # Populate the list
        self.populate_segment_list()
        
        # Select All / Deselect All buttons
        select_buttons_layout = QHBoxLayout()
        
        self.select_all_button = QPushButton("Select All")
        self.select_all_button.clicked.connect(self.select_all_segments)
        select_buttons_layout.addWidget(self.select_all_button)
        
        self.deselect_all_button = QPushButton("Deselect All")
        self.deselect_all_button.clicked.connect(self.deselect_all_segments)
        select_buttons_layout.addWidget(self.deselect_all_button)
        
        layout.addLayout(select_buttons_layout)
        
        # Dialog buttons
        button_layout = QHBoxLayout()
        
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        self.display_button = QPushButton("Display Selected")
        self.display_button.clicked.connect(self.accept_selection)
        self.display_button.setDefault(True)
        button_layout.addWidget(self.display_button)
        
        layout.addLayout(button_layout)
        
    def populate_segment_list(self):
        """Populate the list with segments and checkboxes."""
        self.segment_list.clear()
        
        # Add each segment with a checkbox
        for i, color in enumerate(sorted(self.segment_colors.keys())):
            # Get segment name if available, otherwise use default
            segment_name = self.segment_names.get(color, f"Segment {i+1}")
            
            # Create list item
            item = QListWidgetItem()
            self.segment_list.addItem(item)
            
            # Create widget with checkbox and colored label
            item_widget = QFrame()
            item_layout = QHBoxLayout(item_widget)
            item_layout.setContentsMargins(5, 2, 5, 2)
            
            # Add checkbox
            checkbox = QCheckBox()
            checkbox.setObjectName(f"checkbox_{i}")
            checkbox.setProperty("color", color)  # Store color as a property
            item_layout.addWidget(checkbox)
            
            # Add color indicator
            color_rgb = self.segment_colors.get(color, color)
            color_box = QLabel()
            color_box.setFixedSize(20, 20)
            color_box.setStyleSheet(f"background-color: rgb({color_rgb[0]}, {color_rgb[1]}, {color_rgb[2]}); border: 1px solid black;")
            item_layout.addWidget(color_box)
            
            # Add label
            label = QLabel(segment_name)
            
            # Set text color for better visibility
            brightness = (0.299 * color_rgb[0] + 0.587 * color_rgb[1] + 0.114 * color_rgb[2]) / 255
            if brightness < 0.5:
                color_box.setStyleSheet(f"background-color: rgb({color_rgb[0]}, {color_rgb[1]}, {color_rgb[2]}); border: 1px solid black; color: white;")
            
            item_layout.addWidget(label)
            item_layout.addStretch()
            
            # Set the custom widget for this item
            self.segment_list.setItemWidget(item, item_widget)
            
            # Store the checkbox for later access
            item.setData(Qt.ItemDataRole.UserRole, checkbox)
            
    def select_all_segments(self):
        """Select all segments in the list."""
        for i in range(self.segment_list.count()):
            item = self.segment_list.item(i)
            checkbox = item.data(Qt.ItemDataRole.UserRole)
            if checkbox:
                checkbox.setChecked(True)
                
    def deselect_all_segments(self):
        """Deselect all segments in the list."""
        for i in range(self.segment_list.count()):
            item = self.segment_list.item(i)
            checkbox = item.data(Qt.ItemDataRole.UserRole)
            if checkbox:
                checkbox.setChecked(False)
                
    def update_description(self):
        """Update the description text based on selected display mode."""
        if self.preview_mode_radio.isChecked():
            self.description.setText("Check the segments you want to display combined in the main preview. "
                                   "Selected segments will be overlaid together in a single view.")
        else:
            self.description.setText("Check the segments you want to display in the grid layout. "
                                   "You can select multiple segments.")
    
    def accept_selection(self):
        """Accept the selected segments and emit appropriate signal."""
        selected_colors = []
        
        # Collect all checked segments
        for i in range(self.segment_list.count()):
            item = self.segment_list.item(i)
            checkbox = item.data(Qt.ItemDataRole.UserRole)
            if checkbox and checkbox.isChecked():
                color = checkbox.property("color")
                if color:
                    selected_colors.append(color)
        
        # Emit appropriate signal based on display mode
        if self.preview_mode_radio.isChecked():
            self.segments_selected_for_preview.emit(selected_colors)
        else:
            self.segments_selected.emit(selected_colors)
        
        self.accept()
