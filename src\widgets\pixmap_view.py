# src/widgets/pixmap_view.py
from PySide6.QtWidgets import QScrollArea, QLabel, QWidget
from PySide6.QtGui import QPixmap, QImage
from PySide6.QtCore import Qt, Signal, QTimer, QPoint


class QPixmapView(QScrollArea):
    """Custom QPixmapView for displaying images, zooming, and panning."""
    zoom_changed = Signal(float)
    mouse_pressed = Signal(QPoint)
    mouse_moved = Signal(QPoint)
    mouse_released = Signal(QPoint)
    scroll_changed = Signal(int, int)  # New signal for scroll position changes (h_value, v_value)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAlignment(Qt.AlignTop | Qt.AlignLeft)  # Important for correct scrolling
        self._pixmap = None
        self._label = QLabel()
        self.setWidget(self._label)  # Use QLabel directly, simpler for this case
        self.zoom_scale = 1.0
        self._pan_start_pos = None
        self.setMouseTracking(True)
        self._label.setScaledContents(True) # Key change: Scale label contents
        self.drawing_enabled = False
        self.erasing_enabled = False
        self.sam_bbox = None  # Initialize sam_bbox attribute
        self.temp_sam_prediction = None  # Initialize temp_sam_prediction attribute
        self.current_tool = None  # Add current_tool attribute

        # Connect scrollbar signals to emit scroll_changed signal
        self.horizontalScrollBar().valueChanged.connect(self._emit_scroll_changed)
        self.verticalScrollBar().valueChanged.connect(self._emit_scroll_changed)

        self._zoom_label = QLabel(self)
        self._zoom_label.setStyleSheet(
            "background-color: rgba(0, 0, 0, 100); color: white; padding: 5px;")
        self._zoom_label.hide()
        self.update_zoom_label()

    def _emit_scroll_changed(self, _):
        """Emits the scroll_changed signal with current scrollbar values."""
        h_value = self.horizontalScrollBar().value()
        v_value = self.verticalScrollBar().value()
        self.scroll_changed.emit(h_value, v_value)

    def setScrollPosition(self, h_value, v_value):
        """Sets the scroll position without triggering signals."""
        # Temporarily disconnect signals to avoid feedback loops
        try:
            self.horizontalScrollBar().valueChanged.disconnect(self._emit_scroll_changed)
            self.verticalScrollBar().valueChanged.disconnect(self._emit_scroll_changed)
        except Exception:
            # In case the signals were not connected
            pass

        # Set values
        self.horizontalScrollBar().setValue(h_value)
        self.verticalScrollBar().setValue(v_value)

        # Reconnect signals
        try:
            self.horizontalScrollBar().valueChanged.connect(self._emit_scroll_changed)
            self.verticalScrollBar().valueChanged.connect(self._emit_scroll_changed)
        except Exception:
            # In case the signals were already connected
            pass

    def setPixmap(self, pixmap: QPixmap):
        """Sets the pixmap to be displayed while preserving zoom level."""
        # Store current zoom level
        current_zoom = self.zoom_scale

        # Store current scroll position
        h_value = self.horizontalScrollBar().value()
        v_value = self.verticalScrollBar().value()

        # Update pixmap
        self._pixmap = pixmap
        self._label.setPixmap(self._pixmap)

        # Restore zoom level
        self.zoom_scale = current_zoom
        self.resize_to_image()

        # Restore scroll position (with a small delay to ensure layout is updated)
        def restore_scroll_position():
            try:
                if not self.isVisible() or self.horizontalScrollBar() is None:
                    return
                self.setScrollPosition(h_value, v_value)
            except RuntimeError:
                # Widget has been deleted, ignore
                pass
        QTimer.singleShot(10, restore_scroll_position)


    def resize_to_image(self):
        """Resizes the image label to fit the image and zoom level."""
        if self._pixmap:
            new_size = self._pixmap.size() * self.zoom_scale
            self._label.setFixedSize(new_size)
            self.update_zoom_label()


    def wheelEvent(self, event):
        """Handles mouse wheel events for zooming."""
        if event.modifiers() == Qt.NoModifier:  # Only zoom if no modifiers (like Ctrl) are pressed
            delta = event.angleDelta().y()

            # Get the mouse position relative to the viewport
            viewport_pos = event.position().toPoint()

            # Get the mouse position relative to the image label before zooming
            pos_before_zoom = self._label.mapFrom(self, viewport_pos)

            # Calculate the relative position within the image (as a fraction)
            rel_x = pos_before_zoom.x() / (self._pixmap.width() * self.zoom_scale) if self._pixmap and self._pixmap.width() > 0 else 0.5
            rel_y = pos_before_zoom.y() / (self._pixmap.height() * self.zoom_scale) if self._pixmap and self._pixmap.height() > 0 else 0.5

            # Apply zoom
            if delta > 0:
                self.zoom_in()
            elif delta < 0:
                self.zoom_out()

            # Calculate where the point should be after zooming
            target_x = int(rel_x * self._pixmap.width() * self.zoom_scale) if self._pixmap else 0
            target_y = int(rel_y * self._pixmap.height() * self.zoom_scale) if self._pixmap else 0

            # Calculate the new viewport position that would center the target point under the cursor
            new_viewport_x = target_x - viewport_pos.x()
            new_viewport_y = target_y - viewport_pos.y()

            # Adjust scrollbars to keep the point under the cursor
            h_bar = self.horizontalScrollBar()
            v_bar = self.verticalScrollBar()
            h_bar.setValue(new_viewport_x)
            v_bar.setValue(new_viewport_y)

            # Emit scroll_changed signal to ensure synchronization
            self._emit_scroll_changed(0)

            event.accept()  # Prevent the event from propagating further
        else:
            super().wheelEvent(event)

    def zoom_in(self):
        """Zooms in the image."""
        self.zoom_scale = min(5.0, self.zoom_scale * 1.25)
        self.resize_to_image()
        self.zoom_changed.emit(self.zoom_scale)

    def zoom_out(self):
        """Zooms out the image."""
        self.zoom_scale = max(0.1, self.zoom_scale / 1.25)
        self.resize_to_image()
        self.zoom_changed.emit(self.zoom_scale)

    def setZoomScale(self, scale):
        """Sets the zoom scale to the specified value."""
        self.zoom_scale = max(0.1, min(5.0, scale))  # Limit scale between 0.1 and 5.0
        self.resize_to_image()
        self.zoom_changed.emit(self.zoom_scale)

        # Emit scroll_changed signal to ensure synchronization
        # This helps with bidirectional synchronization
        self._emit_scroll_changed(0)

    def update_zoom_label(self):
        """Updates and shows the zoom level indicator."""
        self._zoom_label.setText(f"{int(self.zoom_scale * 100)}%")
        self._zoom_label.adjustSize()
        self._zoom_label.move(10, 10)
        self._zoom_label.show()
        QTimer.singleShot(2000, self._zoom_label.hide)

    def mousePressEvent(self, event):
        """Handles mouse press events for panning and drawing."""
        if event.button() == Qt.MiddleButton or event.button() == Qt.RightButton:
            self._pan_start_pos = event.pos()
            self.setCursor(Qt.ClosedHandCursor)
            event.accept()
        elif event.button() == Qt.LeftButton:
            # Always emit mouse_pressed signal for left button clicks
            # This is needed for point counting manual click mode
            pos = self._label.mapFromParent(event.pos())
            scaled_pos = QPoint(int(pos.x() / self.zoom_scale), int(pos.y() / self.zoom_scale))
            # Emit the signal with the button information
            self.mouse_pressed.emit(scaled_pos)
            event.accept()

            # Let the parent handle the event if not in a special mode
            if not (self.drawing_enabled or self.erasing_enabled or
                    (hasattr(self, 'current_tool') and self.current_tool in ["sam_magic_wand", "sam_point_prompt", "sam_neg_point_prompt"])):
                super().mousePressEvent(event)
        else:
            super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """Handles mouse move events for panning and drawing."""
        if self._pan_start_pos:
            delta = event.pos() - self._pan_start_pos
            self._pan_start_pos = event.pos()

            h_bar = self.horizontalScrollBar()
            v_bar = self.verticalScrollBar()
            h_bar.setValue(h_bar.value() - delta.x())
            v_bar.setValue(v_bar.value() - delta.y())

            # Emit scroll_changed signal to ensure synchronization
            self._emit_scroll_changed(0)

            event.accept()
        elif event.buttons() & Qt.LeftButton and (self.drawing_enabled or self.erasing_enabled):
            pos = self._label.mapFromParent(event.pos())
            scaled_pos = QPoint(int(pos.x() / self.zoom_scale), int(pos.y() / self.zoom_scale))
            self.mouse_moved.emit(scaled_pos)
            event.accept()
        elif event.buttons() & Qt.LeftButton and self.current_tool in ["sam_magic_wand", "sam_point_prompt", "sam_neg_point_prompt"]:
            # Handle SAM tool interactions
            pos = self._label.mapFromParent(event.pos())
            scaled_pos = QPoint(int(pos.x() / self.zoom_scale), int(pos.y() / self.zoom_scale))
            self.mouse_moved.emit(scaled_pos)
            event.accept()
        else:
            super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """Handles mouse release events."""
        if event.button() == Qt.MiddleButton or event.button() == Qt.RightButton:
            self._pan_start_pos = None
            self.setCursor(Qt.ArrowCursor)
            event.accept()
        elif event.button() == Qt.LeftButton and (self.drawing_enabled or self.erasing_enabled):
            pos = self._label.mapFromParent(event.pos())
            scaled_pos = QPoint(int(pos.x() / self.zoom_scale), int(pos.y() / self.zoom_scale))
            self.mouse_released.emit(scaled_pos)
            event.accept()
        elif event.button() == Qt.LeftButton and self.current_tool in ["sam_magic_wand", "sam_point_prompt", "sam_neg_point_prompt"]:
            # Handle SAM tool interactions
            pos = self._label.mapFromParent(event.pos())
            scaled_pos = QPoint(int(pos.x() / self.zoom_scale), int(pos.y() / self.zoom_scale))
            self.mouse_released.emit(scaled_pos)
            event.accept()
        else:
            super().mouseReleaseEvent(event)

    def clear(self):
        """Clears the pixmap."""
        self._pixmap = QPixmap()
        self._label.setPixmap(self._pixmap)
        self._zoom_label.hide()
        self.zoom_scale = 1.0  # Reset zoom on clear
        self.resize_to_image()


    def get_pixmap_qimage(self) -> QImage:
        """Get current QImage from internal pixmap."""
        if self._pixmap:
             return self._pixmap.toImage()
        return None

    def centerOn(self, x, y):
        """Centers the view precisely on the given point (x, y) in image coordinates.

        This method takes into account the current zoom level and ensures the point
        is exactly centered in the viewport.

        Args:
            x: The x-coordinate in the original image
            y: The y-coordinate in the original image
        """
        if not self._pixmap:
            return

        # Convert image coordinates to widget coordinates (considering zoom)
        widget_x = x * self.zoom_scale
        widget_y = y * self.zoom_scale

        # Calculate the center of the viewport
        viewport_width = self.viewport().width()
        viewport_height = self.viewport().height()

        # Calculate the scroll position that centers the point
        h_value = max(0, int(widget_x - viewport_width / 2))
        v_value = max(0, int(widget_y - viewport_height / 2))

        # Set the scroll position
        self.horizontalScrollBar().setValue(h_value)
        self.verticalScrollBar().setValue(v_value)

        # Emit scroll_changed signal to ensure synchronization
        self._emit_scroll_changed(0)

    def zoomToPoint(self, x, y, zoom_level=None):
        """Zooms to a specific level and centers on a point.

        Args:
            x: The x-coordinate in the original image
            y: The y-coordinate in the original image
            zoom_level: The zoom level to set (if None, uses current zoom)
        """
        if not self._pixmap:
            return

        # Set zoom level if specified
        if zoom_level is not None:
            old_zoom = self.zoom_scale
            self.zoom_scale = max(0.1, min(5.0, zoom_level))

            # Only resize if zoom changed
            if old_zoom != self.zoom_scale:
                self.resize_to_image()
                self.zoom_changed.emit(self.zoom_scale)

        # Center on the point after a short delay to ensure resize is complete
        QTimer.singleShot(10, lambda: self.centerOn(x, y))