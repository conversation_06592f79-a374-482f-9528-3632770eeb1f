# src/utils/point_classifier.py

import numpy as np
import pickle
import os
from sklearn.neighbors import KNeighborsClassifier
from sklearn.svm import SVC
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline

class PointClassifier:
    """Classifier for point data based on extracted features."""
    
    CLASSIFIER_TYPES = {
        'knn': 'K-Nearest Neighbors',
        'svm': 'Support Vector Machine',
        'rf': 'Random Forest'
    }
    
    def __init__(self, classifier_type='knn', **kwargs):
        """
        Initialize the classifier.
        
        Args:
            classifier_type (str): Type of classifier to use ('knn', 'svm', or 'rf')
            **kwargs: Additional parameters for the classifier
        """
        self.classifier_type = classifier_type
        self.classifier = None
        self.scaler = StandardScaler()
        self.kwargs = kwargs
        self.is_trained = False
        self._create_classifier()
        
    def _create_classifier(self):
        """Create the classifier based on the specified type."""
        if self.classifier_type == 'knn':
            n_neighbors = self.kwargs.get('n_neighbors', 5)
            weights = self.kwargs.get('weights', 'distance')
            self.classifier = Pipeline([
                ('scaler', self.scaler),
                ('knn', KNeighborsClassifier(n_neighbors=n_neighbors, weights=weights))
            ])
        elif self.classifier_type == 'svm':
            C = self.kwargs.get('C', 1.0)
            kernel = self.kwargs.get('kernel', 'rbf')
            gamma = self.kwargs.get('gamma', 'scale')
            self.classifier = Pipeline([
                ('scaler', self.scaler),
                ('svm', SVC(C=C, kernel=kernel, gamma=gamma, probability=True))
            ])
        elif self.classifier_type == 'rf':
            n_estimators = self.kwargs.get('n_estimators', 100)
            max_depth = self.kwargs.get('max_depth', None)
            self.classifier = Pipeline([
                ('scaler', self.scaler),
                ('rf', RandomForestClassifier(n_estimators=n_estimators, max_depth=max_depth))
            ])
        else:
            raise ValueError(f"Unsupported classifier type: {self.classifier_type}")
    
    def train(self, features, labels):
        """
        Train the classifier on the provided features and labels.
        
        Args:
            features (numpy.ndarray): Feature matrix with shape (n_samples, n_features)
            labels (numpy.ndarray): Label vector with shape (n_samples,)
            
        Returns:
            bool: True if training was successful
        """
        if features.shape[0] == 0 or len(labels) == 0:
            return False
            
        try:
            self.classifier.fit(features, labels)
            self.is_trained = True
            return True
        except Exception as e:
            print(f"Error training classifier: {e}")
            self.is_trained = False
            return False
    
    def predict(self, features):
        """
        Predict class labels for the provided features.
        
        Args:
            features (numpy.ndarray): Feature matrix with shape (n_samples, n_features)
            
        Returns:
            numpy.ndarray: Predicted class labels
        """
        if not self.is_trained or features.shape[0] == 0:
            return np.array([])
            
        try:
            return self.classifier.predict(features)
        except Exception as e:
            print(f"Error predicting with classifier: {e}")
            return np.array([])
    
    def predict_proba(self, features):
        """
        Predict class probabilities for the provided features.
        
        Args:
            features (numpy.ndarray): Feature matrix with shape (n_samples, n_features)
            
        Returns:
            numpy.ndarray: Predicted class probabilities
        """
        if not self.is_trained or features.shape[0] == 0:
            return np.array([])
            
        try:
            return self.classifier.predict_proba(features)
        except Exception as e:
            print(f"Error predicting probabilities with classifier: {e}")
            return np.array([])
    
    def save_classifier(self, file_path):
        """
        Save the trained classifier to a file.
        
        Args:
            file_path (str): Path to save the classifier
            
        Returns:
            bool: True if saving was successful
        """
        if not self.is_trained:
            print("Cannot save untrained classifier")
            return False
            
        try:
            # Create the classifier data to save
            classifier_data = {
                'classifier': self.classifier,
                'classifier_type': self.classifier_type,
                'kwargs': self.kwargs,
                'is_trained': self.is_trained
            }
            
            # Ensure directory exists
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            with open(file_path, 'wb') as f:
                pickle.dump(classifier_data, f)
            return True
        except Exception as e:
            print(f"Error saving classifier: {e}")
            return False
    
    def load_classifier(self, file_path):
        """
        Load a trained classifier from a file.
        
        Args:
            file_path (str): Path to the saved classifier
            
        Returns:
            bool: True if loading was successful
        """
        try:
            with open(file_path, 'rb') as f:
                classifier_data = pickle.load(f)
            
            # Restore classifier state
            self.classifier = classifier_data['classifier']
            self.classifier_type = classifier_data['classifier_type']
            self.kwargs = classifier_data['kwargs']
            self.is_trained = classifier_data['is_trained']
            
            return True
        except Exception as e:
            print(f"Error loading classifier: {e}")
            return False
    
    @staticmethod
    def create_from_file(file_path):
        """
        Create a PointClassifier instance from a saved file.
        
        Args:
            file_path (str): Path to the saved classifier
            
        Returns:
            PointClassifier or None: Loaded classifier instance or None if failed
        """
        try:
            with open(file_path, 'rb') as f:
                classifier_data = pickle.load(f)
            
            # Create new instance
            instance = PointClassifier(
                classifier_type=classifier_data['classifier_type'],
                **classifier_data['kwargs']
            )
            
            # Restore state
            instance.classifier = classifier_data['classifier']
            instance.is_trained = classifier_data['is_trained']
            
            return instance
        except Exception as e:
            print(f"Error creating classifier from file: {e}")
            return None
    
    @staticmethod
    def get_classifier_types():
        """Get a dictionary of available classifier types."""
        return PointClassifier.CLASSIFIER_TYPES.copy()
