
# src/utils/image_utils.py
import cv2
import os
import numpy as np
from typing import Dict, <PERSON><PERSON>, Optional
from PySide6.QtGui import QImage, QPixmap
from pathlib import Path # Use pathlib
import logging

logger = logging.getLogger(__name__) # Add logger

def resize_image(image, target_size):
    """Resizes an image while maintaining aspect ratio."""
    if image is None or image.size == 0:
        logger.warning("resize_image received None or empty image.")
        return None
    try:
        original_height, original_width = image.shape[:2]
        target_width, target_height = target_size

        # Avoid division by zero
        if original_height == 0:
            logger.warning("resize_image received image with zero height.")
            return None

        aspect_ratio = original_width / original_height

        if target_width / target_height > aspect_ratio:
            new_height = target_height
            new_width = int(new_height * aspect_ratio)
        else:
            new_width = target_width
            new_height = int(new_width / aspect_ratio)

        # Ensure new dimensions are at least 1x1
        new_width = max(1, new_width)
        new_height = max(1, new_height)

        resized_image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
        return resized_image
    except Exception as e:
        logger.error(f"Error resizing image: {e}", exc_info=True)
        return None


def convert_cvimage_to_qimage(cv_image, already_rgb=False):
    """Converts an OpenCV image to a QImage.

    Handles BGR to RGB conversion unless already_rgb is True.
    Returns None if input is invalid.
    """
    if cv_image is None or cv_image.size == 0:
        logger.warning("convert_cvimage_to_qimage received None or empty image.")
        return None

    try:
        # Ensure the image is contiguous in memory
        cv_image = np.ascontiguousarray(cv_image)

        # Determine format based on channels
        if len(cv_image.shape) == 3 and cv_image.shape[2] == 3:
            if not already_rgb:
                # Convert BGR to RGB
                cv_image = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)
            h, w, ch = cv_image.shape
            bytes_per_line = ch * w
            qt_format = QImage.Format_RGB888
        elif len(cv_image.shape) == 2: # Grayscale
            h, w = cv_image.shape
            ch = 1
            bytes_per_line = w
            qt_format = QImage.Format_Grayscale8
        elif len(cv_image.shape) == 3 and cv_image.shape[2] == 4: # BGRA or RGBA? Assume BGRA from OpenCV usually
             if not already_rgb:
                  # Convert BGRA to RGBA
                  cv_image = cv2.cvtColor(cv_image, cv2.COLOR_BGRA2RGBA)
             h, w, ch = cv_image.shape
             bytes_per_line = ch * w
             qt_format = QImage.Format_RGBA8888
        else:
            logger.error(f"Unsupported image shape for QImage conversion: {cv_image.shape}")
            return None

        # Create QImage
        q_img = QImage(cv_image.data, w, h, bytes_per_line, qt_format)
        # Important: QImage shares data with the numpy array. Return a copy
        # to prevent issues if the numpy array goes out of scope or is modified.
        return q_img.copy()
    except Exception as e:
        logger.error(f"Error converting cv_image to QImage: {e}", exc_info=True)
        return None


def convert_cvimage_to_qpixmap(cv_image, already_rgb=False):
    """Converts an OpenCV image to QPixmap.

    Args:
        cv_image: The OpenCV image to convert
        already_rgb: Set to True if the image is already in RGB format
    """
    q_image = convert_cvimage_to_qimage(cv_image, already_rgb)
    if q_image:
        return QPixmap.fromImage(q_image)
    else:
        return None # Return None if QImage conversion failed


def get_image_info(image_path: str) -> Optional[Dict]:
    """Gets information about an image file including size and dimensions.

    Args:
        image_path: Path to the image file

    Returns:
        Dictionary with image information or None if the image couldn't be read
    """
    image_file = Path(image_path)
    if not image_file.is_file():
        logger.warning(f"Image file not found or not a file: {image_path}")
        return None

    try:
        # Get file size in MB
        file_size_bytes = image_file.stat().st_size
        file_size_mb = file_size_bytes / (1024 * 1024)

        # Load image header or minimal data if possible to avoid loading large files
        # For now, using imread, but consider libraries like Pillow for metadata
        img = cv2.imread(str(image_file))
        if img is None:
            logger.error(f"Failed to read image dimensions using OpenCV: {image_path}")
            # Try with Pillow as a fallback for basic info
            try:
                from PIL import Image
                with Image.open(image_file) as pil_img:
                    width, height = pil_img.size
                    channels = len(pil_img.getbands())
                    format_name = pil_img.format.lower() if pil_img.format else "unknown"
            except ImportError:
                 logger.warning("Pillow not installed. Cannot fallback for image info.")
                 return None
            except Exception as pil_e:
                 logger.error(f"Failed to read image dimensions using Pillow fallback: {pil_e}")
                 return None
        else:
            height, width = img.shape[:2]
            channels = img.shape[2] if len(img.shape) > 2 else 1
            # Get file extension
            format_name = image_file.suffix[1:].lower() if image_file.suffix else "unknown"

        return {
            "path": str(image_file), # Store as string
            "size_bytes": file_size_bytes,
            "size_mb": file_size_mb,
            "dimensions": (width, height),
            "channels": channels,
            "format": format_name,
            "needs_optimization": file_size_mb > 20 or width > 4000 or height > 4000 # Adjusted thresholds
        }
    except Exception as e:
        logger.error(f"Error analyzing image {image_path}: {e}", exc_info=True)
        return None


def apply_preprocessing_advanced(img, brightness, contrast, saturation, gamma, sharpness, blur):
    """Applies advanced preprocessing with all parameters."""
    if img is None:
        return None

    processed_img = img.copy() # Work on a copy

    try:
         # Brightness/Contrast: cv2.convertScaleAbs(src, alpha, beta) -> output = saturate(src * alpha + beta)
         # Adjust beta based on brightness. Alpha controls contrast.
         # Beta = 50 * (brightness - 1) maps brightness=1 to beta=0, brightness=2 to beta=50 etc.
         # Ensure brightness and contrast are floats
         brightness = float(brightness)
         contrast = float(contrast)
         processed_img = cv2.convertScaleAbs(processed_img, alpha=contrast, beta=50 * (brightness - 1))

         # Gamma Correction
         gamma = float(gamma)
         if gamma != 1.0 and gamma > 0: # Avoid gamma=0 or negative
             inv_gamma = 1.0 / gamma
             # Build lookup table
             table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in np.arange(0, 256)]).astype("uint8")
             processed_img = cv2.LUT(processed_img, table)

         # Saturation (convert to HSV)
         saturation = float(saturation)
         if saturation != 1.0:
             if len(processed_img.shape) == 3 and processed_img.shape[2] == 3:
                 hsv = cv2.cvtColor(processed_img, cv2.COLOR_BGR2HSV).astype("float32") # Assume BGR input from OpenCV usually
                 (h, s, v) = cv2.split(hsv)
                 s = s * saturation
                 s = np.clip(s, 0, 255) # Clamp saturation values
                 hsv = cv2.merge([h, s, v])
                 processed_img = cv2.cvtColor(hsv.astype("uint8"), cv2.COLOR_HSV2BGR) # Convert back to BGR
             else:
                 logger.warning("Saturation adjustment skipped: Image is not 3-channel BGR.")


         # Sharpness (Unsharp Masking)
         sharpness = float(sharpness)
         if sharpness > 0:
             # Use Gaussian blur as the blurred version
             # Kernel size (0,0) means derived from sigma (3)
             blurred = cv2.GaussianBlur(processed_img, (0, 0), 3)
             # Add weighted difference: img + sharpness * (img - blurred)
             processed_img = cv2.addWeighted(processed_img, 1.0 + sharpness, blurred, -sharpness, 0)

         # Blur (Gaussian Blur)
         blur = int(blur) # Blur radius should be integer
         if blur > 0:
             # Kernel size must be odd
             kernel_size = 2 * blur + 1
             processed_img = cv2.GaussianBlur(processed_img, (kernel_size, kernel_size), 0)

         return processed_img

    except Exception as e:
         logger.error(f"Error applying preprocessing: {e}", exc_info=True)
         return img # Return original image on error