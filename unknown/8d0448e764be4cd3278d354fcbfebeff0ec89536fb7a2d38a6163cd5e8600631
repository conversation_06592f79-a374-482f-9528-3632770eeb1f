_BASE_: "../COCO-InstanceSegmentation/mask_rcnn_R_50_FPN_3x.yaml"
MODEL:
  WEIGHTS: "detectron2://COCO-InstanceSegmentation/mask_rcnn_R_50_FPN_3x/137849600/model_final_f10217.pkl"
DATASETS:
  TEST: ("coco_2017_val_100",)
TEST:
  EXPECTED_RESULTS: [["bbox", "AP", 47.34, 0.02], ["segm", "AP",  42.67, 0.02], ["bbox_TTA", "AP", 49.11, 0.02], ["segm_TTA", "AP", 45.04, 0.02]]
  AUG:
    ENABLED: True
    MIN_SIZES: (700, 800)  # to save some time
FLOAT32_PRECISION: "highest"
