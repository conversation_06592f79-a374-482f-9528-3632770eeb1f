_BASE_: "../Base-RCNN-FPN.yaml"
MODEL:
  WEIGHTS: "detectron2://ImageNetPretrained/MSRA/R-50.pkl"
  ROI_HEADS:
    BATCH_SIZE_PER_IMAGE: 256
  MASK_ON: True
DATASETS:
  TRAIN: ("coco_2017_val",)
  TEST: ("coco_2017_val",)
INPUT:
  MIN_SIZE_TRAIN: (600,)
  MAX_SIZE_TRAIN: 1000
  MIN_SIZE_TEST: 800
  MAX_SIZE_TEST: 1000
SOLVER:
  WARMUP_FACTOR: 0.3333333
  WARMUP_ITERS: 100
  STEPS: (5500, 5800)
  MAX_ITER: 6000
TEST:
  EXPECTED_RESULTS: [["bbox", "AP", 42.5, 1.0], ["segm", "AP", 35.8, 0.8]]
