# -*- coding: utf-8 -*-
# Copyright (c) Facebook, Inc. and its affiliates.
from dataclasses import dataclass
from typing import Optional


@dataclass
class ShapeSpec:
    """
    A simple structure that contains basic shape specification about a tensor.
    It is often used as the auxiliary inputs/outputs of models,
    to complement the lack of shape inference ability among pytorch modules.
    """

    channels: Optional[int] = None
    height: Optional[int] = None
    width: Optional[int] = None
    stride: Optional[int] = None
