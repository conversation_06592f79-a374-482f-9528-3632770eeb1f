# src/widgets/grain_analysis_gallery.py

from PySide6.QtWidgets import QPushButton, QHBoxLayout, QWidget
# QIcon is used in the parent class
from PySide6.QtCore import Signal

from src.widgets.page_image_gallery import PageImageGallery

class GrainAnalysisGallery(PageImageGallery):
    """Image gallery specifically for the Grain Analysis Page."""

    # Additional signals specific to grain analysis page
    grain_analysis_requested = Signal(int)  # Signal to request grain analysis of an image

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_grain_analysis_controls()

    def setup_grain_analysis_controls(self):
        """Adds grain analysis-specific controls to the gallery."""
        # Create a container for additional controls
        controls_container = QWidget()
        controls_layout = QHBoxLayout(controls_container)
        controls_layout.setContentsMargins(0, 0, 0, 0)
        controls_layout.setSpacing(5)

        # Analyze Grains button removed as requested

        # Add the controls container to the layout
        self.layout.addWidget(controls_container)

    def _on_analyze_clicked(self):
        """Handler for analyze button clicks."""
        if self.selected_index >= 0:
            self.grain_analysis_requested.emit(self.selected_index)

    def get_all_images(self):
        """Returns all images in the gallery."""
        return self.images.copy()

    def get_all_file_paths(self):
        """Returns all file paths in the gallery."""
        return self.file_paths.copy()

    def clear_images(self):
        """Removes all images and thumbnails from the gallery."""
        # Call the parent class's clear method to handle the actual clearing
        self.clear()
        print("GrainAnalysisGallery: All images cleared.")
