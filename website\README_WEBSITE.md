# Vision Lab Website

A professional website for Vision Lab, a startup specializing in advanced computer vision applications. The website features a modern design, secure waitlist functionality, and comprehensive information about Vision Lab's software and services.

## Features

- **Modern Responsive Design**: Clean, professional interface that works on all devices
- **Secure Waitlist System**: Protected beta signup with rate limiting and data encryption
- **Comprehensive Sections**:
  - Software details with download links
  - Services offered by Vision Lab team
  - Publications and research
  - News feed and latest technology
  - Secure beta waitlist signup

## Security Features

- Email hashing for privacy protection
- Rate limiting to prevent abuse
- Input sanitization and validation
- CSRF protection
- Security headers (CSP, HSTS, etc.)
- Encrypted data storage
- Audit logging

## File Structure

```
├── index.html          # Main website HTML
├── styles.css          # Website styling
├── script.js           # Client-side JavaScript
├── server.py           # Flask web server
├── api.py              # Secure waitlist API
├── requirements_website.txt  # Python dependencies
└── README_WEBSITE.md   # This file
```

## Local Development Setup

### Prerequisites

- Python 3.8 or higher
- pip (Python package installer)

### Installation

1. **Install Python dependencies**:
   ```bash
   pip install -r requirements_website.txt
   ```

2. **Set environment variables** (optional but recommended):
   ```bash
   # Create a .env file
   SECRET_KEY=your-secret-key-here
   EMAIL_SALT=your-email-salt-here
   ENCRYPTION_KEY=your-encryption-key-here
   
   # Email configuration (optional)
   SMTP_SERVER=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USERNAME=<EMAIL>
   SMTP_PASSWORD=your-app-password
   FROM_EMAIL=<EMAIL>
   ```

3. **Run the development server**:
   ```bash
   python server.py
   ```

4. **Access the website**:
   Open your browser and go to `http://localhost:5000`

## Production Deployment

### Option 1: Netlify (Static Hosting + Serverless Functions)

1. **For static files**:
   - Upload `index.html`, `styles.css`, and `script.js` to Netlify
   - Configure custom domain if needed

2. **For API functionality**:
   - Convert `api.py` to Netlify Functions
   - Use Netlify Forms for simple waitlist (alternative)

### Option 2: Hostinger (Full Stack Hosting)

1. **Upload files** to your Hostinger hosting account
2. **Install Python dependencies** on the server
3. **Configure environment variables** in hosting panel
4. **Set up WSGI** configuration:
   ```python
   # wsgi.py
   from server import app
   
   if __name__ == "__main__":
       app.run()
   ```

### Option 3: VPS/Cloud Server

1. **Set up server** (Ubuntu/CentOS)
2. **Install dependencies**:
   ```bash
   sudo apt update
   sudo apt install python3 python3-pip nginx
   pip3 install -r requirements_website.txt
   pip3 install gunicorn
   ```

3. **Configure Gunicorn**:
   ```bash
   gunicorn --bind 0.0.0.0:8000 server:app
   ```

4. **Set up Nginx reverse proxy**:
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       location / {
           proxy_pass http://127.0.0.1:8000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

## Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|----------|
| `SECRET_KEY` | Flask secret key | No | Auto-generated |
| `EMAIL_SALT` | Salt for email hashing | No | Default salt |
| `ENCRYPTION_KEY` | Key for data encryption | No | Default key |
| `SMTP_SERVER` | Email server hostname | No | localhost |
| `SMTP_PORT` | Email server port | No | 587 |
| `SMTP_USERNAME` | Email username | No | - |
| `SMTP_PASSWORD` | Email password | No | - |
| `FROM_EMAIL` | Sender email address | No | <EMAIL> |
| `FLASK_ENV` | Environment (development/production) | No | production |
| `PORT` | Server port | No | 5000 |
| `HOST` | Server host | No | 127.0.0.1 |

## API Endpoints

### POST /api/waitlist
Add user to beta waitlist

**Request Body**:
```json
{
  "fullName": "John Doe",
  "email": "<EMAIL>",
  "organization": "University of Example",
  "useCase": "research",
  "experience": "intermediate",
  "newsletter": true,
  "privacy": true
}
```

**Response**:
```json
{
  "success": true,
  "message": "Successfully added to waitlist",
  "waitlist_position": 42,
  "verification_sent": true
}
```

### GET /api/stats
Get public waitlist statistics

**Response**:
```json
{
  "total_signups": 150,
  "last_updated": "2024-12-15T10:30:00"
}
```

### GET /api/health
Health check endpoint

**Response**:
```json
{
  "status": "healthy",
  "timestamp": "2024-12-15T10:30:00",
  "version": "1.0.0"
}
```

## Security Considerations

1. **Change default secrets** in production
2. **Use HTTPS** for all production deployments
3. **Configure firewall** to restrict access
4. **Regular security updates** for dependencies
5. **Monitor logs** for suspicious activity
6. **Backup database** regularly
7. **Use environment variables** for sensitive data

## Database

The application uses SQLite for simplicity. The database includes:

- `waitlist` table: Stores encrypted user data
- `rate_limits` table: Tracks request rates
- `audit_log` table: Security audit trail

For production, consider migrating to PostgreSQL or MySQL.

## Monitoring and Logs

- Server logs: `server.log`
- API logs: `waitlist_api.log`
- Database: `waitlist.db`

## Customization

### Styling
Modify `styles.css` to change the website appearance. The design uses:
- Inter font family
- Blue color scheme (#2563eb)
- Responsive grid layouts
- Modern CSS animations

### Content
Update `index.html` to modify:
- Company information
- Service descriptions
- Publication details
- News articles

### Functionality
Extend `script.js` for additional features:
- Analytics integration
- Chat widgets
- Additional form validation
- Custom animations

## Support

For technical support or questions:
- Email: <EMAIL>
- Documentation: [Vision Lab Docs](https://docs.visionlab.ai)
- GitHub Issues: [Report Issues](https://github.com/visionlab/website/issues)

## License

© 2024 Vision Lab. All rights reserved.

---

**Note**: This website is designed to be professional, secure, and scalable. All security best practices have been implemented to protect user data and prevent abuse.