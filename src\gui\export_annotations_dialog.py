# src/gui/export_annotations_dialog.py

import os
import json
import logging
import numpy as np
import cv2
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                              QPushButton, QFileDialog, QMessageBox, QComboBox,
                              QLineEdit, QProgressBar, QGroupBox, QFormLayout,
                              QCheckBox)
from PySide6.QtCore import Qt

logger = logging.getLogger(__name__)

class ExportAnnotationsDialog(QDialog):
    """Dialog for exporting annotations in various formats."""

    def __init__(self, annotations=None, class_names=None, image_paths=None, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Export Annotations")
        self.setMinimumWidth(500)
        self.setMinimumHeight(300)

        # Initialize data
        self.annotations = annotations or {}
        self.class_names = class_names or {}
        self.image_paths = image_paths or []

        # Legacy support for segmentation export
        self.segmented_image = None
        self.original_image = None
        self.segment_names = None
        self.image_path = None

        # Set up the UI
        self.setup_ui()

    def setup_ui(self):
        """Set up the dialog UI."""
        main_layout = QVBoxLayout(self)

        # Check if we're in legacy mode (segmented image)
        if self.segmented_image is not None:
            self._setup_legacy_ui(main_layout)
            return

        # Format selection
        format_group = QGroupBox("Export Format")
        format_layout = QFormLayout(format_group)

        self.format_combo = QComboBox()
        self.format_combo.addItem("COCO JSON", "coco")
        self.format_combo.addItem("YOLO", "yolo")
        self.format_combo.addItem("Pascal VOC", "pascal_voc")
        self.format_combo.addItem("Mask Images", "mask_images")
        self.format_combo.currentIndexChanged.connect(self.on_format_changed)
        format_layout.addRow("Format:", self.format_combo)

        main_layout.addWidget(format_group)

        # Export options
        options_group = QGroupBox("Export Options")
        options_layout = QFormLayout(options_group)

        # Export path
        export_path_layout = QHBoxLayout()
        self.export_path = QLineEdit()
        self.export_path.setReadOnly(True)
        self.browse_button = QPushButton("Browse...")
        self.browse_button.clicked.connect(self.browse_export_path)
        export_path_layout.addWidget(self.export_path)
        export_path_layout.addWidget(self.browse_button)
        options_layout.addRow("Export Path:", export_path_layout)

        # Copy images option
        self.copy_images = QCheckBox("Copy images to export directory")
        self.copy_images.setChecked(True)
        options_layout.addRow("", self.copy_images)

        # Format-specific options
        self.yolo_options = QGroupBox("YOLO Options")
        yolo_layout = QFormLayout(self.yolo_options)

        self.create_dataset_yaml = QCheckBox("Create dataset.yaml file")
        self.create_dataset_yaml.setChecked(True)
        yolo_layout.addRow("", self.create_dataset_yaml)

        self.yolo_options.setVisible(False)
        options_layout.addRow("", self.yolo_options)

        self.mask_options = QGroupBox("Mask Image Options")
        mask_layout = QFormLayout(self.mask_options)

        self.colored_masks = QCheckBox("Use class colors for masks")
        self.colored_masks.setChecked(True)
        mask_layout.addRow("", self.colored_masks)

        self.mask_options.setVisible(False)
        options_layout.addRow("", self.mask_options)

        main_layout.addWidget(options_group)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)

        # Buttons
        buttons_layout = QHBoxLayout()

        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.reject)

        self.export_button = QPushButton("Export")
        self.export_button.clicked.connect(self.export_annotations)

        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.export_button)

        main_layout.addLayout(buttons_layout)

    def _setup_legacy_ui(self, layout):
        """Sets up the legacy dialog UI for segmented image export."""
        # Info label
        if self.segmented_image is not None:
            info_text = f"Ready to export {self.segmented_image.shape[1]}x{self.segmented_image.shape[0]} segmented image as annotations."
        else:
            info_text = "No segmented image available. Please segment an image first."

        info_label = QLabel(info_text)
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # Buttons
        buttons_layout = QHBoxLayout()

        self.export_button = QPushButton("Export Annotations")
        self.export_button.clicked.connect(self.export_segmented_image)
        self.export_button.setEnabled(self.segmented_image is not None)
        buttons_layout.addWidget(self.export_button)

        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_button)

        layout.addLayout(buttons_layout)

    def on_format_changed(self, index):
        """Handle format selection changes."""
        format_id = self.format_combo.currentData()

        # Show/hide format-specific options
        self.yolo_options.setVisible(format_id == "yolo")
        self.mask_options.setVisible(format_id == "mask_images")

    def browse_export_path(self):
        """Browse for export path."""
        path = QFileDialog.getExistingDirectory(self, "Select Export Directory", os.path.expanduser("~"))
        if path:
            self.export_path.setText(path)

    def export_annotations(self):
        """Export annotations in the selected format."""
        # Check if we're in legacy mode
        if self.segmented_image is not None:
            self.export_segmented_image()
            return

        # Check if export path is set
        export_path = self.export_path.text()
        if not export_path:
            QMessageBox.warning(self, "Warning", "Please select an export path")
            return

        # Check if the path exists
        if not os.path.exists(export_path):
            try:
                os.makedirs(export_path)
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to create export directory: {str(e)}")
                return

        # Get export format
        export_format = self.format_combo.currentData()

        # Show progress bar
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.progress_bar.setMaximum(len(self.annotations))

        try:
            # Export based on format
            if export_format == "coco":
                self.export_coco(export_path)
            elif export_format == "yolo":
                self.export_yolo(export_path)
            elif export_format == "pascal_voc":
                self.export_pascal_voc(export_path)
            elif export_format == "mask_images":
                self.export_mask_images(export_path)

            # Show success message
            QMessageBox.information(self, "Success", f"Annotations exported successfully to {export_path}")
            self.accept()

        except Exception as e:
            logger.error(f"Error exporting annotations: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to export annotations: {str(e)}")
            self.progress_bar.setVisible(False)

    def export_segmented_image(self):
        """Exports the segmented image as annotations (legacy method)."""
        if self.segmented_image is None:
            QMessageBox.warning(self, "Warning", "No segmented image available.")
            return

        # Get unique colors in the segmented image
        unique_colors = set()
        for y in range(self.segmented_image.shape[0]):
            for x in range(self.segmented_image.shape[1]):
                unique_colors.add(tuple(self.segmented_image[y, x]))

        # Sort colors
        sorted_colors = list(unique_colors)

        # Create a label map (color -> label index)
        label_map = {}
        for i, color in enumerate(sorted_colors):
            # Skip background (black)
            if color == (0, 0, 0):
                label_map[color] = 0  # Background is 0
            else:
                label_map[color] = i + 1  # Labels start from 1

        # Create annotation mask
        annotations = np.zeros(self.segmented_image.shape[:2], dtype=np.uint8)
        for y in range(self.segmented_image.shape[0]):
            for x in range(self.segmented_image.shape[1]):
                color = tuple(self.segmented_image[y, x])
                annotations[y, x] = label_map[color]

        # Open file dialog to select save location
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export Annotations", "",
            "NumPy Files (*.npz);;All Files (*)"
        )

        if not file_path:
            return

        # Ensure the file has .npz extension
        if not file_path.endswith('.npz'):
            file_path += '.npz'

        # Create a dictionary to store segment names
        segment_names = {}
        for color, label in label_map.items():
            if label > 0:  # Skip background
                name = self.segment_names.get(color, f"Segment {label}")
                segment_names[label] = name

        # Save annotations, original image, and segment names
        try:
            np.savez_compressed(
                file_path,
                annotations=annotations,
                image=self.original_image,
                segment_names=segment_names,
                image_path=self.image_path
            )
            QMessageBox.information(self, "Success", f"Annotations exported to {file_path}")
            self.accept()
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to export annotations: {e}")

    def export_coco(self, export_path):
        """Export annotations in COCO format."""
        # Create COCO structure
        coco_data = {
            "info": {
                "description": "VisionLab Ai Annotations",
                "version": "1.0",
                "year": 2023,
                "contributor": "VisionLab Ai",
                "date_created": ""
            },
            "licenses": [
                {
                    "id": 1,
                    "name": "Unknown",
                    "url": ""
                }
            ],
            "images": [],
            "annotations": [],
            "categories": []
        }

        # Add categories
        for class_id, class_name in self.class_names.items():
            coco_data["categories"].append({
                "id": class_id,
                "name": class_name,
                "supercategory": "object"
            })

        # Create images directory if copying images
        images_dir = os.path.join(export_path, "images")
        if self.copy_images.isChecked():
            os.makedirs(images_dir, exist_ok=True)

        # Process each image
        annotation_id = 1
        for image_id, image_path in enumerate(self.image_paths, 1):
            if image_path not in self.annotations:
                continue

            # Get image info
            image = cv2.imread(image_path)
            if image is None:
                logger.warning(f"Failed to load image: {image_path}")
                continue

            height, width = image.shape[:2]
            filename = os.path.basename(image_path)

            # Add image to COCO data
            coco_data["images"].append({
                "id": image_id,
                "width": width,
                "height": height,
                "file_name": filename,
                "license": 1,
                "flickr_url": "",
                "coco_url": "",
                "date_captured": ""
            })

            # Copy image if needed
            if self.copy_images.isChecked():
                try:
                    cv2.imwrite(os.path.join(images_dir, filename), image)
                except Exception as e:
                    logger.error(f"Error copying image {filename}: {str(e)}")

            # Process annotations
            for annotation in self.annotations[image_path]:
                annotation_data = {
                    "id": annotation_id,
                    "image_id": image_id,
                    "category_id": annotation.get('class_id', 1),
                    "iscrowd": 0
                }

                if annotation['type'] == 'polygon':
                    # Convert polygon points to COCO format
                    points = annotation['points']
                    segmentation = []
                    for point in points:
                        segmentation.extend([float(point[0]), float(point[1])])

                    # Calculate bounding box
                    x_coords = [p[0] for p in points]
                    y_coords = [p[1] for p in points]
                    x_min, y_min = min(x_coords), min(y_coords)
                    width, height = max(x_coords) - x_min, max(y_coords) - y_min

                    annotation_data.update({
                        "segmentation": [segmentation],
                        "area": width * height,
                        "bbox": [x_min, y_min, width, height]
                    })

                elif annotation['type'] == 'rectangle':
                    # Get rectangle points
                    x1, y1 = annotation['points'][0]
                    x2, y2 = annotation['points'][1]

                    # Ensure x1,y1 is top-left and x2,y2 is bottom-right
                    x_min, x_max = min(x1, x2), max(x1, x2)
                    y_min, y_max = min(y1, y2), max(y1, y2)
                    width, height = x_max - x_min, y_max - y_min

                    # Create segmentation (rectangle as polygon)
                    segmentation = [
                        x_min, y_min,
                        x_max, y_min,
                        x_max, y_max,
                        x_min, y_max
                    ]

                    annotation_data.update({
                        "segmentation": [segmentation],
                        "area": width * height,
                        "bbox": [x_min, y_min, width, height]
                    })

                elif annotation['type'] == 'mask':
                    # Convert mask to polygon
                    mask = annotation['mask']
                    contours, _ = cv2.findContours(
                        mask.astype(np.uint8),
                        cv2.RETR_EXTERNAL,
                        cv2.CHAIN_APPROX_SIMPLE
                    )

                    if contours:
                        # Use the largest contour
                        largest_contour = max(contours, key=cv2.contourArea)

                        # Convert contour to segmentation
                        segmentation = []
                        for point in largest_contour.reshape(-1, 2):
                            segmentation.extend([float(point[0]), float(point[1])])

                        # Calculate bounding box
                        x, y, w, h = cv2.boundingRect(largest_contour)

                        annotation_data.update({
                            "segmentation": [segmentation],
                            "area": cv2.contourArea(largest_contour),
                            "bbox": [x, y, w, h]
                        })

                # Add annotation to COCO data
                coco_data["annotations"].append(annotation_data)
                annotation_id += 1

            # Update progress
            self.progress_bar.setValue(self.progress_bar.value() + 1)

        # Write COCO JSON file
        with open(os.path.join(export_path, "annotations.json"), 'w') as f:
            json.dump(coco_data, f, indent=2)

    def export_yolo(self, export_path):
        """Export annotations in YOLO format."""
        # Create directories
        images_dir = os.path.join(export_path, "images")
        labels_dir = os.path.join(export_path, "labels")

        os.makedirs(images_dir, exist_ok=True)
        os.makedirs(labels_dir, exist_ok=True)

        # Process each image
        for i, image_path in enumerate(self.image_paths):
            if image_path not in self.annotations:
                continue

            # Get image info
            image = cv2.imread(image_path)
            if image is None:
                logger.warning(f"Failed to load image: {image_path}")
                continue

            height, width = image.shape[:2]
            filename = os.path.basename(image_path)
            basename = os.path.splitext(filename)[0]

            # Copy image if needed
            if self.copy_images.isChecked():
                try:
                    cv2.imwrite(os.path.join(images_dir, filename), image)
                except Exception as e:
                    logger.error(f"Error copying image {filename}: {str(e)}")

            # Create label file
            label_file = os.path.join(labels_dir, f"{basename}.txt")
            with open(label_file, 'w') as f:
                for annotation in self.annotations[image_path]:
                    class_id = annotation.get('class_id', 1)

                    if annotation['type'] == 'rectangle':
                        # Get rectangle points
                        x1, y1 = annotation['points'][0]
                        x2, y2 = annotation['points'][1]

                        # Ensure x1,y1 is top-left and x2,y2 is bottom-right
                        x_min, x_max = min(x1, x2), max(x1, x2)
                        y_min, y_max = min(y1, y2), max(y1, y2)

                        # Convert to YOLO format (center_x, center_y, width, height)
                        center_x = (x_min + x_max) / 2 / width
                        center_y = (y_min + y_max) / 2 / height
                        box_width = (x_max - x_min) / width
                        box_height = (y_max - y_min) / height

                        # Write to file
                        f.write(f"{class_id-1} {center_x:.6f} {center_y:.6f} {box_width:.6f} {box_height:.6f}\n")

                    elif annotation['type'] == 'polygon' or annotation['type'] == 'mask':
                        # For polygons and masks, convert to bounding box
                        if annotation['type'] == 'polygon':
                            points = annotation['points']
                            x_coords = [p[0] for p in points]
                            y_coords = [p[1] for p in points]
                            x_min, x_max = min(x_coords), max(x_coords)
                            y_min, y_max = min(y_coords), max(y_coords)
                        else:  # mask
                            mask = annotation['mask']
                            y_indices, x_indices = np.where(mask > 0)
                            if len(y_indices) == 0 or len(x_indices) == 0:
                                continue
                            x_min, x_max = min(x_indices), max(x_indices)
                            y_min, y_max = min(y_indices), max(y_indices)

                        # Convert to YOLO format
                        center_x = (x_min + x_max) / 2 / width
                        center_y = (y_min + y_max) / 2 / height
                        box_width = (x_max - x_min) / width
                        box_height = (y_max - y_min) / height

                        # Write to file
                        f.write(f"{class_id-1} {center_x:.6f} {center_y:.6f} {box_width:.6f} {box_height:.6f}\n")

            # Update progress
            self.progress_bar.setValue(i + 1)

        # Create dataset.yaml if needed
        if self.create_dataset_yaml.isChecked():
            yaml_content = f"""
# Dataset configuration
path: {os.path.abspath(export_path)}
train: images
val: images

# Classes
names:
"""
            # Add class names
            for class_id, class_name in sorted(self.class_names.items()):
                yaml_content += f"  {class_id-1}: {class_name}\n"

            # Write YAML file
            with open(os.path.join(export_path, "dataset.yaml"), 'w') as f:
                f.write(yaml_content)

    def export_pascal_voc(self, export_path):
        """Export annotations in Pascal VOC format."""
        # Create directories
        images_dir = os.path.join(export_path, "JPEGImages")
        annotations_dir = os.path.join(export_path, "Annotations")

        os.makedirs(images_dir, exist_ok=True)
        os.makedirs(annotations_dir, exist_ok=True)

        # Create ImageSets directory and Main subdirectory
        imagesets_dir = os.path.join(export_path, "ImageSets")
        main_dir = os.path.join(imagesets_dir, "Main")
        os.makedirs(main_dir, exist_ok=True)

        # List of all image basenames
        all_basenames = []

        # Process each image
        for i, image_path in enumerate(self.image_paths):
            if image_path not in self.annotations:
                continue

            # Get image info
            image = cv2.imread(image_path)
            if image is None:
                logger.warning(f"Failed to load image: {image_path}")
                continue

            height, width = image.shape[:2]
            filename = os.path.basename(image_path)
            basename = os.path.splitext(filename)[0]
            all_basenames.append(basename)

            # Copy image if needed
            if self.copy_images.isChecked():
                try:
                    cv2.imwrite(os.path.join(images_dir, filename), image)
                except Exception as e:
                    logger.error(f"Error copying image {filename}: {str(e)}")

            # Create XML annotation file
            xml_content = f"""<annotation>
    <folder>JPEGImages</folder>
    <filename>{filename}</filename>
    <path>{os.path.join(images_dir, filename)}</path>
    <source>
        <database>VisionLab_Ai</database>
    </source>
    <size>
        <width>{width}</width>
        <height>{height}</height>
        <depth>3</depth>
    </size>
    <segmented>0</segmented>
"""

            # Add objects
            for annotation in self.annotations[image_path]:
                class_id = annotation.get('class_id', 1)
                class_name = self.class_names.get(class_id, f"Class_{class_id}")

                if annotation['type'] == 'rectangle':
                    # Get rectangle points
                    x1, y1 = annotation['points'][0]
                    x2, y2 = annotation['points'][1]

                    # Ensure x1,y1 is top-left and x2,y2 is bottom-right
                    x_min, x_max = min(x1, x2), max(x1, x2)
                    y_min, y_max = min(y1, y2), max(y1, y2)

                    # Add object to XML
                    xml_content += f"""    <object>
        <name>{class_name}</name>
        <pose>Unspecified</pose>
        <truncated>0</truncated>
        <difficult>0</difficult>
        <bndbox>
            <xmin>{int(x_min)}</xmin>
            <ymin>{int(y_min)}</ymin>
            <xmax>{int(x_max)}</xmax>
            <ymax>{int(y_max)}</ymax>
        </bndbox>
    </object>
"""

                elif annotation['type'] == 'polygon':
                    # Convert polygon to bounding box
                    points = annotation['points']
                    x_coords = [p[0] for p in points]
                    y_coords = [p[1] for p in points]
                    x_min, x_max = min(x_coords), max(x_coords)
                    y_min, y_max = min(y_coords), max(y_coords)

                    # Add object to XML
                    xml_content += f"""    <object>
        <name>{class_name}</name>
        <pose>Unspecified</pose>
        <truncated>0</truncated>
        <difficult>0</difficult>
        <bndbox>
            <xmin>{int(x_min)}</xmin>
            <ymin>{int(y_min)}</ymin>
            <xmax>{int(x_max)}</xmax>
            <ymax>{int(y_max)}</ymax>
        </bndbox>
    </object>
"""

                elif annotation['type'] == 'mask':
                    # Convert mask to bounding box
                    mask = annotation['mask']
                    y_indices, x_indices = np.where(mask > 0)
                    if len(y_indices) == 0 or len(x_indices) == 0:
                        continue
                    x_min, x_max = min(x_indices), max(x_indices)
                    y_min, y_max = min(y_indices), max(y_indices)

                    # Add object to XML
                    xml_content += f"""    <object>
        <name>{class_name}</name>
        <pose>Unspecified</pose>
        <truncated>0</truncated>
        <difficult>0</difficult>
        <bndbox>
            <xmin>{int(x_min)}</xmin>
            <ymin>{int(y_min)}</ymin>
            <xmax>{int(x_max)}</xmax>
            <ymax>{int(y_max)}</ymax>
        </bndbox>
    </object>
"""

            # Close XML
            xml_content += "</annotation>"

            # Write XML file
            with open(os.path.join(annotations_dir, f"{basename}.xml"), 'w') as f:
                f.write(xml_content)

            # Update progress
            self.progress_bar.setValue(i + 1)

        # Create train.txt with all image basenames
        with open(os.path.join(main_dir, "train.txt"), 'w') as f:
            for basename in all_basenames:
                f.write(f"{basename}\n")

        # Create trainval.txt (same as train.txt for simplicity)
        with open(os.path.join(main_dir, "trainval.txt"), 'w') as f:
            for basename in all_basenames:
                f.write(f"{basename}\n")

    def export_mask_images(self, export_path):
        """Export annotations as mask images."""
        # Create directories
        images_dir = os.path.join(export_path, "images")
        masks_dir = os.path.join(export_path, "masks")

        os.makedirs(images_dir, exist_ok=True)
        os.makedirs(masks_dir, exist_ok=True)

        # Process each image
        for i, image_path in enumerate(self.image_paths):
            if image_path not in self.annotations:
                continue

            # Get image info
            image = cv2.imread(image_path)
            if image is None:
                logger.warning(f"Failed to load image: {image_path}")
                continue

            height, width = image.shape[:2]
            filename = os.path.basename(image_path)
            basename = os.path.splitext(filename)[0]

            # Copy image if needed
            if self.copy_images.isChecked():
                try:
                    cv2.imwrite(os.path.join(images_dir, filename), image)
                except Exception as e:
                    logger.error(f"Error copying image {filename}: {str(e)}")

            # Create mask image
            if self.colored_masks.isChecked():
                # Colored mask (RGB)
                mask_image = np.zeros((height, width, 3), dtype=np.uint8)
            else:
                # Grayscale mask (class ID as pixel value)
                mask_image = np.zeros((height, width), dtype=np.uint8)

            # Add annotations to mask
            for annotation in self.annotations[image_path]:
                class_id = annotation.get('class_id', 1)

                if self.colored_masks.isChecked():
                    # Use class ID to generate a color (simple hash)
                    color = [
                        (class_id * 50) % 255,
                        (class_id * 100) % 255,
                        (class_id * 150) % 255
                    ]
                else:
                    # Use class ID as pixel value
                    color = class_id

                if annotation['type'] == 'polygon':
                    # Draw filled polygon
                    points = np.array(annotation['points'], dtype=np.int32)
                    if self.colored_masks.isChecked():
                        cv2.fillPoly(mask_image, [points], color)
                    else:
                        cv2.fillPoly(mask_image, [points], color)

                elif annotation['type'] == 'rectangle':
                    # Get rectangle points
                    x1, y1 = annotation['points'][0]
                    x2, y2 = annotation['points'][1]

                    # Ensure x1,y1 is top-left and x2,y2 is bottom-right
                    x_min, x_max = min(x1, x2), max(x1, x2)
                    y_min, y_max = min(y1, y2), max(y1, y2)

                    # Draw filled rectangle
                    if self.colored_masks.isChecked():
                        cv2.rectangle(mask_image, (int(x_min), int(y_min)), (int(x_max), int(y_max)), color, -1)
                    else:
                        cv2.rectangle(mask_image, (int(x_min), int(y_min)), (int(x_max), int(y_max)), color, -1)

                elif annotation['type'] == 'mask':
                    # Apply mask
                    mask = annotation['mask']
                    if self.colored_masks.isChecked():
                        for c in range(3):
                            mask_image[:, :, c][mask > 0] = color[c]
                    else:
                        mask_image[mask > 0] = color

            # Save mask image
            mask_filename = f"{basename}_mask.png"
            cv2.imwrite(os.path.join(masks_dir, mask_filename), mask_image)

            # Update progress
            self.progress_bar.setValue(i + 1)

        # Create class mapping file
        with open(os.path.join(export_path, "class_mapping.txt"), 'w') as f:
            f.write("# Class ID to Name Mapping\n")
            for class_id, class_name in sorted(self.class_names.items()):
                f.write(f"{class_id}: {class_name}\n")
