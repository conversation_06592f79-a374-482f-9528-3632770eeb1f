# src/ai_assistant_components/src/gemini/check_dependencies.py

import importlib
import logging

logger = logging.getLogger(__name__)

def check_gemini_dependencies():
    """Check if the required dependencies for Gemini API are installed."""
    missing_packages = []
    
    # List of required packages
    required_packages = [
        "google.generativeai",
        "PIL",
    ]
    
    # Check each package
    for package in required_packages:
        try:
            importlib.import_module(package)
            logger.info(f"Package {package} is installed")
        except ImportError:
            logger.error(f"Package {package} is not installed")
            missing_packages.append(package)
    
    # Return the list of missing packages
    return missing_packages

def get_installation_instructions(missing_packages):
    """Get installation instructions for missing packages."""
    if not missing_packages:
        return "All required packages are installed."
    
    # Map package names to pip install commands
    package_map = {
        "google.generativeai": "google-generativeai",
        "PIL": "pillow",
    }
    
    # Create installation instructions
    instructions = "The following packages are required but not installed:\n\n"
    instructions += "Run the following command to install them:\n\n"
    instructions += "pip install "
    
    # Add each package to the command
    for package in missing_packages:
        pip_package = package_map.get(package, package)
        instructions += f"{pip_package} "
    
    return instructions
