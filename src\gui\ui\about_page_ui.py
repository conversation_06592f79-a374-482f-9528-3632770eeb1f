# src/gui/ui/about_page_ui.py

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QScrollArea, 
    QFrame, QPushButton, QTabWidget, QTextEdit, QSplitter
)
from PySide6.QtCore import Qt, QUrl
from PySide6.QtGui import Q<PERSON>ont, QPixmap, QDesktopServices
from src.gui.styles.theme_config import get_palette
from src.gui.styles.theme_aware_buttons import get_theme_colors, get_theme_name
import os

class AboutPageUI:
    """UI setup for the About page."""

    def setup_about_page(self):
        """Sets up the About page UI."""
        # Create the main about page widget
        self.about_page = QWidget()
        
        # Add the about page to the stacked widget
        self.stacked_widget.addTab(self.about_page, "About")
        
        # Setup the about page layout
        self.setup_about_page_layout()
        
    def setup_about_page_layout(self):
        """Sets up the layout for the About page."""
        # Main layout
        main_layout = QVBoxLayout(self.about_page)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Create tab widget for different sections
        self.about_tab_widget = QTabWidget()
        self.about_tab_widget.setStyleSheet(self._get_theme_aware_tab_style())
        main_layout.addWidget(self.about_tab_widget)
        
        # Setup individual tabs
        self.setup_app_info_tab()
        self.setup_creator_info_tab()
        self.setup_license_tab()
        self.setup_tutorial_tab()
        
    def setup_app_info_tab(self):
        """Sets up the application information tab."""
        app_info_widget = QWidget()
        layout = QVBoxLayout(app_info_widget)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # App title and version
        title_frame = QFrame()
        title_frame.setFrameStyle(QFrame.Box)
        title_frame.setStyleSheet(self._get_theme_aware_frame_style())
        
        title_layout = QVBoxLayout(title_frame)
        
        # App name
        app_name = QLabel("VisionLab AI")
        app_name.setAlignment(Qt.AlignCenter)
        font = QFont("Arial", 28)
        font.setBold(True)
        app_name.setFont(font)
        app_name.setStyleSheet(self._get_theme_aware_title_style())
        title_layout.addWidget(app_name)
        
        # Version
        version_label = QLabel("Version 4.0")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setFont(QFont("Arial", 16))
        version_label.setStyleSheet(self._get_theme_aware_subtitle_style())
        title_layout.addWidget(version_label)
        
        # Subtitle
        subtitle = QLabel("Advanced Petrographic Image Analysis & Segmentation")
        subtitle.setAlignment(Qt.AlignCenter)
        font = QFont("Arial", 14)
        font.setItalic(True)
        subtitle.setFont(font)
        subtitle.setStyleSheet(self._get_theme_aware_accent_style())
        title_layout.addWidget(subtitle)
        
        layout.addWidget(title_frame)
        
        # Description
        description_text = QTextEdit()
        description_text.setReadOnly(True)
        description_text.setMaximumHeight(300)
        description_text.setStyleSheet(self._get_theme_aware_text_edit_style())
        
        description_content = """
        <h3 style="color: #88C0D0; margin-bottom: 15px;">About VisionLab AI</h3>
        
        <p style="margin-bottom: 12px;">VisionLab AI is a comprehensive petrographic image analysis platform designed for geologists, researchers, and students working with thin section microscopy.</p>
        
        <p style="margin-bottom: 12px;"><strong style="color: #A3BE8C;">Key Features:</strong></p>
        <ul style="margin-left: 20px; margin-bottom: 15px;">
            <li style="margin-bottom: 8px;">🔬 <strong>Trainable Segmentation:</strong> AI-powered mineral identification and grain boundary detection</li>
            <li style="margin-bottom: 8px;">📊 <strong>Point Counting:</strong> Automated and manual modal analysis tools</li>
            <li style="margin-bottom: 8px;">📈 <strong>Grain Analysis:</strong> Comprehensive grain size and shape statistics</li>
            <li style="margin-bottom: 8px;">🤖 <strong>AI Assistant:</strong> Intelligent help and guidance system</li>
            <li style="margin-bottom: 8px;">⚡ <strong>Batch Processing:</strong> Efficient analysis of multiple samples</li>
            <li style="margin-bottom: 8px;">📋 <strong>Project Management:</strong> Organized workflow and data management</li>
        </ul>
        
        <p style="margin-bottom: 12px;">Built with cutting-edge computer vision and machine learning technologies, VisionLab AI streamlines the petrographic analysis workflow while maintaining scientific accuracy and reproducibility.</p>
        """
        
        description_text.setHtml(description_content)
        layout.addWidget(description_text)
        
        self.about_tab_widget.addTab(app_info_widget, "Application Info")
        
    def setup_creator_info_tab(self):
        """Sets up the creator information tab."""
        creator_widget = QWidget()
        layout = QVBoxLayout(creator_widget)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # Creator info frame
        creator_frame = QFrame()
        creator_frame.setFrameStyle(QFrame.Box)
        creator_frame.setStyleSheet(self._get_theme_aware_frame_style())
        
        creator_layout = QVBoxLayout(creator_frame)
        
        # Creator title
        creator_title = QLabel("About the Creator")
        creator_title.setAlignment(Qt.AlignCenter)
        font = QFont("Arial", 22)
        font.setBold(True)
        creator_title.setFont(font)
        creator_title.setStyleSheet(self._get_theme_aware_title_style())
        creator_layout.addWidget(creator_title)
        
        # Creator details
        creator_text = QTextEdit()
        creator_text.setReadOnly(True)
        creator_text.setStyleSheet(self._get_theme_aware_text_edit_style())
        
        creator_content = """
        <div style="text-align: center; margin-bottom: 20px;">
            <h3 style="color: #88C0D0; margin-bottom: 10px;">Development Team</h3>
        </div>
        
        <p style="margin-bottom: 15px;">VisionLab AI is developed and maintained by a dedicated team of researchers and developers passionate about advancing petrographic analysis through artificial intelligence.</p>
        
        <p style="margin-bottom: 15px;"><strong style="color: #A3BE8C;">Lead Developer:</strong><br>
        [Your Name/Team Name]<br>
        [Institution/Organization]<br>
        [Email Contact]</p>
        
        <p style="margin-bottom: 15px;"><strong style="color: #A3BE8C;">Research Focus:</strong></p>
        <ul style="margin-left: 20px; margin-bottom: 15px;">
            <li style="margin-bottom: 8px;">Computer Vision in Geosciences</li>
            <li style="margin-bottom: 8px;">Machine Learning for Petrographic Analysis</li>
            <li style="margin-bottom: 8px;">Automated Mineral Identification</li>
            <li style="margin-bottom: 8px;">Digital Petrography Workflows</li>
        </ul>
        
        <p style="margin-bottom: 15px;"><strong style="color: #A3BE8C;">Acknowledgments:</strong><br>
        We thank the geological community for their valuable feedback and contributions that have shaped VisionLab AI into a comprehensive analysis platform.</p>
        
        <p style="margin-bottom: 15px;">For collaboration opportunities, feature requests, or technical support, please don't hesitate to reach out through our official channels.</p>
        """
        
        creator_text.setHtml(creator_content)
        creator_layout.addWidget(creator_text)
        
        # Contact buttons
        contact_layout = QHBoxLayout()
        
        github_btn = QPushButton("🔗 GitHub Repository")
        github_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5E81AC, stop:1 #4C566A);
                border: 2px solid #5E81AC;
                border-radius: 8px;
                color: #ECEFF4;
                font-size: 14px;
                font-weight: bold;
                padding: 12px 20px;
                min-width: 150px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #81A1C1, stop:1 #5E81AC);
                border-color: #81A1C1;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4C566A, stop:1 #3B4252);
            }
        """)
        github_btn.clicked.connect(lambda: QDesktopServices.openUrl(QUrl("https://github.com/your-repo")))
        
        email_btn = QPushButton("📧 Contact Email")
        email_btn.setStyleSheet(github_btn.styleSheet())
        email_btn.clicked.connect(lambda: QDesktopServices.openUrl(QUrl("mailto:<EMAIL>")))
        
        contact_layout.addWidget(github_btn)
        contact_layout.addWidget(email_btn)
        contact_layout.addStretch()
        
        creator_layout.addLayout(contact_layout)
        layout.addWidget(creator_frame)
        
        self.about_tab_widget.addTab(creator_widget, "Creator Info")
        
    def setup_license_tab(self):
        """Sets up the license information tab."""
        license_widget = QWidget()
        layout = QVBoxLayout(license_widget)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # License frame
        license_frame = QFrame()
        license_frame.setFrameStyle(QFrame.Box)
        license_frame.setStyleSheet(self._get_theme_aware_frame_style())
        
        license_layout = QVBoxLayout(license_frame)
        
        # License title
        license_title = QLabel("License Information")
        license_title.setAlignment(Qt.AlignCenter)
        font = QFont("Arial", 22)
        font.setBold(True)
        license_title.setFont(font)
        license_title.setStyleSheet(self._get_theme_aware_title_style())
        license_layout.addWidget(license_title)
        
        # License text
        license_text = QTextEdit()
        license_text.setReadOnly(True)
        license_text.setStyleSheet(self._get_theme_aware_text_edit_style())
        
        license_content = """
        <h3 style="color: #88C0D0; margin-bottom: 15px;">Proprietary License</h3>
        
        <p style="margin-bottom: 15px;"><strong style="color: #A3BE8C;">Copyright (c) 2024 VisionLab AI Development Team</strong></p>
        
        <p style="margin-bottom: 15px;">This software and associated documentation files (the "Software") are proprietary 
        and confidential to VisionLab AI Development Team. All rights reserved.</p>
        
        <p style="margin-bottom: 15px;">The Software is licensed, not sold. You are granted a limited, non-exclusive, 
        non-transferable license to use the Software solely for your internal business purposes 
        in accordance with the terms and conditions of your license agreement with VisionLab AI.</p>
        
        <p style="margin-bottom: 15px;"><strong style="color: #BF616A;">UNAUTHORIZED COPYING, MODIFICATION, DISTRIBUTION, OR USE OF THIS SOFTWARE 
        IS STRICTLY PROHIBITED AND MAY RESULT IN SEVERE CIVIL AND CRIMINAL PENALTIES. 
        THE SOFTWARE IS PROVIDED "AS IS" WITHOUT WARRANTY OF ANY KIND. VISIONLAB AI 
        DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO 
        WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.</strong></p>
        
        <hr style="border: 1px solid #4C566A; margin: 20px 0;">
        
        <h4 style="color: #88C0D0; margin-bottom: 10px;">Third-Party Libraries</h4>
        
        <p style="margin-bottom: 10px;">VisionLab AI uses the following open-source libraries:</p>
        <ul style="margin-left: 20px;">
            <li style="margin-bottom: 5px;">PySide6 - Qt for Python (LGPL)</li>
            <li style="margin-bottom: 5px;">OpenCV - Computer Vision Library (Apache 2.0)</li>
            <li style="margin-bottom: 5px;">NumPy - Numerical Computing (BSD)</li>
            <li style="margin-bottom: 5px;">Pillow - Python Imaging Library (PIL License)</li>
            <li style="margin-bottom: 5px;">Scikit-learn - Machine Learning (BSD)</li>
            <li style="margin-bottom: 5px;">Matplotlib - Plotting Library (PSF)</li>
        </ul>
        
        <p style="margin-top: 15px;">Each library retains its respective license terms and conditions.</p>
        """
        
        license_text.setHtml(license_content)
        license_layout.addWidget(license_text)
        
        layout.addWidget(license_frame)
        
        self.about_tab_widget.addTab(license_widget, "License")
        
    def setup_tutorial_tab(self):
        """Sets up the tutorial tab."""
        tutorial_widget = QWidget()
        layout = QVBoxLayout(tutorial_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # Main splitter for tutorial list and content
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)

        # Left side: Tutorial selection list
        left_panel = QFrame()
        left_panel.setFixedWidth(250)
        left_panel.setStyleSheet(self._get_theme_aware_panel_style())
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(10, 10, 10, 10)
        left_layout.setSpacing(10)

        title_label = QLabel("Quick Guide")
        title_font = QFont("Arial", 16, QFont.Bold)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(self._get_theme_aware_panel_title_style())
        left_layout.addWidget(title_label)

        # Tutorial buttons
        self.unsupervised_segmentation_tut_btn = self._create_tutorial_button("Unsupervised Segmentation")
        self.trainable_segmentation_tut_btn = self._create_tutorial_button("Trainable Segmentation")
        self.point_counting_tut_btn = self._create_tutorial_button("Point Counting")
        self.grain_analysis_tut_btn = self._create_tutorial_button("Grain Analysis")
        self.batch_processing_tut_btn = self._create_tutorial_button("Batch Processing")
        self.image_lab_tut_btn = self._create_tutorial_button("Image Lab")
        self.advanced_segmentation_tut_btn = self._create_tutorial_button("Advanced Segmentation")
        self.ai_assistant_tut_btn = self._create_tutorial_button("AI Assistant")

        left_layout.addWidget(self.unsupervised_segmentation_tut_btn)
        left_layout.addWidget(self.trainable_segmentation_tut_btn)
        left_layout.addWidget(self.point_counting_tut_btn)
        left_layout.addWidget(self.grain_analysis_tut_btn)
        left_layout.addWidget(self.batch_processing_tut_btn)
        left_layout.addWidget(self.image_lab_tut_btn)
        left_layout.addWidget(self.advanced_segmentation_tut_btn)
        left_layout.addWidget(self.ai_assistant_tut_btn)
        left_layout.addStretch()

        splitter.addWidget(left_panel)

        # Right side: Tutorial content display
        self.tutorial_display = QTextEdit()
        self.tutorial_display.setReadOnly(True)
        self.tutorial_display.setStyleSheet(self._get_theme_aware_tutorial_display_style())
        splitter.addWidget(self.tutorial_display)
        splitter.setSizes([250, 750])

        self.about_tab_widget.addTab(tutorial_widget, "Quick Guide")

    def _create_tutorial_button(self, text):
        """Helper to create a styled tutorial button.""" 
        button = QPushButton(text)
        button.setCheckable(True)
        button.setAutoExclusive(True)
        button.setStyleSheet(self._get_theme_aware_tutorial_button_style())
        return button
    
    def _get_theme_aware_tab_style(self):
        """Get theme-aware tab widget style."""
        colors = get_theme_colors()
        return f"""
            QTabWidget::pane {{
                border: 1px solid {colors.get('border', '#4C566A')};
                border-radius: 8px;
                background-color: {colors.get('background', '#2E3440')};
            }}
            QTabBar::tab {{
                background-color: {colors.get('button_background', '#3B4252')};
                color: {colors.get('text', '#D8DEE9')};
                border: 1px solid {colors.get('border', '#4C566A')};
                border-bottom: none;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                padding: 8px 16px;
                margin-right: 2px;
                font-weight: 500;
            }}
            QTabBar::tab:selected {{
                background-color: {colors.get('accent', '#5E81AC')};
                color: {colors.get('accent_text', '#ECEFF4')};
                border-color: {colors.get('accent', '#5E81AC')};
                font-weight: bold;
            }}
            QTabBar::tab:hover:!selected {{
                background-color: {colors.get('hover', '#4C566A')};
            }}
        """
    
    def _get_theme_aware_frame_style(self):
        """Get theme-aware frame style."""
        colors = get_theme_colors()
        return f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {colors.get('background', '#2E3440')}, 
                    stop:1 {colors.get('alternate_background', '#3B4252')});
                border: 2px solid {colors.get('accent', '#5E81AC')};
                border-radius: 15px;
                padding: 20px;
            }}
        """
    
    def _get_theme_aware_title_style(self):
        """Get theme-aware title style."""
        colors = get_theme_colors()
        return f"color: {colors.get('text', '#ECEFF4')}; margin: 10px;"
    
    def _get_theme_aware_subtitle_style(self):
        """Get theme-aware subtitle style."""
        colors = get_theme_colors()
        return f"color: {colors.get('secondary_text', '#D8DEE9')}; margin: 5px;"
    
    def _get_theme_aware_accent_style(self):
        """Get theme-aware accent style."""
        colors = get_theme_colors()
        return f"color: {colors.get('accent', '#88C0D0')}; margin: 10px;"
    
    def _get_theme_aware_text_edit_style(self):
        """Get theme-aware text edit style."""
        colors = get_theme_colors()
        return f"""
            QTextEdit {{
                background-color: {colors.get('alternate_background', '#3B4252')};
                border: 2px solid {colors.get('border', '#4C566A')};
                border-radius: 10px;
                padding: 15px;
                color: {colors.get('text', '#ECEFF4')};
                font-size: 14px;
                line-height: 1.6;
                selection-background-color: {colors.get('accent', '#5E81AC')};
                selection-color: {colors.get('accent_text', '#ECEFF4')};
            }}
            QScrollBar:vertical {{
                background-color: {colors.get('background', '#2E3440')};
                width: 12px;
                border-radius: 6px;
            }}
            QScrollBar::handle:vertical {{
                background-color: {colors.get('accent', '#5E81AC')};
                border-radius: 6px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                 background-color: {colors.get('hover', '#88C0D0')};
             }}
         """
    
    def _get_theme_aware_panel_style(self):
        """Get theme-aware panel style."""
        colors = get_theme_colors()
        return f"""
            QFrame {{
                background-color: {colors.get('alternate_background', '#3B4252')};
                border: 1px solid {colors.get('border', '#4C566A')};
                border-radius: 10px;
            }}
        """
    
    def _get_theme_aware_panel_title_style(self):
        """Get theme-aware panel title style."""
        colors = get_theme_colors()
        return f"color: {colors.get('text', '#ECEFF4')}; margin-bottom: 10px; font-weight: bold;"
    
    def _get_theme_aware_content_panel_style(self):
        """Get theme-aware content panel style."""
        colors = get_theme_colors()
        return f"""
            QFrame {{
                background-color: {colors.get('main_background', '#2E3440')};
                border: 1px solid {colors.get('border', '#4C566A')};
                border-radius: 10px;
            }}
        """
    
    def _get_theme_aware_tutorial_display_style(self):
        """Get theme-aware tutorial display style."""
        colors = get_theme_colors()
        return f"""
            QTextEdit {{
                background-color: {colors.get('bg_primary', '#2E3440')};
                border: none;
                padding: 15px;
                color: {colors.get('text', '#ECEFF4')};
                font-size: 14px;
                line-height: 1.6;
                selection-background-color: {colors.get('accent', '#5E81AC')};
                selection-color: {colors.get('accent_text', '#ECEFF4')};
            }}
            QScrollBar:vertical {{
                background-color: {colors.get('background', '#2E3440')};
                width: 12px;
                border-radius: 6px;
            }}
            QScrollBar::handle:vertical {{
                background-color: {colors.get('accent', '#5E81AC')};
                border-radius: 6px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: {colors.get('hover', '#88C0D0')};
            }}
        """
    
    def _get_theme_aware_tutorial_button_style(self):
        """Get theme-aware tutorial button style."""
        colors = get_theme_colors()
        return f"""
            QPushButton {{
                background-color: {colors.get('button_background', '#4C566A')};
                color: {colors.get('text', '#ECEFF4')};
                border: 1px solid {colors.get('accent', '#5E81AC')};
                border-radius: 8px;
                padding: 12px 16px;
                text-align: left;
                font-size: 13px;
                font-weight: 500;
                margin: 2px 0;
            }}
            QPushButton:hover {{
                background-color: {colors.get('hover', '#5E81AC')};
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            }}
            QPushButton:checked {{
                background-color: {colors.get('accent', '#88C0D0')};
                color: {colors.get('background', '#2E3440')};
                border: 1px solid {colors.get('accent_light', '#8FBCBB')};
                font-weight: bold;
            }}
            QPushButton:pressed {{
                transform: translateY(1px);
            }}
        """