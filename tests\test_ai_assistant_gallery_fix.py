"""
Test script to verify the AI Assistant image gallery navigation fix.

This test checks that:
1. The setup_ai_assistant_connections method is called during initialization
2. The signal connection exists between ai_assistant_gallery.image_clicked and the handler
3. The on_ai_assistant_image_clicked method exists and properly updates the display
"""

import os
import sys
import inspect

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_setup_connections_called():
    """Test that setup_ai_assistant_connections is called during initialization."""
    print("Testing setup_ai_assistant_connections call...")
    
    # Read the AI assistant handlers file
    handlers_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'gui', 'handlers', 'ai_assistant_handlers.py')
    
    if not os.path.exists(handlers_file):
        print(f"✗ FAILED: Handlers file not found: {handlers_file}")
        return False
    
    with open(handlers_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for the call to setup_ai_assistant_connections
    if 'self.setup_ai_assistant_connections()' in content:
        print("✓ setup_ai_assistant_connections() call found in initialization")
    else:
        print("✗ FAILED: setup_ai_assistant_connections() call not found")
        return False
    
    # Check for the logging message
    if 'logger.info("AI Assistant signal connections set up")' in content:
        print("✓ Initialization logging found")
    else:
        print("✗ FAILED: Initialization logging not found")
        return False
    
    return True


def test_signal_connection_exists():
    """Test that the signal connection code exists in the setup method."""
    print("\nTesting AI Assistant gallery signal connection...")
    
    # Read the AI assistant handlers file
    handlers_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'gui', 'handlers', 'ai_assistant_handlers.py')
    
    with open(handlers_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for the signal connection
    if 'self.ai_assistant_gallery.image_clicked.connect(self.on_ai_assistant_image_clicked)' in content:
        print("✓ Signal connection found: ai_assistant_gallery.image_clicked.connect(self.on_ai_assistant_image_clicked)")
    else:
        print("✗ FAILED: Signal connection not found")
        return False
    
    # Check for the remove signal connection too
    if 'self.ai_assistant_gallery.remove_clicked.connect(self.on_ai_assistant_gallery_remove_clicked)' in content:
        print("✓ Remove signal connection found: ai_assistant_gallery.remove_clicked.connect(self.on_ai_assistant_gallery_remove_clicked)")
    else:
        print("✗ FAILED: Remove signal connection not found")
        return False
    
    return True


def test_handler_method_exists():
    """Test that the on_ai_assistant_image_clicked method exists."""
    print("\nTesting handler method existence...")
    
    try:
        # Import the handlers class
        from src.gui.handlers.ai_assistant_handlers import AIAssistantHandlers
        
        # Check if the method exists
        if hasattr(AIAssistantHandlers, 'on_ai_assistant_image_clicked'):
            method = getattr(AIAssistantHandlers, 'on_ai_assistant_image_clicked')
            
            # Check method signature
            sig = inspect.signature(method)
            params = list(sig.parameters.keys())
            
            # Should have 'self' and 'index' parameters
            if len(params) >= 2 and params[0] == 'self' and 'index' in params:
                print(f"✓ Method signature correct: {params}")
            else:
                print(f"✗ FAILED: Incorrect method signature: {params}")
                return False
        else:
            print("✗ FAILED: Method on_ai_assistant_image_clicked not found in class")
            return False
        
        # Check for the remove handler too
        if hasattr(AIAssistantHandlers, 'on_ai_assistant_gallery_remove_clicked'):
            print("✓ Method on_ai_assistant_gallery_remove_clicked exists")
        else:
            print("✗ FAILED: Method on_ai_assistant_gallery_remove_clicked not found")
            return False
            
        return True
        
    except ImportError as e:
        print(f"✗ FAILED: Could not import AIAssistantHandlers: {e}")
        return False
    except Exception as e:
        print(f"✗ FAILED: Error checking method signature: {e}")
        return False


def test_handler_method_functionality():
    """Test that the handler method has the expected functionality."""
    print("\nTesting handler method functionality...")
    
    handlers_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'gui', 'handlers', 'ai_assistant_handlers.py')
    
    with open(handlers_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for key functionality in the handler method
    functionality_checks = [
        ('self.ai_assistant_current_image_id = image_filename', 'Sets current image ID'),
        ('convert_cvimage_to_qpixmap(image, already_rgb=True)', 'Converts image to pixmap'),
        ('self.ai_assistant_image_item.setPixmap', 'Updates image display'),
        ('self.ai_assistant_gallery.select_image(index)', 'Updates gallery selection')
    ]
    
    all_passed = True
    for check, description in functionality_checks:
        if check in content:
            print(f"✓ {description} found in handler")
        else:
            print(f"✗ FAILED: {description} not found in handler")
            all_passed = False
    
    return all_passed


def test_gallery_widget_compatibility():
    """Test that the gallery widget has the required signals."""
    print("\nTesting gallery widget compatibility...")
    
    try:
        from src.widgets.page_image_gallery import PageImageGallery
        
        # Check if the base class has the required signals
        if hasattr(PageImageGallery, 'image_clicked'):
            print("✓ PageImageGallery has image_clicked signal")
        else:
            print("✗ FAILED: PageImageGallery does not have image_clicked signal")
            return False
        
        if hasattr(PageImageGallery, 'remove_clicked'):
            print("✓ PageImageGallery has remove_clicked signal")
        else:
            print("✗ FAILED: PageImageGallery does not have remove_clicked signal")
            return False
        
        return True
        
    except ImportError as e:
        print(f"✗ FAILED: Could not import PageImageGallery: {e}")
        return False


def test_ui_gallery_definition():
    """Test that the AI Assistant gallery is properly defined in the UI."""
    print("\nTesting UI gallery definition...")
    
    try:
        ui_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'gui', 'ui', 'ai_assistant_page_ui.py')
        
        if not os.path.exists(ui_file):
            print(f"✗ FAILED: UI file not found: {ui_file}")
            return False
        
        with open(ui_file, 'r', encoding='utf-8') as f:
            ui_content = f.read()
        
        # Check for gallery definition
        if 'self.ai_assistant_gallery = PageImageGallery(' in ui_content:
            print("✓ AI Assistant gallery defined in UI")
        else:
            print("✗ FAILED: AI Assistant gallery not found in UI")
            return False
        
        # Check for gallery height setting
        if 'self.ai_assistant_gallery.setMaximumHeight(150)' in ui_content:
            print("✓ Gallery height properly configured")
        else:
            print("✗ WARNING: Gallery height configuration not found")
        
        return True
        
    except Exception as e:
        print(f"✗ FAILED: Error checking UI gallery: {e}")
        return False


def main():
    """Run all tests."""
    print("AI Assistant Image Gallery Navigation Fix Test")
    print("=" * 55)
    
    # Run tests
    test1_passed = test_setup_connections_called()
    test2_passed = test_signal_connection_exists()
    test3_passed = test_handler_method_exists()
    test4_passed = test_handler_method_functionality()
    test5_passed = test_gallery_widget_compatibility()
    test6_passed = test_ui_gallery_definition()
    
    print("\n" + "=" * 55)
    if test1_passed and test2_passed and test3_passed and test4_passed and test5_passed and test6_passed:
        print("✓ ALL TESTS PASSED - AI Assistant image gallery navigation should work correctly!")
        print("\nThe fix includes:")
        print("1. ✓ Initialization: setup_ai_assistant_connections() is now called")
        print("2. ✓ Signal connection: ai_assistant_gallery.image_clicked -> on_ai_assistant_image_clicked")
        print("3. ✓ Handler method: on_ai_assistant_image_clicked(index) updates display")
        print("4. ✓ Gallery widget: PageImageGallery has required signals")
        print("5. ✓ UI definition: ai_assistant_gallery is properly defined")
        print("\nUsers should now be able to click on gallery images to switch the main display!")
        return 0
    else:
        print("✗ SOME TESTS FAILED - Check the implementation")
        return 1


if __name__ == "__main__":
    sys.exit(main())
