import sys
import os
import logging
import time
import warnings
from PySide6.QtWidgets import QApplication, QStyleFactory, QMessageBox
from PySide6.QtGui import QFont, QIcon
from PySide6.QtCore import QTimer, QSettings, qInstallMessageHandler, QtMsgType
from src.gui.splash_screen import VisionLabAiSplashScreen
from src.utils.startup_timer import StartupTimer

# Suppress all warnings to clean up startup output
warnings.filterwarnings("ignore")

# Suppress Qt warnings and debug messages
os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false;qt.gui.*=false;qt.widgets.*=false;qt.core.*=false'
# Suppress specific Qt warnings
os.environ['QT_ASSUME_STDERR_HAS_CONSOLE'] = '1'
os.environ['QT_LOGGING_TO_CONSOLE'] = '0'
# Additional Qt warning suppression
os.environ['QT_LOGGING_RULES'] += ';qt.qml.*=false;qt.quick.*=false'

# Custom Qt message handler to suppress warnings
def qt_message_handler(mode, context, message):
    """Custom Qt message handler to suppress unwanted warnings."""
    _ = context  # Suppress unused parameter warning
    # Suppress specific Qt warnings that appear during startup
    suppress_messages = [
        "Could not parse stylesheet",
        "QLayout: Attempting to add QLayout",
        "called with wrong argument types",
        "Supported signatures"
    ]

    # Only suppress warning and debug messages, allow critical errors
    if mode in (QtMsgType.QtWarningMsg, QtMsgType.QtDebugMsg):
        for suppress_msg in suppress_messages:
            if suppress_msg in message:
                return

    # For other messages, we can still suppress them during startup
    # but you might want to log critical errors
    pass

# Install the custom message handler
qInstallMessageHandler(qt_message_handler)

# Try to import the new theme system
try:
    from src.gui.styles.theme_config import apply_theme, FONT_SIZES, FONT_FAMILIES, COLOR_SCHEMES
    USE_NEW_THEME_SYSTEM = True
except ImportError:
    # Fall back to the old theme system
    from src.gui.styles.dark_theme import apply_modern_dark_theme
    USE_NEW_THEME_SYSTEM = False

# Configure logging to suppress warnings and debug messages
logging.basicConfig(level=logging.ERROR, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set logging level for specific modules to suppress warnings and debug output
logging.getLogger('src.gui.handlers').setLevel(logging.ERROR)
logging.getLogger('src.widgets').setLevel(logging.ERROR)
logging.getLogger('src.gui.ui').setLevel(logging.ERROR)
logging.getLogger('src.gui.handlers.point_counting_page_handler').setLevel(logging.ERROR)
logging.getLogger('src.gui.handlers.about_page_handler').setLevel(logging.ERROR)
logging.getLogger('src.gui.handlers.ai_assistant_handlers').setLevel(logging.ERROR)
logging.getLogger('src.gui.handlers.analysis_handlers').setLevel(logging.ERROR)
logging.getLogger('src.gui.ui.app_ui').setLevel(logging.ERROR)
logging.getLogger('src.gui.ui.base_ui').setLevel(logging.ERROR)

# Suppress PySide6/Qt warnings
logging.getLogger('PySide6').setLevel(logging.CRITICAL)
logging.getLogger('Qt').setLevel(logging.CRITICAL)

# Import the main application class at the module level to avoid import freezes
from src.gui.app import VisionLabAiApp

# Optimized initialization function with lazy loading
def initialize_app(splash, app, timer):
    # Track progress
    progress = 0

    # Update splash screen
    splash.update_progress(progress, "Initializing application...")
    timer.checkpoint("Splash screen initialized")

    # Apply basic theme quickly
    progress += 20
    splash.update_progress(progress, "Setting up environment...")
    timer.start_component("Base style")
    app.setStyle(QStyleFactory.create("Fusion"))  # Use Fusion style as base
    timer.end_component("Base style")

    # Set application details for QSettings
    app.setOrganizationName("VisionLab Ai")
    app.setApplicationName("VisionLab_Ai_V4")
    timer.checkpoint("Application details set")

    # Quick font setup
    progress += 20
    splash.update_progress(progress, "Loading interface...")
    timer.start_component("Font loading")
    font = QFont("Segoe UI", 9)  # Use default font for faster startup
    app.setFont(font)
    timer.end_component("Font loading")
    timer.checkpoint("Fonts loaded")

    # Apply minimal theme for startup
    progress += 20
    splash.update_progress(progress, "Applying theme...")
    timer.start_component("Theme application")
    
    # Use simplified theme application
    try:
        if USE_NEW_THEME_SYSTEM:
            settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")
            theme = settings.value("app/theme", "Dark Theme").lower().replace(" theme", "")
            color_scheme = settings.value("app/color_scheme", "Default").lower()
            theme_name = f"{color_scheme}-{theme}"
            apply_theme(app, theme_name, "Segoe UI", 9)
        else:
            apply_modern_dark_theme(app)
    except Exception:
        # Fallback to basic dark theme
        app.setStyleSheet("QWidget { background-color: #2b2b2b; color: #ffffff; }")
    
    timer.end_component("Theme application")
    timer.checkpoint("Theme applied")

    # Create the main window with minimal initialization
    progress += 30
    splash.update_progress(progress, "Creating main window...")
    timer.start_component("Main window creation")
    try:
        window = VisionLabAiApp()
        timer.end_component("Main window creation")
        timer.checkpoint("Main window created")
    except Exception as e:
        timer.end_component("Main window creation")
        return None

    # Final preparations
    progress += 10
    splash.update_progress(progress, "Ready!")
    timer.start_component("Finalization")
    timer.end_component("Finalization")
    timer.checkpoint("Application ready")

    # Return the window
    return window

if __name__ == "__main__":
    # Initialize startup timer with a specific log file path
    startup_timer = StartupTimer("visionlab_ai_startup_timing.log")

    # Create the application
    startup_timer.start_component("QApplication creation")
    app = QApplication(sys.argv)
    startup_timer.end_component("QApplication creation")
    startup_timer.checkpoint("QApplication created")

    # Create and show the splash screen (optimized)
    startup_timer.start_component("Splash screen creation")
    splash = VisionLabAiSplashScreen()
    splash.show()
    startup_timer.end_component("Splash screen creation")
    startup_timer.checkpoint("Splash screen shown")

    # Initialize the application in a timer to allow the splash screen to show
    window_container = [None]
    splash_container = [splash]  # Store splash screen in a container to ensure proper cleanup
    timer_container = [startup_timer]  # Store timer in a container to ensure proper access

    def init_and_show():
        try:
            # Initialize the application
            timer_container[0].start_component("Application initialization")
            window_container[0] = initialize_app(splash_container[0], app, timer_container[0])
            timer_container[0].end_component("Application initialization")

            # Check if initialization was successful
            if window_container[0] is None:
                print("Failed to initialize application")
                cleanup_splash()
                return

            # Show the main window
            timer_container[0].start_component("Window display")
            window_container[0].show()
            timer_container[0].end_component("Window display")
            timer_container[0].checkpoint("Main window displayed")

            # Process events to keep UI responsive
            app.processEvents()

            # Close the splash screen with fade effect only if it's still visible
            if splash_container[0] and splash_container[0].isVisible():
                QTimer.singleShot(500, lambda: splash_container[0].finish(window_container[0]))

            # Connect window close event to ensure splash screen is properly cleaned up
            app.aboutToQuit.connect(cleanup_splash)

            # Finish timing the startup process
            timer_container[0].finish()

        except Exception:
            # Error during initialization (debug message removed)
            cleanup_splash()

    def cleanup_splash():
        # Ensure splash screen is properly closed and cleaned up
        try:
            if splash_container[0]:
                # Stop video playback if using video
                if hasattr(splash_container[0], 'use_video_background') and splash_container[0].use_video_background:
                    if hasattr(splash_container[0], 'media_player'):
                        try:
                            splash_container[0].media_player.stop()
                        except Exception as e:
                            print(f"Error stopping media player: {e}")

                # Reset dragging state to prevent issues
                splash_container[0].dragging = False
                splash_container[0].drag_position = None

                # Hide the splash screen
                splash_container[0].hide()

                # Remove parent to allow proper cleanup
                splash_container[0].setParent(None)

                # Clear the reference
                splash_container[0] = None

                # Process events to ensure UI updates
                app.processEvents()
        except Exception as e:
            print(f"Error cleaning up splash screen: {e}")

    # Use a timer to start initialization after splash is shown
    # Reduced delay for faster startup
    startup_timer.checkpoint("Starting initialization timer")
    QTimer.singleShot(100, init_and_show)

    # Start the event loop
    sys.exit(app.exec())