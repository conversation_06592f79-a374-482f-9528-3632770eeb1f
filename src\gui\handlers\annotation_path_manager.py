import os
import json

class AnnotationPathManager:
    """
    Class to manage the mapping between images and their exported annotation files.
    This ensures that annotations can be reloaded correctly when switching between pages.
    """
    
    def __init__(self):
        self.image_to_annotation_path = {}
        
    def register_annotation_path(self, image_path, annotation_path):
        """Register an annotation file path for an image."""
        if image_path and annotation_path:
            self.image_to_annotation_path[image_path] = annotation_path
            print(f"DEBUG: Registered annotation path {annotation_path} for image {image_path}")
            
    def get_annotation_path(self, image_path):
        """Get the annotation file path for an image."""
        if image_path in self.image_to_annotation_path:
            annotation_path = self.image_to_annotation_path[image_path]
            print(f"DEBUG: Found annotation path {annotation_path} for image {image_path}")
            return annotation_path
        print(f"DEBUG: No annotation path found for image {image_path}")
        return None
        
    def save_to_file(self, file_path):
        """Save the mapping to a file."""
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, 'w') as f:
                json.dump(self.image_to_annotation_path, f)
            print(f"DEBUG: Saved annotation path mapping to {file_path}")
            return True
        except Exception as e:
            print(f"ERROR: Failed to save annotation path mapping: {e}")
            return False
            
    def load_from_file(self, file_path):
        """Load the mapping from a file."""
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r') as f:
                    self.image_to_annotation_path = json.load(f)
                print(f"DEBUG: Loaded annotation path mapping from {file_path}")
                return True
            else:
                print(f"DEBUG: Annotation path mapping file {file_path} does not exist")
                return False
        except Exception as e:
            print(f"ERROR: Failed to load annotation path mapping: {e}")
            return False
