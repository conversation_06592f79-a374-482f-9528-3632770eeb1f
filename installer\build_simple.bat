@echo off
echo ===============================================
echo VisionLab Ai Simple Installer Builder
echo ===============================================
echo.

:: Check if Inno Setup is installed
set "INNO_SETUP_PATH=C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
if not exist "%INNO_SETUP_PATH%" (
    set "INNO_SETUP_PATH=C:\Program Files\Inno Setup 6\ISCC.exe"
)

if not exist "%INNO_SETUP_PATH%" (
    echo ERROR: Inno Setup 6 not found.
    echo Please install Inno Setup 6 from: https://jrsoftware.org/isinfo.php
    echo.
    pause
    exit /b 1
)

:: Check if the application dist folder exists
if not exist "..\dist\VisionLab_Ai_Simple" (
    echo ERROR: Application distribution folder not found.
    echo Expected location: ..\dist\VisionLab_Ai_Simple
    echo.
    echo Please ensure you have built the application using PyInstaller first.
    echo.
    pause
    exit /b 1
)

:: Create output directory
if not exist "output" mkdir output

:: Build the installer using the simple script
echo Building VisionLab Ai installer (simple version)...
echo.

"%INNO_SETUP_PATH%" "VisionLab_Ai_Setup_Simple.iss"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ===============================================
    echo Installer built successfully!
    echo ===============================================
    echo.
    echo Output location: output\
    echo Installer file: VisionLab_Ai_v4.0.0_Setup.exe
    echo.
    echo The installer includes:
    echo - Professional installation wizard
    echo - Start Menu shortcuts
    echo - Optional desktop shortcut
    echo - Proper uninstaller
    echo - Registry entries
    echo - License agreement
    echo.
    echo You can now distribute this installer to end users.
    echo.
    echo To add custom branding:
    echo 1. Create app_icon.ico from your logo
    echo 2. Use the full VisionLab_Ai_Setup.iss script
    echo 3. Uncomment the icon lines in the script
    echo.
) else (
    echo.
    echo ===============================================
    echo ERROR: Installer build failed!
    echo ===============================================
    echo.
    echo Please check the error messages above.
    echo.
)

echo Press any key to continue...
pause >nul
