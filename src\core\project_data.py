# src/core/project_data.py
import os
import uuid
import shutil
import logging
import cv2
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple

# Use our custom JSON utility module
from src.utils import json_utils

logger = logging.getLogger(__name__)

class ImageInfo:
    """Class to store information about an image in a project."""

    def __init__(self, id: str, filename: str, filepath: str, metadata: Dict[str, Any] = None):
        self.id = id  # Unique identifier for the image
        self.filename = filename  # Original filename
        self.filepath = filepath  # Path to the image file within the project
        self.metadata = metadata or {}  # User-defined metadata
        self.analysis_results = {}  # Dictionary to store paths to analysis results

    def to_dict(self) -> Dict[str, Any]:
        """Convert the image info to a dictionary for serialization."""
        return {
            'id': self.id,
            'filename': self.filename,
            'filepath': self.filepath,
            'metadata': self.metadata,
            'analysis_results': self.analysis_results
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ImageInfo':
        """Create an ImageInfo object from a dictionary."""
        info = cls(
            id=data['id'],
            filename=data['filename'],
            filepath=data['filepath'],
            metadata=data.get('metadata', {})
        )
        info.analysis_results = data.get('analysis_results', {})
        return info


class Project:
    """Class to manage a VisionLab Ai project."""

    PROJECT_FILE_NAME = "visionlab_ai_project.json"

    def __init__(self, project_dir: str, name: str = None, description: str = ""):
        self.project_dir = os.path.abspath(project_dir)
        self.name = name or os.path.basename(project_dir)
        self.description = description
        self.created_at = datetime.now().isoformat()
        self.modified_at = self.created_at
        self.images: Dict[str, ImageInfo] = {}  # Dictionary of image_id -> ImageInfo

        # Create project directory structure if it doesn't exist
        self._create_project_structure()

    def _create_project_structure(self):
        """Create the project directory structure if it doesn't exist."""
        os.makedirs(self.project_dir, exist_ok=True)

        # Create subdirectories for project organization
        os.makedirs(os.path.join(self.project_dir, "images"), exist_ok=True)
        os.makedirs(os.path.join(self.project_dir, "results"), exist_ok=True)
        os.makedirs(os.path.join(self.project_dir, "results", "segmentation"), exist_ok=True)
        os.makedirs(os.path.join(self.project_dir, "results", "analysis"), exist_ok=True)

        # Create data directory with state subdirectory for storing application state
        data_dir_name = f"{self.name}_data"
        data_dir = os.path.join(self.project_dir, data_dir_name)
        os.makedirs(data_dir, exist_ok=True)

        # Create state directory for storing application state
        state_dir = os.path.join(data_dir, "state")
        os.makedirs(state_dir, exist_ok=True)

        # Create advanced_segmentation directory for storing annotations
        adv_seg_dir = os.path.join(state_dir, "advanced_segmentation")
        os.makedirs(adv_seg_dir, exist_ok=True)

        # Create images directory inside data directory (for potential future use)
        os.makedirs(os.path.join(data_dir, "images"), exist_ok=True)

    def save(self):
        """Save the project metadata to the project file."""
        self.modified_at = datetime.now().isoformat()

        project_data = {
            'name': self.name,
            'description': self.description,
            'created_at': self.created_at,
            'modified_at': self.modified_at,
            'images': {img_id: info.to_dict() for img_id, info in self.images.items()}
        }

        project_file_path = os.path.join(self.project_dir, self.PROJECT_FILE_NAME)
        with open(project_file_path, 'w') as f:
            json_utils.dump(project_data, f, indent=4)

        logger.info(f"Project saved to {project_file_path}")

    @classmethod
    def load(cls, project_dir: str) -> 'Project':
        """Load a project from the specified directory."""
        project_file_path = os.path.join(project_dir, cls.PROJECT_FILE_NAME)

        if not os.path.exists(project_file_path):
            raise FileNotFoundError(f"Project file not found at {project_file_path}")

        with open(project_file_path, 'r') as f:
            project_data = json_utils.load(f)

        project = cls(project_dir, project_data.get('name'), project_data.get('description', ""))
        project.created_at = project_data.get('created_at', project.created_at)
        project.modified_at = project_data.get('modified_at', project.modified_at)

        # Load images
        for img_id, img_data in project_data.get('images', {}).items():
            project.images[img_id] = ImageInfo.from_dict(img_data)

        return project

    def add_image(self, source_path: str) -> Optional[ImageInfo]:
        """Add an image to the project.

        Args:
            source_path: Path to the source image file.

        Returns:
            ImageInfo object if successful, None otherwise.
        """
        if not os.path.exists(source_path):
            logger.error(f"Source image not found: {source_path}")
            return None

        # Generate a unique ID for the image
        image_id = str(uuid.uuid4())

        # Get the original filename
        filename = os.path.basename(source_path)

        # Create a path for the image within the project
        project_images_dir = os.path.join(self.project_dir, "images")
        dest_path = os.path.join(project_images_dir, filename)

        # Handle filename conflicts by adding a suffix
        base_name, ext = os.path.splitext(filename)
        counter = 1
        while os.path.exists(dest_path):
            new_filename = f"{base_name}_{counter}{ext}"
            dest_path = os.path.join(project_images_dir, new_filename)
            counter += 1
            if counter > 100:  # Prevent infinite loop
                logger.error(f"Failed to find unique filename for {filename}")
                return None

        # Copy the image to the project directory
        try:
            shutil.copy2(source_path, dest_path)
            logger.info(f"Copied image to project: {dest_path}")
        except Exception as e:
            logger.exception(f"Failed to copy image: {e}")
            return None

        # Create relative path for storage in project file
        rel_path = os.path.relpath(dest_path, self.project_dir)

        # Create and store the image info
        info = ImageInfo(
            id=image_id,
            filename=os.path.basename(dest_path),
            filepath=rel_path,
            metadata={}
        )
        self.images[image_id] = info

        return info

    def get_image_info(self, image_id: str) -> Optional[ImageInfo]:
        """Get information about an image in the project."""
        return self.images.get(image_id)

    def get_image_path(self, image_id: str) -> Optional[str]:
        """Get the absolute path to an image in the project."""
        info = self.get_image_info(image_id)
        if not info:
            return None
        return os.path.join(self.project_dir, info.filepath)

    def set_metadata(self, image_id: str, metadata: Dict[str, Any]) -> bool:
        """Set metadata for an image in the project."""
        info = self.get_image_info(image_id)
        if not info:
            return False
        info.metadata = metadata
        return True

    def remove_image(self, image_id: str, delete_file: bool = False) -> bool:
        """Remove an image from the project.

        Args:
            image_id: ID of the image to remove.
            delete_file: If True, also delete the image file from disk.

        Returns:
            True if successful, False otherwise.
        """
        info = self.get_image_info(image_id)
        if not info:
            return False

        if delete_file:
            try:
                file_path = os.path.join(self.project_dir, info.filepath)
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.info(f"Deleted image file: {file_path}")
            except Exception as e:
                logger.exception(f"Failed to delete image file: {e}")
                return False

        # Remove from project
        del self.images[image_id]
        return True

    def store_analysis_result(self, image_id: str, analysis_type: str, result_path: str) -> bool:
        """Store a path to an analysis result for an image.

        Args:
            image_id: ID of the image.
            analysis_type: Type of analysis (e.g., 'segmentation', 'grain_size').
            result_path: Path to the result file, relative to project directory.

        Returns:
            True if successful, False otherwise.
        """
        info = self.get_image_info(image_id)
        if not info:
            return False

        info.analysis_results[analysis_type] = result_path
        return True

    def get_analysis_result(self, image_id: str, analysis_type: str) -> Optional[str]:
        """Get the path to an analysis result for an image.

        Args:
            image_id: ID of the image.
            analysis_type: Type of analysis.

        Returns:
            Absolute path to the result file if it exists, None otherwise.
        """
        info = self.get_image_info(image_id)
        if not info or analysis_type not in info.analysis_results:
            return None

        rel_path = info.analysis_results[analysis_type]
        return os.path.join(self.project_dir, rel_path)

    def get_result_dir(self, result_type: str) -> str:
        """Get the directory for a specific type of result.

        Args:
            result_type: Type of result (e.g., 'segmentation', 'analysis').

        Returns:
            Absolute path to the result directory.
        """
        return os.path.join(self.project_dir, "results", result_type)

    def update_image(self, image_id: str, image_data: np.ndarray) -> bool:
        """Update an existing image in the project with new image data.

        Args:
            image_id: ID of the image to update.
            image_data: New image data (numpy array in BGR format for OpenCV).

        Returns:
            True if successful, False otherwise.
        """
        info = self.get_image_info(image_id)
        if not info:
            logger.error(f"Cannot update image: Image ID {image_id} not found")
            return False

        # Get the absolute path to the image file
        image_path = self.get_image_path(image_id)
        if not image_path:
            logger.error(f"Cannot update image: Path for image ID {image_id} not found")
            return False

        try:
            # Save the new image data to the existing file path
            cv2.imwrite(image_path, image_data)
            logger.info(f"Updated image file: {image_path}")
            return True
        except Exception as e:
            logger.exception(f"Failed to update image file: {e}")
            return False