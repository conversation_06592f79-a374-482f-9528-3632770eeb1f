"""
Create a simple splash background image for PetroSEG_V4.
This script generates a gradient background image and saves it to the assets directory.
"""

import os
import sys
import numpy as np
from PIL import Image, ImageDraw, ImageFilter

def create_gradient_background(width=600, height=400, color1=(30, 30, 30), color2=(0, 120, 212, 30)):
    """Create a gradient background image."""
    # Create a new image with a black background
    img = Image.new('RGBA', (width, height), color=color1)
    draw = ImageDraw.Draw(img)
    
    # Create a gradient overlay
    for y in range(height):
        # Calculate the gradient color at this y position
        r = int(color1[0] + (color2[0] - color1[0]) * y / height)
        g = int(color1[1] + (color2[1] - color1[1]) * y / height)
        b = int(color1[2] + (color2[2] - color1[2]) * y / height)
        a = int(255 * (0.1 + 0.2 * y / height))  # Gradually increase alpha
        
        # Draw a line with this color
        draw.line([(0, y), (width, y)], fill=(r, g, b, a))
    
    # Add a subtle blur
    img = img.filter(ImageFilter.GaussianBlur(radius=2))
    
    return img

def main():
    """Create and save the splash background image."""
    # Get the path to the assets directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    assets_dir = os.path.join(os.path.dirname(os.path.dirname(script_dir)), "assets")
    
    # Create the assets directory if it doesn't exist
    os.makedirs(assets_dir, exist_ok=True)
    
    # Create the background image
    img = create_gradient_background()
    
    # Save the image
    output_path = os.path.join(assets_dir, "splash_background.png")
    img.save(output_path)
    
    print(f"Splash background image saved to: {output_path}")

if __name__ == "__main__":
    main()
