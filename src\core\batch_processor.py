# src/core/batch_processor.py
import os
import logging
import threading
from typing import List, Dict, Any, Callable, Optional
from PySide6.QtCore import QObject, Signal

from src.core.project_data import Project, ImageInfo

logger = logging.getLogger(__name__)

class BatchProcessingProgress(QObject):
    """Class to emit signals for batch processing progress."""
    progress_updated = Signal(int, int)  # current, total
    status_updated = Signal(str)  # status message
    processing_completed = Signal(bool, str)  # success, message
    image_completed = Signal(str, bool)  # image_id, success

class BatchProcessor:
    """Class to handle batch processing of multiple images."""
    
    def __init__(self, project: Project):
        self.project = project
        self.progress = BatchProcessingProgress()
        self._processing_thread = None
        self._stop_requested = False
    
    def process_images(self, image_infos: List[ImageInfo], analysis_type: str, 
                      parameters: Dict[str, Any] = None, 
                      processing_func: Callable = None):
        """Start batch processing of images.
        
        Args:
            image_infos: List of ImageInfo objects to process
            analysis_type: Type of analysis to perform
            parameters: Dictionary of parameters for the analysis
            processing_func: Function to call for processing each image
                             If None, will use the default processing function for the analysis type
        """
        if self._processing_thread and self._processing_thread.is_alive():
            logger.warning("Batch processing already in progress")
            return False
        
        self._stop_requested = False
        self._processing_thread = threading.Thread(
            target=self._process_images_thread,
            args=(image_infos, analysis_type, parameters, processing_func)
        )
        self._processing_thread.daemon = True
        self._processing_thread.start()
        return True
    
    def stop_processing(self):
        """Request to stop the current batch processing."""
        if self._processing_thread and self._processing_thread.is_alive():
            self._stop_requested = True
            self.progress.status_updated.emit("Stopping batch processing...")
            return True
        return False
    
    def _process_images_thread(self, image_infos: List[ImageInfo], analysis_type: str,
                             parameters: Dict[str, Any] = None,
                             processing_func: Callable = None):
        """Thread function to process images in batch."""
        if not image_infos:
            self.progress.processing_completed.emit(False, "No images to process")
            return
        
        total_images = len(image_infos)
        processed_count = 0
        success_count = 0
        
        self.progress.status_updated.emit(f"Starting batch processing of {total_images} images")
        self.progress.progress_updated.emit(0, total_images)
        
        for image_info in image_infos:
            if self._stop_requested:
                self.progress.processing_completed.emit(False, "Batch processing stopped by user")
                return
            
            try:
                self.progress.status_updated.emit(f"Processing {image_info.filename}")
                
                # Get the absolute path to the image
                image_path = self.project.get_image_path(image_info.id)
                if not image_path or not os.path.exists(image_path):
                    logger.error(f"Image file not found: {image_path}")
                    self.progress.image_completed.emit(image_info.id, False)
                    continue
                
                # Determine the output directory and filename
                result_dir = self.project.get_result_dir(analysis_type)
                os.makedirs(result_dir, exist_ok=True)
                
                # Create a base filename for results
                base_name = os.path.splitext(image_info.filename)[0]
                result_filename = f"{base_name}_{analysis_type}_result"
                
                # This is where we would call the actual processing function
                # For now, we'll just simulate success
                if processing_func:
                    success = self._call_processing_function(
                        processing_func, image_path, result_dir, result_filename, parameters
                    )
                else:
                    # Simulate processing
                    logger.info(f"Simulating processing for {image_info.filename}")
                    success = True
                    # In a real implementation, we would call the appropriate processing function
                    # based on the analysis_type
                
                if success:
                    # Store the result path in the project
                    result_path = os.path.join("results", analysis_type, f"{result_filename}.json")
                    self.project.store_analysis_result(image_info.id, analysis_type, result_path)
                    success_count += 1
                    self.progress.image_completed.emit(image_info.id, True)
                else:
                    self.progress.image_completed.emit(image_info.id, False)
                
            except Exception as e:
                logger.exception(f"Error processing {image_info.filename}: {e}")
                self.progress.image_completed.emit(image_info.id, False)
            
            processed_count += 1
            self.progress.progress_updated.emit(processed_count, total_images)
        
        # Save the project to persist the analysis results
        try:
            self.project.save()
        except Exception as e:
            logger.exception(f"Error saving project after batch processing: {e}")
        
        # Complete the processing
        completion_message = f"Batch processing completed. {success_count}/{total_images} images processed successfully."
        self.progress.status_updated.emit(completion_message)
        self.progress.processing_completed.emit(success_count > 0, completion_message)
    
    def _call_processing_function(self, func: Callable, image_path: str, 
                                result_dir: str, result_filename: str,
                                parameters: Dict[str, Any]) -> bool:
        """Call the processing function with the appropriate parameters."""
        try:
            # Different processing functions might have different parameter requirements
            # This is a generic approach that might need to be adapted
            result = func(
                image_path=image_path,
                output_dir=result_dir,
                output_filename=result_filename,
                **parameters if parameters else {}
            )
            return result is not None
        except Exception as e:
            logger.exception(f"Error in processing function: {e}")
            return False