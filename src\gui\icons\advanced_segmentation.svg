<?xml version="1.0" encoding="UTF-8"?>
<!-- Generator: visioncortex VTracer 0.6.4 -->
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" width="595" height="583">
<path d="M0,0 L23,0 L47,2 L75,7 L108,17 L133,28 L153,39 L170,50 L183,60 L195,71 L203,78 L223,98 L238,118 L247,132 L257,150 L267,172 L277,203 L282,225 L285,246 L286,262 L286,286 L283,314 L279,336 L272,361 L262,385 L252,405 L242,422 L230,438 L222,448 L213,458 L191,480 L177,491 L163,501 L141,514 L114,527 L81,538 L58,543 L33,546 L-5,546 L-32,543 L-59,537 L-79,531 L-102,521 L-124,510 L-139,500 L-153,490 L-165,479 L-176,470 L-184,462 L-189,457 L-200,444 L-210,431 L-221,414 L-231,396 L-240,377 L-250,349 L-257,318 L-260,294 L-260,248 L-256,222 L-250,198 L-240,169 L-226,140 L-214,121 L-201,104 L-189,90 L-163,64 L-143,49 L-126,38 L-110,29 L-93,21 L-68,12 L-45,6 L-29,3 L-14,1 Z M-7,27 L-25,29 L-51,34 L-73,41 L-92,49 L-114,61 L-131,73 L-141,81 L-155,93 L-164,101 L-173,112 L-180,119 L-193,137 L-201,150 L-207,160 L-218,185 L-225,205 L-229,219 L-234,251 L-234,292 L-232,309 L-228,330 L-222,350 L-214,371 L-205,390 L-192,411 L-178,429 L-167,441 L-160,449 L-149,459 L-135,470 L-124,478 L-107,489 L-88,499 L-64,508 L-39,515 L-15,519 L39,519 L64,515 L84,510 L108,501 L129,491 L149,479 L167,465 L181,453 L192,443 L201,432 L209,423 L220,407 L229,392 L235,382 L245,358 L251,339 L257,312 L259,297 L259,249 L256,228 L250,203 L242,180 L234,162 L223,143 L212,127 L198,110 L186,97 L178,90 L170,83 L154,71 L140,62 L120,51 L100,42 L79,35 L51,29 L35,27 Z " fill="#000000" transform="translate(284,18)"/>
<path d="M0,0 L62,0 L68,6 L68,17 L62,21 L37,22 L16,22 L16,45 L15,69 L9,73 L-2,72 L-6,66 L-6,6 Z " fill="#000000" transform="translate(150,127)"/>
<path d="M0,0 L62,0 L67,5 L67,68 L60,73 L51,72 L46,67 L45,44 L45,22 L24,22 L0,21 L-6,17 L-6,4 Z " fill="#000000" transform="translate(382,127)"/>
<path d="M0,0 L42,0 L66,4 L81,8 L104,16 L127,27 L144,37 L161,49 L175,61 L185,70 L203,88 L214,103 L219,111 L219,122 L211,136 L203,145 L196,153 L174,175 L157,188 L140,199 L135,201 L142,209 L149,216 L157,223 L171,237 L175,247 L175,261 L168,275 L158,281 L151,283 L142,283 L134,281 L124,274 L117,267 L117,265 L113,264 L112,261 L104,254 L90,240 L85,237 L85,234 L81,232 L75,227 L60,231 L40,234 L3,234 L-19,231 L-46,224 L-65,217 L-86,207 L-103,197 L-120,185 L-133,174 L-148,160 L-158,150 L-170,134 L-177,123 L-177,112 L-168,98 L-157,84 L-130,57 L-128,57 L-128,55 L-108,41 L-90,30 L-71,20 L-51,12 L-32,6 Z M8,30 L-9,34 L-20,39 L-32,46 L-47,60 L-54,69 L-62,84 L-67,100 L-68,107 L-68,126 L-64,144 L-59,157 L-49,172 L-36,185 L-25,193 L-11,200 L2,204 L7,205 L32,205 L49,201 L60,196 L72,189 L85,178 L91,171 L100,157 L105,145 L109,129 L109,104 L105,88 L97,72 L92,64 L74,46 L64,40 L50,34 L33,30 Z M91,34 Z M92,35 L98,42 L107,52 L114,62 L121,77 L126,94 L128,106 L128,127 L125,144 L120,159 L112,175 L110,178 L111,183 L115,187 L120,186 L138,176 L151,166 L161,158 L171,149 L185,135 L197,119 L196,114 L185,100 L177,92 L170,84 L159,74 L142,61 L126,51 L108,41 L95,35 Z M-53,35 L-77,46 L-93,56 L-108,67 L-125,81 L-136,92 L-143,100 L-155,116 L-154,120 L-143,134 L-127,151 L-115,162 L-102,172 L-88,181 L-72,190 L-56,198 L-53,198 L-55,194 L-65,183 L-72,172 L-80,156 L-84,142 L-86,132 L-86,99 L-81,80 L-73,63 L-66,53 L-58,43 L-51,36 Z " fill="#000000" transform="translate(276,174)"/>
<path d="M0,0 L26,0 L40,3 L55,9 L67,17 L77,25 L88,40 L97,59 L100,71 L100,98 L96,114 L86,133 L79,142 L68,153 L54,162 L39,168 L24,171 L4,171 L-12,168 L-25,163 L-38,155 L-53,141 L-60,131 L-68,115 L-72,101 L-72,69 L-67,53 L-58,36 L-47,24 L-40,17 L-29,10 L-16,4 Z M1,21 L-13,25 L-14,27 L-2,28 L10,34 L17,42 L20,48 L31,52 L40,57 L46,64 L51,74 L52,77 L52,94 L47,106 L38,116 L26,122 L22,123 L7,123 L-4,119 L-13,112 L-20,104 L-26,92 L-35,85 L-42,77 L-45,69 L-46,59 L-48,61 L-52,76 L-52,93 L-49,106 L-44,117 L-36,128 L-27,137 L-16,144 L-2,149 L3,150 L23,150 L38,146 L53,138 L69,122 L77,105 L80,93 L80,79 L76,62 L72,54 L65,44 L55,34 L48,29 L34,23 L27,21 Z M-14,45 L-20,48 L-26,54 L-27,57 L-27,64 L-23,72 L-17,76 L-9,77 L-2,74 L3,68 L4,66 L4,56 L0,50 L-6,46 Z " fill="#000000" transform="translate(283,206)"/>
<path d="M0,0 L9,1 L12,2 L14,6 L14,52 L59,52 L66,56 L67,67 L61,73 L-1,73 L-7,67 L-7,7 L-3,1 Z " fill="#000000" transform="translate(151,382)"/>
</svg>
