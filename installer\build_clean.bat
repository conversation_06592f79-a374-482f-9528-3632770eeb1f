@echo off
echo ===============================================
echo VisionLab Ai Clean Installer Builder
echo ===============================================
echo.

:: Check if Inno Setup is installed
set "INNO_SETUP_PATH=C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
if not exist "%INNO_SETUP_PATH%" (
    set "INNO_SETUP_PATH=C:\Program Files\Inno Setup 6\ISCC.exe"
)

if not exist "%INNO_SETUP_PATH%" (
    echo ERROR: Inno Setup 6 not found.
    echo Please install Inno Setup 6 from: https://jrsoftware.org/isinfo.php
    echo.
    pause
    exit /b 1
)

:: Check if the application dist folder exists
if not exist "..\dist\VisionLab_Ai_Simple" (
    echo ERROR: Application distribution folder not found.
    echo Expected location: ..\dist\VisionLab_Ai_Simple
    echo.
    echo Please ensure you have built the application using PyInstaller first.
    echo.
    pause
    exit /b 1
)

:: Create output directory
if not exist "output" mkdir output

:: Build the installer using the clean script
echo Building VisionLab Ai installer (clean version)...
echo.

"%INNO_SETUP_PATH%" "VisionLab_Ai_Setup_Clean.iss"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ===============================================
    echo SUCCESS: Installer built successfully!
    echo ===============================================
    echo.
    echo Output location: output\
    echo Installer file: VisionLab_Ai_v4.0.0_Setup.exe
    echo.
    echo The installer includes:
    echo - Professional installation wizard
    echo - Start Menu shortcuts
    echo - Optional desktop shortcut
    echo - Complete uninstaller
    echo - Registry integration
    echo - License agreement display
    echo - User data directory creation
    echo.
    echo You can now distribute this installer to end users.
    echo.
) else (
    echo.
    echo ===============================================
    echo ERROR: Installer build failed!
    echo ===============================================
    echo.
    echo Please check the error messages above.
    echo Common issues:
    echo - Missing dist folder: ..\dist\VisionLab_Ai_Simple
    echo - Missing license file: resources\license.txt
    echo - Missing readme file: resources\readme.txt
    echo.
)

echo Press any key to continue...
pause >nul
