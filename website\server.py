#!/usr/bin/env python3
"""
Vision Lab Website Server
Flask server for hosting the Vision Lab website with secure API endpoints.
"""

import os
import json
import logging
from datetime import datetime
from flask import Flask, request, jsonify, send_from_directory, render_template_string
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from werkzeug.middleware.proxy_fix import ProxyFix
from api import WaitlistAPI
import secrets

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('server.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)

# Security configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', secrets.token_hex(32))
app.config['WTF_CSRF_ENABLED'] = True
app.config['SESSION_COOKIE_SECURE'] = True
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'

# Handle proxy headers if behind reverse proxy
app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_prefix=1)

# Configure CORS
CORS(app, origins=[
    'http://localhost:3000',
    'http://localhost:5000',
    'https://visionlab.netlify.app',
    'https://visionlab.hostinger.com'
])

# Rate limiting
limiter = Limiter(
    key_func=get_remote_address,
    app=app,
    default_limits=["200 per day", "50 per hour"]
)

# Initialize API
waitlist_api = WaitlistAPI()

# Security headers middleware
@app.after_request
def add_security_headers(response):
    """Add security headers to all responses"""
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
    response.headers['Content-Security-Policy'] = (
        "default-src 'self'; "
        "script-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com; "
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; "
        "font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; "
        "img-src 'self' data: https:; "
        "connect-src 'self'; "
        "frame-ancestors 'none';"
    )
    return response

# Error handlers
@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return jsonify({
        'error': 'Not found',
        'message': 'The requested resource was not found'
    }), 404

@app.errorhandler(429)
def rate_limit_exceeded(error):
    """Handle rate limit errors"""
    return jsonify({
        'error': 'Rate limit exceeded',
        'message': 'Too many requests. Please try again later.'
    }), 429

@app.errorhandler(500)
def internal_error(error):
    """Handle internal server errors"""
    logger.error(f"Internal server error: {error}")
    return jsonify({
        'error': 'Internal server error',
        'message': 'An unexpected error occurred'
    }), 500

# Routes
@app.route('/')
def index():
    """Serve the main website"""
    try:
        return send_from_directory('.', 'index.html')
    except FileNotFoundError:
        return "Website files not found. Please ensure index.html exists.", 404

@app.route('/styles.css')
def styles():
    """Serve CSS file"""
    try:
        return send_from_directory('.', 'styles.css', mimetype='text/css')
    except FileNotFoundError:
        return "CSS file not found", 404

@app.route('/script.js')
def script():
    """Serve JavaScript file"""
    try:
        return send_from_directory('.', 'script.js', mimetype='application/javascript')
    except FileNotFoundError:
        return "JavaScript file not found", 404

@app.route('/home.html')
def home():
    """Redirect home.html to index.html"""
    return send_from_directory('.', 'index.html')

@app.route('/favicon.ico')
def favicon():
    """Serve favicon"""
    try:
        return send_from_directory('.', 'favicon.ico', mimetype='image/x-icon')
    except FileNotFoundError:
        return "Favicon not found", 404

@app.route('/@vite/<path:path>')
def block_vite(path):
    """Block Vite development requests"""
    logger.warning(f"Blocked Vite request: @vite/{path}")
    return "Development tools not available in production", 404

@app.route('/<path:filename>')
def static_files(filename):
    """Serve static files (images, etc.)"""
    logger.info(f"Static file requested: {filename}")
    try:
        # Strip query parameters if present
        actual_filename = filename.split('?')[0]
        allowed_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.ico', '.svg', '.webp', '.html'}
        file_ext = os.path.splitext(actual_filename)[1].lower()
        
        if file_ext in allowed_extensions:
            logger.info(f"Serving allowed file: {actual_filename}")
            return send_from_directory('.', actual_filename)
        else:
            logger.warning(f"File type not allowed: {actual_filename}")
            return "File type not allowed", 403
    except FileNotFoundError:
        logger.error(f"File not found: {actual_filename}")
        return "File not found", 404
    except Exception as e:
        logger.error(f"Error serving file {filename}: {str(e)}")
        return "Error serving file", 500

@app.route('/api/waitlist', methods=['POST'])
@limiter.limit("5 per hour")
def add_to_waitlist():
    """API endpoint to add user to waitlist"""
    try:
        # Validate content type
        if not request.is_json:
            return jsonify({
                'success': False,
                'error': 'Content-Type must be application/json'
            }), 400
        
        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400
        
        # Get client information
        ip_address = get_remote_address()
        user_agent = request.headers.get('User-Agent', 'Unknown')
        
        # Log the request (without sensitive data)
        logger.info(f"Waitlist submission from {ip_address[:8]}... UA: {user_agent[:50]}...")
        
        # Process the request
        result = waitlist_api.add_to_waitlist(data, ip_address, user_agent)
        
        # Return appropriate status code
        status_code = 200 if result['success'] else 400
        return jsonify(result), status_code
        
    except Exception as e:
        logger.error(f"Waitlist API error: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'code': 'SERVER_ERROR'
        }), 500

@app.route('/api/stats', methods=['GET'])
@limiter.limit("10 per minute")
def get_stats():
    """API endpoint to get waitlist statistics (public, limited info)"""
    try:
        stats = waitlist_api.get_stats()
        # Only return non-sensitive statistics
        public_stats = {
            'total_signups': stats['total_signups'],
            'last_updated': stats['timestamp']
        }
        return jsonify(public_stats)
    except Exception as e:
        logger.error(f"Stats API error: {e}")
        return jsonify({
            'error': 'Unable to fetch statistics'
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

@app.route('/privacy-policy')
def privacy_policy():
    """Serve privacy policy"""
    privacy_html = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Privacy Policy - Vision Lab</title>
        <link rel="stylesheet" href="/styles.css">
    </head>
    <body>
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <h2><a href="/" style="color: #446493; text-decoration: none;">Vision Lab</a></h2>
                </div>
            </div>
        </nav>
        
        <div style="padding: 120px 20px 60px; max-width: 800px; margin: 0 auto;">
            <h1>Privacy Policy</h1>
            <p><strong>Last updated: December 2024</strong></p>
            
            <h2>Information We Collect</h2>
            <p>When you join our waitlist, we collect:</p>
            <ul>
                <li>Name and email address (required)</li>
                <li>Organization name (optional)</li>
                <li>Use case and experience level (optional)</li>
                <li>IP address and browser information (for security)</li>
            </ul>
            
            <h2>How We Use Your Information</h2>
            <p>We use your information to:</p>
            <ul>
                <li>Notify you about beta access availability</li>
                <li>Send product updates (if you opt-in)</li>
                <li>Improve our services</li>
                <li>Prevent fraud and abuse</li>
            </ul>
            
            <h2>Data Security</h2>
            <p>We implement industry-standard security measures including:</p>
            <ul>
                <li>Email hashing for privacy protection</li>
                <li>Encrypted data storage</li>
                <li>Rate limiting and abuse prevention</li>
                <li>Secure server infrastructure</li>
            </ul>
            
            <h2>Your Rights</h2>
            <p>You have the right to:</p>
            <ul>
                <li>Access your personal data</li>
                <li>Request data deletion</li>
                <li>Opt-out of communications</li>
                <li>Data portability</li>
            </ul>
            
            <h2>Contact Us</h2>
            <p>For privacy-related questions, contact us at: <EMAIL></p>
        </div>
    </body>
    </html>
    """
    return privacy_html

@app.route('/terms-of-service')
def terms_of_service():
    """Serve terms of service"""
    terms_html = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Terms of Service - Vision Lab</title>
        <link rel="stylesheet" href="/styles.css">
    </head>
    <body>
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <h2><a href="/" style="color: #446493; text-decoration: none;">Vision Lab</a></h2>
                </div>
            </div>
        </nav>
        
        <div style="padding: 120px 20px 60px; max-width: 800px; margin: 0 auto;">
            <h1>Terms of Service</h1>
            <p><strong>Last updated: December 2024</strong></p>
            
            <h2>Acceptance of Terms</h2>
            <p>By accessing and using Vision Lab services, you accept and agree to be bound by the terms and provision of this agreement.</p>
            
            <h2>Beta Software</h2>
            <p>Vision Lab is currently in beta. The software is provided "as is" without warranty of any kind.</p>
            
            <h2>User Responsibilities</h2>
            <p>Users agree to:</p>
            <ul>
                <li>Provide accurate information</li>
                <li>Use the service lawfully</li>
                <li>Respect intellectual property rights</li>
                <li>Not attempt to reverse engineer the software</li>
            </ul>
            
            <h2>Intellectual Property</h2>
            <p>All content, features, and functionality are owned by Vision Lab and protected by copyright and other intellectual property laws.</p>
            
            <h2>Limitation of Liability</h2>
            <p>Vision Lab shall not be liable for any indirect, incidental, special, consequential, or punitive damages.</p>
            
            <h2>Contact Us</h2>
            <p>For questions about these terms, contact us at: <EMAIL></p>
        </div>
    </body>
    </html>
    """
    return terms_html

# Development server configuration
if __name__ == '__main__':
    # Environment-specific configuration
    debug_mode = os.environ.get('FLASK_ENV') == 'development'
    port = int(os.environ.get('PORT', 5000))
    host = os.environ.get('HOST', '127.0.0.1')
    
    if debug_mode:
        logger.warning("Running in development mode - not suitable for production!")
    
    logger.info(f"Starting Vision Lab server on {host}:{port}")
    
    # Run the server
    app.run(
        host=host,
        port=port,
        debug=debug_mode,
        threaded=True
    )