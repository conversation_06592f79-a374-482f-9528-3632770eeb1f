import os
import logging
from PySide6.QtWidgets import QGraphicsView, QGraphicsScene, QGraphicsPixmapItem
from PySide6.QtCore import Qt, QRectF, QPointF
from PySide6.QtGui import QPixmap, QWheelEvent, QMouseEvent, QPainter, QTransform

logger = logging.getLogger(__name__)

class ZoomableImageView(QGraphicsView):
    """A QGraphicsView that displays an image with zoom and pan capabilities."""

    def __init__(self, parent=None):
        super().__init__(parent)

        # Initialize variables
        self.pixmap_item = None
        self.zoom_factor = 1.0
        self.min_zoom = 0.1
        self.max_zoom = 10.0
        self.zoom_step = 0.1
        self.panning = False
        self.last_pan_point = QPointF()

        # Set up the scene
        self.scene = QGraphicsScene(self)
        self.setScene(self.scene)

        # Set up view properties
        self.setRenderHint(QPainter.Antialiasing, True)
        self.setRenderHint(QPainter.SmoothPixmapTransform, True)
        self.setDragMode(QGraphicsView.NoDrag)
        self.setResizeAnchor(QGraphicsView.AnchorUnderMouse)
        self.setTransformationAnchor(QGraphicsView.AnchorUnderMouse)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # Set background color
        self.setStyleSheet("background-color: #f0f0f0;")

    def set_pixmap(self, pixmap):
        """Set the pixmap to display."""
        # Clear the scene
        self.scene.clear()

        if pixmap and not pixmap.isNull():
            # Create a pixmap item
            self.pixmap_item = QGraphicsPixmapItem(pixmap)
            self.scene.addItem(self.pixmap_item)

            # Reset zoom
            self.zoom_factor = 1.0

            # Fit the image to the view
            self.fit_in_view()
        else:
            self.pixmap_item = None

    def fit_in_view(self):
        """Fit the image to the view."""
        if self.pixmap_item:
            # Get the pixmap rect
            rect = self.pixmap_item.boundingRect()

            # Fit in view
            self.fitInView(rect, Qt.KeepAspectRatio)

            # Update zoom factor based on the current transform
            if self.pixmap_item.pixmap().width() > 0:
                self.zoom_factor = self.transform().m11()

    def wheelEvent(self, event: QWheelEvent):
        """Handle wheel events for zooming."""
        if self.pixmap_item:
            # Calculate zoom factor
            zoom_in = event.angleDelta().y() > 0

            if zoom_in:
                self.zoom_factor += self.zoom_step
            else:
                self.zoom_factor -= self.zoom_step

            # Clamp zoom factor
            self.zoom_factor = max(self.min_zoom, min(self.max_zoom, self.zoom_factor))

            # Apply zoom
            self.setTransform(QTransform().scale(self.zoom_factor, self.zoom_factor))

            # Accept the event
            event.accept()
        else:
            # Pass the event to the parent
            super().wheelEvent(event)

    def mousePressEvent(self, event: QMouseEvent):
        """Handle mouse press events for panning."""
        if event.button() == Qt.LeftButton and self.pixmap_item:
            # Start panning
            self.panning = True
            self.last_pan_point = event.position()
            self.setCursor(Qt.ClosedHandCursor)
            event.accept()
        else:
            # Pass the event to the parent
            super().mousePressEvent(event)

    def mouseMoveEvent(self, event: QMouseEvent):
        """Handle mouse move events for panning."""
        if self.panning and self.pixmap_item:
            # Calculate the delta
            delta = event.position() - self.last_pan_point
            self.last_pan_point = event.position()

            # Pan the view
            self.horizontalScrollBar().setValue(self.horizontalScrollBar().value() - delta.x())
            self.verticalScrollBar().setValue(self.verticalScrollBar().value() - delta.y())

            event.accept()
        else:
            # Pass the event to the parent
            super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event: QMouseEvent):
        """Handle mouse release events for panning."""
        if event.button() == Qt.LeftButton and self.panning:
            # Stop panning
            self.panning = False
            self.setCursor(Qt.ArrowCursor)
            event.accept()
        else:
            # Pass the event to the parent
            super().mouseReleaseEvent(event)

    def resizeEvent(self, event):
        """Handle resize events."""
        super().resizeEvent(event)

        # Fit the image to the view if we have a pixmap
        if self.pixmap_item and not self.panning:
            self.fit_in_view()
