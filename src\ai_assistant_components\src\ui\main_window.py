# src/ui/main_window.py
import sys
import os
import logging # Import logging
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QTabWidget, QStatusBar,
    QMessageBox, QApplication, QFileDialog # Import QFileDialog
)
from PySide6.QtGui import QAction, QIcon
from PySide6.QtCore import Slot, QSettings

# ... (keep existing imports: GrainSightProject, AIAssistantPage, SettingsDialog) ...
from src.core.grainsight_project import GrainSightProject
from src.ui.pages.ai_assistant_page import AIAssistantPage
from src.ui.widgets.settings_dialog import SettingsDialog


ICON_PATH = os.path.join(os.path.dirname(__file__), "..", "resources", "icons")
def get_icon(name):
    path = os.path.join(ICON_PATH, name)
    return QIcon(path) if os.path.exists(path) else QIcon()

logger = logging.getLogger(__name__) # Define logger for this module


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        # ... (keep window setup) ...
        self.setWindowTitle("VisionLab Ai V4 - GrainSight AI")
        self.setGeometry(100, 100, 1200, 800)

        self.project = GrainSightProject()

        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.layout = QVBoxLayout(self.central_widget)

        self.tab_widget = QTabWidget()
        self.layout.addWidget(self.tab_widget)

        self.tab_widget.addTab(QWidget(), "Image Segmentation")
        self.tab_widget.addTab(QWidget(), "Data Analysis")

        self.ai_assistant_page = AIAssistantPage(self.project)
        self.tab_widget.addTab(self.ai_assistant_page, get_icon("ai_icon.png"), "AI Assistant")
        self.ai_assistant_page.status_message_requested.connect(self.show_status_message)

        # --- Menu Bar ---
        self.menu_bar = self.menuBar()
        self.file_menu = self.menu_bar.addMenu("&File")
        self.settings_menu = self.menu_bar.addMenu("&Settings")

        # --- Modified File Actions ---
        open_action = QAction(get_icon("folder_open.png"), "&Open Project...", self)
        # open_action.triggered.connect(self.open_project) # Implement later
        self.file_menu.addAction(open_action)

        # --- ADD THIS ACTION ---
        add_image_action = QAction(get_icon("add_image.png"), "&Add Image to Project...", self) # Add icon if you have one
        add_image_action.triggered.connect(self.add_image_to_project)
        self.file_menu.addAction(add_image_action)
        # ----------------------

        self.file_menu.addSeparator()
        exit_action = QAction(get_icon("exit.png"), "E&xit", self)
        exit_action.triggered.connect(self.close)
        self.file_menu.addAction(exit_action)

        # Settings Actions (keep as is)
        api_key_action = QAction(get_icon("settings_icon.png"), "&Configure API Key...", self)
        api_key_action.triggered.connect(self.configure_api_key)
        self.settings_menu.addAction(api_key_action)

        # Status Bar (keep as is)
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.show_status_message("Ready.", 0)

        self._check_initial_api_key()

    # ... (keep show_status_message, configure_api_key, _check_initial_api_key) ...
    @Slot(str, int)
    def show_status_message(self, message, timeout=3000):
        """Displays a message in the status bar."""
        if timeout > 0:
            self.status_bar.showMessage(message, timeout)
        else:
            self.status_bar.showMessage(message) # Persistent message

    def configure_api_key(self):
        """Opens the settings dialog to configure the API key."""
        dialog = SettingsDialog(self)
        if dialog.exec(): # User clicked OK
             self.ai_assistant_page.update_api_key()
             self.show_status_message("API Key settings updated.", 3000)


    def _check_initial_api_key(self):
         """Checks if API key is set on startup and warns if not."""
         settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")
         api_key = settings.value("gemini/api_key", "")
         if not api_key:
             QMessageBox.warning(self, "API Key Not Set",
                                 "The Google Gemini API Key is not configured in Settings.\n"
                                 "The AI Assistant features require a valid API key to function.")


    # --- ADD THIS METHOD ---
    def add_image_to_project(self):
        """Opens a file dialog to select an image and adds it to the project."""
        file_dialog = QFileDialog(self)
        # Use native dialogs if possible
        file_dialog.setFileMode(QFileDialog.FileMode.ExistingFile)
        file_dialog.setNameFilter("Images (*.png *.jpg *.jpeg *.tif *.tiff *.bmp *.webp)")
        file_dialog.setViewMode(QFileDialog.ViewMode.Detail)

        if file_dialog.exec():
            selected_files = file_dialog.selectedFiles()
            if selected_files:
                source_path = selected_files[0]
                self.show_status_message(f"Adding {os.path.basename(source_path)} to project...")
                QApplication.processEvents() # Update UI

                new_image_id, error_msg = self.project.add_image(source_path)

                if new_image_id:
                    self.show_status_message(f"Successfully added image: {new_image_id}", 5000)
                    # Refresh the image list in the AI Assistant page
                    self.ai_assistant_page.refresh_image_list()
                    # Optionally, select the newly added image
                    index = self.ai_assistant_page.image_selector.findText(new_image_id)
                    if index != -1:
                        self.ai_assistant_page.image_selector.setCurrentIndex(index)
                else:
                    QMessageBox.critical(self, "Error Adding Image",
                                         f"Could not add image to project.\nError: {error_msg}")
                    self.show_status_message("Failed to add image.", 5000)
    # ----------------------

    # --- Modified closeEvent ---
    def closeEvent(self, event):
        """Handle window close event."""
        # Ensure logger is available if needed here
        logger.info("Close event triggered.") # Example log

        # Give pages a chance to clean up (like stopping threads)
        # Check if ai_assistant_page exists before calling its methods
        if hasattr(self, 'ai_assistant_page') and self.ai_assistant_page:
             self.ai_assistant_page.closeEvent(event) # Pass the event along
             if event.isAccepted(): # Check if the page aborted the close
                 logger.info("AI Assistant page accepted close event.")
             else:
                 logger.info("AI Assistant page ignored close event.")
                 return # Don't proceed with closing if page ignored it


        # Ask for confirmation (only if page didn't ignore the event)
        reply = QMessageBox.question(self, 'Confirm Exit',
                                     "Are you sure you want to exit VisionLab Ai V4?"
                                     QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                                     QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
             logger.info("Exiting application.")
             event.accept() # Accept the close event
        else:
             logger.info("Exit cancelled by user.")
             event.ignore() # Ignore the close event