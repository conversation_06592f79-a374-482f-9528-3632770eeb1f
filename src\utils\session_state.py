
"""
Session state management for VisionLab Ai application.
Allows saving and loading session state to preserve user progress.
"""

import os
import pickle
import time
import logging
import hashlib
from pathlib import Path

# Use our custom JSON utility module
from src.utils import json_utils

logger = logging.getLogger(__name__)

class SessionState:
    """Class for managing session state across the application."""

    def __init__(self, project_dir=None):
        """Initialize the session state manager.

        Args:
            project_dir: The project directory or project file path.
        """
        self.project_path = None # Can be .vlp file or directory
        self.project_dir = None # The main project directory
        self.data_dir = None # Specific directory for project data (_data)
        self.state_dir = None # Directory for saving state files
        self.images_dir = None # Directory containing project images

        if project_dir:
            self.set_project_dir(project_dir)

    def _determine_paths(self, project_path):
        """Determine project, data, state, and image directories."""
        self.project_path = Path(project_path).resolve()
        logger.info(f"Determining paths for project: {self.project_path}")

        if self.project_path.is_file() and self.project_path.suffix == '.vlp':
            # VisionLabProject (.vlp file)
            project_name = self.project_path.stem
            self.project_dir = self.project_path.parent
            self.data_dir = self.project_dir / f"{project_name}_data"
            logger.info(f"Project type: VisionLabProject (.vlp)")
        elif self.project_path.is_dir() and self.project_path.name.endswith('_data'):
            # GrainSightProject (_data directory)
            vlp_file = self.project_path.parent / f"{self.project_path.stem.replace('_data', '')}.vlp"
            if vlp_file.exists():
                self.project_dir = self.project_path.parent
                self.data_dir = self.project_path
                logger.info(f"Project type: GrainSightProject (data dir)")
            else:
                # Regular project where dir name happens to end in _data
                self.project_dir = self.project_path
                self.data_dir = self.project_dir / f"{self.project_path.name}_data"
                logger.warning(f"Directory ends in _data but no corresponding .vlp found. Treating as regular project.")
        else:
            # Regular project directory
            self.project_dir = self.project_path
            self.data_dir = self.project_dir / f"{self.project_path.name}_data"
            logger.info(f"Project type: Regular project directory")

        # Define state and image directories relative to the data directory
        self.state_dir = self.data_dir / "state"
        self.images_dir = self.data_dir / "images" # Default image location

        # Fallback if default images_dir doesn't exist, check inside project_dir
        if not self.images_dir.exists() and (self.project_dir / "images").exists():
             self.images_dir = self.project_dir / "images"
             logger.info(f"Using images directory inside project root: {self.images_dir}")
        elif not self.images_dir.exists():
             logger.warning(f"Default images directory does not exist: {self.images_dir}. Image path resolution might fail.")
             # Attempt to create data_dir and images_dir if they don't exist
             try:
                 self.data_dir.mkdir(parents=True, exist_ok=True)
                 self.images_dir.mkdir(exist_ok=True) # Create images dir inside data_dir
                 logger.info(f"Created missing directories: {self.data_dir} and {self.images_dir}")
             except OSError as e:
                 logger.error(f"Failed to create project directories: {e}")
                 # Set images_dir to None if creation failed or doesn't exist
                 self.images_dir = None

        # Ensure state directory exists
        if not self.state_dir.exists():
            try:
                # Create state directory and advanced_segmentation subdirectory
                self.state_dir.mkdir(parents=True, exist_ok=True)
                adv_seg_dir = self.state_dir / "advanced_segmentation"
                adv_seg_dir.mkdir(exist_ok=True)
                logger.info(f"Created state directory: {self.state_dir} and advanced_segmentation subdirectory")
            except OSError as e:
                logger.error(f"Failed to create state directory: {e}")
                self.state_dir = None # Indicate state cannot be saved/loaded
        else:
            # State directory exists, ensure advanced_segmentation subdirectory exists
            try:
                adv_seg_dir = self.state_dir / "advanced_segmentation"
                adv_seg_dir.mkdir(exist_ok=True)
                logger.info(f"Using existing state directory: {self.state_dir}")
            except OSError as e:
                logger.error(f"Failed to create advanced_segmentation subdirectory: {e}")

        if not self.state_dir:
            logger.error("Could not determine state directory.")


    def set_project_dir(self, project_path):
        """Set the project path and determine related directories.

        Args:
            project_path: The project directory path or .vlp file path.
        """
        try:
            self._determine_paths(project_path)
        except Exception as e:
            logger.error(f"Error setting project directory '{project_path}': {e}", exc_info=True)
            # Reset paths on error
            self.project_path = None
            self.project_dir = None
            self.data_dir = None
            self.state_dir = None
            self.images_dir = None

    def get_canonical_path(self, absolute_path_str):
        """Converts an absolute image path to a canonical, relative path.

        The canonical path is relative to the project's images directory
        and uses forward slashes.

        Args:
            absolute_path_str: The absolute path to the image file.

        Returns:
            The canonical relative path (str) or None if it cannot be determined.
        """
        if not self.images_dir or not absolute_path_str:
            logger.warning(f"Cannot get canonical path: images_dir ({self.images_dir}) or absolute_path ({absolute_path_str}) is invalid.")
            return None
        try:
            absolute_path = Path(absolute_path_str).resolve()
            # Check if the path is within the determined images_dir
            if self.images_dir in absolute_path.parents:
                 relative_path = absolute_path.relative_to(self.images_dir)
                 # Convert to string with forward slashes
                 return relative_path.as_posix()
            else:
                # If not directly inside images_dir, maybe it's in project_dir?
                if self.project_dir and self.project_dir in absolute_path.parents:
                     relative_path = absolute_path.relative_to(self.project_dir)
                     logger.warning(f"Image {absolute_path} is outside images_dir but inside project_dir. Using path relative to project_dir: {relative_path.as_posix()}")
                     # Prepend a marker? Or just use relative path from project?
                     # Using relative path from project might be okay if consistent.
                     return relative_path.as_posix()
                else:
                     logger.warning(f"Path {absolute_path} is not relative to images_dir {self.images_dir} or project_dir {self.project_dir}. Cannot determine canonical path.")
                     # Fallback: return just the filename? Or the full absolute path?
                     # Returning None is safer to indicate failure.
                     return None
        except (ValueError, TypeError) as e:
            logger.error(f"Error converting path '{absolute_path_str}' to canonical: {e}")
            return None


    def get_absolute_path(self, canonical_path_str):
        """Converts a canonical, relative path back to an absolute path.

        Args:
            canonical_path_str: The canonical relative path (using forward slashes).

        Returns:
            The absolute path (Path object) or None if resolution fails.
        """
        if not self.images_dir or not canonical_path_str:
            logger.warning(f"Cannot get absolute path: images_dir ({self.images_dir}) or canonical_path ({canonical_path_str}) is invalid.")
            return None

        try:
            # Assume path is relative to images_dir first
            absolute_path = (self.images_dir / canonical_path_str).resolve()
            if absolute_path.exists():
                 return str(absolute_path) # Return as string for consistency
            else:
                 # If not found relative to images_dir, try relative to project_dir
                 if self.project_dir:
                      absolute_path_proj = (self.project_dir / canonical_path_str).resolve()
                      if absolute_path_proj.exists():
                           logger.warning(f"Resolved canonical path {canonical_path_str} relative to project_dir instead of images_dir.")
                           return str(absolute_path_proj) # Return as string
                 # If neither exists, log error and return None
                 logger.error(f"Could not resolve canonical path '{canonical_path_str}' to an existing file relative to {self.images_dir} or {self.project_dir}.")
                 return None

        except TypeError as e:
            logger.error(f"Error converting canonical path '{canonical_path_str}' to absolute: {e}")
            return None

    def get_state_subdir(self, page_name):
        """Gets the specific subdirectory path for a page's state."""
        if not self.state_dir:
            return None
        return self.state_dir / page_name

    def get_mask_subdir_hash(self, canonical_path_str):
        """Generates a unique subdirectory name hash for storing masks."""
        if not canonical_path_str:
             return None
        # Use sha1 for reasonable uniqueness and length control
        return hashlib.sha1(canonical_path_str.encode()).hexdigest()[:16]

    def save_state(self, page_name, state_data, cleanup_old=True):
        """Save state data (typically JSON) for a specific page.

        Handles timestamping, pointer file, and cleanup.
        Assumes complex objects like masks are handled *before* this call
        and `state_data` is JSON-serializable.

        Args:
            page_name: The name of the page (used for subdirectory and filenames).
            state_data: The JSON-serializable data to save.
            cleanup_old: Whether to remove old state files (default: True).

        Returns:
            bool: True if successful, False otherwise.
        """
        page_state_dir = self.get_state_subdir(page_name)
        if not page_state_dir:
            logger.error(f"Cannot save state for '{page_name}': State directory not available.")
            return False

        try:
            # Ensure the page-specific state directory exists
            page_state_dir.mkdir(parents=True, exist_ok=True)

            # Create a timestamp for the filename
            timestamp = int(time.time())
            filename = f"state_{timestamp}.json"
            filepath = page_state_dir / filename

            # Save as JSON using the custom utility
            with open(filepath, 'w') as f:
                json_utils.dump(state_data, f, indent=2)

            # Create or update the latest state pointer
            pointer_file = page_state_dir / "latest_state.txt"
            with open(pointer_file, 'w') as f:
                f.write(filename)

            # Clean up old state files if requested
            if cleanup_old:
                self._cleanup_old_state_files(page_state_dir, prefix="state_", suffix=".json", keep_latest=3)

            logger.info(f"Saved state for '{page_name}' to {filepath}")
            return True

        except Exception as e:
            logger.error(f"Error saving state for '{page_name}': {e}", exc_info=True)
            return False

    def _cleanup_old_state_files(self, directory, prefix, suffix, keep_latest=3):
        """Clean up old state files within a directory.

        Args:
            directory (Path): The directory containing the state files.
            prefix (str): Filename prefix (e.g., "state_").
            suffix (str): Filename suffix (e.g., ".json").
            keep_latest (int): Number of most recent files to keep.
        """
        try:
            state_files = []
            for item in directory.iterdir():
                if item.is_file() and item.name.startswith(prefix) and item.name.endswith(suffix):
                    try:
                        mtime = item.stat().st_mtime
                        state_files.append((mtime, item))
                    except OSError:
                        continue # Skip files we can't stat

            # Sort by modification time (newest first)
            state_files.sort(key=lambda x: x[0], reverse=True)

            # Delete all but the most recent files
            files_to_delete = state_files[keep_latest:]
            if files_to_delete:
                 logger.info(f"Cleaning up {len(files_to_delete)} old state files in {directory}...")
                 for _, filepath in files_to_delete:
                     try:
                         filepath.unlink() # Delete file
                     except OSError as e:
                         logger.error(f"Error removing old state file {filepath}: {e}")

        except Exception as e:
            logger.error(f"Error cleaning up old state files in {directory}: {e}", exc_info=True)

    def load_state(self, page_name):
        """Load the latest state data (JSON) for a specific page.

        Args:
            page_name: The name of the page.

        Returns:
            The loaded state data (dict), or None if no state exists or an error occurs.
        """
        page_state_dir = self.get_state_subdir(page_name)
        if not page_state_dir or not page_state_dir.exists():
            # logger.info(f"Cannot load state for '{page_name}': State directory not found ({page_state_dir}).")
            return None

        try:
            # Check if a latest state pointer exists
            pointer_file = page_state_dir / "latest_state.txt"
            if not pointer_file.is_file():
                # Try finding the most recent json file directly if pointer is missing
                state_files = sorted(
                    [f for f in page_state_dir.glob("state_*.json") if f.is_file()],
                    key=lambda f: f.stat().st_mtime,
                    reverse=True
                )
                if not state_files:
                    logger.info(f"No state pointer or state files found for '{page_name}' in {page_state_dir}.")
                    return None
                filepath = state_files[0]
                logger.warning(f"State pointer missing for '{page_name}', loading most recent file: {filepath.name}")
            else:
                # Read the latest state filename from the pointer
                with open(pointer_file, 'r') as f:
                    filename = f.read().strip()
                filepath = page_state_dir / filename

            # Load the state file
            if not filepath.is_file():
                logger.error(f"State file specified by pointer does not exist: {filepath}")
                return None

            # Load JSON using the custom utility
            with open(filepath, 'r') as f:
                state_data = json_utils.load(f)
            logger.info(f"Loaded state for '{page_name}' from {filepath}")
            return state_data

        except Exception as e:
            logger.error(f"Error loading state for '{page_name}': {e}", exc_info=True)
            return None

    def clear_state_subdir(self, page_name):
         """Clears all files and subdirectories within a page's state subdir."""
         page_state_dir = self.get_state_subdir(page_name)
         if not page_state_dir or not page_state_dir.exists():
              return False
         try:
              logger.warning(f"Clearing state subdirectory for page '{page_name}': {page_state_dir}")
              for item in page_state_dir.iterdir():
                   if item.is_file():
                        item.unlink()
                   elif item.is_dir():
                        # Recursively remove subdirectory contents
                        import shutil
                        shutil.rmtree(item)
              return True
         except Exception as e:
              logger.error(f"Error clearing state for page '{page_name}': {e}", exc_info=True)
              return False

    def cleanup_orphaned_mask_dirs(self, page_name, expected_hashes):
        """Removes mask subdirectories that don't correspond to expected hashes."""
        page_state_dir = self.get_state_subdir(page_name)
        if not page_state_dir or not page_state_dir.exists():
            return

        logger.info(f"Checking for orphaned mask directories in {page_state_dir}...")
        expected_hashes_set = set(expected_hashes)
        deleted_count = 0

        try:
            for item in page_state_dir.iterdir():
                # Check if it's a directory and looks like one of our hash dirs (16 hex chars)
                if item.is_dir() and len(item.name) == 16 and all(c in '0123456789abcdef' for c in item.name):
                    if item.name not in expected_hashes_set:
                        try:
                            import shutil
                            shutil.rmtree(item)
                            logger.info(f"Deleted orphaned mask directory: {item.name}")
                            deleted_count += 1
                        except Exception as e:
                            logger.error(f"Failed to delete orphaned directory {item}: {e}")
            if deleted_count > 0:
                 logger.info(f"Finished cleanup: Deleted {deleted_count} orphaned mask directories.")
            else:
                 logger.info("No orphaned mask directories found.")

        except Exception as e:
            logger.error(f"Error during orphaned mask directory cleanup: {e}", exc_info=True)

