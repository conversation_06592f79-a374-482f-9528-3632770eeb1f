# src/gui/ui/navigation_tabs.py

from PySide6.QtWidgets import QFrame, QHBoxLayout, QPushButton
from PySide6.QtCore import Signal, QObject

class NavigationTabs(QObject):
    """Class for creating and managing the navigation tabs."""

    tab_clicked = Signal(str)  # Signal emitted when a tab is clicked

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.nav_bar = None
        self.buttons = {}
        self.current_tab = None

    def setup_navigation_tabs(self, parent_layout):
        """Creates the navigation tabs."""
        # Create the navigation bar frame
        self.nav_bar = QFrame()
        self.nav_bar.setObjectName("navigation_bar")

        # Try to load custom stylesheet
        try:
            with open("src/gui/styles/navigation_tabs_style.css", "r") as f:
                self.nav_bar.setStyleSheet(f.read())
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error loading navigation tabs style: {e}")

        # Create layout for the navigation bar
        nav_layout = QHBoxLayout(self.nav_bar)
        nav_layout.setSpacing(4)
        nav_layout.setContentsMargins(5, 0, 5, 0)

        # Add the navigation bar to the parent layout
        parent_layout.addWidget(self.nav_bar)

        # Create navigation buttons
        tab_names = [
            "Project Hub",
            "Unsupervised Segmentation",
            "Trainable Segmentation",
            "Point Counting",
            "Grain Analysis",
            "Advanced Segmentation",
            "Image Lab",
            "Settings",
            "AI Assistant"
        ]

        # Create buttons for each tab
        for tab_name in tab_names:
            btn = QPushButton(tab_name)
            btn.setMinimumWidth(120)
            btn.setCheckable(True)
            btn.clicked.connect(lambda checked, name=tab_name: self.on_tab_clicked(name))
            nav_layout.addWidget(btn)
            self.buttons[tab_name] = btn

        # Add stretch to push buttons to the left
        nav_layout.addStretch()

        # Set the default active tab
        self.set_active_tab("Project Hub")

    def on_tab_clicked(self, tab_name):
        """Handle tab button clicks."""
        self.set_active_tab(tab_name)
        self.tab_clicked.emit(tab_name)

    def set_active_tab(self, tab_name):
        """Set the active tab."""
        # Uncheck all buttons
        for name, btn in self.buttons.items():
            btn.setChecked(False)

        # Check the active button
        if tab_name in self.buttons:
            self.buttons[tab_name].setChecked(True)
            self.current_tab = tab_name
