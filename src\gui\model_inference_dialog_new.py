import os
import logging
import numpy as np
import cv2
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                              QGroupBox, QFormLayout, QComboBox, QDoubleSpinBox,
                              QFileDialog, QMessageBox, QListWidget, QCheckBox,
                              QProgressBar, QSplitter, QWidget)
from PySide6.QtCore import Qt, Signal, QThread
from PySide6.QtGui import QPixmap, QImage, QColor

# Import the zoomable image view
from src.gui.widgets.zoomable_image_view import ZoomableImageView

logger = logging.getLogger(__name__)

class InferenceWorker(QThread):
    """Worker thread for running inference with Detectron2 models."""

    # Signals
    progress_updated = Signal(int, int)  # current, total
    inference_completed = Signal(bool, str, object)  # success, message, results

    def __init__(self, model_path, image_paths, threshold, model_type, num_classes, class_names=None, use_custom_classes=True, batch_size=1):
        super().__init__()
        self.model_path = model_path
        self.image_paths = image_paths
        self.threshold = threshold
        self.model_type = model_type
        self.num_classes = num_classes
        self.class_names = class_names or {}
        self.use_custom_classes = use_custom_classes
        self.batch_size = batch_size
        self.is_running = True

    def run(self):
        """Run the inference process."""
        try:
            # Import required modules
            import os
            import sys
            import cv2

            # Import detectron2 here to avoid import errors if not installed
            try:
                from detectron2.config import get_cfg
                from detectron2.engine import DefaultPredictor
                from detectron2.utils.visualizer import Visualizer
                from detectron2.data import MetadataCatalog
            except ImportError:
                # Try to import from local path
                detectron2_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'detectron2')
                if os.path.exists(detectron2_path):
                    sys.path.append(os.path.dirname(detectron2_path))
                    from detectron2.config import get_cfg
                    from detectron2.engine import DefaultPredictor
                    from detectron2.utils.visualizer import Visualizer
                    from detectron2.data import MetadataCatalog
                else:
                    raise ImportError("Detectron2 not found in local path")

            # Set the model architecture based on model type
            try:
                from detectron2.model_zoo import model_zoo
            except ImportError:
                # Try to import from local path if not already imported
                # Note: os and sys are already imported at the beginning of the method
                detectron2_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'detectron2')
                if os.path.exists(detectron2_path):
                    sys.path.append(os.path.dirname(detectron2_path))
                    from detectron2.model_zoo import model_zoo
                else:
                    raise ImportError("Detectron2 model_zoo not found in local path")

            # Create config
            cfg = get_cfg()

            if self.model_type == "Faster R-CNN":
                cfg.merge_from_file(model_zoo.get_config_file("COCO-Detection/faster_rcnn_R_50_FPN_3x.yaml"))
            elif self.model_type == "Mask R-CNN":
                cfg.merge_from_file(model_zoo.get_config_file("COCO-InstanceSegmentation/mask_rcnn_R_50_FPN_3x.yaml"))

            # Try to infer the number of classes from the model metadata
            # If not possible, use the user-specified value
            try:
                # First check if we can determine the number of classes from the model file
                import pickle
                with open(self.model_path, 'rb') as f:
                    # Skip the header
                    f.seek(0)
                    try:
                        metadata = pickle.load(f)
                        if isinstance(metadata, dict) and 'model' in metadata:
                            # Try to extract num_classes from model metadata
                            pass  # This is challenging to extract reliably
                    except:
                        pass  # If we can't load the metadata, we'll use the user-specified value
            except:
                pass

            # Use the user-specified number of classes
            cfg.MODEL.ROI_HEADS.NUM_CLASSES = self.num_classes

            # Load model weights
            cfg.MODEL.WEIGHTS = self.model_path
            cfg.MODEL.ROI_HEADS.SCORE_THRESH_TEST = self.threshold

            # Create predictor
            predictor = DefaultPredictor(cfg)

            # Process each image
            results = {}
            for i, image_path in enumerate(self.image_paths):
                if not self.is_running:
                    break

                # Load image
                image = cv2.imread(image_path)
                if image is None:
                    logger.warning(f"Failed to load image: {image_path}")
                    continue

                # Run inference
                outputs = predictor(image)

                # Process results
                if self.model_type == "Faster R-CNN":
                    # Get bounding boxes
                    instances = outputs["instances"].to("cpu")
                    boxes = instances.pred_boxes.tensor.numpy()
                    scores = instances.scores.numpy()
                    classes = instances.pred_classes.numpy()

                    # Create visualization
                    metadata = MetadataCatalog.get(cfg.DATASETS.TRAIN[0])
                    # Apply custom class names if needed
                    metadata = self.get_custom_metadata(metadata)
                    visualizer = Visualizer(image[:, :, ::-1], metadata=metadata, scale=1.0)
                    vis_output = visualizer.draw_instance_predictions(instances)
                    result_image = vis_output.get_image()[:, :, ::-1]

                    # Create results dictionary
                    image_results = {
                        "boxes": boxes,
                        "scores": scores,
                        "classes": classes,
                        "visualization": result_image,
                        "annotations": []
                    }

                    # Convert to annotations format
                    for j in range(len(boxes)):
                        box = boxes[j]
                        score = scores[j]
                        class_id = int(classes[j]) + 1  # Convert from 0-indexed to 1-indexed

                        # Create rectangle annotation
                        annotation = {
                            'type': 'rectangle',
                            'points': [(box[0], box[1]), (box[2], box[3])],
                            'class_id': class_id,
                            'score': float(score)
                        }

                        image_results["annotations"].append(annotation)

                elif self.model_type == "Mask R-CNN":
                    # Get instance segmentation results
                    instances = outputs["instances"].to("cpu")
                    boxes = instances.pred_boxes.tensor.numpy()
                    scores = instances.scores.numpy()
                    classes = instances.pred_classes.numpy()
                    masks = instances.pred_masks.numpy()

                    # Create visualization
                    metadata = MetadataCatalog.get(cfg.DATASETS.TRAIN[0])
                    # Apply custom class names if needed
                    metadata = self.get_custom_metadata(metadata)
                    visualizer = Visualizer(image[:, :, ::-1], metadata=metadata, scale=1.0)
                    vis_output = visualizer.draw_instance_predictions(instances)
                    result_image = vis_output.get_image()[:, :, ::-1]

                    # Create results dictionary
                    image_results = {
                        "boxes": boxes,
                        "scores": scores,
                        "classes": classes,
                        "masks": masks,
                        "visualization": result_image,
                        "annotations": []
                    }

                    # Convert to annotations format
                    for j in range(len(masks)):
                        mask = masks[j]
                        score = scores[j]
                        class_id = int(classes[j]) + 1  # Convert from 0-indexed to 1-indexed

                        # Create mask annotation
                        annotation = {
                            'type': 'mask',
                            'mask': mask,
                            'class_id': class_id,
                            'score': float(score)
                        }

                        image_results["annotations"].append(annotation)

                # Add to results
                results[image_path] = image_results

                # Update progress
                self.progress_updated.emit(i + 1, len(self.image_paths))

            # Emit completion signal
            if self.is_running:
                self.inference_completed.emit(True, "Inference completed successfully", results)
            else:
                self.inference_completed.emit(False, "Inference was cancelled", None)

        except Exception as e:
            logger.error(f"Error during inference: {str(e)}")
            self.inference_completed.emit(False, f"Error during inference: {str(e)}", None)

    def get_custom_metadata(self, metadata):
        """Create custom metadata with user-defined class names."""
        if not self.use_custom_classes or not self.class_names:
            return metadata

        # Create a copy of the metadata to avoid modifying the original
        from detectron2.data import Metadata
        custom_metadata = Metadata()

        # Copy all attributes from the original metadata
        for key, value in metadata.__dict__.items():
            if key != "thing_classes" and key != "_RENAMED" and key != "name":
                setattr(custom_metadata, key, value)

        # Create a list of class names for visualization
        # Map from 0-indexed model classes to user-friendly names
        custom_thing_classes = []
        for i in range(self.num_classes):
            # Convert from 0-indexed (model) to 1-indexed (UI)
            class_id = i + 1
            if class_id in self.class_names:
                custom_thing_classes.append(self.class_names[class_id])
            else:
                custom_thing_classes.append(f"Class {class_id}")

        # Set the custom class names
        custom_metadata.thing_classes = custom_thing_classes
        return custom_metadata

    def stop(self):
        """Stop the inference process."""
        self.is_running = False


class ModelInferenceDialog(QDialog):
    """Dialog for running inference with trained models."""

    # Signals
    inference_complete = Signal(dict)  # annotations
    preview_results_ready = Signal(dict)  # image_path -> visualization

    def __init__(self, parent=None, image_paths=None, class_names=None):
        super().__init__(parent)
        self.setWindowTitle("Model Inference")
        self.setMinimumWidth(800)
        self.setMinimumHeight(600)

        # Initialize variables
        self.image_paths = image_paths or []
        self.class_names = class_names or {}
        self.model_path = ""
        self.inference_worker = None
        self.inference_results = {}
        self.num_classes = 5  # Default value, will be updated by user input

        # Initialize class mapping (model class index -> application class ID)
        self.class_mapping = {}

        # Initialize model class names (will be populated during inference if available)
        self.model_class_names = {}

        # Set up the UI
        self.setup_ui()

    def setup_ui(self):
        """Set up the dialog UI."""
        main_layout = QVBoxLayout(self)

        # Create a splitter for the main layout
        splitter = QSplitter(Qt.Horizontal)

        # Left panel (settings)
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)

        # Model selection
        model_group = QGroupBox("Model")
        model_layout = QFormLayout(model_group)

        model_path_layout = QHBoxLayout()
        self.model_path_edit = QLabel("No model selected")
        self.model_path_edit.setWordWrap(True)
        self.browse_model_btn = QPushButton("Browse...")
        self.browse_model_btn.clicked.connect(self.browse_model)
        model_path_layout.addWidget(self.model_path_edit)
        model_path_layout.addWidget(self.browse_model_btn)
        model_layout.addRow("Model:", model_path_layout)

        # Model type
        self.model_type_combo = QComboBox()
        self.model_type_combo.addItems(["Faster R-CNN", "Mask R-CNN"])
        model_layout.addRow("Model Type:", self.model_type_combo)

        # Number of classes
        from PySide6.QtWidgets import QSpinBox
        self.num_classes_spin = QSpinBox()
        self.num_classes_spin.setRange(1, 100)
        self.num_classes_spin.setValue(5)  # Default to 5 classes
        self.num_classes_spin.valueChanged.connect(self.update_num_classes)
        model_layout.addRow("Number of Classes:", self.num_classes_spin)

        # Confidence threshold
        self.threshold_spin = QDoubleSpinBox()
        self.threshold_spin.setRange(0.1, 1.0)
        self.threshold_spin.setValue(0.5)
        self.threshold_spin.setSingleStep(0.05)
        model_layout.addRow("Confidence Threshold:", self.threshold_spin)

        left_layout.addWidget(model_group)

        # Images
        images_group = QGroupBox("Images")
        images_layout = QVBoxLayout(images_group)

        self.images_list = QListWidget()
        self.images_list.setSelectionMode(QListWidget.ExtendedSelection)
        images_layout.addWidget(self.images_list)

        images_buttons_layout = QHBoxLayout()
        self.add_images_btn = QPushButton("Add Images")
        self.add_images_btn.clicked.connect(self.add_images)
        self.remove_images_btn = QPushButton("Remove Selected")
        self.remove_images_btn.clicked.connect(self.remove_selected_images)
        self.clear_images_btn = QPushButton("Clear All")
        self.clear_images_btn.clicked.connect(self.clear_images)

        images_buttons_layout.addWidget(self.add_images_btn)
        images_buttons_layout.addWidget(self.remove_images_btn)
        images_buttons_layout.addWidget(self.clear_images_btn)

        images_layout.addLayout(images_buttons_layout)

        left_layout.addWidget(images_group)

        # Options
        options_group = QGroupBox("Options")
        options_layout = QVBoxLayout(options_group)

        self.add_annotations_cb = QCheckBox("Add results as annotations")
        self.add_annotations_cb.setChecked(False)  # Disabled by default
        options_layout.addWidget(self.add_annotations_cb)

        self.show_in_preview_cb = QCheckBox("Show results in segmentation preview")
        self.show_in_preview_cb.setChecked(False)  # Disabled by default
        options_layout.addWidget(self.show_in_preview_cb)

        self.use_original_classes_cb = QCheckBox("Use original class names from model")
        self.use_original_classes_cb.setChecked(False)  # Disabled by default
        options_layout.addWidget(self.use_original_classes_cb)

        # Add class mapping section (collapsed by default)
        self.class_mapping_btn = QPushButton("Class Mapping...")
        self.class_mapping_btn.clicked.connect(self.show_class_mapping_dialog)
        options_layout.addWidget(self.class_mapping_btn)

        left_layout.addWidget(options_group)

        # Progress
        progress_group = QGroupBox("Progress")
        progress_layout = QVBoxLayout(progress_group)

        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        progress_layout.addWidget(self.progress_bar)

        self.status_label = QLabel("Ready")
        progress_layout.addWidget(self.status_label)

        left_layout.addWidget(progress_group)

        # Right panel (preview)
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)

        preview_group = QGroupBox("Preview")
        preview_layout = QVBoxLayout(preview_group)

        # Create a zoomable image view for the preview
        self.preview_view = ZoomableImageView()
        self.preview_view.setMinimumHeight(300)
        preview_layout.addWidget(self.preview_view)

        # Add a label for instructions
        self.preview_instructions = QLabel("Use mouse wheel to zoom, drag to pan")
        self.preview_instructions.setAlignment(Qt.AlignCenter)
        self.preview_instructions.setStyleSheet("color: #666666; font-style: italic;")
        preview_layout.addWidget(self.preview_instructions)

        right_layout.addWidget(preview_group)

        # Results
        results_group = QGroupBox("Results")
        results_layout = QVBoxLayout(results_group)

        self.results_list = QListWidget()
        self.results_list.itemSelectionChanged.connect(self.on_result_selected)
        results_layout.addWidget(self.results_list)

        right_layout.addWidget(results_group)

        # Add panels to splitter
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)

        # Set initial sizes
        splitter.setSizes([300, 500])

        main_layout.addWidget(splitter)

        # Buttons
        buttons_layout = QHBoxLayout()

        self.close_btn = QPushButton("Close")
        self.close_btn.clicked.connect(self.reject)

        self.run_btn = QPushButton("Run Inference")
        self.run_btn.clicked.connect(self.run_inference)

        buttons_layout.addWidget(self.close_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.run_btn)

        main_layout.addLayout(buttons_layout)

        # Populate the images list if we have images
        self.populate_images_list()

    def populate_images_list(self):
        """Populate the images list with the current image paths."""
        self.images_list.clear()
        for image_path in self.image_paths:
            self.images_list.addItem(os.path.basename(image_path))

    def browse_model(self):
        """Browse for a model file."""
        model_path, _ = QFileDialog.getOpenFileName(
            self, "Select Model File", os.path.expanduser("~"),
            "Model Files (*.pth);;All Files (*)"
        )

        if model_path:
            self.model_path = model_path
            self.model_path_edit.setText(os.path.basename(model_path))

    def add_images(self):
        """Add images to the list."""
        image_paths, _ = QFileDialog.getOpenFileNames(
            self, "Select Images", os.path.expanduser("~"),
            "Image Files (*.jpg *.jpeg *.png);;All Files (*)"
        )

        if image_paths:
            # Add to the list
            for image_path in image_paths:
                if image_path not in self.image_paths:
                    self.image_paths.append(image_path)

            # Update the UI
            self.populate_images_list()

    def remove_selected_images(self):
        """Remove selected images from the list."""
        selected_items = self.images_list.selectedItems()
        if not selected_items:
            return

        # Get the selected image names
        selected_names = [item.text() for item in selected_items]

        # Remove from the list
        self.image_paths = [path for path in self.image_paths if os.path.basename(path) not in selected_names]

        # Update the UI
        self.populate_images_list()

    def clear_images(self):
        """Clear all images from the list."""
        self.image_paths = []
        self.images_list.clear()

    def update_num_classes(self, value):
        """Update the number of classes."""
        self.num_classes = value

    def show_class_mapping_dialog(self):
        """Show a dialog to map model classes to application classes."""
        # Create a simple dialog for class mapping
        from PySide6.QtWidgets import QDialog, QVBoxLayout, QFormLayout, QComboBox, QPushButton, QLabel

        dialog = QDialog(self)
        dialog.setWindowTitle("Class Mapping")
        dialog.setMinimumWidth(400)

        layout = QVBoxLayout(dialog)
        form_layout = QFormLayout()

        # Create a mapping from model class indices to application class names
        self.class_mapping = {}

        # Get the available class names from the application
        available_classes = list(self.class_names.items())

        # Create a dropdown for each model class
        for i in range(1, self.num_classes + 1):  # 1-indexed for user display
            combo = QComboBox()

            # Add available classes
            for class_id, class_name in available_classes:
                combo.addItem(f"{class_name} (ID: {class_id})", class_id)

            # Set default selection if possible
            if i <= len(available_classes):
                combo.setCurrentIndex(i - 1)
                self.class_mapping[i] = available_classes[i - 1][0]

            # Connect signal
            combo.currentIndexChanged.connect(lambda idx, class_idx=i, cb=combo:
                                             self.update_class_mapping(class_idx, cb.currentData()))

            form_layout.addRow(f"Model Class {i}:", combo)

        layout.addLayout(form_layout)

        # Add OK button
        ok_button = QPushButton("OK")
        ok_button.clicked.connect(dialog.accept)
        layout.addWidget(ok_button)

        # Show the dialog
        dialog.exec()

    def update_class_mapping(self, model_class_idx, app_class_id):
        """Update the class mapping."""
        self.class_mapping[model_class_idx] = app_class_id

    def run_inference(self):
        """Run inference on the selected images."""
        # Validate inputs
        if not self.model_path:
            QMessageBox.warning(self, "Warning", "Please select a model file")
            return

        if not self.image_paths:
            QMessageBox.warning(self, "Warning", "Please add at least one image")
            return

        # Get inference parameters
        model_type = self.model_type_combo.currentText()
        threshold = self.threshold_spin.value()
        use_custom_classes = not self.use_original_classes_cb.isChecked()

        # Create and start the inference worker
        self.inference_worker = InferenceWorker(
            self.model_path,
            self.image_paths,
            threshold,
            model_type,
            self.num_classes,
            class_names=self.class_names,
            use_custom_classes=use_custom_classes
        )
        self.inference_worker.progress_updated.connect(self.update_progress)
        self.inference_worker.inference_completed.connect(self.on_inference_completed)

        # Update UI
        self.run_btn.setEnabled(False)
        self.close_btn.setText("Cancel")
        self.close_btn.clicked.disconnect()
        self.close_btn.clicked.connect(self.cancel_inference)

        self.progress_bar.setValue(0)
        self.status_label.setText("Running inference...")

        # Clear results
        self.results_list.clear()
        self.preview_view.set_pixmap(None)
        self.preview_instructions.setText("No preview available")
        self.inference_results = {}

        # Start inference
        self.inference_worker.start()

    def cancel_inference(self):
        """Cancel the inference process."""
        if self.inference_worker and self.inference_worker.isRunning():
            self.inference_worker.stop()
            self.inference_worker.wait()

        # Update UI
        self.run_btn.setEnabled(True)
        self.close_btn.setText("Close")
        self.close_btn.clicked.disconnect()
        self.close_btn.clicked.connect(self.reject)

        self.status_label.setText("Inference cancelled")

    def update_progress(self, current, total):
        """Update the progress bar."""
        progress = int(current / total * 100)
        self.progress_bar.setValue(progress)
        self.status_label.setText(f"Processing image {current} of {total}...")

    def on_inference_completed(self, success, message, results):
        """Handle inference completion."""
        # Update UI
        self.run_btn.setEnabled(True)
        self.close_btn.setText("Close")
        self.close_btn.clicked.disconnect()
        self.close_btn.clicked.connect(self.accept)

        if success and results:
            self.status_label.setText(message)

            # Make sure all visualization arrays are C-contiguous
            for image_path in results:
                if "visualization" in results[image_path]:
                    results[image_path]["visualization"] = np.ascontiguousarray(results[image_path]["visualization"])

            self.inference_results = results

            # Populate results list
            self.results_list.clear()
            for image_path in results:
                item_text = f"{os.path.basename(image_path)} ({len(results[image_path]['annotations'])} detections)"
                self.results_list.addItem(item_text)

            # Select the first result
            if self.results_list.count() > 0:
                self.results_list.setCurrentRow(0)

            # Emit signals based on user options
            annotations = {}
            preview_results = {}

            for image_path, result in results.items():
                # Process annotations if requested
                if self.add_annotations_cb.isChecked():
                    annotations[image_path] = []

                    for annotation in result["annotations"]:
                        # If using original class names, map the model class to application class
                        if self.use_original_classes_cb.isChecked() and hasattr(self, 'class_mapping'):
                            # Get the model class index (0-indexed in results, 1-indexed in mapping)
                            model_class_idx = annotation['class_id']

                            # Map to application class if available
                            if model_class_idx in self.class_mapping:
                                annotation['class_id'] = self.class_mapping[model_class_idx]

                        # Add to annotations
                        annotations[image_path].append(annotation)

                # Process preview results if requested
                if self.show_in_preview_cb.isChecked():
                    preview_results[image_path] = result["visualization"]

            # Emit signals
            if self.add_annotations_cb.isChecked() and annotations:
                self.inference_complete.emit(annotations)

            if self.show_in_preview_cb.isChecked() and preview_results:
                self.preview_results_ready.emit(preview_results)
        else:
            self.status_label.setText(message)
            QMessageBox.warning(self, "Warning", message)

    def on_result_selected(self):
        """Handle result selection."""
        selected_items = self.results_list.selectedItems()
        if not selected_items:
            # Clear the preview
            self.preview_view.set_pixmap(None)
            self.preview_instructions.setText("No preview available")
            return

        # Get the selected image path
        selected_text = selected_items[0].text()
        selected_name = selected_text.split(" (")[0]

        # Find the image path
        selected_path = None
        for image_path in self.inference_results:
            if os.path.basename(image_path) == selected_name:
                selected_path = image_path
                break

        if not selected_path:
            # Clear the preview
            self.preview_view.set_pixmap(None)
            self.preview_instructions.setText("Image not found")
            return

        # Get the result
        result = self.inference_results[selected_path]

        # Display the visualization
        visualization = result["visualization"]

        # Convert to QPixmap
        # Make sure the array is C-contiguous
        visualization_contiguous = np.ascontiguousarray(visualization)
        height, width = visualization_contiguous.shape[:2]
        bytes_per_line = 3 * width
        q_img = QImage(visualization_contiguous.data, width, height, bytes_per_line, QImage.Format_RGB888)
        pixmap = QPixmap.fromImage(q_img)

        # Set the pixmap in the zoomable view
        self.preview_view.set_pixmap(pixmap)

        # Update instructions
        self.preview_instructions.setText("Use mouse wheel to zoom, drag to pan")

    def resizeEvent(self, event):
        """Handle resize event to update the preview."""
        super().resizeEvent(event)

        # Update preview if we have a selected result
        selected_items = self.results_list.selectedItems()
        if selected_items:
            self.on_result_selected()

    def closeEvent(self, event):
        """Handle close event."""
        # Stop any running inference
        if self.inference_worker and self.inference_worker.isRunning():
            self.inference_worker.stop()
            self.inference_worker.wait()

        event.accept()
