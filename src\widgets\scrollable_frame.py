# src/widgets/scrollable_frame.py
from PySide6.QtWidgets import QFrame, QScrollArea, QVBoxLayout

class ScrollableFrame(QFrame):
    """Custom widget for a scrollable frame."""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFrameShape(QFrame.StyledPanel)
        self.setFrameShadow(QFrame.Raised)
        self.setLineWidth(1)

        self.scroll_area = QScrollArea(self)
        self.scroll_area.setWidgetResizable(True)

        self.content_frame = QFrame(self.scroll_area)
        self.scroll_area.setWidget(self.content_frame)

        layout = QVBoxLayout(self)
        layout.addWidget(self.scroll_area)
        self.setLayout(layout)

    def get_content_frame(self):
        """Returns the content frame."""
        return self.content_frame