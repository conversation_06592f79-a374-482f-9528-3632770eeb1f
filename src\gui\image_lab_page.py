# src/gui/image_lab_page.py

import logging
from PySide6.QtCore import Signal, Slot
from PySide6.QtWidgets import QWidget

from src.gui.ui.image_lab_page_ui import ImageLabPageUI
from src.gui.handlers.image_lab_handlers import ImageLabHandlers

logger = logging.getLogger(__name__)

class ImageLabPage(QWidget):
    """Independent Image Lab page for image enhancement and processing."""

    # Define signals
    image_processed = Signal(object)  # Signal emitted when an image is processed

    def __init__(self, parent=None, project=None):
        """Initialize the Image Lab page.

        Args:
            parent: Parent widget
            project: Current project (optional)
        """
        super().__init__(parent)

        # Create UI
        self.ui = ImageLabPageUI(self)

        # Create handlers
        self.handlers = ImageLabHandlers(self.ui, project)

        # Set up layout
        self.setup_layout()

        # Set up view synchronization
        self.setup_view_synchronization()

    def setup_layout(self):
        """Set up the page layout."""
        from PySide6.QtWidgets import QVBoxLayout

        # Create main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # Add UI widget to layout
        layout.addWidget(self.ui)

    def set_project(self, project):
        """Set the current project.

        Args:
            project: The project to set
        """
        self.handlers.set_project(project)

    @Slot(list, list)
    def load_images(self, image_paths, image_infos=None):
        """Load images into the Image Lab page.

        Args:
            image_paths: List of image paths to load
            image_infos: List of image info objects (optional)
        """
        self.handlers.load_images(image_paths, image_infos)

    def clear(self):
        """Clear all images and reset the page state."""
        self.handlers.clear()

    def save_state(self):
        """Save the current state of the page.

        This method is called when switching away from the page.
        """
        # Currently, we don't need to save any state as everything is stored in memory
        # and the handlers maintain their own state.
        logger.info("Image Lab page state saved")

    def load_state(self):
        """Load the saved state of the page.

        This method is called when switching to the page.
        """
        # Currently, we don't need to load any state as everything is stored in memory
        # and the handlers maintain their own state.
        logger.info("Image Lab page state loaded")

    def setup_view_synchronization(self):
        """Set up synchronization between the original and processed image views."""
        # Connect zoom signals
        self.ui.original_view.zoom_changed.connect(self.sync_zoom)
        self.ui.processed_view.zoom_changed.connect(self.sync_zoom)
        self.ui.full_processed_view.zoom_changed.connect(self.sync_zoom_full_view)

        # Connect scroll signals
        self.ui.original_view.scroll_changed.connect(self.sync_scroll)
        self.ui.processed_view.scroll_changed.connect(self.sync_scroll)

    def sync_zoom(self, scale):
        """Synchronize zoom level between original and processed views.

        Args:
            scale: The new zoom scale
        """
        # Get the sender (the view that triggered the signal)
        sender = self.sender()

        # Update the other view's zoom level
        if sender == self.ui.original_view:
            # Original view changed, update processed view
            if self.ui.processed_view.zoom_scale != scale:
                self.ui.processed_view.setZoomScale(scale)
        elif sender == self.ui.processed_view:
            # Processed view changed, update original view
            if self.ui.original_view.zoom_scale != scale:
                self.ui.original_view.setZoomScale(scale)

        logger.debug(f"Synchronized zoom level to {scale:.2f}")

    def sync_zoom_full_view(self, scale):
        """Handle zoom changes in the full processed view.

        Args:
            scale: The new zoom scale
        """
        # This is separate from sync_zoom because we don't want to
        # synchronize the full view with the side-by-side views
        logger.debug(f"Full view zoom level changed to {scale:.2f}")

    def sync_scroll(self, h_value, v_value):
        """Synchronize scroll position between original and processed views.

        Args:
            h_value: Horizontal scroll position
            v_value: Vertical scroll position
        """
        # Get the sender (the view that triggered the signal)
        sender = self.sender()

        # Update the other view's scroll position
        if sender == self.ui.original_view:
            # Original view changed, update processed view
            self.ui.processed_view.horizontalScrollBar().setValue(h_value)
            self.ui.processed_view.verticalScrollBar().setValue(v_value)
        elif sender == self.ui.processed_view:
            # Processed view changed, update original view
            self.ui.original_view.horizontalScrollBar().setValue(h_value)
            self.ui.original_view.verticalScrollBar().setValue(v_value)

        logger.debug(f"Synchronized scroll position to ({h_value}, {v_value})")
