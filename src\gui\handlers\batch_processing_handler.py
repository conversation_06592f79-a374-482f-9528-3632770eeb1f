# src/gui/handlers/batch_processing_handler.py

import os
import logging
import json
import numpy as np
from typing import List, Dict, Any, Optional
from PySide6.QtCore import QObject, Signal, QThread, QTimer
from PySide6.QtWidgets import QWidget, QMessageBox, QFileDialog, QListWidgetItem, QDialog
from PySide6.QtGui import QPixmap

from src.core.project_data import ImageInfo
from src.utils.image_utils import resize_image, convert_cvimage_to_qpixmap
import cv2

# Import the individual scale configuration dialog
from ..individual_scale_config_dialog import IndividualScaleConfigDialog

logger = logging.getLogger(__name__)

class BatchProcessingWorker(QThread):
    """Worker thread for batch processing operations."""
    
    # Signals
    progress_updated = Signal(int, int)  # current, total
    current_image_updated = Signal(str)  # current image name
    patch_progress_updated = Signal(int, int, str)  # current_patch, total_patches, patch_info
    image_completed = Signal(str, dict)  # image_path, results
    processing_finished = Signal()
    error_occurred = Signal(str)  # error message
    log_message = Signal(str)  # log message
    
    def __init__(self, task_type, image_paths, image_infos, parameters, project=None):
        super().__init__()
        self.task_type = task_type
        self.image_paths = image_paths
        self.image_infos = image_infos
        self.parameters = parameters
        self.project = project
        self.should_stop = False
        
    def stop(self):
        """Stop the processing."""
        self.should_stop = True
        
    def run(self):
        """Run the batch processing."""
        try:
            total_images = len(self.image_paths)
            self.log_message.emit(f"Starting batch processing of {total_images} images...")
            
            for i, (image_path, image_info) in enumerate(zip(self.image_paths, self.image_infos)):
                if self.should_stop:
                    self.log_message.emit("Processing stopped by user.")
                    break
                    
                # Update progress
                self.progress_updated.emit(i, total_images)
                self.current_image_updated.emit(os.path.basename(image_path))
                self.log_message.emit(f"Processing image {i+1}/{total_images}: {os.path.basename(image_path)}")
                
                # Process the image based on task type
                try:
                    if self.task_type == "Grain Size Analysis":
                        results = self._process_grain_size_analysis(image_path, image_info)
                    elif self.task_type == "Trainable Segmentation":
                        results = self._process_trainable_segmentation(image_path, image_info)
                    elif self.task_type == "Advanced Segmentation":
                        results = self._process_advanced_segmentation(image_path, image_info)
                    else:
                        raise ValueError(f"Unknown task type: {self.task_type}")
                        
                    # Emit completion signal
                    self.image_completed.emit(image_path, results)
                    self.log_message.emit(f"Completed processing: {os.path.basename(image_path)}")
                    
                except Exception as e:
                    error_msg = f"Error processing {os.path.basename(image_path)}: {str(e)}"
                    self.log_message.emit(error_msg)
                    self.error_occurred.emit(error_msg)
                    
                    # Create error results for display
                    error_results = {
                        'error': str(e),
                        'scale_factor': self.parameters.get('scale_factor', 1.0),
                        'segmentation_method': self.parameters.get('segmentation_method', 'Unknown')
                    }
                    self.image_completed.emit(image_path, error_results)
                    
            # Final progress update
            if not self.should_stop:
                self.progress_updated.emit(total_images, total_images)
                self.log_message.emit("Batch processing completed successfully!")
            
            self.processing_finished.emit()
            
        except Exception as e:
            error_msg = f"Fatal error during batch processing: {str(e)}"
            self.log_message.emit(error_msg)
            self.error_occurred.emit(error_msg)
            
    def _process_grain_size_analysis(self, image_path, image_info):
        """Process grain size analysis for a single image."""
        try:
            # Import required modules
            from PIL import Image
            import torch
            from ultralytics import YOLO
            from src.utils.mobilesam_wrapper import is_available as is_mobilesam_available
            from src.utils.mobilesam_wrapper import get_sam_model_registry
            from src.grainsight_components.core.segmentation import segment_image
            from src.grainsight_components.core.analysis import calculate_parameters
            from src.grainsight_components.core.image_utils import create_segmented_visualization
            
            # Load image
            pil_image = Image.open(image_path).convert('RGB')
            if pil_image is None:
                raise ValueError(f"Could not load image: {image_path}")
                
            # Get scale factor
            if self.parameters.get('uniform_scale', True):
                scale_factor = self.parameters.get('uniform_scale_factor', 1.0)
            else:
                # Use individual scale factor if available
                individual_scale_factors = self.parameters.get('individual_scale_factors', {})
                scale_factor = individual_scale_factors.get(image_path, 1.0)
                
            # Load model based on segmentation method
            segmentation_method = self.parameters.get('segmentation_method', 'FastSAM')
            device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            
            if segmentation_method == 'FastSAM':
                # Load FastSAM model
                component_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                model_dir = os.path.join(component_root, 'grainsight_components', 'models')
                model_path = os.path.join(model_dir, "FastSAM-x.pt")
                
                if not os.path.exists(model_path):
                    raise ValueError(f"FastSAM model not found: {model_path}")
                    
                model = YOLO(model_path)
                device = torch.device("cpu")  # FastSAM works better on CPU
                
            elif segmentation_method == 'MobileSAM':
                # Load MobileSAM model
                if not is_mobilesam_available():
                    raise ValueError("MobileSAM library not available")
                    
                component_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                weights_dir = os.path.join(component_root, 'grainsight_components', 'weights')
                model_path = os.path.join(weights_dir, "mobile_sam.pt")
                
                if not os.path.exists(model_path):
                    raise ValueError(f"MobileSAM weights not found: {model_path}")
                    
                sam_model_registry = get_sam_model_registry()
                model = sam_model_registry["vit_t"](checkpoint=model_path)
                # Move MobileSAM model to GPU if available
                model = model.to(device)
                self.log_message.emit(f"MobileSAM model loaded on device: {device}")
                
            else:
                raise ValueError(f"Unknown segmentation method: {segmentation_method}")
                
            # Prepare parameters for segmentation
            seg_params = {
                'model_type': segmentation_method.lower(),
                'points_per_side': self.parameters.get('points_per_side', 32),
                'pred_iou_thresh': self.parameters.get('pred_iou_thresh', 0.88),
                'stability_score_thresh': self.parameters.get('stability_score_thresh', 0.95),
                'box_nms_thresh': self.parameters.get('box_nms_thresh', 0.3),
                'crop_n_layers': self.parameters.get('crop_n_layers', 0),
                'crop_n_points_downscale_factor': self.parameters.get('crop_n_points_downscale_factor', 1),
                'min_mask_region_area': self.parameters.get('min_mask_region_area', 0),
                'artifact_sensitivity': 0.5,  # Default artifact sensitivity
                'duplicate_sensitivity': 0.7,  # Default duplicate sensitivity
                'use_intelligent_patch_merge': True
            }
            
            # Add FastSAM-specific parameters
            if segmentation_method == 'FastSAM':
                seg_params.update({
                    'input_size': self.parameters.get('fastsam_input_size', 1024),
                    'iou': self.parameters.get('pred_iou_thresh', 0.88),  # Use pred_iou_thresh for FastSAM iou
                    'conf': self.parameters.get('fastsam_conf_thresh', 0.4),
                    'max_det': self.parameters.get('fastsam_max_det', 100)
                })
            
            # Add intelligent artifact detection parameters with MobileSAM-specific defaults
            if self.parameters.get('enable_artifact_detection', True):
                # Use more aggressive artifact detection for MobileSAM to remove border artifacts
                if segmentation_method.lower() == 'mobilesam':
                    # Higher default sensitivity for MobileSAM to combat border detection issues
                    default_artifact_sensitivity = 8  # 0.8 instead of 0.5
                    default_duplicate_sensitivity = 8  # 0.8 instead of 0.7
                    self.log_message.emit("Using enhanced artifact detection for MobileSAM to remove border artifacts")
                else:
                    default_artifact_sensitivity = 5  # 0.5 for other models
                    default_duplicate_sensitivity = 7  # 0.7 for other models
                    
                seg_params['artifact_sensitivity'] = self.parameters.get('artifact_sensitivity', default_artifact_sensitivity) / 10.0
                seg_params['duplicate_sensitivity'] = self.parameters.get('duplicate_sensitivity', default_duplicate_sensitivity) / 10.0
            else:
                seg_params['artifact_sensitivity'] = 0  # Disable artifact detection
                seg_params['duplicate_sensitivity'] = 0  # Disable duplicate detection
                
            # Add patch processing parameters
            patch_params = {
                'enable_patch_processing': self.parameters.get('enable_patch_processing', True),
                'patch_size': self.parameters.get('patch_size', 1024),
                'patch_overlap': self.parameters.get('patch_overlap', 128)
            }
            seg_params.update(patch_params)
            
            # Perform segmentation with patch processing if enabled
            self.log_message.emit(f"Running {segmentation_method} segmentation...")
            
            if seg_params.get('enable_patch_processing', True):
                try:
                    # Use the existing PatchProcessingWorker from grainsight_components
                    from src.grainsight_components.gui.workers import PatchProcessingWorker
                    
                    # Set up patch configuration
                    patch_size = seg_params.get('patch_size', 1024)
                    overlap = seg_params.get('patch_overlap', 128)
                    
                    # Calculate patch grid based on image size and patch size
                    image_width, image_height = pil_image.size
                    
                    # Calculate number of patches needed
                    patches_x = max(1, (image_width + patch_size - 1) // patch_size)
                    patches_y = max(1, (image_height + patch_size - 1) // patch_size)
                    
                    # Convert overlap from pixels to decimal (0.0 to 1.0) for patch config
                    overlap_decimal = overlap / patch_size
                    
                    patch_config = {
                        'rows': patches_y,
                        'cols': patches_x,
                        'overlap': overlap_decimal
                    }
                    
                    self.log_message.emit(f"Processing with patches: {patches_x}x{patches_y} grid (size: {patch_size}, overlap: {overlap})")
                    
                    # Set artifact detection parameters based on segmentation method
                    if segmentation_method.lower() == 'mobilesam':
                        # Use more aggressive settings for MobileSAM
                        seg_params['artifact_sensitivity'] = seg_params.get('artifact_sensitivity', 0.8)
                        seg_params['duplicate_sensitivity'] = seg_params.get('duplicate_sensitivity', 0.8)
                    else:
                        # Use default settings for other methods
                        seg_params['artifact_sensitivity'] = seg_params.get('artifact_sensitivity', 0.5)
                        seg_params['duplicate_sensitivity'] = seg_params.get('duplicate_sensitivity', 0.7)
                    
                    # Enable intelligent patch merging
                    seg_params['use_intelligent_patch_merge'] = True
                    
                    # Create the patch processing worker
                    patch_worker = PatchProcessingWorker(
                        pil_image=pil_image,
                        model=model,
                        device=device,
                        scale_factor=scale_factor,
                        seg_params=seg_params,
                        patch_config=patch_config
                    )
                    
                    # Store results from worker
                    worker_results = {'df': None, 'annotations': None, 'vis_image': None, 'error': None}
                    
                    def on_worker_finished(df, annotations, vis_image):
                        worker_results['df'] = df
                        worker_results['annotations'] = annotations
                        worker_results['vis_image'] = vis_image
                    
                    def on_worker_error(error_msg):
                        worker_results['error'] = error_msg
                    
                    def on_worker_progress(progress):
                        # Forward progress to patch progress signal
                        total_patches = patches_x * patches_y
                        current_patch = int((progress / 100.0) * total_patches)
                        self.patch_progress_updated.emit(current_patch, total_patches, f"Processing patches ({progress}%)")
                    
                    # Connect signals
                    patch_worker.finished.connect(on_worker_finished)
                    patch_worker.error.connect(on_worker_error)
                    patch_worker.progress.connect(on_worker_progress)
                    
                    # Run the worker synchronously
                    patch_worker.run()
                    
                    # Check results
                    if worker_results['error']:
                        raise ValueError(f"Patch processing failed: {worker_results['error']}")
                    
                    if worker_results['annotations'] is None:
                        raise ValueError("No valid annotations found in patch processing")
                    
                    annotations = worker_results['annotations']
                    df = worker_results['df']  # Use the DataFrame from patch worker
                    
                    raw_results = annotations  # For patch processing, raw_results = annotations
                    
                    self.log_message.emit(f"Patch processing complete: {annotations.shape[0]} objects found")
                    
                except ImportError as e:
                    self.log_message.emit(f"Patch processing not available, falling back to standard processing: {e}")
                    # Fall back to standard processing
                    raw_results, annotations = segment_image(pil_image, model, device, seg_params)
                    df = None  # Will be calculated later
                except Exception as e:
                    self.log_message.emit(f"Error in patch processing, falling back to standard processing: {e}")
                    # Fall back to standard processing
                    raw_results, annotations = segment_image(pil_image, model, device, seg_params)
                    df = None  # Will be calculated later
            else:
                # Use standard single-image processing
                raw_results, annotations = segment_image(pil_image, model, device, seg_params)
                
                # Apply artifact detection for non-patch processing if enabled
                if self.parameters.get('enable_artifact_detection', True) and annotations is not None and isinstance(annotations, torch.Tensor) and annotations.shape[0] > 0:
                    self.log_message.emit(f"Applying artifact detection for non-patch processing...")
                    annotations = self._apply_non_patch_artifact_detection(
                        annotations, 
                        pil_image.size,
                        artifact_sensitivity=seg_params.get('artifact_sensitivity', 0.5)
                    )
            
            if annotations is None or (isinstance(annotations, torch.Tensor) and annotations.shape[0] == 0):
                self.log_message.emit(f"No grains detected in {os.path.basename(image_path)}")
                return {
                    'segmentation_method': segmentation_method,
                    'scale_factor': scale_factor,
                    'num_grains': 0,
                    'error': 'No grains detected'
                }
                
            # Calculate parameters (if not already done by patch processing)
            if 'df' not in locals() or df is None:
                self.log_message.emit(f"Calculating grain parameters...")
                df, valid_mask = calculate_parameters(annotations, scale_factor)
            else:
                # Parameters already calculated by PatchProcessingWorker
                self.log_message.emit(f"Using parameters from patch processing...")
                # Create valid_mask from df indices
                if df is not None and not df.empty:
                    valid_indices = df.index.to_list()
                    valid_mask = torch.zeros(annotations.shape[0], dtype=torch.bool, device=annotations.device)
                    valid_mask[valid_indices] = True
                else:
                    valid_mask = torch.zeros(annotations.shape[0], dtype=torch.bool, device=annotations.device)
            
            if df is None or df.empty:
                self.log_message.emit(f"No valid grains found in {os.path.basename(image_path)}")
                return {
                    'segmentation_method': segmentation_method,
                    'scale_factor': scale_factor,
                    'num_grains': 0,
                    'error': 'No valid grains found'
                }
                
            # Create visualization
            self.log_message.emit(f"Creating visualization...")
            self.log_message.emit(f"Debug: annotations shape: {annotations.shape}, dtype: {annotations.dtype}")
            self.log_message.emit(f"Debug: valid_mask shape: {valid_mask.shape if hasattr(valid_mask, 'shape') else len(valid_mask)}, sum: {torch.sum(valid_mask).item() if isinstance(valid_mask, torch.Tensor) else sum(valid_mask)}")
            filtered_annotations = annotations[valid_mask]
            self.log_message.emit(f"Debug: filtered_annotations shape: {filtered_annotations.shape}, dtype: {filtered_annotations.dtype}")
            self.log_message.emit(f"Debug: filtered_annotations min/max: {filtered_annotations.min()}/{filtered_annotations.max()}")
            segmented_image_vis = create_segmented_visualization(pil_image, filtered_annotations)
            logger.info(f"[DEBUG] _process_grain_size_analysis: segmented_image_vis type: {type(segmented_image_vis)}, mode: {segmented_image_vis.mode if hasattr(segmented_image_vis, 'mode') else 'N/A'}, size: {segmented_image_vis.size if hasattr(segmented_image_vis, 'size') else 'N/A'}")
            logger.info(f"[DEBUG] _process_grain_size_analysis: filtered_annotations type: {type(filtered_annotations)}, shape/len: {filtered_annotations.shape if hasattr(filtered_annotations, 'shape') else len(filtered_annotations) if filtered_annotations is not None else 'None'}")

            # Calculate statistics
            grain_sizes = df['Area (µm²)'].values if 'Area (µm²)' in df.columns else df['Area (μm²)'].values if 'Area (μm²)' in df.columns else df['Area'].values
            statistics = {
                'min_size': float(grain_sizes.min()),
                'max_size': float(grain_sizes.max()),
                'median_size': float(np.median(grain_sizes)),
                'mean_size': float(grain_sizes.mean()),
                'std_size': float(grain_sizes.std()),
                'total_area': float(grain_sizes.sum()),
                'total_grains': len(grain_sizes)
            }
            
            # Prepare results
            results = {
                'segmentation_method': segmentation_method,
                'scale_factor': scale_factor,
                'num_grains': len(df),
                'mean_grain_size': statistics['mean_size'],
                'std_grain_size': statistics['std_size'],
                'grain_sizes': grain_sizes.tolist(),
                'statistics': statistics,
                'dataframe': df,
                'annotations': annotations[valid_mask] if isinstance(annotations, torch.Tensor) else annotations,
                'segmented_image_path': None
            }
            
            # Save results if requested
            if self.parameters.get('save_contours', True):
                # Use project's state directory instead of image directory
                if self.project and hasattr(self.project, 'temp_dir'):
                    output_dir = os.path.join(self.project.temp_dir, 'state', 'batch_results')
                else:
                    # Fallback to image directory if project not available
                    output_dir = os.path.join(os.path.dirname(image_path), 'batch_results')
                os.makedirs(output_dir, exist_ok=True)
                
                base_name = os.path.splitext(os.path.basename(image_path))[0]
                
                # Save segmented image without grain IDs
                segmented_path = os.path.join(output_dir, f"{base_name}_segmented.png")
                if segmented_image_vis:
                    segmented_image_vis.save(segmented_path)
                    results['segmented_image_path'] = segmented_path
                    logger.info(f"[DEBUG] _process_grain_size_analysis: Saved segmented_image_vis to {segmented_path}")
                else:
                    logger.warning("[DEBUG] _process_grain_size_analysis: segmented_image_vis is None, not saving.")
                    
                # Save CSV results
                csv_path = os.path.join(output_dir, f"{base_name}_results.csv")
                df.to_csv(csv_path, index=False)
                results['csv_path'] = csv_path
                
                # Save statistics JSON
                stats_path = os.path.join(output_dir, f"{base_name}_statistics.json")
                with open(stats_path, 'w') as f:
                    json.dump(statistics, f, indent=2)
                results['statistics_path'] = stats_path
                
            self.log_message.emit(f"Completed analysis: {len(df)} grains found")
            return results
            
        except Exception as e:
            error_msg = f"Error processing {os.path.basename(image_path)}: {str(e)}"
            self.log_message.emit(error_msg)
            raise ValueError(error_msg)
    
    def _apply_non_patch_artifact_detection(self, annotations, image_size, artifact_sensitivity=0.5):
        """
        Apply enhanced artifact detection for non-patch processing with special handling for border artifacts.
        
        Args:
            annotations (torch.Tensor): Binary masks tensor (N, H, W)
            image_size (tuple): Image size (width, height)
            artifact_sensitivity (float): Sensitivity for artifact detection (0.0-1.0)
            
        Returns:
            torch.Tensor: Filtered annotations with artifacts removed
        """
        if annotations is None or annotations.shape[0] == 0:
            return annotations
            
        try:
            import cv2
            import numpy as np
            import torch
            
            filtered_masks = []
            image_width, image_height = image_size
            
            # Convert sensitivity to threshold (higher sensitivity = more aggressive filtering)
            area_threshold = int((1.0 - artifact_sensitivity) * 1000)  # Min area in pixels
            edge_distance_threshold = int(artifact_sensitivity * 100)  # Increased distance from edge for better border detection
            circularity_threshold = 0.2 + (artifact_sensitivity * 0.5)  # 0.2-0.7 range (more permissive for real grains)
            
            # Enhanced border detection parameters
            border_margin = max(10, int(min(image_width, image_height) * 0.02))  # 2% of smaller dimension
            large_object_threshold = image_width * image_height * 0.3  # Objects covering >30% of image are likely borders
            
            for i, mask in enumerate(annotations):
                # Convert to numpy for processing
                mask_np = mask.cpu().numpy().astype(np.uint8)
                
                # Find contours
                contours, _ = cv2.findContours(mask_np, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                if not contours:
                    continue
                    
                # Get the largest contour (main object)
                main_contour = max(contours, key=cv2.contourArea)
                area = cv2.contourArea(main_contour)
                
                # Filter 1: Minimum area
                if area < area_threshold:
                    continue
                    
                # Filter 1.5: Large object filter (likely entire image borders)
                if area > large_object_threshold:
                    continue
                    
                # Filter 2: Enhanced edge artifacts detection
                moments = cv2.moments(main_contour)
                if moments['m00'] > 0:
                    cx = int(moments['m10'] / moments['m00'])
                    cy = int(moments['m01'] / moments['m00'])
                    
                    # Check if centroid is too close to edges
                    if (cx < edge_distance_threshold or 
                        cx > image_width - edge_distance_threshold or
                        cy < edge_distance_threshold or 
                        cy > image_height - edge_distance_threshold):
                        continue
                        
                # Filter 2.5: Check if object touches image borders (enhanced border detection)
                x, y, w, h = cv2.boundingRect(main_contour)
                touches_border = (x <= border_margin or 
                                y <= border_margin or 
                                x + w >= image_width - border_margin or 
                                y + h >= image_height - border_margin)
                
                # If object touches border and is large or rectangular, likely an artifact
                if touches_border:
                    # Check if it's a large rectangular object (common border artifact)
                    rect_area = w * h
                    fill_ratio = area / rect_area if rect_area > 0 else 0
                    aspect_ratio = max(w, h) / min(w, h) if min(w, h) > 0 else float('inf')
                    
                    # Filter out rectangular border objects
                    if (fill_ratio > 0.7 and aspect_ratio > 3.0) or area > image_width * image_height * 0.1:
                        continue
                
                # Filter 3: Shape-based filtering (circularity)
                perimeter = cv2.arcLength(main_contour, True)
                if perimeter > 0:
                    circularity = 4 * np.pi * area / (perimeter * perimeter)
                    if circularity < circularity_threshold:
                        continue
                
                # Filter 4: Aspect ratio check
                rect = cv2.minAreaRect(main_contour)
                width, height = rect[1]
                if width > 0 and height > 0:
                    aspect_ratio = max(width, height) / min(width, height)
                    max_aspect_ratio = 5.0 + (artifact_sensitivity * 10.0)  # 5-15 range
                    if aspect_ratio > max_aspect_ratio:
                        continue
                
                # If mask passes all filters, keep it
                filtered_masks.append(mask)
            
            if filtered_masks:
                filtered_annotations = torch.stack(filtered_masks)
                self.log_message.emit(f"Artifact detection: {len(annotations)} -> {len(filtered_annotations)} masks")
                return filtered_annotations
            else:
                self.log_message.emit("Artifact detection: All masks filtered out")
                return torch.empty(0, *annotations.shape[1:], device=annotations.device, dtype=annotations.dtype)
                
        except Exception as e:
            self.log_message.emit(f"Error in artifact detection: {e}")
            return annotations  # Return original if error occurs
        
    def _process_trainable_segmentation(self, image_path, image_info):
        """Process trainable segmentation for a single image."""
        try:
            # Import required modules
            from PIL import Image
            import numpy as np
            import os
            import pickle
            from skimage import feature
            from src.gui.handlers.multi_image_trainable_handler import MultiImageTrainableHandler
            
            # Get model path from parameters
            model_name = self.parameters.get('model', '')
            model_path = self.parameters.get('model_path')
            
            if not model_name or model_name == "Select a trained model...":
                raise ValueError("No trained model selected")
            
            if not model_path or not os.path.exists(model_path):
                raise ValueError(f"Trained model '{model_name}' not found or path is invalid: {model_path}")
            
            # Load the trained classifier from the project
            if not self.project:
                raise ValueError("No project loaded")
            
            # Get project directory for output
            if hasattr(self.project, 'project_file_path'):
                project_dir = os.path.dirname(self.project.project_file_path)
            elif hasattr(self.project, 'project_file'):
                project_dir = os.path.dirname(self.project.project_file)
            else:
                raise ValueError("Unable to determine project directory")
            
            # Load the classifier and associated data
            with open(model_path, 'rb') as f:
                model_data = pickle.load(f)
            
            # Extract classifier and parameters
            if isinstance(model_data, dict):
                classifier = model_data.get('classifier')
                feature_params = model_data.get('feature_params', {
                    'intensity': True,
                    'edges': True, 
                    'texture': True,
                    'sigma_min': 1,
                    'sigma_max': 16,
                    'channel_axis': -1
                })
                label_mapping = model_data.get('label_mapping', {})
                mask_colors = model_data.get('mask_colors')
            else:
                # Legacy format - just the classifier
                classifier = model_data
                feature_params = {
                    'intensity': True,
                    'edges': True,
                    'texture': True, 
                    'sigma_min': 1,
                    'sigma_max': 16,
                    'channel_axis': -1
                }
                label_mapping = {}
                mask_colors = None
            
            if classifier is None:
                raise ValueError("Invalid model file: no classifier found")
            
            # Load and process the image
            image = Image.open(image_path)
            if image.mode != 'RGB':
                image = image.convert('RGB')
            image_array = np.array(image)
            
            self.log_message.emit(f"Processing {os.path.basename(image_path)} with model {model_name}")
            
            # Extract features using the same method as training
            features_func = lambda img: feature.multiscale_basic_features(img, **feature_params)
            image_features = features_func(image_array)
            
            # Reshape features for prediction
            features_reshaped = image_features.reshape(-1, image_features.shape[-1])
            
            # Apply the classifier
            predictions = classifier.predict(features_reshaped)
            
            # Reshape predictions back to image shape
            segmentation_result = predictions.reshape(image_array.shape[:2])
            
            # Apply label mapping if available
            if label_mapping:
                # Create reverse mapping
                reverse_mapping = {v: k for k, v in label_mapping.items()}
                mapped_result = np.zeros_like(segmentation_result)
                for pred_val, orig_val in reverse_mapping.items():
                    mapped_result[segmentation_result == pred_val] = orig_val + 1  # Make 1-indexed
                segmentation_result = mapped_result
            else:
                # Make 1-indexed if no mapping
                segmentation_result = segmentation_result + 1
            
            # Calculate segmentation statistics
            unique_labels = np.unique(segmentation_result)
            unique_labels = unique_labels[unique_labels > 0]  # Exclude background
            
            segment_stats = []
            total_pixels = segmentation_result.size
            
            for label in unique_labels:
                mask = segmentation_result == label
                pixel_count = np.sum(mask)
                percentage = (pixel_count / total_pixels) * 100
                
                segment_stats.append({
                    'label': int(label),
                    'pixel_count': int(pixel_count),
                    'percentage': float(percentage)
                })
            
            # Create output directory for segmentation results
            output_dir = os.path.join(project_dir, 'batch_segmentation_results')
            os.makedirs(output_dir, exist_ok=True)
            
            # Save segmentation result as image
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            result_image_path = os.path.join(output_dir, f"{base_name}_segmented.png")
            
            # Create colored segmentation visualization
            if mask_colors is not None and len(mask_colors) > 0:
                # Use stored colors
                colored_result = np.zeros((*segmentation_result.shape, 3), dtype=np.uint8)
                for label in unique_labels:
                    if label - 1 < len(mask_colors):
                        color = mask_colors[label - 1]
                        colored_result[segmentation_result == label] = color[:3]
            else:
                # Generate colors automatically
                import matplotlib.pyplot as plt
                cmap = plt.cm.get_cmap('tab10')
                colored_result = np.zeros((*segmentation_result.shape, 3), dtype=np.uint8)
                for i, label in enumerate(unique_labels):
                    color = cmap(i % 10)[:3]
                    colored_result[segmentation_result == label] = [int(c * 255) for c in color]
            
            # Save the colored result
            result_image = Image.fromarray(colored_result)
            result_image.save(result_image_path)
            
            # Save raw segmentation data
            segmentation_data_path = os.path.join(output_dir, f"{base_name}_segmentation.npy")
            np.save(segmentation_data_path, segmentation_result)
            
            self.log_message.emit(f"Completed segmentation for {os.path.basename(image_path)}")
            
            return {
                'model_used': model_name,
                'segmentation_result': segmentation_result,
                'segment_stats': segment_stats,
                'unique_labels': [int(l) for l in unique_labels],
                'total_segments': len(unique_labels),
                'segmented_image_path': result_image_path,
                'segmentation_data_path': segmentation_data_path,
                'feature_params': feature_params,
                'label_mapping': label_mapping
            }
            
        except Exception as e:
            error_msg = f"Error processing {os.path.basename(image_path)}: {str(e)}"
            self.log_message.emit(error_msg)
            raise ValueError(error_msg)
        
    def _process_advanced_segmentation(self, image_path, image_info):
        """Process advanced segmentation for a single image."""
        # Placeholder implementation
        results = {
            'model_used': self.parameters.get('advanced_model', 'SAM'),
            'prompt_type': self.parameters.get('prompt_type', 'Automatic'),
            'masks': [],  # Placeholder for mask results
            'segmented_image_path': None
        }
        return results

class BatchProcessingHandler:
    """Handles logic for the Batch Processing page."""
    
    def __init__(self):
        self.current_project = None
        self.selected_images = []
        self.selected_image_infos = []
        self.processing_worker = None
        self.processing_results = {}
        
        # Individual scale factors storage
        self.individual_scale_factors = {}  # {image_path: scale_factor}
        
        # Initialize visualization settings
        self._initialize_visualization_settings()
        
    def setup_batch_processing_connections(self):
        """Set up signal connections for batch processing."""
        # Task selection
        if hasattr(self, 'task_combo'):
            self.task_combo.currentTextChanged.connect(self._on_task_changed)
            
        # Control buttons
        if hasattr(self, 'start_batch_button'):
            self.start_batch_button.clicked.connect(self._start_batch_processing)
            
        if hasattr(self, 'stop_batch_button'):
            self.stop_batch_button.clicked.connect(self._stop_batch_processing)
            
        # Grid controls
        if hasattr(self, 'grid_size_slider'):
            self.grid_size_slider.valueChanged.connect(self._update_grid_size)
            
        # Export buttons
        if hasattr(self, 'export_results_button'):
            self.export_results_button.clicked.connect(self._export_results)
            
        if hasattr(self, 'generate_report_button'):
            self.generate_report_button.clicked.connect(self._generate_report)
            
        # Log controls
        if hasattr(self, 'clear_log_button'):
            self.clear_log_button.clicked.connect(self._clear_log)
            
        if hasattr(self, 'save_log_button'):
            self.save_log_button.clicked.connect(self._save_log)
            
        # Scale factor controls
        if hasattr(self, 'uniform_scale_checkbox'):
            self.uniform_scale_checkbox.toggled.connect(self._on_uniform_scale_toggled)
            
        # Individual scale factor configuration buttons
        if hasattr(self, 'configure_individual_scale_button'):
            self.configure_individual_scale_button.clicked.connect(self._configure_individual_scale_factors)
            
        if hasattr(self, 'load_scale_config_button'):
            self.load_scale_config_button.clicked.connect(self._load_scale_configuration)
            
        # Load model button for trainable segmentation
        if hasattr(self, 'load_model_button'):
            self.load_model_button.clicked.connect(self._load_external_model)
        
        # Set up visualization settings connections
        self.setup_visualization_connections()
            
    def set_current_project(self, project):
        """Set the current project and update UI accordingly."""
        self.current_project = project
        
        # If trainable segmentation is currently selected, populate models
        if (hasattr(self, 'task_combo') and 
            self.task_combo.currentText() == "Trainable Segmentation"):
            self._populate_trained_models()
            
        logger.info(f"Set current project for batch processing: {project.name if project else 'None'}")
        
    def load_images_for_batch_processing(self, image_paths, image_infos):
        """Load images for batch processing."""
        self.selected_images = image_paths
        self.selected_image_infos = image_infos
        
        # Update UI
        if hasattr(self, 'selected_images_label'):
            self.selected_images_label.setText(f"{len(image_paths)} images selected")
            
        if hasattr(self, 'selected_images_list'):
            self.selected_images_list.clear()
            for image_path in image_paths:
                item = QListWidgetItem(os.path.basename(image_path))
                item.setToolTip(image_path)
                self.selected_images_list.addItem(item)
                
        # Reset progress
        self._reset_progress()
        
        # Clear previous results
        self.processing_results.clear()
        self._update_results_grid()
        
        logger.info(f"Loaded {len(image_paths)} images for batch processing")
        
    def _on_task_changed(self, task_name):
        """Handle task selection change."""
        if task_name == "Grain Size Analysis":
            self._setup_grain_size_parameters()
        elif task_name == "Trainable Segmentation":
            self._setup_trainable_segmentation_parameters()
            self._populate_trained_models()
        elif task_name == "Advanced Segmentation":
            self._setup_advanced_segmentation_parameters()
            
    def _start_batch_processing(self):
        """Start batch processing."""
        if not self.selected_images:
            QMessageBox.warning(self, "No Images", "No images selected for processing.")
            return
            
        # Get current task and parameters
        task_type = self.task_combo.currentText() if hasattr(self, 'task_combo') else "Grain Size Analysis"
        parameters = self._get_current_parameters()
        
        # Validate parameters
        if not self._validate_parameters(task_type, parameters):
            return
            
        # Update UI state
        self.start_batch_button.setEnabled(False)
        self.stop_batch_button.setEnabled(True)
        self._reset_progress()
        self._add_log_message(f"Starting {task_type} for {len(self.selected_images)} images...")
        
        # Create and start worker thread
        self.processing_worker = BatchProcessingWorker(
            task_type, self.selected_images, self.selected_image_infos, 
            parameters, self.current_project
        )
        
        # Connect worker signals
        self.processing_worker.progress_updated.connect(self._update_progress)
        self.processing_worker.current_image_updated.connect(self._update_current_image)
        self.processing_worker.patch_progress_updated.connect(self._update_patch_progress)
        self.processing_worker.image_completed.connect(self._on_image_completed)
        self.processing_worker.processing_finished.connect(self._on_processing_finished)
        self.processing_worker.error_occurred.connect(self._on_error_occurred)
        self.processing_worker.log_message.connect(self._add_log_message)
        
        # Start processing
        self.processing_worker.start()
        
    def _stop_batch_processing(self):
        """Stop batch processing."""
        if self.processing_worker and self.processing_worker.isRunning():
            self.processing_worker.stop()
            self.processing_worker.wait(5000)  # Wait up to 5 seconds
            
        self._reset_ui_state()
        self._add_log_message("Processing stopped by user.")
        
    def _safe_get_spinbox_value(self, attr_name: str, default_value: float) -> float:
        """Safely get value from a spinbox widget, handling deleted widgets."""
        try:
            widget = getattr(self, attr_name, None)
            if widget is not None:
                return widget.value()
        except (RuntimeError, AttributeError):
            # Widget was deleted or doesn't exist
            pass
        return default_value
    
    def _get_current_parameters(self):
        """Get current parameters based on selected task."""
        task_type = self.task_combo.currentText() if hasattr(self, 'task_combo') else "Grain Size Analysis"
        
        # Debug logging for box_nms_thresh
        logger.info(f"Debug: hasattr(self, 'sam_box_nms_thresh'): {hasattr(self, 'sam_box_nms_thresh')}")
        if hasattr(self, 'sam_box_nms_thresh') and getattr(self, 'sam_box_nms_thresh', None) is not None:
            try:
                logger.info(f"Debug: sam_box_nms_thresh value: {getattr(self, 'sam_box_nms_thresh', None).value()}")
            except RuntimeError:
                logger.info("Debug: sam_box_nms_thresh widget deleted, using default 0.3")
        else:
            logger.info("Debug: sam_box_nms_thresh attribute not found, using default 0.3")
        
        if task_type == "Grain Size Analysis":
            return {
                # SAM Parameters
                'segmentation_method': getattr(self, 'segmentation_method_combo', None).currentText() if hasattr(self, 'segmentation_method_combo') else 'FastSAM',
                'points_per_side': self._safe_get_spinbox_value('sam_points_per_side', 32),
                'pred_iou_thresh': self._safe_get_spinbox_value('sam_pred_iou_thresh', 0.88),
                'stability_score_thresh': self._safe_get_spinbox_value('sam_stability_score_thresh', 0.95),
                'crop_n_layers': self._safe_get_spinbox_value('sam_crop_n_layers', 0),
                'crop_n_points_downscale_factor': self._safe_get_spinbox_value('sam_crop_n_points_downscale_factor', 1),
                'min_mask_region_area': self._safe_get_spinbox_value('sam_min_mask_region_area', 0),
                'box_nms_thresh': self._safe_get_spinbox_value('sam_box_nms_thresh', 0.3),
                
                # FastSAM-specific Parameters
                'fastsam_input_size': self._safe_get_spinbox_value('fastsam_input_size', 1024),
                'fastsam_conf_thresh': self._safe_get_spinbox_value('fastsam_conf_thresh', 0.4),
                'fastsam_max_det': self._safe_get_spinbox_value('fastsam_max_det', 100),
                
                # Scale Parameters
                'uniform_scale': getattr(self, 'uniform_scale_checkbox', None).isChecked() if hasattr(self, 'uniform_scale_checkbox') else True,
                'uniform_scale_factor': self._safe_get_spinbox_value('scale_factor_spinbox', 1.0),
                'individual_scale_factors': self.individual_scale_factors.copy(),
                
                # Patch Processing Parameters
                'enable_patch_processing': getattr(self, 'enable_patch_processing_checkbox', None).isChecked() if hasattr(self, 'enable_patch_processing_checkbox') else True,
                'patch_size': self._safe_get_spinbox_value('patch_size_spinbox', 1024),
                'patch_overlap': self._safe_get_spinbox_value('patch_overlap_spinbox', 128),
                
                # Intelligent Artifact Detection Parameters
                'enable_artifact_detection': getattr(self, 'enable_artifact_detection_checkbox', None).isChecked() if hasattr(self, 'enable_artifact_detection_checkbox') else True,
                'artifact_sensitivity': getattr(self, 'artifact_sensitivity_slider', None).value() if hasattr(self, 'artifact_sensitivity_slider') else 5,
                'duplicate_sensitivity': getattr(self, 'duplicate_sensitivity_slider', None).value() if hasattr(self, 'duplicate_sensitivity_slider') else 5,
                
                # Analysis Options
                'calculate_statistics': getattr(self, 'calculate_statistics_checkbox', None).isChecked() if hasattr(self, 'calculate_statistics_checkbox') else True,
                # 'generate_histograms' option removed as it was not used in processing
                'save_contours': getattr(self, 'save_contours_checkbox', None).isChecked() if hasattr(self, 'save_contours_checkbox') else True
            }
        elif task_type == "Trainable Segmentation":
            model_name = getattr(self, 'model_combo', None).currentText() if hasattr(self, 'model_combo') else ''
            model_path = None
            
            # Get the model path from the combo box data
            if hasattr(self, 'model_combo') and self.model_combo.currentIndex() > 0:
                model_path = self.model_combo.itemData(self.model_combo.currentIndex())
            
            return {
                'model': model_name,
                'model_path': model_path,
                'batch_size': self._safe_get_spinbox_value('batch_size', 4)
            }
        elif task_type == "Advanced Segmentation":
            return {
                'advanced_model': getattr(self, 'advanced_model_combo', None).currentText() if hasattr(self, 'advanced_model_combo') else 'SAM',
                'prompt_type': getattr(self, 'prompt_type_combo', None).currentText() if hasattr(self, 'prompt_type_combo') else 'Automatic',
                'num_points': self._safe_get_spinbox_value('num_points', 10)
            }
        
        return {}
        
    def _validate_parameters(self, task_type, parameters):
        """Validate parameters for the selected task."""
        if task_type == "Trainable Segmentation":
            if not parameters.get('model') or parameters.get('model') == "Select a trained model...":
                QMessageBox.warning(self, "Invalid Parameters", "Please select a trained model for trainable segmentation.")
                return False
                
        return True
    
    def _populate_trained_models(self):
        """Populate the model combo box with available trained models."""
        if not hasattr(self, 'model_combo') or not self.current_project:
            return
            
        # Clear existing items
        self.model_combo.clear()
        self.model_combo.addItem("Select a trained model...")
        
        try:
            # Get project directory
            if hasattr(self.current_project, 'project_file_path'):
                project_dir = os.path.dirname(self.current_project.project_file_path)
            elif hasattr(self.current_project, 'project_file'):
                project_dir = os.path.dirname(self.current_project.project_file)
            else:
                raise ValueError("Unable to determine project directory")
            
            # Look for trained models in the correct subdirectory
            # Models are saved in {project_name}_data/state/trainable_segmentation/
            project_name = self.current_project.name
            trainable_seg_dir = os.path.join(project_dir, f"{project_name}_data", "state", "trainable_segmentation")
            
            model_files = []
            
            # Check if the trainable segmentation directory exists
            if os.path.exists(trainable_seg_dir):
                for file in os.listdir(trainable_seg_dir):
                    if file.endswith('.pkl'):
                        # Extract model name from filename
                        model_name = os.path.splitext(file)[0]
                        # Remove common prefixes
                        for prefix in ['classifier_', 'trainable_classifier_', 'trainable_model_', 'model_']:
                            if model_name.startswith(prefix):
                                model_name = model_name[len(prefix):]
                                break
                        model_files.append((model_name, file, trainable_seg_dir))
            
            # Also check the project directory for backward compatibility
            if os.path.exists(project_dir):
                for file in os.listdir(project_dir):
                    if file.endswith('.pkl'):
                        # Extract model name from filename
                        model_name = os.path.splitext(file)[0]
                        # Remove common prefixes
                        for prefix in ['classifier_', 'trainable_classifier_', 'trainable_model_', 'model_']:
                            if model_name.startswith(prefix):
                                model_name = model_name[len(prefix):]
                                break
                        model_files.append((model_name, file, project_dir))
            
            # Sort by model name
            model_files.sort(key=lambda x: x[0])
            
            # Add to combo box
            for model_name, file_name, model_dir in model_files:
                self.model_combo.addItem(model_name)
                # Store the full path for later use
                self.model_combo.setItemData(self.model_combo.count() - 1, os.path.join(model_dir, file_name))
                
            if model_files:
                logger.info(f"Found {len(model_files)} trained models in project directory")
            else:
                logger.info("No trained models found in project directory")
                
        except Exception as e:
            logger.error(f"Error populating trained models: {str(e)}")
    
    def _load_external_model(self):
        """Load a trained model from an external location."""
        from PySide6.QtWidgets import QFileDialog, QMessageBox
        import os
        
        if not hasattr(self, 'model_combo'):
            return
            
        # Open file dialog to select model file
        model_path, _ = QFileDialog.getOpenFileName(
            self, "Select Trained Model", os.path.expanduser("~"),
            "Pickle Files (*.pkl);;All Files (*)"
        )
        
        if model_path and os.path.exists(model_path):
            try:
                # Extract model name from filename
                model_name = os.path.splitext(os.path.basename(model_path))[0]
                
                # Remove common prefixes to clean up the display name
                for prefix in ['classifier_', 'trainable_classifier_', 'trainable_model_', 'model_']:
                    if model_name.startswith(prefix):
                        model_name = model_name[len(prefix):]
                        break
                
                # Add "(External)" suffix to distinguish from project models
                display_name = f"{model_name} (External)"
                
                # Check if this model is already loaded
                for i in range(self.model_combo.count()):
                    if self.model_combo.itemData(i) == model_path:
                        # Model already loaded, just select it
                        self.model_combo.setCurrentIndex(i)
                        QMessageBox.information(self, "Model Already Loaded", 
                                              f"The model '{display_name}' is already available in the list.")
                        return
                
                # Add the model to the combo box
                self.model_combo.addItem(display_name)
                # Store the full path for later use
                self.model_combo.setItemData(self.model_combo.count() - 1, model_path)
                # Select the newly added model
                self.model_combo.setCurrentIndex(self.model_combo.count() - 1)
                
                logger.info(f"Loaded external model: {model_path}")
                QMessageBox.information(self, "Model Loaded", 
                                      f"Successfully loaded model '{display_name}' from:\n{model_path}")
                
            except Exception as e:
                logger.error(f"Error loading external model: {str(e)}")
                QMessageBox.warning(self, "Error Loading Model", 
                                  f"Failed to load model from:\n{model_path}\n\nError: {str(e)}")
        
    def _update_progress(self, current, total):
        """Update progress bars."""
        if hasattr(self, 'overall_progress_bar'):
            progress = int((current / total) * 100) if total > 0 else 0
            self.overall_progress_bar.setValue(progress)
            
        if hasattr(self, 'overall_progress_label'):
            self.overall_progress_label.setText(f"Overall Progress: {current}/{total}")
            
    def _update_current_image(self, image_name):
        """Update current image being processed."""
        if hasattr(self, 'current_image_label'):
            self.current_image_label.setText(f"Current: {image_name}")
            
        if hasattr(self, 'current_progress_bar'):
            # Reset current progress for new image
            self.current_progress_bar.setValue(0)
            
    def _update_patch_progress(self, current_patch, total_patches, patch_info):
        """Update patch processing progress."""
        if hasattr(self, 'current_progress_bar'):
            progress = int((current_patch / total_patches) * 100) if total_patches > 0 else 0
            self.current_progress_bar.setValue(progress)
            
        if hasattr(self, 'current_image_label'):
            # Update label to show patch progress
            current_text = self.current_image_label.text()
            if " - " in current_text:
                base_text = current_text.split(" - ")[0]
            else:
                base_text = current_text
            self.current_image_label.setText(f"{base_text} - Patch {current_patch}/{total_patches}")
            
    def _on_image_completed(self, image_path, results):
        """Handle completion of a single image."""
        self.processing_results[image_path] = results
        self._update_results_grid()
        
        if hasattr(self, 'current_progress_bar'):
            self.current_progress_bar.setValue(100)
            
    def _on_processing_finished(self):
        """Handle completion of all processing."""
        self._reset_ui_state()
        self._add_log_message("All processing completed!")
        
        # Generate report for trainable segmentation
        if hasattr(self, 'task_combo') and self.task_combo.currentText() == "Trainable Segmentation":
            self._generate_trainable_segmentation_report_from_results()
        
        # Show completion message
        QMessageBox.information(self, "Processing Complete", 
                              f"Batch processing completed successfully!\n"
                              f"Processed {len(self.processing_results)} images.")
        
    def _on_error_occurred(self, error_message):
        """Handle processing errors."""
        self._add_log_message(f"ERROR: {error_message}")
        
    def _reset_progress(self):
        """Reset progress indicators."""
        if hasattr(self, 'overall_progress_bar'):
            self.overall_progress_bar.setValue(0)
        if hasattr(self, 'current_progress_bar'):
            self.current_progress_bar.setValue(0)
        if hasattr(self, 'overall_progress_label'):
            self.overall_progress_label.setText("Overall Progress: 0/0")
        if hasattr(self, 'current_image_label'):
            self.current_image_label.setText("Current: None")
            
    def _reset_ui_state(self):
        """Reset UI state after processing."""
        if hasattr(self, 'start_batch_button'):
            self.start_batch_button.setEnabled(True)
        if hasattr(self, 'stop_batch_button'):
            self.stop_batch_button.setEnabled(False)
            
    def _add_log_message(self, message):
        """Add a message to the log."""
        if hasattr(self, 'log_text'):
            from datetime import datetime
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.log_text.append(f"[{timestamp}] {message}")
            
    def _update_results_grid(self):
        """Update the results grid with processed images."""
        if not hasattr(self, 'results_grid_layout'):
            return
            
        # Clear existing grid
        while self.results_grid_layout.count():
            child = self.results_grid_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
                
        # Get grid size
        grid_size = getattr(self, 'grid_size_slider', None).value() if hasattr(self, 'grid_size_slider') else 3
        
        # Add results to grid
        row, col = 0, 0
        for image_path, results in self.processing_results.items():
            # Create result widget (placeholder)
            result_widget = self._create_result_widget(image_path, results)
            self.results_grid_layout.addWidget(result_widget, row, col)
            
            col += 1
            if col >= grid_size:
                col = 0
                row += 1
                
    def _create_result_widget(self, image_path, results):
        """Create a widget to display processing results with grain boundaries and hover preview."""
        from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QGraphicsView, QGraphicsScene, QGraphicsPixmapItem
        from PySide6.QtCore import Qt, QTimer
        from PySide6.QtGui import QPixmap, QPainter, QPen, QColor, QFont
        from PIL import Image, ImageDraw, ImageFont
        import cv2
        import numpy as np
        import json
        
        widget = QWidget()
        widget.setFixedSize(450, 550)  # Much larger size for better grain boundary visibility
        widget.setStyleSheet("border: 1px solid gray; border-radius: 5px; padding: 8px;")
        
        layout = QVBoxLayout(widget)
        
        # Image name
        name_label = QLabel(os.path.basename(image_path))
        name_label.setAlignment(Qt.AlignCenter)
        name_label.setStyleSheet("font-weight: bold; font-size: 14px; padding: 5px;")
        layout.addWidget(name_label)
        
        # Enhanced image preview with grain boundaries and zoom/pan functionality
        preview_view = self._create_zoomable_view()
        preview_view.setMinimumHeight(400)  # Much larger preview area for grain visibility
        preview_view.setStyleSheet("border: 1px solid gray;")
        preview_scene = QGraphicsScene()
        preview_view.setScene(preview_scene)
        
        # Create preview with base image and separate graphics items for efficient updates
        try:
            # Load base image
            from PIL import Image
            base_image = None
            if 'segmented_image_path' in results and results['segmented_image_path'] and os.path.exists(results['segmented_image_path']):
                base_image = Image.open(results['segmented_image_path'])
            elif os.path.exists(image_path):
                base_image = Image.open(image_path)
            
            if base_image:
                if base_image.mode != 'RGB':
                    base_image = base_image.convert('RGB')
                
                # Resize for preview while maintaining aspect ratio
                base_image.thumbnail((400, 320), Image.Resampling.LANCZOS)
                
                # Convert to QPixmap
                qimage = base_image.toqimage()
                pixmap = QPixmap.fromImage(qimage)
                pixmap_item = QGraphicsPixmapItem(pixmap)
                preview_scene.addItem(pixmap_item)
                
                # Add grain visualization as separate graphics items
                self._add_grain_graphics_to_scene(preview_scene, results, base_image.width, base_image.height)
                
                preview_view.fitInView(preview_scene.itemsBoundingRect(), Qt.KeepAspectRatio)
            else:
                # Fallback text
                preview_view.setStyleSheet("background-color: lightgray; border: 1px solid gray;")
                
        except Exception as e:
            logger.error(f"Failed to create preview for {image_path}: {e}")
            preview_view.setStyleSheet("background-color: lightcoral; border: 1px solid gray;")
        
        layout.addWidget(preview_view)
        
        # Enhanced results summary
        if 'num_grains' in results and 'mean_grain_size' in results:
            summary_text = f"Grains: {results['num_grains']}\nMean Size: {results['mean_grain_size']:.1f}"
            if 'total_area' in results:
                summary_text += f"\nTotal Area: {results['total_area']:.0f}"
        elif 'error' in results:
            summary_text = f"Error: {results['error']}"
        elif 'num_grains' in results:
            summary_text = f"Grains: {results['num_grains']}"
        else:
            summary_text = "Processing completed"
            
        summary_label = QLabel(summary_text)
        summary_label.setAlignment(Qt.AlignCenter)
        summary_label.setStyleSheet("font-size: 11px; padding: 5px;")
        layout.addWidget(summary_label)
        
        return widget
        
    def _create_enhanced_preview(self, image_path, results):
        """Create an enhanced preview image with grain boundaries and IDs."""
        try:
            from PIL import Image
            import cv2
            import numpy as np
            
            # Load the base image
            base_image = None
            loaded_base_image_path = "None"
            logger.info(f"[DEBUG_ENHANCED] _create_enhanced_preview: Initial image_path: {image_path}, results: {results.keys() if results else 'None'}")
            if 'segmented_image_path' in results and results['segmented_image_path'] and os.path.exists(results['segmented_image_path']):
                logger.info(f"[DEBUG_ENHANCED] _create_enhanced_preview: Attempting to load from results['segmented_image_path']: {results['segmented_image_path']}")
                base_image = Image.open(results['segmented_image_path'])
                loaded_base_image_path = results['segmented_image_path']
            elif os.path.exists(image_path):
                logger.info(f"[DEBUG_ENHANCED] _create_enhanced_preview: Attempting to load from original image_path: {image_path}")
                base_image = Image.open(image_path)
                loaded_base_image_path = image_path
            else:
                logger.warning(f"[DEBUG_ENHANCED] _create_enhanced_preview: Neither segmented_image_path nor original image_path found. Cannot load base image. image_path: {image_path}, segmented_image_path in results: {results.get('segmented_image_path')}")
                return None
            logger.info(f"[DEBUG_ENHANCED] _create_enhanced_preview: Attempting to load base_image from: {loaded_base_image_path}")
            if base_image:
                 logger.info(f"[DEBUG_ENHANCED] _create_enhanced_preview: Loaded base_image: type {type(base_image)}, mode {base_image.mode}, size {base_image.size}")
            else:
                 logger.warning(f"[DEBUG_ENHANCED] _create_enhanced_preview: Failed to load base_image from {loaded_base_image_path}")

                
            if base_image.mode != 'RGB':
                logger.info(f"[DEBUG_ENHANCED] _create_enhanced_preview: Converting base_image from {base_image.mode} to RGB")
                base_image = base_image.convert('RGB')
                
            # Resize for preview while maintaining aspect ratio - larger size for better visibility
            logger.info(f"[DEBUG_ENHANCED] _create_enhanced_preview: Resizing base_image to thumbnail (400, 320)")
            base_image.thumbnail((400, 320), Image.Resampling.LANCZOS)
            
            # Try to find annotations file
            annotations_path = None
            logger.info(f"[DEBUG_ENHANCED] _create_enhanced_preview: Starting annotation search.")
            
            # Look for annotations in various possible locations
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            possible_paths = [
                # NPZ format (the actual format used by the system)
                os.path.join(os.path.dirname(image_path), f"{base_name}_annotations.npz"),
                os.path.join(os.path.dirname(image_path), 'batch_results', f"{base_name}_annotations.npz"),
                os.path.join(os.path.dirname(image_path), 'results', f"{base_name}_annotations.npz"),
                # JSON format (fallback)
                os.path.join(os.path.dirname(image_path), f"{base_name}_annotations.json"),
                os.path.join(os.path.dirname(image_path), f"{base_name}.json"),
                os.path.join(os.path.dirname(image_path), 'results', f"{base_name}_annotations.json"),
                os.path.join(os.path.dirname(image_path), 'batch_results', f"{base_name}_annotations.json"),
            ]
            
            # Check if we have annotations in the result data itself
            if results and 'annotations' in results:
                try:
                    # Create a temporary NPZ file with the annotations
                    import tempfile
                    temp_dir = tempfile.mkdtemp()
                    temp_annotations_path = os.path.join(temp_dir, 'temp_annotations.npz')
                    logger.info(f"[DEBUG_ENHANCED] _create_enhanced_preview: Using annotations from results['annotations']. Creating temp NPZ at {temp_annotations_path}")
                    
                    annotations = results['annotations']
                    logger.info(f"[DEBUG_ENHANCED] _create_enhanced_preview: results['annotations'] type: {type(annotations)}, len/shape: {len(annotations) if isinstance(annotations, list) else annotations.shape if hasattr(annotations, 'shape') else 'N/A'}")
                    
                    # Convert CUDA tensor to CPU before saving to numpy
                    if hasattr(annotations, 'cpu'):
                        logger.info(f"[DEBUG_ENHANCED] _create_enhanced_preview: Converting CUDA tensor to CPU")
                        annotations = annotations.cpu()
                    
                    if isinstance(annotations, list):
                        np.savez_compressed(temp_annotations_path, annotations=np.array(annotations, dtype=object), is_list=True)
                    else:
                        np.savez_compressed(temp_annotations_path, annotations=annotations)
                    
                    enhanced_image = self._add_grain_visualization(base_image, temp_annotations_path)
                    
                    # Clean up temp file
                    try:
                        os.remove(temp_annotations_path)
                        os.rmdir(temp_dir)
                    except:
                        pass # TODO: Log this error
                    
                    if enhanced_image:
                        logger.info(f"[DEBUG_ENHANCED] _create_enhanced_preview: Returning enhanced_image from results['annotations'] data.")
                        return enhanced_image
                    else:
                        logger.warning(f"[DEBUG_ENHANCED] _create_enhanced_preview: _add_grain_visualization returned None when using results['annotations'] data.")
                except Exception as e:
                    logger.error(f"[DEBUG_ENHANCED] Error using annotations from result data: {e}", exc_info=True)
            
            # Check if annotations_path is provided in results
            if 'annotations_path' in results and results['annotations_path'] and os.path.exists(results['annotations_path']):
                annotations_path = results['annotations_path']
            else:
                # Find the first existing annotations file
                for path in possible_paths:
                    if os.path.exists(path):
                        annotations_path = path
                        logger.info(f"[DEBUG] _create_enhanced_preview: Found annotations_path from possible_paths: {annotations_path}")
                        break
            if 'annotations_path' in results and results['annotations_path'] and os.path.exists(results['annotations_path']):
                 if not annotations_path: # Only log if not already found by possible_paths
                    logger.info(f"[DEBUG] _create_enhanced_preview: Found annotations_path from results: {results['annotations_path']}")
                 annotations_path = results['annotations_path'] # Prioritize if also found in possible_paths

            if not annotations_path and not (results and 'annotations' in results):
                logger.warning("[DEBUG] _create_enhanced_preview: No annotations_path found after checking all sources.")

            # If we have annotations, add grain visualization
            if annotations_path:
                logger.info(f"[DEBUG_ENHANCED] _create_enhanced_preview: Calling _add_grain_visualization with annotations_path: {annotations_path}")
                enhanced_image = self._add_grain_visualization(base_image, annotations_path)
                if enhanced_image:
                    logger.info(f"[DEBUG_ENHANCED] _create_enhanced_preview: Returning enhanced_image from _add_grain_visualization using {annotations_path}.")
                else:
                    logger.warning(f"[DEBUG_ENHANCED] _create_enhanced_preview: _add_grain_visualization returned None using {annotations_path}. Returning base_image.")
                return enhanced_image if enhanced_image else base_image
            else:
                if not (results and 'annotations' in results): # Avoid logging if we are about to use results['annotations']
                    logger.info("[DEBUG_ENHANCED] _create_enhanced_preview: No annotations_path and no direct annotations in results, returning base_image directly.")
                else:
                    logger.info("[DEBUG_ENHANCED] _create_enhanced_preview: annotations_path is None, but results['annotations'] might be used. Base image returned for now.")
                return base_image
                
        except Exception as e:
            logger.error(f"[DEBUG_ENHANCED] Error creating enhanced preview: {e}", exc_info=True)
            return None
            
    def _add_grain_visualization(self, base_image, annotations_path):
        """Add grain boundaries and IDs to the image based on visualization settings."""
        logger.info(f"[DEBUG_VIS] _add_grain_visualization: Called with base_image type {type(base_image)}, annotations_path: {annotations_path}")
        try:
            from PIL import Image
            # Convert PIL to OpenCV format for contour processing
            cv_image = cv2.cvtColor(np.array(base_image), cv2.COLOR_RGB2BGR)
            logger.info(f"[DEBUG_VIS] _add_grain_visualization: base_image converted to cv_image, shape: {cv_image.shape}")
            
            # Get visualization settings from instance variables (set by UI controls)
            show_grain_ids = getattr(self, 'show_grain_ids', True)
            grain_id_font_size = getattr(self, 'grain_id_font_size', 10)
            grain_id_color = getattr(self, 'grain_id_color', (255, 255, 255))  # Default: white
            show_grain_boundaries = getattr(self, 'show_grain_boundaries', True)
            grain_boundary_color = getattr(self, 'grain_boundary_color', (0, 255, 255))  # Default: yellow
            grain_boundary_thickness = getattr(self, 'grain_boundary_thickness', 2)  # Default: 2px
            
            logger.info(f"[DEBUG_VIS] Visualization settings: show_ids={show_grain_ids}, font_size={grain_id_font_size}, "
                      f"id_color={grain_id_color}, show_boundaries={show_grain_boundaries}, boundary_color={grain_boundary_color}, "
                      f"boundary_thickness={grain_boundary_thickness}")
            
            # Load annotations from NPZ file
            logger.info(f"[DEBUG_VIS] _add_grain_visualization: Loading annotations from {annotations_path}")
            if annotations_path.endswith('.npz'):
                # Load NPZ annotations (the actual format used by the system)
                npz_data = np.load(annotations_path, allow_pickle=True)
                annotations = npz_data['annotations']
                logger.info(f"[DEBUG_VIS] _add_grain_visualization: Loaded NPZ. Keys: {list(npz_data.keys())}, annotations type: {type(annotations)}, shape/len: {annotations.shape if hasattr(annotations, 'shape') else len(annotations) if isinstance(annotations, list) else 'N/A'}")
                
                # Handle different annotation formats
                if npz_data.get('is_list', False):
                    logger.info("[DEBUG_VIS] _add_grain_visualization: NPZ 'is_list' is True.")
                    # List of masks
                    annotations_list = annotations.tolist() if hasattr(annotations, 'tolist') else annotations
                elif npz_data.get('is_tensor', False):
                    logger.info("[DEBUG_VIS] _add_grain_visualization: NPZ 'is_tensor' is True.")
                    # Single tensor
                    annotations_list = [annotations]
                else:
                    logger.info("[DEBUG_VIS] _add_grain_visualization: NPZ 'is_list' and 'is_tensor' are False. Handling as array.")
                    # Try to handle as array
                    if hasattr(annotations, 'ndim') and annotations.ndim == 3:  # Multiple masks
                        logger.info(f"[DEBUG_VIS] _add_grain_visualization: Annotations is 3D array (multiple masks), shape: {annotations.shape}")
                        annotations_list = [annotations[i] for i in range(annotations.shape[0])]
                    elif hasattr(annotations, 'ndim'):  # Single mask or other array format
                        logger.info(f"[DEBUG_VIS] _add_grain_visualization: Annotations is array (single mask or other), ndim: {annotations.ndim}, shape: {annotations.shape}")
                        annotations_list = [annotations]
                    else:
                        logger.warning(f"[DEBUG_VIS] _add_grain_visualization: Annotations is not a recognized array format. Type: {type(annotations)}. Treating as list.")
                        annotations_list = list(annotations) # Fallback for unknown array-like types
            else:
                # Fallback: try JSON format
                logger.info(f"[DEBUG_VIS] _add_grain_visualization: annotations_path is not NPZ, attempting to load as JSON: {annotations_path}")
                with open(annotations_path, 'r') as f:
                    json_data = json.load(f)
                annotations_list = json_data.get('annotations', [])
                logger.info(f"[DEBUG_VIS] _add_grain_visualization: Loaded JSON. Found {len(annotations_list)} annotations.")
            
            # Draw grain boundaries and IDs using OpenCV (for now, keeping the existing approach)
            # This will be used to create the base image with visualization
            grain_count = 0
            logger.info(f"[DEBUG_VIS] _add_grain_visualization: Iterating through {len(annotations_list)} annotations.")
            for i, annotation in enumerate(annotations_list):
                try:
                    logger.info(f"[DEBUG_VIS] _add_grain_visualization: Processing annotation {i}, type: {type(annotation)}")
                    # Handle different annotation formats
                    if isinstance(annotation, dict) and 'segmentation' in annotation:
                        # COCO-style annotation
                        logger.info(f"[DEBUG_VIS] _add_grain_visualization: Annotation {i} is COCO-style.")
                        segmentation = annotation['segmentation']
                        if isinstance(segmentation, list) and len(segmentation) > 0:
                            points = np.array(segmentation[0]).reshape(-1, 2).astype(np.int32)
                        else:
                            logger.warning(f"[DEBUG_VIS] _add_grain_visualization: Skipping COCO annotation {i} due to empty or invalid segmentation data.")
                            continue
                    elif isinstance(annotation, np.ndarray):
                        # Mask format - find contours
                        logger.info(f"[DEBUG_VIS] _add_grain_visualization: Annotation {i} is ndarray (mask), shape: {annotation.shape}, dtype: {annotation.dtype}")
                        if annotation.dtype == bool:
                            mask = annotation.astype(np.uint8) * 255
                        else:
                            mask = annotation.astype(np.uint8)
                        
                        # Scale mask to preview size if needed
                        if mask.shape[:2] != (base_image.height, base_image.width):
                            logger.info(f"[DEBUG_VIS] _add_grain_visualization: Resizing mask for annotation {i} from {mask.shape[:2]} to {(base_image.height, base_image.width)}")
                            mask = cv2.resize(mask, (base_image.width, base_image.height), interpolation=cv2.INTER_NEAREST)
                        
                        # Find contours
                        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                        if not contours:
                            logger.warning(f"[DEBUG_VIS] _add_grain_visualization: No contours found for mask annotation {i}.")
                            continue
                        
                        # Use the largest contour
                        points = max(contours, key=cv2.contourArea).squeeze()
                        if points.ndim == 1:
                            points = points.reshape(-1, 2)
                        logger.info(f"[DEBUG_VIS] _add_grain_visualization: Found {len(contours)} contours for mask {i}, using largest. Points shape: {points.shape}")
                    else:
                        logger.warning(f"[DEBUG_VIS] _add_grain_visualization: Skipping annotation {i} due to unrecognized format: {type(annotation)}")
                        continue
                    
                    # Ensure points are valid
                    if points.size == 0 or points.shape[0] < 3:
                        logger.warning(f"[DEBUG_VIS] _add_grain_visualization: Skipping annotation {i} due to invalid points (size: {points.size}, shape[0]: {points.shape[0] if hasattr(points, 'shape') else 'N/A'})")
                        continue
                    
                    # Draw boundary if enabled
                    if show_grain_boundaries:
                        cv2.polylines(cv_image, [points], True, grain_boundary_color, grain_boundary_thickness)
                        grain_count += 1
                        logger.info(f"[DEBUG_VIS] _add_grain_visualization: Drawn polyline for annotation {i}, total drawn: {grain_count}")
                    
                    # Add grain ID at centroid if enabled
                    if show_grain_ids:
                        M = cv2.moments(points)
                        if M["m00"] != 0:
                            cX = int(M["m10"] / M["m00"])
                            cY = int(M["m01"] / M["m00"])
                            
                            # Draw grain ID with user-defined font size
                            grain_id = str(i + 1)
                            
                            # Convert font size to scale (OpenCV uses scale rather than pixel size)
                            # Base scale on the user-defined font size
                            font_scale = grain_id_font_size / 20.0  # Convert pixel size to scale
                            thickness = max(1, int(font_scale * 3))
                            
                            # Add subtle background for better visibility
                            (text_width, text_height), _ = cv2.getTextSize(grain_id, cv2.FONT_HERSHEY_SIMPLEX, font_scale, thickness)
                            padding = 2
                            cv2.rectangle(cv_image, 
                                        (cX - text_width//2 - padding, cY - text_height//2 - padding),
                                        (cX + text_width//2 + padding, cY + text_height//2 + padding),
                                        (0, 0, 0), -1)  # Black background
                            
                            # Add text with user-defined color
                            cv2.putText(cv_image, grain_id, (cX - text_width//2, cY + text_height//2),
                                      cv2.FONT_HERSHEY_SIMPLEX, font_scale, grain_id_color, thickness)
                        
                except Exception as e:
                    logger.warning(f"Error processing annotation {i}: {e}")
                    continue
                            
            # Convert back to PIL
            enhanced_image = Image.fromarray(cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB))
            return enhanced_image
            
        except Exception as e:
            logger.error(f"Error adding grain visualization: {e}")
            return None
            
    def _add_grain_graphics_to_scene(self, scene, results, image_width, image_height):
        """Add grain boundaries and IDs as separate graphics items to the scene for efficient updates."""
        try:
            from PySide6.QtWidgets import QGraphicsPolygonItem, QGraphicsTextItem
            from PySide6.QtCore import QPointF
            from PySide6.QtGui import QPen, QColor, QPolygonF, QFont
            import cv2
            import numpy as np
            
            # Get visualization settings
            show_grain_ids = getattr(self, 'show_grain_ids', True)
            grain_id_font_size = getattr(self, 'grain_id_font_size', 10)
            grain_id_color = getattr(self, 'grain_id_color', (255, 255, 255))
            show_grain_boundaries = getattr(self, 'show_grain_boundaries', True)
            grain_boundary_color = getattr(self, 'grain_boundary_color', (0, 255, 255))
            grain_boundary_thickness = getattr(self, 'grain_boundary_thickness', 2)
            
            logger.info(f"[DEBUG_GRAPHICS] Adding graphics to scene: image_size=({image_width}, {image_height})")
            logger.info(f"[DEBUG_GRAPHICS] Visualization settings: boundaries={show_grain_boundaries}, ids={show_grain_ids}")
            logger.info(f"[DEBUG_GRAPHICS] Results keys: {list(results.keys()) if results else 'None'}")
            
            # Load annotations
            annotations_list = []
            
            # Try to get annotations from results
            if 'annotations' in results:
                annotations = results['annotations']
                logger.info(f"[DEBUG_GRAPHICS] Found annotations in results: type={type(annotations)}")
                if isinstance(annotations, list):
                    annotations_list = annotations
                    logger.info(f"[DEBUG_GRAPHICS] Using list annotations: {len(annotations_list)} items")
                elif hasattr(annotations, 'ndim') and annotations.ndim == 3:
                    annotations_list = [annotations[i] for i in range(annotations.shape[0])]
                    logger.info(f"[DEBUG_GRAPHICS] Converted tensor to list: {len(annotations_list)} items")
                else:
                    annotations_list = [annotations]
                    logger.info(f"[DEBUG_GRAPHICS] Using single annotation as list")
            else:
                logger.warning(f"[DEBUG_GRAPHICS] No annotations found in results")
            
            # Process each annotation and add graphics items
            logger.info(f"[DEBUG_GRAPHICS] Processing {len(annotations_list)} annotations")
            items_added = 0
            
            for i, annotation in enumerate(annotations_list):
                try:
                    points = None
                    
                    # Handle different annotation formats
                    if isinstance(annotation, dict) and 'segmentation' in annotation:
                        # COCO-style annotation
                        segmentation = annotation['segmentation']
                        if isinstance(segmentation, list) and len(segmentation) > 0:
                            points = np.array(segmentation[0]).reshape(-1, 2).astype(np.int32)
                            logger.info(f"[DEBUG_GRAPHICS] Annotation {i}: COCO format, {len(points)} points")
                    elif isinstance(annotation, np.ndarray):
                        # Mask format - find contours
                        logger.info(f"[DEBUG_GRAPHICS] Annotation {i}: Mask format, shape={annotation.shape}, dtype={annotation.dtype}")
                        if annotation.dtype == bool:
                            mask = annotation.astype(np.uint8) * 255
                        else:
                            mask = annotation.astype(np.uint8)
                        
                        # Scale mask to image size if needed
                        if mask.shape[:2] != (image_height, image_width):
                            logger.info(f"[DEBUG_GRAPHICS] Resizing mask from {mask.shape[:2]} to ({image_height}, {image_width})")
                            mask = cv2.resize(mask, (image_width, image_height), interpolation=cv2.INTER_NEAREST)
                        
                        # Find contours
                        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                        if contours:
                            # Use the largest contour
                            points = max(contours, key=cv2.contourArea).squeeze()
                            if points.ndim == 1:
                                points = points.reshape(-1, 2)
                            logger.info(f"[DEBUG_GRAPHICS] Found {len(contours)} contours, using largest with {len(points)} points")
                        else:
                            logger.warning(f"[DEBUG_GRAPHICS] No contours found for annotation {i}")
                    elif hasattr(annotation, 'shape'):
                        logger.info(f"[DEBUG_GRAPHICS] Annotation {i}: Tensor/array format, shape={annotation.shape}, dtype={annotation.dtype}")
                        # Handle tensor format
                        if len(annotation.shape) == 2:  # 2D mask
                            mask = annotation.cpu().numpy() if hasattr(annotation, 'cpu') else annotation
                            if mask.dtype == bool:
                                mask = mask.astype(np.uint8) * 255
                            else:
                                mask = mask.astype(np.uint8)
                            
                            # Scale mask to image size if needed
                            if mask.shape[:2] != (image_height, image_width):
                                logger.info(f"[DEBUG_GRAPHICS] Resizing tensor mask from {mask.shape[:2]} to ({image_height}, {image_width})")
                                mask = cv2.resize(mask, (image_width, image_height), interpolation=cv2.INTER_NEAREST)
                            
                            # Find contours
                            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                            if contours:
                                # Use the largest contour
                                points = max(contours, key=cv2.contourArea).squeeze()
                                if points.ndim == 1:
                                    points = points.reshape(-1, 2)
                                logger.info(f"[DEBUG_GRAPHICS] Found {len(contours)} contours from tensor, using largest with {len(points)} points")
                            else:
                                logger.warning(f"[DEBUG_GRAPHICS] No contours found for tensor annotation {i}")
                    else:
                        logger.warning(f"[DEBUG_GRAPHICS] Annotation {i}: Unknown format, type={type(annotation)}")
                    
                    if points is None or points.size == 0 or points.shape[0] < 3:
                        logger.warning(f"[DEBUG_GRAPHICS] Skipping annotation {i}: insufficient points")
                        continue
                    
                    # Create polygon for grain boundary
                    if show_grain_boundaries:
                        polygon = QPolygonF([QPointF(float(pt[0]), float(pt[1])) for pt in points])
                        poly_item = QGraphicsPolygonItem(polygon)
                        
                        # Set pen for boundary
                        pen = QPen(QColor(grain_boundary_color[0], grain_boundary_color[1], grain_boundary_color[2]))
                        pen.setWidth(grain_boundary_thickness)
                        poly_item.setPen(pen)
                        poly_item.setBrush(QColor(0, 0, 0, 0))  # Transparent fill
                        
                        # Tag the item for easy identification during updates
                        poly_item.setData(0, "grain_boundary")
                        scene.addItem(poly_item)
                        logger.info(f"[DEBUG_GRAPHICS] Added grain boundary {i} with {len(points)} points, color={grain_boundary_color}, thickness={grain_boundary_thickness}")
                        items_added += 1
                    
                    # Add grain ID text
                    if show_grain_ids:
                        # Calculate centroid
                        M = cv2.moments(points)
                        if M["m00"] != 0:
                            cX = int(M["m10"] / M["m00"])
                            cY = int(M["m01"] / M["m00"])
                            
                            # Create text item
                            text_item = QGraphicsTextItem(str(i + 1))
                            font = QFont("Arial", grain_id_font_size)
                            text_item.setFont(font)
                            text_item.setDefaultTextColor(QColor(grain_id_color[0], grain_id_color[1], grain_id_color[2]))
                            
                            # Position text at centroid
                            text_rect = text_item.boundingRect()
                            text_item.setPos(cX - text_rect.width()/2, cY - text_rect.height()/2)
                            
                            # Tag the item for easy identification during updates
                            text_item.setData(0, "grain_id")
                            scene.addItem(text_item)
                            logger.info(f"[DEBUG_GRAPHICS] Added grain ID {i+1} at ({cX}, {cY}), color={grain_id_color}, size={grain_id_font_size}")
                            items_added += 1
                        else:
                            logger.warning(f"[DEBUG_GRAPHICS] Could not calculate centroid for annotation {i} (zero area)")
                            
                except Exception as e:
                    logger.warning(f"Error processing annotation {i} for graphics scene: {e}")
                    continue
            
            logger.info(f"[DEBUG_GRAPHICS] Total graphics items added: {items_added}")
                    
        except Exception as e:
            logger.error(f"Error adding grain graphics to scene: {e}")
            return
            
    def _create_zoomable_view(self):
        """Create a zoomable and pannable graphics view for segmented images."""
        try:
            from PySide6.QtWidgets import QGraphicsView
            from PySide6.QtCore import Qt
            from PySide6.QtGui import QWheelEvent
            
            class ZoomableGraphicsView(QGraphicsView):
                def __init__(self):
                    super().__init__()
                    self.setDragMode(QGraphicsView.RubberBandDrag)
                    from PySide6.QtGui import QPainter
                    self.setRenderHint(QPainter.Antialiasing)
                    self.setTransformationAnchor(QGraphicsView.AnchorUnderMouse)
                    self.setResizeAnchor(QGraphicsView.AnchorUnderMouse)
                    self.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
                    self.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
                    self.setFrameShape(QGraphicsView.NoFrame)
                    self.zoom_factor = 1.15
                    self.min_zoom = 0.1
                    self.max_zoom = 10.0
                    self.current_zoom = 1.0
                    
                def wheelEvent(self, event: QWheelEvent):
                    """Handle mouse wheel for zooming."""
                    if event.modifiers() == Qt.ControlModifier:
                        # Zoom with Ctrl + wheel
                        zoom_in = event.angleDelta().y() > 0
                        zoom_factor = self.zoom_factor if zoom_in else 1 / self.zoom_factor
                        
                        new_zoom = self.current_zoom * zoom_factor
                        if self.min_zoom <= new_zoom <= self.max_zoom:
                            self.scale(zoom_factor, zoom_factor)
                            self.current_zoom = new_zoom
                    else:
                        # Normal scrolling
                        super().wheelEvent(event)
                        
                def mousePressEvent(self, event):
                    """Handle mouse press for panning."""
                    if event.button() == Qt.MiddleButton:
                        self.setDragMode(QGraphicsView.ScrollHandDrag)
                    super().mousePressEvent(event)
                    
                def mouseReleaseEvent(self, event):
                    """Handle mouse release."""
                    if event.button() == Qt.MiddleButton:
                        self.setDragMode(QGraphicsView.RubberBandDrag)
                    super().mouseReleaseEvent(event)
                    
                def keyPressEvent(self, event):
                    """Handle keyboard shortcuts for zoom and pan."""
                    if event.key() == Qt.Key_Plus or event.key() == Qt.Key_Equal:
                        # Zoom in with + key
                        if self.current_zoom * self.zoom_factor <= self.max_zoom:
                            self.scale(self.zoom_factor, self.zoom_factor)
                            self.current_zoom *= self.zoom_factor
                    elif event.key() == Qt.Key_Minus:
                        # Zoom out with - key
                        if self.current_zoom / self.zoom_factor >= self.min_zoom:
                            self.scale(1 / self.zoom_factor, 1 / self.zoom_factor)
                            self.current_zoom /= self.zoom_factor
                    elif event.key() == Qt.Key_0:
                        # Reset zoom with 0 key
                        self.resetTransform()
                        self.current_zoom = 1.0
                        if self.scene() and self.scene().items():
                            self.fitInView(self.scene().itemsBoundingRect(), Qt.KeepAspectRatio)
                    else:
                        super().keyPressEvent(event)
                        
            return ZoomableGraphicsView()
            
        except Exception as e:
            logger.error(f"Error creating zoomable view: {e}")
            # Fallback to regular QGraphicsView
            from PySide6.QtWidgets import QGraphicsView
            return QGraphicsView()
        
    def _update_grid_size(self, size):
        """Update grid size label and refresh grid."""
        if hasattr(self, 'grid_size_label'):
            self.grid_size_label.setText(f"{size}x{size}")
        self._update_results_grid()
        
    def _initialize_visualization_settings(self):
        """Initialize visualization settings with default values."""
        # Default settings
        self.show_grain_ids = True
        self.grain_id_font_size = 10
        self.grain_id_color = (255, 255, 255)  # White
        self.show_grain_boundaries = True
        self.grain_boundary_color = (0, 255, 255)  # Yellow
        self.grain_boundary_thickness = 2  # Default thickness
        
        logger.info("Initialized visualization settings with defaults")
        
    def setup_visualization_connections(self):
        """Set up connections for visualization settings UI controls."""
        # Connect UI elements if they exist
        if hasattr(self, 'show_grain_ids_checkbox'):
            self.show_grain_ids = self.show_grain_ids_checkbox.isChecked()
            self.show_grain_ids_checkbox.stateChanged.connect(self._on_show_grain_ids_changed)
            logger.info("Connected show_grain_ids_checkbox")
            
        if hasattr(self, 'grain_id_font_size_slider'):
            self.grain_id_font_size = self.grain_id_font_size_slider.value()
            self.grain_id_font_size_slider.valueChanged.connect(self._on_grain_id_font_size_changed)
            logger.info("Connected grain_id_font_size_slider")
            
        if hasattr(self, 'grain_id_color_button'):
            self.grain_id_color_button.clicked.connect(self._on_grain_id_color_clicked)
            logger.info("Connected grain_id_color_button")
            
        if hasattr(self, 'show_grain_boundaries_checkbox'):
            self.show_grain_boundaries = self.show_grain_boundaries_checkbox.isChecked()
            self.show_grain_boundaries_checkbox.stateChanged.connect(self._on_show_grain_boundaries_changed)
            logger.info("Connected show_grain_boundaries_checkbox")
            
        if hasattr(self, 'grain_boundary_thickness_slider'):
            self.grain_boundary_thickness = self.grain_boundary_thickness_slider.value()
            self.grain_boundary_thickness_slider.valueChanged.connect(self._on_grain_boundary_thickness_changed)
            logger.info("Connected grain_boundary_thickness_slider")
            
        if hasattr(self, 'apply_vis_settings_button'):
            self.apply_vis_settings_button.clicked.connect(self._apply_visualization_settings)
            logger.info("Connected apply_vis_settings_button")
            
    def _on_show_grain_ids_changed(self, state):
        """Handle change in grain ID visibility."""
        self.show_grain_ids = bool(state)
        logger.info(f"Grain ID visibility changed to {self.show_grain_ids}")
        
    def _on_grain_id_font_size_changed(self, value):
        """Handle change in grain ID font size."""
        self.grain_id_font_size = value
        if hasattr(self, 'grain_id_font_size_label'):
            self.grain_id_font_size_label.setText(f"{value}px")
        logger.info(f"Grain ID font size changed to {value}px")
        
    def _on_grain_id_color_clicked(self):
        """Handle grain ID color selection."""
        from PySide6.QtWidgets import QColorDialog
        from PySide6.QtGui import QColor
        
        # Get current color
        current_color = QColor(*self.grain_id_color)
        
        # Open color dialog
        color = QColorDialog.getColor(current_color, self, "Select Grain ID Color")
        if color.isValid():
            # Update color
            self.grain_id_color = (color.red(), color.green(), color.blue())
            
            # Update button style
            self.grain_id_color_button.setStyleSheet(
                f"background-color: rgb({color.red()}, {color.green()}, {color.blue()}); "
                f"border: 1px solid black;"
            )
            logger.info(f"Grain ID color changed to {self.grain_id_color}")
        
    def _on_show_grain_boundaries_changed(self, state):
        """Handle change in grain boundary visibility."""
        self.show_grain_boundaries = bool(state)
        logger.info(f"Grain boundary visibility changed to {self.show_grain_boundaries}")
        
    def _on_grain_boundary_thickness_changed(self, value):
        """Handle change in grain boundary thickness."""
        self.grain_boundary_thickness = value
        if hasattr(self, 'grain_boundary_thickness_label'):
            self.grain_boundary_thickness_label.setText(f"{value}px")
        logger.info(f"Grain boundary thickness changed to {value}px")
        
    def _apply_visualization_settings(self):
        """Apply visualization settings to all displayed images efficiently without full redraw."""
        logger.info("Applying visualization settings to all images efficiently...")
        
        # Instead of completely redrawing the grid, update existing visualizations
        self._update_existing_visualizations()
        
    def _update_existing_visualizations(self):
        """Update existing visualization widgets with new settings without full redraw."""
        if not hasattr(self, 'results_grid_layout'):
            return
            
        # Get current visualization settings
        show_grain_ids = getattr(self, 'show_grain_ids', True)
        grain_id_font_size = getattr(self, 'grain_id_font_size', 10)
        grain_id_color = getattr(self, 'grain_id_color', (255, 255, 255))
        show_grain_boundaries = getattr(self, 'show_grain_boundaries', True)
        grain_boundary_color = getattr(self, 'grain_boundary_color', (0, 255, 255))
        grain_boundary_thickness = getattr(self, 'grain_boundary_thickness', 2)
        
        # Iterate through existing widgets and update their visualizations
        for i in range(self.results_grid_layout.count()):
            widget_item = self.results_grid_layout.itemAt(i)
            if widget_item and widget_item.widget():
                result_widget = widget_item.widget()
                
                # Find the graphics view within the widget
                graphics_view = self._find_graphics_view_in_widget(result_widget)
                if graphics_view and graphics_view.scene():
                    scene = graphics_view.scene()
                    
                    # Update grain boundary styles
                    self._update_grain_boundaries_in_scene(scene, show_grain_boundaries, 
                                                          grain_boundary_color, grain_boundary_thickness)
                    
                    # Update grain ID styles
                    self._update_grain_ids_in_scene(scene, show_grain_ids, grain_id_color, grain_id_font_size)
                    
                    # Refresh the scene
                    scene.update()
                    
        logger.info("Efficiently updated visualization settings for all displayed images")
        
    def _find_graphics_view_in_widget(self, widget):
        """Find the QGraphicsView within a result widget."""
        from PySide6.QtWidgets import QGraphicsView
        
        # Recursively search for QGraphicsView
        for child in widget.findChildren(QGraphicsView):
            return child
        return None
        
    def _update_grain_boundaries_in_scene(self, scene, show_boundaries, boundary_color, thickness):
        """Update grain boundary styles in a graphics scene."""
        from PySide6.QtWidgets import QGraphicsPathItem, QGraphicsPolygonItem
        from PySide6.QtGui import QPen, QColor
        from PySide6.QtCore import Qt
        
        # Create pen with new settings
        pen = QPen(QColor(boundary_color[0], boundary_color[1], boundary_color[2]), thickness)
        pen.setCosmetic(True)
        pen.setStyle(Qt.SolidLine)
        
        # Update all path and polygon items (grain boundaries)
        for item in scene.items():
            if isinstance(item, (QGraphicsPathItem, QGraphicsPolygonItem)):
                if hasattr(item, 'data') and item.data(0) == 'grain_boundary':
                    item.setVisible(show_boundaries)
                    if show_boundaries:
                        item.setPen(pen)
                        
    def _update_grain_ids_in_scene(self, scene, show_ids, id_color, font_size):
        """Update grain ID text styles in a graphics scene."""
        from PySide6.QtWidgets import QGraphicsTextItem
        from PySide6.QtGui import QColor, QFont
        
        # Create font with new settings
        font = QFont("Arial", font_size)
        color = QColor(id_color[0], id_color[1], id_color[2])
        
        # Update all text items (grain IDs)
        for item in scene.items():
            if isinstance(item, QGraphicsTextItem):
                if hasattr(item, 'data') and item.data(0) == 'grain_id':
                    item.setVisible(show_ids)
                    if show_ids:
                        item.setDefaultTextColor(color)
                        item.setFont(font)
        

                    
    def _on_uniform_scale_toggled(self, checked):
        """Handle uniform scale checkbox toggle."""
        if hasattr(self, 'scale_factor_spinbox'):
            self.scale_factor_spinbox.setEnabled(checked)
        
        # Enable/disable individual scale factor buttons
        if hasattr(self, 'configure_individual_scale_button'):
            self.configure_individual_scale_button.setEnabled(not checked)
        if hasattr(self, 'load_scale_config_button'):
            self.load_scale_config_button.setEnabled(not checked)
            
    def _export_results(self):
        """Export results based on current task type."""
        if not self.processing_results:
            QMessageBox.warning(self, "No Results", "No processing results to export.")
            return
            
        # Determine current task type
        task_type = self.task_combo.currentText() if hasattr(self, 'task_combo') else "Grain Size Analysis"
        
        if task_type == "Trainable Segmentation":
            self._export_trainable_segmentation_results()
        else:
            # Default to grain analysis export
            self._export_grain_analysis_results()
    
    def _export_grain_analysis_results(self):
        """Export grain statistics in CSV format."""
        # Get export file path
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export Results", "grain_statistics.csv", "CSV Files (*.csv)"
        )
        
        if file_path:
            try:
                self._export_grain_statistics_csv(file_path)
                QMessageBox.information(self, "Export Complete", f"Grain statistics exported to {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Export Error", f"Failed to export results: {str(e)}")
    
    def _export_trainable_segmentation_results(self):
        """Export trainable segmentation results in CSV format."""
        # Get export file path
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export Results", "segmentation_results.csv", "CSV Files (*.csv)"
        )
        
        if file_path:
            try:
                self._export_segmentation_statistics_csv(file_path)
                QMessageBox.information(self, "Export Complete", f"Segmentation results exported to {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Export Error", f"Failed to export results: {str(e)}")
    
    def _export_grain_statistics_csv(self, file_path):
        """Export detailed grain statistics for each image in CSV format."""
        import pandas as pd
        import os
        
        all_grain_data = []
        
        for image_path, results in self.processing_results.items():
            if 'error' not in results and 'dataframe' in results:
                df = results['dataframe']
                image_name = os.path.basename(image_path)
                sample_name = os.path.splitext(image_name)[0]
                
                # Add sample identification columns
                df_copy = df.copy()
                df_copy['Sample_Name'] = sample_name
                df_copy['Image_Path'] = image_path
                df_copy['Segmentation_Method'] = results.get('segmentation_method', 'Unknown')
                df_copy['Scale_Factor'] = results.get('scale_factor', 1.0)
                
                # Reorder columns to put identification first
                id_cols = ['Sample_Name', 'Image_Path', 'Segmentation_Method', 'Scale_Factor']
                other_cols = [col for col in df_copy.columns if col not in id_cols]
                df_copy = df_copy[id_cols + other_cols]
                
                all_grain_data.append(df_copy)
        
        if all_grain_data:
            # Combine all grain data
            combined_df = pd.concat(all_grain_data, ignore_index=True)
            combined_df.to_csv(file_path, index=False)
        else:
            # Create empty CSV with headers if no data
            empty_df = pd.DataFrame(columns=['Sample_Name', 'Image_Path', 'Segmentation_Method', 'Scale_Factor', 'Area', 'Perimeter'])
            empty_df.to_csv(file_path, index=False)
    
    def _export_segmentation_statistics_csv(self, file_path):
        """Export detailed segmentation statistics for each image in CSV format."""
        import pandas as pd
        import os
        
        all_segmentation_data = []
        
        for image_path, results in self.processing_results.items():
            if 'error' not in results:
                image_name = os.path.basename(image_path)
                sample_name = os.path.splitext(image_name)[0]
                
                # Create base row data
                row_data = {
                    'Sample_Name': sample_name,
                    'Image_Path': image_path,
                    'Model_Used': results.get('model_used', 'Unknown'),
                    'Processing_Time': results.get('processing_time', 0),
                    'Total_Segments': results.get('total_segments', 0),
                    # Note: Confidence and IOU thresholds removed as they're not needed for trainable segmentation
                }
                
                # Add segment statistics if available
                segment_stats = results.get('segment_stats', [])
                if segment_stats:
                    for i, segment in enumerate(segment_stats):
                        segment_row = row_data.copy()
                        segment_row.update({
                            'Segment_Index': i,
                            'Segment_Label': segment.get('label', f'Segment_{i}'),
                            'Pixel_Count': segment.get('pixel_count', 0),
                            'Percentage': segment.get('percentage', 0.0)
                        })
                        all_segmentation_data.append(segment_row)
                else:
                    # If no segment stats, add just the summary row
                    row_data.update({
                        'Segment_Index': 'N/A',
                        'Segment_Label': 'N/A',
                        'Pixel_Count': 'N/A',
                        'Percentage': 'N/A'
                    })
                    all_segmentation_data.append(row_data)
        
        if all_segmentation_data:
            # Create DataFrame and save to CSV
            df = pd.DataFrame(all_segmentation_data)
            df.to_csv(file_path, index=False)
        else:
            # Create empty CSV with headers if no data
            empty_df = pd.DataFrame(columns=[
                'Sample_Name', 'Image_Path', 'Model_Used', 'Processing_Time', 
                'Total_Segments', 'Segment_Index', 'Segment_Label', 'Pixel_Count', 'Percentage'
            ])
            empty_df.to_csv(file_path, index=False)
                
    def _make_json_serializable(self, obj):
        """Convert objects to JSON-serializable format."""
        import torch
        import numpy as np
        import pandas as pd
        
        if isinstance(obj, dict):
            return {key: self._make_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        elif isinstance(obj, torch.Tensor):
            return obj.cpu().numpy().tolist()
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, pd.DataFrame):
            return obj.to_dict('records')
        elif isinstance(obj, (np.integer, np.floating)):
            return obj.item()
        elif hasattr(obj, '__dict__'):
            # For custom objects, try to serialize their attributes
            return self._make_json_serializable(obj.__dict__)
        else:
            return obj
    
    def _generate_report(self):
        """Generate a statistical report based on current task type."""
        if not self.processing_results:
            QMessageBox.warning(self, "No Results", "No processing results to generate report from.")
            return
            
        # Determine current task type
        task_type = self.task_combo.currentText() if hasattr(self, 'task_combo') else "Grain Size Analysis"
        
        if task_type == "Trainable Segmentation":
            self._generate_trainable_segmentation_report_interactive()
        else:
            # Default to grain analysis report
            self._generate_grain_analysis_report()
    
    def _generate_grain_analysis_report(self):
        """Generate grain analysis statistical report."""
        # Get report file path
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Generate Report", "batch_analysis_report.html", "HTML Files (*.html)"
        )
        
        if file_path:
            try:
                self._create_html_report(file_path)
                QMessageBox.information(self, "Report Generated", f"Report saved to {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Report Error", f"Failed to generate report: {str(e)}")
    
    def _generate_trainable_segmentation_report_interactive(self):
        """Generate trainable segmentation report with user file selection."""
        # Get report file path
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Generate Report", "trainable_segmentation_report.html", "HTML Files (*.html)"
        )
        
        if file_path:
            try:
                # Format results data for report generation
                formatted_results = {}
                
                for image_path, results in self.processing_results.items():
                    # Determine if processing was successful
                    success = 'error' not in results
                    
                    formatted_data = {
                        'success': success,
                        'processing_time': results.get('processing_time', 0)
                    }
                    
                    if success:
                        # Get current parameters for batch size
                        current_params = self._get_current_parameters()
                        
                        # Add successful processing data
                        formatted_data.update({
                            'model_name': results.get('model_used', 'Unknown'),
                            'batch_size': current_params.get('batch_size', 'N/A'),
                            'num_segments': results.get('total_segments', 0),
                            'colored_image_path': results.get('segmented_image_path'),
                            'segment_stats': results.get('segment_stats', [])
                        })
                    else:
                        # Add error information
                        formatted_data['error'] = results.get('error', 'Unknown error')
                    
                    formatted_results[image_path] = formatted_data
                
                # Generate the report using existing method
                output_dir = os.path.dirname(file_path)
                report_path = self._generate_trainable_segmentation_report(formatted_results, output_dir)
                
                if report_path:
                    # Move the generated report to the user-selected location
                    import shutil
                    if report_path != file_path:
                        shutil.move(report_path, file_path)
                    
                    QMessageBox.information(self, "Report Generated", f"Report saved to {file_path}")
                else:
                    QMessageBox.critical(self, "Report Error", "Failed to generate report")
                    
            except Exception as e:
                QMessageBox.critical(self, "Report Error", f"Failed to generate report: {str(e)}")
    
    def _create_html_report(self, file_path):
        """Create a detailed geological analysis report with phi scale analysis and histograms."""
        import numpy as np
        import matplotlib.pyplot as plt
        import matplotlib
        matplotlib.use('Agg')  # Use non-interactive backend
        from datetime import datetime
        import os
        import base64
        from io import BytesIO
        from scipy import stats
        
        # Create output directory for plots
        report_dir = os.path.dirname(file_path)
        plots_dir = os.path.join(report_dir, 'plots')
        os.makedirs(plots_dir, exist_ok=True)
        
        # Collect data for each sample
        sample_data = {}
        all_samples_phi = []
        all_samples_mm = []
        
        for image_path, results in self.processing_results.items():
            if 'error' not in results and 'grain_sizes' in results:
                sample_name = os.path.splitext(os.path.basename(image_path))[0]
                grain_sizes_um2 = np.array(results['grain_sizes'])
                
                # Convert to equivalent diameter in micrometers
                grain_diameters_um = 2 * np.sqrt(grain_sizes_um2 / np.pi)
                
                # Convert to millimeters
                grain_diameters_mm = grain_diameters_um / 1000
                
                # Convert to phi scale: phi = -log2(diameter_in_mm)
                # Add small epsilon to avoid log(0) and handle very small values
                grain_diameters_mm_safe = np.maximum(grain_diameters_mm, 1e-6)
                grain_phi = -np.log2(grain_diameters_mm_safe)
                
                sample_data[sample_name] = {
                    'phi': grain_phi,
                    'mm': grain_diameters_mm,
                    'um2': grain_sizes_um2,
                    'results': results
                }
                
                all_samples_phi.extend(grain_phi)
                all_samples_mm.extend(grain_diameters_mm)
        
        # Helper function to calculate phi scale statistics
        def calculate_phi_statistics(phi_values, mm_values):
            if len(phi_values) == 0:
                return {}
            
            phi_array = np.array(phi_values)
            mm_array = np.array(mm_values)
            
            # Calculate statistics
            stats_dict = {
                'mean_phi': float(np.mean(phi_array)),
                'std_phi': float(np.std(phi_array)),
                'skewness_phi': float(stats.skew(phi_array)),
                'kurtosis_phi': float(stats.kurtosis(phi_array)),
                'mean_mm': float(np.mean(mm_array)),
                'std_mm': float(np.std(mm_array)),
                'skewness_mm': float(stats.skew(mm_array)),
                'kurtosis_mm': float(stats.kurtosis(mm_array)),
                'min_phi': float(np.min(phi_array)),
                'max_phi': float(np.max(phi_array)),
                'median_phi': float(np.median(phi_array)),
                'min_mm': float(np.min(mm_array)),
                'max_mm': float(np.max(mm_array)),
                'median_mm': float(np.median(mm_array)),
                'total_grains': len(phi_array)
            }
            
            return stats_dict
        
        # Helper function to create phi scale histogram
        def create_phi_histogram(phi_values, sample_name, save_path):
            if len(phi_values) == 0:
                return None
                
            plt.figure(figsize=(10, 6))
            
            # Define phi scale bins (standard geological bins)
            phi_bins = np.arange(-5, 8, 0.5)  # From -5 to 7.5 phi
            
            # Create histogram
            counts, bins, patches = plt.hist(phi_values, bins=phi_bins, alpha=0.7, color='steelblue', edgecolor='black')
            
            # Customize plot
            plt.xlabel('Phi Scale (φ)', fontsize=12)
            plt.ylabel('Frequency (%)', fontsize=12)
            plt.title(f'{sample_name} - Grain Size Distribution', fontsize=14, fontweight='bold')
            plt.grid(True, alpha=0.3)
            
            # Convert counts to percentages
            total_grains = len(phi_values)
            percentages = (counts / total_grains) * 100
            
            # Add percentage labels on bars
            for i, (count, percentage) in enumerate(zip(counts, percentages)):
                if count > 0:
                    plt.text(bins[i] + 0.25, percentage + 0.5, f'{percentage:.1f}%', 
                            ha='center', va='bottom', fontsize=8)
            
            # Set y-axis to show percentages
            plt.gca().set_ylim(0, max(percentages) * 1.1 if len(percentages) > 0 else 10)
            
            # Add secondary x-axis with mm scale
            ax1 = plt.gca()
            ax2 = ax1.twiny()
            
            # Convert phi to mm for secondary axis
            phi_ticks = np.arange(-4, 8, 1)
            mm_ticks = np.power(2.0, -phi_ticks.astype(float))
            mm_labels = [f'{mm:.3f}' if mm < 1 else f'{mm:.1f}' for mm in mm_ticks]
            
            ax2.set_xlim(ax1.get_xlim())
            ax2.set_xticks(phi_ticks)
            ax2.set_xticklabels(mm_labels)
            ax2.set_xlabel('Grain Size (mm)', fontsize=12)
            
            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return save_path
        
        # Helper function to create frequency table
        def create_frequency_table(phi_values, mm_values):
            if len(phi_values) == 0:
                return []
                
            # Define phi scale ranges
            phi_ranges = [
                (7, 7.99, 3.9, 7.8),
                (6, 6.99, 7.8, 15.6),
                (5, 5.99, 15.6, 31),
                (4, 4.99, 31, 62.5),
                (3, 3.99, 62.5, 125),
                (2, 2.99, 125, 250),
                (1, 1.99, 250, 500),
                (0, 0.99, 500, 1000),
                (-1, -0.1, 1, 2),
                (-2, -1.01, 2, 4),
                (-3, -2.01, 4, 8),
                (-4, -3.01, 8, 16),
                (-5, -4.01, 16, 32)
            ]
            
            frequency_data = []
            total_grains = len(phi_values)
            
            for phi_min, phi_max, microns_min, microns_max in phi_ranges:
                count = np.sum((phi_values >= phi_min) & (phi_values < phi_max))
                frequency_data.append({
                    'phi_range': f'{phi_min} - {phi_max}',
                    'microns_range': f'{microns_min} - {microns_max}',
                    'frequency': count
                })
            
            return frequency_data
        
        # Generate individual sample reports and histograms
        sample_reports = []
        comparison_data = []
        
        for sample_name, data in sample_data.items():
            phi_values = data['phi']
            mm_values = data['mm']
            
            # Calculate statistics
            sample_stats = calculate_phi_statistics(phi_values, mm_values)
            
            # Create histogram
            histogram_path = os.path.join(plots_dir, f'{sample_name}_histogram.png')
            create_phi_histogram(phi_values, sample_name, histogram_path)
            
            # Create frequency table
            frequency_table = create_frequency_table(phi_values, mm_values)
            
            # Store for comparison
            comparison_data.append({
                'name': sample_name,
                'phi': phi_values,
                'mm': mm_values,
                'stats': sample_stats
            })
            
            sample_reports.append({
                'name': sample_name,
                'stats': sample_stats,
                'histogram_path': histogram_path,
                'frequency_table': frequency_table
            })
        
        # Create comparison histogram
        comparison_plot_path = os.path.join(plots_dir, 'samples_comparison.png')
        if len(comparison_data) > 1:
            plt.figure(figsize=(12, 8))
            
            colors = plt.cm.Set3(np.linspace(0, 1, len(comparison_data)))
            phi_bins = np.arange(-5, 8, 0.5)
            
            for i, sample in enumerate(comparison_data):
                if len(sample['phi']) > 0:
                    counts, _ = np.histogram(sample['phi'], bins=phi_bins)
                    percentages = (counts / len(sample['phi'])) * 100
                    bin_centers = (phi_bins[:-1] + phi_bins[1:]) / 2
                    plt.plot(bin_centers, percentages, label=sample['name'], 
                            color=colors[i], linewidth=2, marker='o', markersize=4)
            
            plt.xlabel('Phi Scale (φ)', fontsize=12)
            plt.ylabel('Frequency (%)', fontsize=12)
            plt.title('Sample Comparison - Grain Size Distributions', fontsize=14, fontweight='bold')
            plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            plt.savefig(comparison_plot_path, dpi=300, bbox_inches='tight')
            plt.close()
        
        # Generate HTML content
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>PetroSEG Geological Analysis Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.4; }}
        .header {{ background-color: #2c3e50; color: white; padding: 20px; border-radius: 5px; text-align: center; }}
        .sample-section {{ margin: 30px 0; padding: 20px; border: 2px solid #34495e; border-radius: 8px; page-break-inside: avoid; }}
        .sample-header {{ background-color: #ecf0f1; padding: 15px; margin: -20px -20px 20px -20px; border-radius: 6px 6px 0 0; }}
        .stats-table {{ width: 100%; border-collapse: collapse; margin: 15px 0; }}
        .stats-table th, .stats-table td {{ border: 1px solid #bdc3c7; padding: 8px; text-align: center; }}
        .stats-table th {{ background-color: #95a5a6; color: white; font-weight: bold; }}
        .frequency-table {{ width: 100%; border-collapse: collapse; margin: 15px 0; font-size: 12px; }}
        .frequency-table th, .frequency-table td {{ border: 1px solid #bdc3c7; padding: 6px; text-align: center; }}
        .frequency-table th {{ background-color: #7f8c8d; color: white; }}
        .histogram {{ text-align: center; margin: 20px 0; }}
        .histogram img {{ max-width: 100%; height: auto; border: 1px solid #bdc3c7; }}
        .comparison-section {{ margin: 30px 0; padding: 20px; border: 2px solid #e74c3c; border-radius: 8px; }}
        .two-column {{ display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }}
        @media print {{ .sample-section {{ page-break-inside: avoid; }} }}
    </style>
</head>
<body>
    <div class="header">
        <h1>PetroSEG Geological Analysis Report</h1>
        <h2>Grain Size Distribution Analysis</h2>
        <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>"""
        
        # Add individual sample sections
        for report in sample_reports:
            sample_name = report['name']
            stats = report['stats']
            frequency_table = report['frequency_table']
            histogram_rel_path = os.path.relpath(report['histogram_path'], report_dir)
            
            html_content += f"""
    
    <div class="sample-section">
        <div class="sample-header">
            <h2>Sample: {sample_name}</h2>
            <p>Sample depth (m): 2471.06</p>
        </div>
        
        <h3>Sample Statistics</h3>
        <table class="stats-table">
            <tr>
                <th></th>
                <th>phi</th>
                <th>mm</th>
            </tr>
            <tr>
                <td><strong>Mean</strong></td>
                <td>{stats.get('mean_phi', 0):.4f}</td>
                <td>{stats.get('mean_mm', 0):.4f}</td>
            </tr>
            <tr>
                <td><strong>Standard Deviation</strong></td>
                <td>{stats.get('std_phi', 0):.4f}</td>
                <td>{stats.get('std_mm', 0):.4f}</td>
            </tr>
            <tr>
                <td><strong>Skewness</strong></td>
                <td>{stats.get('skewness_phi', 0):.4f}</td>
                <td>{stats.get('skewness_mm', 0):.4f}</td>
            </tr>
            <tr>
                <td><strong>Kurtosis</strong></td>
                <td>{stats.get('kurtosis_phi', 0):.4f}</td>
                <td>{stats.get('kurtosis_mm', 0):.4f}</td>
            </tr>
        </table>
        
        <div class="two-column">
            <div>
                <h3>Frequency Distribution</h3>
                <table class="frequency-table">
                    <tr>
                        <th>phi</th>
                        <th>Range</th>
                        <th>Microns-mm</th>
                        <th>Frequency</th>
                    </tr>"""
            
            for freq_data in frequency_table:
                html_content += f"""
                    <tr>
                        <td>{freq_data['phi_range']}</td>
                        <td>-</td>
                        <td>{freq_data['microns_range']}</td>
                        <td>{freq_data['frequency']}</td>
                    </tr>"""
            
            html_content += f"""
                </table>
                <p><strong>Bold Value Is Mode</strong></p>
            </div>
            
            <div>
                <h3>Sample Statistics</h3>
                <table class="stats-table">
                    <tr>
                        <th></th>
                        <th>phi</th>
                        <th>mm</th>
                    </tr>
                    <tr>
                        <td><strong>Maximum Grain Size</strong></td>
                        <td>{stats.get('min_phi', 0):.4f}</td>
                        <td>{stats.get('max_mm', 0):.4f}</td>
                    </tr>
                    <tr>
                        <td><strong>Minimum Grain Size</strong></td>
                        <td>{stats.get('max_phi', 0):.4f}</td>
                        <td>{stats.get('min_mm', 0):.4f}</td>
                    </tr>
                    <tr>
                        <td><strong>Median Value</strong></td>
                        <td>{stats.get('median_phi', 0):.4f}</td>
                        <td>{stats.get('median_mm', 0):.4f}</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="histogram">
            <img src="{histogram_rel_path}" alt="{sample_name} Histogram">
        </div>
    </div>"""
        
        # Add comparison section if multiple samples
        if len(comparison_data) > 1:
            comparison_rel_path = os.path.relpath(comparison_plot_path, report_dir)
            html_content += f"""
    
    <div class="comparison-section">
        <h2>Sample Comparison</h2>
        <p>Comparison of grain size distributions across all samples</p>
        <div class="histogram">
            <img src="{comparison_rel_path}" alt="Sample Comparison">
        </div>
    </div>"""
        
        html_content += """
</body>
</html>"""
        
        # Write HTML file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
    
    def _generate_trainable_segmentation_report(self, results_data: Dict, output_dir: str):
        """Generate comprehensive HTML report for trainable segmentation results."""
        try:
            import os
            import json
            from datetime import datetime
            
            # Create report directory
            report_dir = os.path.join(output_dir, "reports")
            os.makedirs(report_dir, exist_ok=True)
            
            # Generate report filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_filename = f"trainable_segmentation_report_{timestamp}.html"
            file_path = os.path.join(report_dir, report_filename)
            
            # Calculate summary statistics
            total_images = len(results_data)
            successful_images = sum(1 for data in results_data.values() if data.get('success', False))
            failed_images = total_images - successful_images
            
            # Collect data for charts and analysis
            chart_data = []
            segment_data = []
            
            # Get model information from first successful result
            model_info = None
            for image_path, data in results_data.items():
                if data.get('success', False):
                    if model_info is None and 'model_name' in data:
                        model_info = {
                            'name': data['model_name'],
                            'batch_size': data.get('batch_size', 'N/A')
                        }
                    
                    # Collect data for charts
                    image_name = os.path.basename(image_path)
                    segment_stats = data.get('segment_stats', [])
                    
                    for stat in segment_stats:
                        chart_data.append({
                            'image': image_name,
                            'segment': f"Segment {stat['label']}",
                            'percentage': stat['percentage'],
                            'pixel_count': stat['pixel_count']
                        })
                    
                    segment_data.append({
                        'image_path': image_path,
                        'image_name': image_name,
                        'segments': segment_stats,
                        'total_segments': data.get('total_segments', 0),
                        'colored_image_path': data.get('colored_image_path'),
                        'processing_time': data.get('processing_time', 0)
                    })
            
            # Start HTML content with enhanced styling and Chart.js
            html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trainable Segmentation Report</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            line-height: 1.6;
        }}
        .header {{
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }}
        .section {{
            background-color: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }}
        .section-title {{
            color: #2c3e50;
            font-size: 1.8em;
            margin-bottom: 20px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }}
        .chart-container {{
            position: relative;
            height: 400px;
            margin: 20px 0;
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }}
        .stat-card {{
            background: linear-gradient(135deg, #ecf0f1, #bdc3c7);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }}
        .stat-value {{
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
        }}
        .stat-label {{
            color: #7f8c8d;
            font-size: 0.9em;
        }}
        .mask-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }}
        .mask-card {{
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: 1px solid #e0e0e0;
        }}
        .mask-image {{
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 6px;
            margin-bottom: 10px;
        }}
        .segment-stats {{
            margin-top: 10px;
        }}
        .segment-item {{
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #f0f0f0;
        }}
        .model-info {{
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }}
        .status-success {{
            color: #27ae60;
            font-weight: bold;
        }}
        .status-failed {{
            color: #e74c3c;
            font-weight: bold;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 Trainable Segmentation Analysis Report</h1>
        <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <!-- Processing Summary Section -->
    <div class="section">
        <h2 class="section-title">📊 Processing Summary</h2>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">{total_images}</div>
                <div class="stat-label">Total Images</div>
            </div>
            <div class="stat-card">
                <div class="stat-value status-success">{successful_images}</div>
                <div class="stat-label">Successfully Processed</div>
            </div>
            <div class="stat-card">
                <div class="stat-value status-failed">{failed_images}</div>
                <div class="stat-label">Failed</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{(successful_images/total_images*100):.1f}%</div>
                <div class="stat-label">Success Rate</div>
            </div>
        </div>"""
            
            # Add model information if available
            if model_info:
                html_content += f"""
        <div class="model-info">
            <h3>🔧 Model Configuration</h3>
            <p><strong>Model:</strong> {model_info['name']}</p>
            <p><strong>Batch Size:</strong> {model_info['batch_size']}</p>
            <p><em>Note: Confidence and IoU thresholds are not applicable to trainable segmentation models.</em></p>
        </div>"""
        
            html_content += """
    </div>
    
    <!-- Section 1: Quantitative Analysis (Bar Chart) -->
    <div class="section">
        <h2 class="section-title">📊 Section 1: Quantitative Analysis (Bar Chart)</h2>
        <p>This section provides a quantitative summary of the segmentation results using a clustered bar chart to compare performance metrics across different images and object classes.</p>
        <div class="chart-container">
            <canvas id="barChart"></canvas>
        </div>
    </div>
    
    <!-- Section 2: Quantitative Analysis (Scatter Plot) -->
    <div class="section">
        <h2 class="section-title">🎯 Section 2: Quantitative Analysis (Scatter Plot)</h2>
        <p>This section displays the same quantitative data as the bar chart but in a scatter plot format for different visualization perspective.</p>
        <div class="chart-container">
            <canvas id="scatterChart"></canvas>
        </div>
    </div>
    
    <!-- Section 3: Visual Segmentation Results (Mask Showcase) -->
    <div class="section">
        <h2 class="section-title">🎨 Section 3: Visual Segmentation Results (Mask Showcase)</h2>
        <p>This is the most critical section, displaying the actual visual outputs of the segmentation process. The charts above are derived from this data.</p>
        <div class="mask-grid">"""
            
            # Add mask showcase for each successful image
            for seg_data in segment_data:
                image_name = seg_data['image_name']
                colored_image_path = seg_data['colored_image_path']
                
                html_content += f"""
        <div class="mask-card">
            <h3>{image_name}</h3>"""
                
                if colored_image_path and os.path.exists(colored_image_path):
                    # Embed image as base64 to ensure it's always accessible
                    import base64
                    try:
                        with open(colored_image_path, 'rb') as img_file:
                            img_data = img_file.read()
                            img_base64 = base64.b64encode(img_data).decode('utf-8')
                            
                            # Determine image format from file extension
                            _, ext = os.path.splitext(colored_image_path)
                            if ext.lower() in ['.jpg', '.jpeg']:
                                img_format = 'jpeg'
                            elif ext.lower() == '.png':
                                img_format = 'png'
                            else:
                                img_format = 'jpeg'  # default fallback
                            
                            html_content += f'<img src="data:image/{img_format};base64,{img_base64}" alt="{image_name} Segmentation" class="mask-image">'
                    except Exception as e:
                        html_content += f'<p style="color: #e74c3c;">Image could not be loaded: {os.path.basename(colored_image_path)}</p>'
                        print(f"Warning: Could not encode image {colored_image_path}: {e}")
                
                html_content += f"""
            <div class="segment-stats">
                <strong>Segmentation Details:</strong>
                <div style="margin-top: 10px;">
                    <div class="segment-item">
                        <span><strong>Total Segments:</strong></span>
                        <span>{seg_data['total_segments']}</span>
                    </div>
                    <div class="segment-item">
                        <span><strong>Processing Time:</strong></span>
                        <span>{seg_data['processing_time']:.2f}s</span>
                    </div>
                </div>
                <div style="margin-top: 15px;">
                    <strong>Segment Breakdown:</strong>"""
                
                for segment in seg_data['segments']:
                    html_content += f"""
                    <div class="segment-item">
                        <span>Segment {segment['label']}</span>
                        <span>{segment['percentage']:.1f}%</span>
                    </div>"""
                
                html_content += """
                </div>
            </div>
        </div>"""
        
            # Add failed images if any
            failed_data = [(path, data) for path, data in results_data.items() if not data.get('success', False)]
            if failed_data:
                html_content += """
        </div>
        <h3 style="color: #e74c3c; margin-top: 30px;">❌ Failed Processing</h3>
        <div class="mask-grid">"""
                
                for image_path, data in failed_data:
                    image_name = os.path.basename(image_path)
                    error_msg = data.get('error', 'Unknown error')
                    html_content += f"""
        <div class="mask-card" style="border-left: 4px solid #e74c3c;">
            <h3>{image_name}</h3>
            <p><strong>Status:</strong> <span class="status-failed">Failed</span></p>
            <p><strong>Error:</strong> {error_msg}</p>
        </div>"""
        
            html_content += """
        </div>
    </div>
    
    <script>
        // Chart.js configuration and data
        const chartData = """ + json.dumps(chart_data) + """;
        
        // Prepare data for bar chart
        const images = [...new Set(chartData.map(d => d.image))];
        const segments = [...new Set(chartData.map(d => d.segment))];
        
        // Generate colors for segments
        const colors = [
            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
            '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
        ];
        
        // Bar Chart
        const barCtx = document.getElementById('barChart').getContext('2d');
        const barChart = new Chart(barCtx, {
            type: 'bar',
            data: {
                labels: images,
                datasets: segments.map((segment, index) => ({
                    label: segment,
                    data: images.map(image => {
                        const item = chartData.find(d => d.image === image && d.segment === segment);
                        return item ? item.percentage : 0;
                    }),
                    backgroundColor: colors[index % colors.length],
                    borderColor: colors[index % colors.length],
                    borderWidth: 1
                }))
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Segment Percentage Distribution by Image'
                    },
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Percentage (%)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Images'
                        }
                    }
                }
            }
        });
        
        // Scatter Plot
        const scatterCtx = document.getElementById('scatterChart').getContext('2d');
        const scatterChart = new Chart(scatterCtx, {
            type: 'scatter',
            data: {
                datasets: segments.map((segment, index) => ({
                    label: segment,
                    data: chartData
                        .filter(d => d.segment === segment)
                        .map((d, i) => ({
                            x: i + 1,
                            y: d.percentage
                        })),
                    backgroundColor: colors[index % colors.length],
                    borderColor: colors[index % colors.length],
                    pointRadius: 6
                }))
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Segment Percentage Distribution (Scatter View)'
                    },
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Percentage (%)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Image Index'
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>"""
            
            # Write HTML file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            self._add_log_message(f"Trainable segmentation report generated: {file_path}")
            return file_path
            
        except Exception as e:
            import logging
            logging.error(f"Error generating trainable segmentation report: {e}")
            self._add_log_message(f"Error generating report: {str(e)}")
            return None
    
    def _generate_trainable_segmentation_report_from_results(self):
        """Generate trainable segmentation report from current processing results."""
        try:
            if not self.processing_results:
                self._add_log_message("No results available for report generation.")
                return
            
            # Format results data for report generation
            formatted_results = {}
            
            for image_path, results in self.processing_results.items():
                # Determine if processing was successful
                success = 'error' not in results
                
                formatted_data = {
                    'success': success,
                    'processing_time': results.get('processing_time', 0)
                }
                
                if success:
                    # Get current parameters for batch size
                    current_params = self._get_current_parameters()
                    
                    # Add successful processing data
                    formatted_data.update({
                        'model_name': results.get('model_used', 'Unknown'),
                        'batch_size': current_params.get('batch_size', 'N/A'),
                        'num_segments': results.get('total_segments', 0),
                        'colored_image_path': results.get('segmented_image_path'),
                        'segment_stats': results.get('segment_stats', [])
                    })
                else:
                    # Add error information
                    formatted_data['error'] = results.get('error', 'Unknown error')
                
                formatted_results[image_path] = formatted_data
            
            # Get output directory from current project
            if self.current_project:
                if hasattr(self.current_project, 'project_file_path'):
                    project_dir = os.path.dirname(self.current_project.project_file_path)
                elif hasattr(self.current_project, 'project_file'):
                    project_dir = os.path.dirname(self.current_project.project_file)
                else:
                    # Fallback to first image directory
                    first_image_path = next(iter(self.processing_results.keys()))
                    project_dir = os.path.dirname(first_image_path)
                output_dir = os.path.join(project_dir, 'batch_segmentation_results')
            else:
                # Fallback to first image directory
                first_image_path = next(iter(self.processing_results.keys()))
                output_dir = os.path.dirname(first_image_path)
            
            # Generate the report
            report_path = self._generate_trainable_segmentation_report(formatted_results, output_dir)
            
            if report_path:
                self._add_log_message(f"Report generated successfully: {os.path.basename(report_path)}")
            
        except Exception as e:
            import logging
            logging.error(f"Error generating trainable segmentation report from results: {e}")
            self._add_log_message(f"Error generating report: {str(e)}")
         
    def _clear_log(self):
        """Clear the log text."""
        if hasattr(self, 'log_text'):
            self.log_text.clear()
            
    def _save_log(self):
        """Save the log to a file."""
        if not hasattr(self, 'log_text'):
            return
            
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Log", "batch_processing_log.txt", "Text Files (*.txt)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w') as f:
                    f.write(self.log_text.toPlainText())
                QMessageBox.information(self, "Log Saved", f"Log saved to {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Save Error", f"Failed to save log: {str(e)}")
                
    def _configure_individual_scale_factors(self):
        """Open dialog to configure individual scale factors."""
        if not self.selected_images:
            QMessageBox.warning(self, "No Images", "No images selected for processing.")
            return
            
        # Create and show the configuration dialog
        dialog = IndividualScaleConfigDialog(
            parent=self,
            image_paths=self.selected_images,
            current_scale_factors=self.individual_scale_factors
        )
        
        # Connect the signal to update our scale factors
        dialog.scale_factors_updated.connect(self._update_individual_scale_factors)
        
        if dialog.exec() == QDialog.Accepted:
            self._add_log_message(f"Individual scale factors configured for {len(self.individual_scale_factors)} images.")
            
    def _update_individual_scale_factors(self, scale_factors: Dict[str, float]):
        """Update the individual scale factors."""
        self.individual_scale_factors = scale_factors.copy()
        
    def _load_scale_configuration(self):
        """Load scale factor configuration from file."""
        if not self.selected_images:
            QMessageBox.warning(self, "No Images", "No images selected for processing.")
            return
            
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Load Scale Configuration", 
            "", "JSON Files (*.json)"
        )
        
        if file_path:
            try:
                with open(file_path, 'r') as f:
                    config = json.load(f)
                    
                if "scale_factors" not in config:
                    QMessageBox.warning(
                        self, "Invalid Configuration", 
                        "The selected file does not contain valid scale factor configuration."
                    )
                    return
                    
                # Apply loaded configuration
                loaded_factors = config["scale_factors"]
                applied_count = 0
                
                for image_path in self.selected_images:
                    image_name = os.path.basename(image_path)
                    if image_name in loaded_factors:
                        self.individual_scale_factors[image_path] = loaded_factors[image_name]
                        applied_count += 1
                        
                QMessageBox.information(
                    self, "Configuration Loaded", 
                    f"Scale factors applied to {applied_count} out of {len(self.selected_images)} images."
                )
                
                self._add_log_message(f"Scale configuration loaded from {file_path}. Applied to {applied_count} images.")
                
            except Exception as e:
                QMessageBox.critical(
                    self, "Load Error", 
                    f"Failed to load configuration: {str(e)}"
                )
                
    def _get_scale_factor_for_image(self, image_path: str) -> float:
        """Get the scale factor for a specific image."""
        # Check if using uniform scale factor
        if hasattr(self, 'uniform_scale_checkbox') and self.uniform_scale_checkbox.isChecked():
            return self._safe_get_spinbox_value('scale_factor_spinbox', 1.0)
        else:
            # Use individual scale factor if available, otherwise default to 1.0
            return self.individual_scale_factors.get(image_path, 1.0)