from PySide6.QtWidgets import QComboBox, QStyledItemDelegate, QStyle, QApplication
from PySide6.QtGui import QPainter, QColor, QBrush, QPen, QFontMetrics
from PySide6.QtCore import Qt, QRect, QSize

class PaletteItemDelegate(QStyledItemDelegate):
    """Custom delegate for drawing palette items in the combo box."""
    
    def __init__(self, parent=None, custom_palettes=None):
        super().__init__(parent)
        self.custom_palettes = custom_palettes or {}
    
    def paint(self, painter, option, index):
        """Paint the combo box item."""
        # Get the item text
        text = index.data()
        
        # Check if this is a custom palette
        if text.startswith("Custom: "):
            palette_name = text[8:]  # Remove "Custom: " prefix
            
            # Get the palette data
            palette_data = self.custom_palettes.get(palette_name, {})
            
            # Draw the background
            if option.state & QStyle.State_Selected:
                painter.fillRect(option.rect, option.palette.highlight())
                text_color = option.palette.highlightedText().color()
            else:
                painter.fillRect(option.rect, option.palette.base())
                text_color = option.palette.text().color()
            
            # Draw the palette preview
            if palette_data:
                # Calculate the preview rect
                preview_rect = QRect(
                    option.rect.left() + 5,
                    option.rect.top() + 5,
                    option.rect.width() - 10,
                    10
                )
                
                # Draw the preview
                num_colors = len(palette_data)
                if num_colors > 0:
                    # Calculate the width of each color block
                    width = preview_rect.width() / num_colors
                    
                    # Draw each color block
                    x = preview_rect.left()
                    for name, color_str in palette_data.items():
                        # Parse the color
                        r, g, b = map(int, color_str.split(','))
                        color = QColor(r, g, b)
                        
                        # Draw the color block
                        painter.fillRect(
                            x, preview_rect.top(),
                            width, preview_rect.height(),
                            color
                        )
                        
                        # Move to the next position
                        x += width
                
                # Draw a border around the preview
                painter.setPen(QPen(Qt.gray))
                painter.drawRect(preview_rect)
                
                # Draw the text below the preview
                text_rect = QRect(
                    option.rect.left() + 5,
                    preview_rect.bottom() + 5,
                    option.rect.width() - 10,
                    option.rect.height() - preview_rect.height() - 10
                )
                
                painter.setPen(QPen(text_color))
                painter.drawText(text_rect, Qt.AlignLeft | Qt.AlignVCenter, text)
            else:
                # Just draw the text
                painter.setPen(QPen(text_color))
                painter.drawText(
                    option.rect.adjusted(5, 0, -5, 0),
                    Qt.AlignLeft | Qt.AlignVCenter,
                    text
                )
        else:
            # Use the default painting for non-custom palettes
            super().paint(painter, option, index)
    
    def sizeHint(self, option, index):
        """Return the size hint for the combo box item."""
        # Get the item text
        text = index.data()
        
        # Check if this is a custom palette
        if text.startswith("Custom: "):
            # Calculate the size based on the text and preview
            fm = QFontMetrics(option.font)
            text_width = fm.horizontalAdvance(text)
            text_height = fm.height()
            
            # Return a size that accommodates the text and preview
            return QSize(max(text_width + 10, 100), text_height + 20)
        else:
            # Use the default size hint for non-custom palettes
            return super().sizeHint(option, index)

class PaletteComboBox(QComboBox):
    """Custom combo box for displaying palettes with previews."""
    
    def __init__(self, parent=None, custom_palettes=None):
        super().__init__(parent)
        self.custom_palettes = custom_palettes or {}
        self.delegate = PaletteItemDelegate(self, self.custom_palettes)
        self.setItemDelegate(self.delegate)
        
        # Set a minimum height to accommodate the previews
        self.setMinimumHeight(40)
    
    def set_custom_palettes(self, custom_palettes):
        """Set the custom palettes dictionary."""
        self.custom_palettes = custom_palettes
        self.delegate.custom_palettes = custom_palettes
        
        # Force a repaint
        self.update()
    
    def showPopup(self):
        """Override to adjust the popup size."""
        # Call the base implementation
        super().showPopup()
        
        # Get the popup
        popup = self.findChild(QApplication.focusWidget().__class__)
        if popup:
            # Set a minimum width
            popup.setMinimumWidth(max(self.width(), 200))
