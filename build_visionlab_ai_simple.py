#!/usr/bin/env python
# build_visionlab_ai_simple.py - Simplified build script with better error handling

import os
import sys
import subprocess
import logging
import argparse
import shutil
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_environment():
    """Check if PyInstaller is installed."""
    try:
        import PyInstaller
        logger.info(f"PyInstaller version: {PyInstaller.__version__}")
        return True
    except ImportError:
        logger.error("PyInstaller is not installed. Installing it now...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], check=True)
            logger.info("PyInstaller installed successfully.")
            return True
        except Exception as e:
            logger.error(f"Failed to install PyInstaller: {e}")
            return False

def install_missing_deps():
    """Install only essential missing dependencies."""
    logger.info("Installing essential dependencies...")
    
    essential_deps = ['expecttest', 'fvcore']
    
    for dep in essential_deps:
        try:
            __import__(dep)
            logger.info(f"✓ {dep} is available")
        except ImportError:
            logger.info(f"Installing {dep}...")
            try:
                subprocess.run([sys.executable, '-m', 'pip', 'install', dep], check=True)
                logger.info(f"✓ {dep} installed successfully")
            except Exception as e:
                logger.warning(f"Failed to install {dep}: {e}")
    
    return True

def find_xgboost_package():
    """Find the path to the xgboost package directory."""
    try:
        import xgboost
        # Get the path to the xgboost package
        xgboost_path = os.path.dirname(xgboost.__file__)
        logger.info(f"Found xgboost package at: {xgboost_path}")
        return xgboost_path
    except ImportError:
        logger.error("XGBoost is not installed.")
        return None

def build_executable_simple():
    """Build executable with minimal configuration."""
    logger.info("Building executable with simplified configuration...")
    
    # Clean build and dist directories
    for dir_name in ['build', 'dist']:
        if os.path.exists(dir_name):
            logger.info(f"Cleaning {dir_name} directory...")
            shutil.rmtree(dir_name)
    
    xgboost_package_path = find_xgboost_package()

    # Simplified command with minimal imports
    cmd = [
        sys.executable,
        '-m',
        'PyInstaller',
        '--name=VisionLab_Ai_Simple',
        '--onedir',  # Create one directory instead of one file
        '--console',  # Keep console for debugging
        '--noconfirm',
        # Essential hidden imports only
        '--hidden-import=PySide6.QtCore',
        '--hidden-import=PySide6.QtGui', 
        '--hidden-import=PySide6.QtWidgets',
        '--hidden-import=numpy',
        '--hidden-import=PIL',
        '--hidden-import=cv2',
        '--hidden-import=yaml',
        '--hidden-import=xgboost',
        '--hidden-import=torch.onnx',
        # Exclude problematic modules
        '--exclude-module=tensorflow',
        '--exclude-module=tensorflow-plugins', 
        '--exclude-module=keras',
        '--exclude-module=openvino',
        '--exclude-module=markdown',
        '--exclude-module=matplotlib.tests',
        '--exclude-module=scipy.tests',
        '--exclude-module=sklearn.tests',
        '--exclude-module=pandas.tests',
        '--exclude-module=numpy.tests',
        # Add essential data
        '--add-data=src/gui/styles;src/gui/styles',
        '--add-data=src/gui/icons;src/gui/icons',
        'main.py'
    ]

    if xgboost_package_path:
        # Add the entire XGBoost package directory to preserve structure
        cmd.insert(-1, f'--add-data={xgboost_package_path};xgboost')
    
    try:
        logger.info("Running simplified PyInstaller build...")
        logger.info(f"Command: {' '.join(cmd[:10])}...")  # Log first 10 args
        
        # Set environment
        env = os.environ.copy()
        src_path = os.path.abspath('src')
        env['PYTHONPATH'] = src_path
        
        result = subprocess.run(cmd, check=True, env=env, cwd=os.getcwd())
        logger.info("Build completed successfully!")
        
        output_path = os.path.join('dist', 'VisionLab_Ai_Simple')
        logger.info(f"Executable created at: {os.path.abspath(output_path)}")
        logger.info(f"To run: {os.path.join(output_path, 'VisionLab_Ai_Simple.exe')}")
        
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"Build failed with exit code {e.returncode}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error during build: {e}")
        return False

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Simple VisionLab Ai build script')
    args = parser.parse_args()
    
    logger.info("Starting simplified VisionLab Ai build...")
    
    # Check environment
    if not check_environment():
        logger.error("Environment check failed.")
        return 1
    
    # Install missing dependencies
    install_missing_deps()
    
    # Build executable
    if not build_executable_simple():
        logger.error("Build failed. Check the logs above for details.")
        return 1
    
    logger.info("Build process completed successfully!")
    return 0

if __name__ == "__main__":
    sys.exit(main())