@echo off
REM PetroSEG V4 Launcher
REM This batch file ensures the application runs with the correct virtual environment

echo Starting PetroSEG V4...
echo.

REM Check if virtual environment exists
if not exist "venv\Scripts\python.exe" (
    echo ERROR: Virtual environment not found!
    echo Please make sure the 'venv' folder exists in the current directory.
    echo.
    pause
    exit /b 1
)

REM Run the application with the virtual environment Python
echo Using virtual environment Python: %CD%\venv\Scripts\python.exe
echo.
"%CD%\venv\Scripts\python.exe" main.py

REM Keep the window open if there's an error
if errorlevel 1 (
    echo.
    echo Application exited with an error.
    pause
)
