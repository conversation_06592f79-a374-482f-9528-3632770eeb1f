"""
Test script to verify the brush size slider display synchronization fix.

This test checks that:
1. The signal connection exists between trainable_brush_size_slider.valueChanged and update_brush_size
2. The update_brush_size method exists and properly updates the label
3. The initialization correctly sets both the brush_size and label text
"""

import os
import sys
import inspect

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_signal_connection_exists():
    """Test that the signal connection code exists in the handlers."""
    print("Testing brush size slider signal connection...")
    
    # Read the trainable segmentation handlers file
    handlers_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'gui', 'handlers', 'trainable_segmentation_handlers.py')
    
    if not os.path.exists(handlers_file):
        print(f"✗ FAILED: Handlers file not found: {handlers_file}")
        return False
    
    with open(handlers_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for the signal connection
    if 'trainable_brush_size_slider.valueChanged.connect(self.update_brush_size)' in content:
        print("✓ Signal connection found: trainable_brush_size_slider.valueChanged.connect(self.update_brush_size)")
    else:
        print("✗ FAILED: Signal connection not found")
        return False
    
    # Check for the update_brush_size method
    if 'def update_brush_size(self, value):' in content:
        print("✓ Handler method found: update_brush_size")
    else:
        print("✗ FAILED: update_brush_size method not found")
        return False
    
    # Check that the method updates the label
    if 'self.trainable_brush_size_label.setText(str(value))' in content:
        print("✓ Handler updates label: trainable_brush_size_label.setText(str(value))")
    else:
        print("✗ FAILED: Handler does not update label")
        return False
    
    return True


def test_method_signature():
    """Test that the update_brush_size method has the correct signature."""
    print("\nTesting update_brush_size method signature...")
    
    try:
        # Import the handlers class
        from src.gui.handlers.trainable_segmentation_handlers import TrainableSegmentationHandlers
        
        # Check if the method exists
        if hasattr(TrainableSegmentationHandlers, 'update_brush_size'):
            method = getattr(TrainableSegmentationHandlers, 'update_brush_size')
            
            # Check method signature
            sig = inspect.signature(method)
            params = list(sig.parameters.keys())
            
            # Should have 'self' and 'value' parameters
            if len(params) >= 2 and params[0] == 'self' and 'value' in params:
                print(f"✓ Method signature correct: {params}")
                return True
            else:
                print(f"✗ FAILED: Incorrect method signature: {params}")
                return False
        else:
            print("✗ FAILED: Method update_brush_size not found in class")
            return False
            
    except ImportError as e:
        print(f"✗ FAILED: Could not import TrainableSegmentationHandlers: {e}")
        return False
    except Exception as e:
        print(f"✗ FAILED: Error checking method signature: {e}")
        return False


def test_initialization_logic():
    """Test that the initialization logic is correct."""
    print("\nTesting initialization logic...")
    
    handlers_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'gui', 'handlers', 'trainable_segmentation_handlers.py')
    
    with open(handlers_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for proper initialization
    checks = [
        ('self.brush_size = self.trainable_brush_size_slider.value()', 'Brush size initialization'),
        ('self.trainable_brush_size_label.setText(str(self.brush_size))', 'Label text initialization'),
        ('logger.debug("Connected trainable_brush_size_slider.valueChanged to update_brush_size")', 'Connection logging')
    ]
    
    all_passed = True
    for check, description in checks:
        if check in content:
            print(f"✓ {description} found")
        else:
            print(f"✗ FAILED: {description} not found")
            all_passed = False
    
    return all_passed


def test_ui_components_exist():
    """Test that the UI components are properly defined."""
    print("\nTesting UI components...")
    
    try:
        # Check the UI file for slider and label
        ui_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'gui', 'ui', 'trainable_segmentation_page_ui.py')
        
        if not os.path.exists(ui_file):
            print(f"✗ FAILED: UI file not found: {ui_file}")
            return False
        
        with open(ui_file, 'r', encoding='utf-8') as f:
            ui_content = f.read()
        
        # Check for slider and label components
        ui_checks = [
            ('self.trainable_brush_size_slider = QSlider(Qt.Horizontal)', 'Brush size slider'),
            ('self.trainable_brush_size_label = QLabel("5")', 'Brush size label'),
            ('self.trainable_brush_size_slider.setRange(1, 50)', 'Slider range'),
            ('self.trainable_brush_size_slider.setValue(5)', 'Slider default value')
        ]
        
        all_passed = True
        for check, description in ui_checks:
            if check in ui_content:
                print(f"✓ {description} found in UI")
            else:
                print(f"✗ FAILED: {description} not found in UI")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"✗ FAILED: Error checking UI components: {e}")
        return False


def test_comparison_with_working_implementation():
    """Test that the implementation matches the working advanced segmentation pattern."""
    print("\nTesting comparison with advanced segmentation implementation...")
    
    try:
        # Check advanced segmentation implementation
        adv_seg_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'gui', 'handlers', 'advanced_segmentation_page_handler.py')
        
        if not os.path.exists(adv_seg_file):
            print(f"✗ WARNING: Advanced segmentation file not found: {adv_seg_file}")
            return True  # Not a failure, just a warning
        
        with open(adv_seg_file, 'r', encoding='utf-8') as f:
            adv_content = f.read()
        
        # Check for similar pattern
        if 'brush_size_slider.valueChanged.connect(' in adv_content:
            print("✓ Similar pattern exists in advanced segmentation")
            
            # Check for the handler method pattern
            if 'def on_brush_size_changed(self, value):' in adv_content:
                print("✓ Advanced segmentation has similar handler method")
                
                # Check for label update pattern
                if 'brush_size_value_label.setText(str(value))' in adv_content:
                    print("✓ Advanced segmentation updates label correctly")
                    return True
        
        print("✗ WARNING: Could not find similar pattern in advanced segmentation")
        return True  # Not a failure, just a warning
        
    except Exception as e:
        print(f"✗ WARNING: Error checking advanced segmentation: {e}")
        return True  # Not a failure, just a warning


def main():
    """Run all tests."""
    print("Brush Size Slider Display Synchronization Fix Test")
    print("=" * 60)
    
    # Run tests
    test1_passed = test_signal_connection_exists()
    test2_passed = test_method_signature()
    test3_passed = test_initialization_logic()
    test4_passed = test_ui_components_exist()
    test5_passed = test_comparison_with_working_implementation()
    
    print("\n" + "=" * 60)
    if test1_passed and test2_passed and test3_passed and test4_passed and test5_passed:
        print("✓ ALL TESTS PASSED - Brush size slider display synchronization should work correctly!")
        print("\nThe fix includes:")
        print("1. ✓ Signal connection: trainable_brush_size_slider.valueChanged -> update_brush_size")
        print("2. ✓ Handler method: update_brush_size(value) updates both brush_size and label")
        print("3. ✓ Proper initialization: sets initial values for both brush_size and label")
        print("4. ✓ UI components: slider and label are properly defined")
        print("\nNow when users move the slider, the label should update in real-time!")
        return 0
    else:
        print("✗ SOME TESTS FAILED - Check the implementation")
        return 1


if __name__ == "__main__":
    sys.exit(main())
