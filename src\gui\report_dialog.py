# src/gui/report_dialog.py
import os
import logging
from typing import List, Optional
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                             QComboBox, QListWidget, QGroupBox, QFormLayout, QCheckBox,
                             QFileDialog, QMessageBox, QTabWidget, QWidget, QListWidgetItem,
                             QAbstractItemView, QSpacerItem, QSizePolicy)
from PySide6.QtCore import Qt, Slot

from src.core.project_data import Project, ImageInfo
from src.core.report_generator import ReportGenerator

logger = logging.getLogger(__name__)

class ReportDialog(QDialog):
    """Dialog for generating reports and visualizations from project data."""

    def __init__(self, parent=None, project=None, selected_images=None):
        super().__init__(parent)
        self.project = project
        self.selected_images = selected_images or []
        self.report_generator = ReportGenerator(project) if project else None

        self.setWindowTitle("Project Reports")
        self.resize(700, 500)
        self.setup_ui()
        self.connect_signals()

    def setup_ui(self):
        """Set up the dialog UI."""
        self.main_layout = QVBoxLayout(self)

        # Header with selection info
        self.header_label = QLabel(f"Generate Reports for Project: {self.project.name if self.project else 'No Project'}")
        self.header_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        self.main_layout.addWidget(self.header_label)

        # Tab widget for different report types
        self.tab_widget = QTabWidget()

        # Summary Report Tab
        self.summary_tab = QWidget()
        self.setup_summary_tab()
        self.tab_widget.addTab(self.summary_tab, "Summary Report")

        # Grain Size Analysis Tab
        self.grain_size_tab = QWidget()
        self.setup_grain_size_tab()
        self.tab_widget.addTab(self.grain_size_tab, "Grain Size Analysis")

        # Porosity Analysis Tab
        self.porosity_tab = QWidget()
        self.setup_porosity_tab()
        self.tab_widget.addTab(self.porosity_tab, "Porosity Analysis")

        # Export Tab
        self.export_tab = QWidget()
        self.setup_export_tab()
        self.tab_widget.addTab(self.export_tab, "Export Data")

        self.main_layout.addWidget(self.tab_widget)

        # Bottom buttons
        self.button_layout = QHBoxLayout()
        self.close_button = QPushButton("Close")
        self.button_layout.addStretch()
        self.button_layout.addWidget(self.close_button)

        self.main_layout.addLayout(self.button_layout)

    def setup_summary_tab(self):
        """Set up the Summary Report tab."""
        layout = QVBoxLayout(self.summary_tab)

        # Description
        description = QLabel("Generate a summary report of all images and analysis results in the project.")
        description.setWordWrap(True)
        layout.addWidget(description)

        # Options group
        options_group = QGroupBox("Report Options")
        options_layout = QFormLayout(options_group)

        self.summary_include_all = QCheckBox("Include all images")
        self.summary_include_all.setChecked(True)
        options_layout.addRow("", self.summary_include_all)

        self.summary_include_selected = QCheckBox("Include only selected images")
        self.summary_include_selected.setChecked(False)
        self.summary_include_selected.setEnabled(len(self.selected_images) > 0)
        options_layout.addRow("", self.summary_include_selected)

        layout.addWidget(options_group)

        # Output directory
        output_group = QGroupBox("Output Options")
        output_layout = QFormLayout(output_group)

        self.summary_output_dir = QLabel(os.path.join(self.project.project_dir, "reports") if self.project else "")
        output_layout.addRow("Output Directory:", self.summary_output_dir)

        self.summary_browse_button = QPushButton("Browse...")
        output_layout.addRow("", self.summary_browse_button)

        layout.addWidget(output_group)

        # Generate button
        self.generate_summary_button = QPushButton("Generate Summary Report")
        layout.addWidget(self.generate_summary_button)

        # Add spacer
        layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

    def setup_grain_size_tab(self):
        """Set up the Grain Size Analysis tab."""
        layout = QVBoxLayout(self.grain_size_tab)

        # Description
        description = QLabel("Generate grain size distribution plots and statistics.")
        description.setWordWrap(True)
        layout.addWidget(description)

        # Image selection
        selection_group = QGroupBox("Select Images")
        selection_layout = QVBoxLayout(selection_group)

        self.grain_size_image_list = QListWidget()
        self.grain_size_image_list.setSelectionMode(QAbstractItemView.ExtendedSelection)
        selection_layout.addWidget(self.grain_size_image_list)

        # Populate the list with images that have grain size analysis results
        if self.project:
            for image_id, image_info in self.project.images.items():
                if 'grain_size_analysis' in image_info.analysis_results:
                    item = QListWidgetItem(f"{image_info.filename} (ID: {image_info.id})")
                    item.setData(Qt.UserRole, image_id)
                    self.grain_size_image_list.addItem(item)

        selection_layout.addWidget(QLabel("Select images to include in the grain size distribution plot."))

        layout.addWidget(selection_group)

        # Output directory
        output_group = QGroupBox("Output Options")
        output_layout = QFormLayout(output_group)

        self.grain_size_output_dir = QLabel(os.path.join(self.project.project_dir, "reports") if self.project else "")
        output_layout.addRow("Output Directory:", self.grain_size_output_dir)

        self.grain_size_browse_button = QPushButton("Browse...")
        output_layout.addRow("", self.grain_size_browse_button)

        layout.addWidget(output_group)

        # Generate button
        self.generate_grain_size_button = QPushButton("Generate Grain Size Plot")
        layout.addWidget(self.generate_grain_size_button)

        # Add spacer
        layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

    def setup_porosity_tab(self):
        """Set up the Porosity Analysis tab."""
        layout = QVBoxLayout(self.porosity_tab)

        # Description
        description = QLabel("Generate porosity comparison plots and statistics.")
        description.setWordWrap(True)
        layout.addWidget(description)

        # Image selection
        selection_group = QGroupBox("Select Images")
        selection_layout = QVBoxLayout(selection_group)

        self.porosity_image_list = QListWidget()
        self.porosity_image_list.setSelectionMode(QAbstractItemView.ExtendedSelection)
        selection_layout.addWidget(self.porosity_image_list)

        # Populate the list with images that have image lab results
        if self.project:
            for image_id, image_info in self.project.images.items():
                if 'image_lab' in image_info.analysis_results:
                    item = QListWidgetItem(f"{image_info.filename} (ID: {image_info.id})")
                    item.setData(Qt.UserRole, image_id)
                    self.porosity_image_list.addItem(item)

        selection_layout.addWidget(QLabel("Select images to include in the porosity comparison plot."))

        layout.addWidget(selection_group)

        # Output directory
        output_group = QGroupBox("Output Options")
        output_layout = QFormLayout(output_group)

        self.porosity_output_dir = QLabel(os.path.join(self.project.project_dir, "reports") if self.project else "")
        output_layout.addRow("Output Directory:", self.porosity_output_dir)

        self.porosity_browse_button = QPushButton("Browse...")
        output_layout.addRow("", self.porosity_browse_button)

        layout.addWidget(output_group)

        # Generate button
        self.generate_porosity_button = QPushButton("Generate Porosity Plot")
        layout.addWidget(self.generate_porosity_button)

        # Add spacer
        layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

    def setup_export_tab(self):
        """Set up the Export Data tab."""
        layout = QVBoxLayout(self.export_tab)

        # Description
        description = QLabel("Export analysis results to CSV files for further processing.")
        description.setWordWrap(True)
        layout.addWidget(description)

        # Analysis type selection
        type_group = QGroupBox("Analysis Type")
        type_layout = QFormLayout(type_group)

        self.export_analysis_type = QComboBox()
        self.export_analysis_type.addItems(["Grain Size Analysis", "Porosity Analysis"])
        type_layout.addRow("Export data for:", self.export_analysis_type)

        layout.addWidget(type_group)

        # Image selection
        selection_group = QGroupBox("Select Images")
        selection_layout = QVBoxLayout(selection_group)

        self.export_include_all = QCheckBox("Include all images with selected analysis")
        self.export_include_all.setChecked(True)
        selection_layout.addWidget(self.export_include_all)

        self.export_image_list = QListWidget()
        self.export_image_list.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.export_image_list.setEnabled(False)  # Disabled when include_all is checked
        selection_layout.addWidget(self.export_image_list)

        # Populate the list with all images
        if self.project:
            for image_id, image_info in self.project.images.items():
                item = QListWidgetItem(f"{image_info.filename} (ID: {image_info.id})")
                item.setData(Qt.UserRole, image_id)
                self.export_image_list.addItem(item)

        layout.addWidget(selection_group)

        # Output directory
        output_group = QGroupBox("Output Options")
        output_layout = QFormLayout(output_group)

        self.export_output_dir = QLabel(os.path.join(self.project.project_dir, "reports") if self.project else "")
        output_layout.addRow("Output Directory:", self.export_output_dir)

        self.export_browse_button = QPushButton("Browse...")
        output_layout.addRow("", self.export_browse_button)

        layout.addWidget(output_group)

        # Export button
        self.export_button = QPushButton("Export to CSV")
        layout.addWidget(self.export_button)

        # Add spacer
        layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

    def connect_signals(self):
        """Connect UI signals to slots."""
        # Summary tab
        self.summary_browse_button.clicked.connect(lambda: self._browse_output_dir(self.summary_output_dir))
        self.generate_summary_button.clicked.connect(self._generate_summary_report)
        self.summary_include_all.toggled.connect(lambda checked: self.summary_include_selected.setChecked(not checked))
        self.summary_include_selected.toggled.connect(lambda checked: self.summary_include_all.setChecked(not checked))

        # Grain size tab
        self.grain_size_browse_button.clicked.connect(lambda: self._browse_output_dir(self.grain_size_output_dir))
        self.generate_grain_size_button.clicked.connect(self._generate_grain_size_plot)

        # Porosity tab
        self.porosity_browse_button.clicked.connect(lambda: self._browse_output_dir(self.porosity_output_dir))
        self.generate_porosity_button.clicked.connect(self._generate_porosity_plot)

        # Export tab
        self.export_browse_button.clicked.connect(lambda: self._browse_output_dir(self.export_output_dir))
        self.export_button.clicked.connect(self._export_to_csv)
        self.export_include_all.toggled.connect(lambda checked: self.export_image_list.setEnabled(not checked))
        self.export_analysis_type.currentIndexChanged.connect(self._update_export_image_list)

        # Close button
        self.close_button.clicked.connect(self.close)

    def _browse_output_dir(self, label_widget):
        """Open a file dialog to select an output directory."""
        if not self.project:
            return

        current_dir = label_widget.text() or self.project.project_dir
        dir_path = QFileDialog.getExistingDirectory(self, "Select Output Directory", current_dir)

        if dir_path:
            label_widget.setText(dir_path)

    def _get_selected_image_ids(self, list_widget) -> List[str]:
        """Get the image IDs for selected items in a list widget."""
        selected_ids = []
        for item in list_widget.selectedItems():
            image_id = item.data(Qt.UserRole)
            selected_ids.append(image_id)
        return selected_ids

    def _generate_summary_report(self):
        """Generate a summary report for the project."""
        if not self.project or not self.report_generator:
            QMessageBox.warning(self, "Error", "No project loaded.")
            return

        output_dir = self.summary_output_dir.text()
        if not output_dir:
            QMessageBox.warning(self, "Error", "Please select an output directory.")
            return

        try:
            # Generate the report
            report_path = self.report_generator.generate_summary_report(output_dir)

            if report_path and os.path.exists(report_path):
                QMessageBox.information(
                    self, "Report Generated",
                    f"Summary report generated successfully.\n\nSaved to: {report_path}"
                )
            else:
                QMessageBox.warning(self, "Error", "Failed to generate summary report.")
        except Exception as e:
            logger.exception("Error generating summary report")
            QMessageBox.critical(self, "Error", f"An error occurred: {str(e)}")

    def _generate_grain_size_plot(self):
        """Generate a grain size distribution plot."""
        if not self.project or not self.report_generator:
            QMessageBox.warning(self, "Error", "No project loaded.")
            return

        selected_ids = self._get_selected_image_ids(self.grain_size_image_list)
        if not selected_ids:
            QMessageBox.warning(self, "Error", "Please select at least one image.")
            return

        output_dir = self.grain_size_output_dir.text()
        if not output_dir:
            QMessageBox.warning(self, "Error", "Please select an output directory.")
            return

        try:
            # Generate the plot
            plot_path = self.report_generator.generate_grain_size_distribution_plot(
                image_ids=selected_ids,
                output_dir=output_dir
            )

            if plot_path and os.path.exists(plot_path):
                QMessageBox.information(
                    self, "Plot Generated",
                    f"Grain size distribution plot generated successfully.\n\nSaved to: {plot_path}"
                )
            else:
                QMessageBox.warning(self, "Error", "Failed to generate grain size plot.")
        except Exception as e:
            logger.exception("Error generating grain size plot")
            QMessageBox.critical(self, "Error", f"An error occurred: {str(e)}")

    def _generate_porosity_plot(self):
        """Generate a porosity comparison plot."""
        if not self.project or not self.report_generator:
            QMessageBox.warning(self, "Error", "No project loaded.")
            return

        selected_ids = self._get_selected_image_ids(self.porosity_image_list)
        if not selected_ids:
            QMessageBox.warning(self, "Error", "Please select at least one image.")
            return

        output_dir = self.porosity_output_dir.text()
        if not output_dir:
            QMessageBox.warning(self, "Error", "Please select an output directory.")
            return

        try:
            # Generate the plot
            plot_path = self.report_generator.generate_porosity_comparison_plot(
                image_ids=selected_ids,
                output_dir=output_dir
            )

            if plot_path and os.path.exists(plot_path):
                QMessageBox.information(
                    self, "Plot Generated",
                    f"Porosity comparison plot generated successfully.\n\nSaved to: {plot_path}"
                )
            else:
                QMessageBox.warning(self, "Error", "Failed to generate porosity plot.")
        except Exception as e:
            logger.exception("Error generating porosity plot")
            QMessageBox.critical(self, "Error", f"An error occurred: {str(e)}")

    def _update_export_image_list(self):
        """Update the export image list based on the selected analysis type."""
        if not self.project:
            return

        analysis_type = self._get_export_analysis_type_key()

        # Clear the list
        self.export_image_list.clear()

        # Add images that have the selected analysis type
        for image_id, image_info in self.project.images.items():
            if analysis_type in image_info.analysis_results:
                item = QListWidgetItem(f"{image_info.filename} (ID: {image_info.id})")
                item.setData(Qt.UserRole, image_id)
                self.export_image_list.addItem(item)

    def _get_export_analysis_type_key(self) -> str:
        """Convert the display analysis type to a key for the project."""
        analysis_type = self.export_analysis_type.currentText()

        if "Grain Size" in analysis_type:
            return "grain_size_analysis"
        elif "Porosity" in analysis_type or "Image Lab" in analysis_type:
            return "image_lab"
        return "unknown_analysis"

    def _export_to_csv(self):
        """Export analysis results to a CSV file."""
        if not self.project or not self.report_generator:
            QMessageBox.warning(self, "Error", "No project loaded.")
            return

        analysis_type = self._get_export_analysis_type_key()
        output_dir = self.export_output_dir.text()

        if not output_dir:
            QMessageBox.warning(self, "Error", "Please select an output directory.")
            return

        # Determine which images to include
        image_ids = None  # None means all images with this analysis type
        if not self.export_include_all.isChecked():
            image_ids = self._get_selected_image_ids(self.export_image_list)
            if not image_ids:
                QMessageBox.warning(self, "Error", "Please select at least one image.")
                return

        try:
            # Export to CSV
            csv_path = self.report_generator.export_results_to_csv(
                analysis_type=analysis_type,
                image_ids=image_ids,
                output_dir=output_dir
            )

            if csv_path and os.path.exists(csv_path):
                QMessageBox.information(
                    self, "Export Complete",
                    f"Analysis results exported successfully.\n\nSaved to: {csv_path}"
                )
            else:
                QMessageBox.warning(self, "Error", "Failed to export analysis results.")
        except Exception as e:
            logger.exception("Error exporting analysis results")
            QMessageBox.critical(self, "Error", f"An error occurred: {str(e)}")