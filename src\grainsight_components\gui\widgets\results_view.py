# gui/widgets/results_view.py

from typing import Optional
import logging
import locale
import numpy as np
import pandas as pd
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtCore import Qt, QItemSelectionModel, QItemSelection, Signal, Slot
from PySide6.QtGui import QStandardItemModel, QStandardItem, QIcon
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QTreeView, QAbstractItemView,
                               QPushButton, QHeaderView, QStyledItemDelegate,
                               QGroupBox, QHBoxLayout)

logger = logging.getLogger(__name__)

class CustomItemDelegate(QStyledItemDelegate):
    """Custom delegate for potential future drawing customization in the tree view."""
    # Inherits QStyledItemDelegate, currently using default painting logic
    pass

class ResultsViewWidget(QWidget):
    """
    Widget encapsulating the results tree view and associated controls (e.g., delete button).
    """
    # Signal emitted when the selection in the tree view changes.
    # Passes the set of selected DataFrame indices.
    selection_changed = Signal(set)

    # Signal emitted when the delete button is clicked.
    delete_requested = Signal(set) # Passes the set of selected DataFrame indices to delete

    def __init__(self, parent=None):
        super().__init__(parent)
        self._df = None # Internal storage for the dataframe
        self.selected_df_indices = set() # Track selection internally as well

        self.setup_ui()

    def setup_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0,0,0,0) # No margins for the container widget

        results_group = QGroupBox("Analysis Results")
        group_layout = QVBoxLayout(results_group)
        main_layout.addWidget(results_group)

        # --- Controls ---
        control_layout = QHBoxLayout()
        self.delete_button = QPushButton(QIcon("gui/assets/icons/delete.png"), " Delete Selected") # Use relative path adjusted for runtime
        # Consider using resource_path from gui.utils if available
        self.delete_button.setToolTip("Remove the selected grains from the results and visualization")
        self.delete_button.clicked.connect(self._on_delete_clicked)
        self.delete_button.setEnabled(False) # Disabled initially
        control_layout.addWidget(self.delete_button)
        control_layout.addStretch()
        group_layout.addLayout(control_layout)

        # --- Tree View ---
        self.result_tree = QTreeView()
        self.result_tree.setSortingEnabled(True)
        self.result_tree.setAlternatingRowColors(True)
        self.result_tree.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.result_tree.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.result_tree.setItemDelegate(CustomItemDelegate(self.result_tree))
        group_layout.addWidget(self.result_tree)

        self.tree_model = QStandardItemModel()
        self.result_tree.setModel(self.tree_model)
        self.setup_result_tree_headers()

        # Connect treeview selection change internal handler
        self.result_tree.selectionModel().selectionChanged.connect(self._on_tree_selection_changed)


    def setup_result_tree_headers(self, columns=None):
        """Configures the columns and headers for the result tree."""
        if columns is None:
             # Define default/expected columns if none provided
             columns = [
                 'Object ID', 'Area (µm²)', 'Length (µm)', 'Width (µm)', 'Perimeter (µm)',
                 'Elongation', 'Compactness', 'Roundness', 'Solidity', 'Convexity',
                 'Rectangularity', 'Circle-Equivalent Diameter (µm)',
                 'Center_X (px)', 'Center_Y (px)'
             ]
        self.tree_model.setColumnCount(len(columns))
        self.tree_model.setHorizontalHeaderLabels(columns)

        header = self.result_tree.header()
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        self.result_tree.setSortingEnabled(True)

    def populate(self, df: Optional[pd.DataFrame]):
        """Populates the result treeview with data from the DataFrame."""
        self._df = df # Store reference
        self.tree_model.removeRows(0, self.tree_model.rowCount()) # Clear existing items

        if df is None or df.empty:
            logger.info("No data to display in result tree.")
            self.setup_result_tree_headers() # Reset headers even if empty
            self.result_tree.header().setStretchLastSection(True)
            return

        # Ensure required 'Object ID' column exists (might have been added in core.analysis)
        if 'Object ID' not in df.columns:
             logger.warning("'Object ID' column missing from DataFrame, results view might be limited.")
             # Use index as fallback? This assumes index matches original annotation order.
             # df['Object ID'] = [f"Object_{idx}" for idx in df.index]
             # Determine headers from the df itself if standard ones fail
             headers = df.columns.tolist()
             self.setup_result_tree_headers(headers) # Setup headers based on actual df
        else:
             # Use predefined headers if possible
             current_headers = [self.tree_model.horizontalHeaderItem(i).text() for i in range(self.tree_model.columnCount())]
             # Check if df columns match headers, adjust headers if necessary
             if not all(h in df.columns for h in current_headers):
                  logger.warning("DataFrame columns mismatch predefined headers. Adjusting headers.")
                  self.setup_result_tree_headers(df.columns.tolist()) # Use actual df columns
             headers = [self.tree_model.horizontalHeaderItem(i).text() for i in range(self.tree_model.columnCount())]


        logger.info(f"Displaying {len(df)} results in tree view.")

        # Block signals during population for performance
        selection_model = self.result_tree.selectionModel()
        if selection_model: selection_model.blockSignals(True)
        self.tree_model.blockSignals(True)

        # Determine locale decimal separator once
        decimal_sep = locale.localeconv().get('decimal_point', '.')

        for df_index, row in df.iterrows():
            items = []
            for header in headers:
                if header in row:
                    value = row[header]
                    try:
                        # Format numeric types using locale settings
                        if isinstance(value, (float, np.floating)):
                            # Format with 3 decimal places and locale separator
                            formatted_value = locale.format_string("%.3f", value, grouping=True)
                        elif isinstance(value, (int, np.integer)):
                             formatted_value = locale.format_string("%d", value, grouping=True)
                        else:
                            formatted_value = str(value) # Use string for others
                    except (TypeError, ValueError):
                         formatted_value = str(value) # Fallback to string on formatting error

                    item = QStandardItem(formatted_value)

                    # Set alignment based on original type before formatting
                    is_numeric_col = pd.api.types.is_numeric_dtype(df[header].dtype) and header != 'Object ID'
                    item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter if is_numeric_col else Qt.AlignLeft | Qt.AlignVCenter)

                    item.setData(df_index, Qt.UserRole) # Store the original DataFrame index
                    item.setEditable(False)
                    items.append(item)
                else:
                     items.append(QStandardItem("")) # Add empty item if column missing in this row

            self.tree_model.appendRow(items)

        # Unblock signals
        self.tree_model.blockSignals(False)
        if selection_model: selection_model.blockSignals(False)

        # Resize columns after populating
        logger.debug("Resizing tree view columns to contents.")
        for i in range(self.tree_model.columnCount()):
            self.result_tree.resizeColumnToContents(i)
        self.result_tree.header().setStretchLastSection(False) # Turn off stretch first
        self.result_tree.header().setStretchLastSection(True)  # Turn it back on

        # Clear internal selection state when repopulating
        self.selected_df_indices = set()
        self.update_delete_button_state() # Update button based on new state

        logger.debug("Finished populating result tree.")

    def clear(self):
        """Clears the results view."""
        self.populate(None)

    @Slot(QItemSelection, QItemSelection)
    def _on_tree_selection_changed(self, selected, deselected):
        """Internal slot to handle tree selection and emit simplified signal."""
        current_selection_model = self.result_tree.selectionModel()
        if not current_selection_model: return

        selected_rows = current_selection_model.selectedRows() # QModelIndex list for first column

        new_selection_indices = set()
        for model_index in selected_rows:
             # Get the original DataFrame index stored in the item's data
             item = self.tree_model.itemFromIndex(model_index)
             if item:
                  df_index = item.data(Qt.UserRole)
                  if df_index is not None:
                       new_selection_indices.add(df_index)

        # Update internal state and emit signal only if changed
        if new_selection_indices != self.selected_df_indices:
            self.selected_df_indices = new_selection_indices
            logger.debug(f"ResultsView selection changed to indices: {self.selected_df_indices}")
            self.update_delete_button_state()
            self.selection_changed.emit(self.selected_df_indices) # Emit the signal

    def set_selected_indices(self, indices_to_select: set):
        """Programmatically sets the selection in the tree view."""
        if not isinstance(indices_to_select, set):
            logger.error("set_selected_indices requires a set.")
            return

        if indices_to_select == self.selected_df_indices:
            # logger.debug("set_selected_indices: Selection unchanged, skipping update.")
            return # Avoid redundant updates and signal loops

        logger.debug(f"Setting results view selection programmatically to: {indices_to_select}")
        self.selected_df_indices = indices_to_select # Update internal state first

        selection_model = self.result_tree.selectionModel()
        if not selection_model: return

        # Block signals to prevent feedback loop
        selection_model.blockSignals(True)
        selection_model.clearSelection() # Clear existing selection

        items_selected = False
        first_index_to_scroll = None

        # Iterate through the model rows to find matching items
        for row in range(self.tree_model.rowCount()):
            item = self.tree_model.item(row, 0) # Check first column item
            if item:
                 df_index = item.data(Qt.UserRole)
                 if df_index is not None and df_index in self.selected_df_indices:
                      model_index = self.tree_model.index(row, 0)
                      # Create a QItemSelection for the entire row
                      start_index = model_index
                      end_index = model_index.siblingAtColumn(self.tree_model.columnCount() - 1)
                      row_selection = QItemSelection(start_index, end_index)
                      selection_model.select(row_selection, QItemSelectionModel.Select | QItemSelectionModel.Rows)
                      items_selected = True
                      if first_index_to_scroll is None:
                          first_index_to_scroll = start_index # Store first match for scrolling

        # Scroll to the first selected item
        if first_index_to_scroll:
            self.result_tree.scrollTo(first_index_to_scroll, QAbstractItemView.PositionAtCenter)

        # Unblock signals
        selection_model.blockSignals(False)
        self.update_delete_button_state() # Update button state

    def update_delete_button_state(self):
        """Enables/disables the delete button based on selection."""
        has_selection = bool(self.selected_df_indices)
        self.delete_button.setEnabled(has_selection)

    @Slot()
    def _on_delete_clicked(self):
        """Emits the delete_requested signal with the current selection."""
        if self.selected_df_indices:
            logger.debug(f"Delete requested for indices: {self.selected_df_indices}")
            self.delete_requested.emit(self.selected_df_indices)