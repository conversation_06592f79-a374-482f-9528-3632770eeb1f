
# -*- mode: python ; coding: utf-8 -*-

import sys
import os

block_cipher = None

# Add the src directory to the path
sys.path.append(os.path.join(os.path.abspath('.'), 'src'))

a = Analysis(
    ['main.py'],
    pathex=[os.path.abspath('.'), os.path.join(os.path.abspath('.'), 'src')],
    binaries=[],
    datas=[
        ('src/gui/styles', 'src/gui/styles'),
        ('src/gui/icons', 'src/gui/icons'),
        ('src/grainsight_components/models', 'src/grainsight_components/models'),
        ('src/grainsight_components/weights', 'src/grainsight_components/weights'),
        ('src/detectron2', 'detectron2'),
    , ('torch_numpy_replacement', 'torch_numpy_replacement')],
    hiddenimports=[
        'PySide6.QtCore',
        'PySide6.QtGui',
        'PySide6.QtWidgets',
        'numpy',
        'PIL',
        'cv2',
        'torch',
        'torchvision',
        'yaml',
        'ultralytics',
        'scipy.stats',
        'scipy.stats._distn_infrastructure',
        'xgboost',
        'detectron2',
    , 'torch._numpy', 'torch._numpy._ndarray', 'torch._numpy._ufuncs'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=['hooks/rthooks/pyi_rth_torch_numpy.py'],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='VisionLab_Ai',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='VisionLab_Ai',
)
