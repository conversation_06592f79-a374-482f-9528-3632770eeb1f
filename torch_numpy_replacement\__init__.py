# Replacement for torch._numpy/__init__.py
import sys
import types

# Create a dummy _ndarray module
_ndarray = types.ModuleType('torch._numpy._ndarray')
sys.modules['torch._numpy._ndarray'] = _ndarray

# Add ndarray class to _ndarray module
class ndarray:
    def __init__(self, tensor=None):
        self.tensor = tensor
    
    def __getattr__(self, name):
        # For any attribute access, just return a dummy function
        def dummy(*args, **kwargs):
            return args[0] if args else None
        return dummy

# Add ndarray to _ndarray module
_ndarray.ndarray = ndarray

# Create a dummy _ufuncs module with all required functions
_ufuncs = types.ModuleType('torch._numpy._ufuncs')
sys.modules['torch._numpy._ufuncs'] = _ufuncs

# Define all the binary ufunc names
binary_ufunc_names = [
    "add", "subtract", "multiply", "divide", "logaddexp", "logaddexp2", 
    "true_divide", "floor_divide", "power", "remainder", "mod", "fmod", 
    "divmod", "gcd", "lcm", "maximum", "minimum", "fmax", "fmin", 
    "copysign", "nextafter", "ldexp", "hypot"
]

# Add all binary ufuncs to the module
for name in binary_ufunc_names:
    def make_dummy(name):
        def dummy(*args, **kwargs):
            return args[0] if args else None
        dummy.__name__ = name
        return dummy
    
    setattr(_ufuncs, name, make_dummy(name))

# Export the modules
__all__ = ['ndarray']
from ._ndarray import ndarray
