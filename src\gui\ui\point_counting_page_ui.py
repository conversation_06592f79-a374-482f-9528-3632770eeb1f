# src/gui/ui/point_counting_page_ui.py

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
        QGroupBox, QFrame, QSlider, QSpinBox, QComboBox, QFormLayout, QCheckBox, QRadioButton,
        QButtonGroup, QSplitter, QSizePolicy, QTabWidget, QTextEdit)
from PySide6.QtCore import Qt, QSize

from src.widgets.scrollable_frame import ScrollableFrame
from src.widgets.pixmap_view import QPixmapView
from src.widgets.point_counting_gallery import PointCountingGallery
from src.widgets.class_percentages_widget import ClassPercentagesWidget
from src.widgets.vertical_class_selector import VerticalClassSelector
from src.widgets.clickable_combo_box import ClickableComboBox

class PointCountingPageUI:
    """Class for creating and managing the point counting page UI (Redesigned)."""

    def setup_point_counting_page(self):
        """Sets up the Point Counting page with a new layout."""
        self.point_counting_page = QWidget()
        point_counting_layout = QHBoxLayout(self.point_counting_page)
        point_counting_layout.setContentsMargins(5, 5, 5, 5) # Reduced margins
        point_counting_layout.setSpacing(5) # Reduced spacing
        self.stacked_widget.addTab(self.point_counting_page, "Point Counting")

        # Create main horizontal splitter for resizable panels
        self.point_counting_main_splitter = QSplitter(Qt.Orientation.Horizontal)
        # Style the splitter for better visibility
        self.point_counting_main_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #d0d0d0;
                border: 1px solid #a0a0a0;
                border-radius: 2px;
                margin: 2px;
            }
            QSplitter::handle:hover {
                background-color: #b0b0b0;
            }
            QSplitter::handle:pressed {
                background-color: #909090;
            }
        """)
        point_counting_layout.addWidget(self.point_counting_main_splitter)

        # --- Left Controls Panel ---
        self.point_counting_controls_panel = ScrollableFrame()
        self.point_counting_controls_panel.setMinimumWidth(300) # Minimum width instead of fixed
        self.point_counting_controls_panel.setMaximumWidth(600) # Maximum width to prevent over-expansion
        self.point_counting_controls_panel.setObjectName("pointCountingControlsPanel")
        self.point_counting_controls_panel.setStyleSheet("") # Add a visual separator
        controls_panel_layout = QVBoxLayout(self.point_counting_controls_panel.get_content_frame())
        controls_panel_layout.setContentsMargins(5, 5, 5, 5) # Reduced margins
        controls_panel_layout.setSpacing(6) # Reduced spacing
        self.point_counting_main_splitter.addWidget(self.point_counting_controls_panel)
        self.setup_point_counting_controls(controls_panel_layout) # Add Controls to the panel

        # --- Main Content Area (Image + Gallery) ---
        main_content_widget = QWidget()
        main_content_layout = QVBoxLayout(main_content_widget)
        main_content_layout.setContentsMargins(0, 0, 0, 0)
        main_content_layout.setSpacing(5)
        self.point_counting_main_splitter.addWidget(main_content_widget) # Add to splitter

        # --- Image Display Area ---
        self.point_counting_image_container = QFrame() # Use QFrame for potential styling
        self.point_counting_image_container.setFrameShape(QFrame.StyledPanel)
        self.point_counting_image_container.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        image_container_layout = QVBoxLayout(self.point_counting_image_container)
        image_container_layout.setContentsMargins(0, 0, 0, 0)

        self.point_counting_image_view = QPixmapView()
        self.point_counting_image_view.setMinimumSize(400, 300)
        image_container_layout.addWidget(self.point_counting_image_view)

        # Vertical class selector (embedded in the image container)
        self.vertical_class_selector = VerticalClassSelector(self.point_counting_image_container)
        self.vertical_class_selector.hide() # Initially hidden
        # We'll position it in the handler based on container size later if needed,
        # or better, attach it to a corner dynamically. For now, top-left.
        self.vertical_class_selector.move(10, 10)

        main_content_layout.addWidget(self.point_counting_image_container, stretch=1) # Image takes most vertical space

        # --- Gallery Area ---
        gallery_container = QFrame()
        gallery_container.setFrameShape(QFrame.StyledPanel)
        gallery_container.setFixedHeight(190) # Fixed height for gallery
        gallery_container.setStyleSheet("")
        gallery_layout = QVBoxLayout(gallery_container)
        gallery_layout.setContentsMargins(0, 0, 0, 0)

        # Gallery Label
        gallery_header = QLabel("Image Gallery")
        gallery_header.setStyleSheet("padding: 2px 5px;")
        gallery_layout.addWidget(gallery_header)

        # Gallery Widget
        self.point_counting_gallery = PointCountingGallery()
        gallery_layout.addWidget(self.point_counting_gallery)

        # Clear Image Gallery button
        self.clear_point_counting_gallery_button = QPushButton("Clear Image Gallery")
        self.clear_point_counting_gallery_button.setStyleSheet("padding: 6px; margin-top: 5px;")
        gallery_layout.addWidget(self.clear_point_counting_gallery_button)

        main_content_layout.addWidget(gallery_container, stretch=0) # Gallery has fixed height

        # --- Right Panel (Classes and Results) ---
        self.right_panel = ScrollableFrame()
        self.right_panel.setMinimumWidth(280) # Increased minimum width for better display
        self.right_panel.setMaximumWidth(600) # Increased maximum width for flexibility
        self.right_panel.setObjectName("pointCountingRightPanel")
        self.right_panel.setStyleSheet("")
        right_panel_layout = QVBoxLayout(self.right_panel.get_content_frame())
        right_panel_layout.setContentsMargins(8, 8, 8, 8)
        right_panel_layout.setSpacing(10)
        self.point_counting_main_splitter.addWidget(self.right_panel)
        
        # Set initial splitter sizes with better proportions (left: 350px, center: remaining, right: 320px)
        self.point_counting_main_splitter.setSizes([350, 700, 320])
        # Allow all panels to be resizable
        self.point_counting_main_splitter.setStretchFactor(0, 1)  # Left panel can stretch
        self.point_counting_main_splitter.setStretchFactor(1, 3)  # Center area gets most stretch
        self.point_counting_main_splitter.setStretchFactor(2, 1)  # Right panel can stretch moderately
        
        # Set splitter handle width for better visibility
        self.point_counting_main_splitter.setHandleWidth(6)
        
        # Enable collapsible splitter handles
        self.point_counting_main_splitter.setChildrenCollapsible(True)

        # Common GroupBox style - removed hardcoded background colors and font styles
        group_box_style = """
            QGroupBox {
                border-radius: 6px;
                margin-top: 6px;
                padding: 10px 5px 5px 5px; /* top, right, bottom, left */
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                left: 10px;
                padding: 0 3px 0 3px;
            }
        """

        # --- Classes Section (Moved to Right Panel) ---
        classes_group = QGroupBox("Classes")
        classes_group.setStyleSheet(group_box_style)
        classes_layout = QVBoxLayout(classes_group)
        classes_layout.setSpacing(6)

        # Create a horizontal layout for the class name display and selector
        top_class_layout = QHBoxLayout()

        # Add a prominent class name display
        self.current_class_name_label = QLabel("None")
        self.current_class_name_label.setAlignment(Qt.AlignCenter)
        # Style will be set dynamically in the handler based on theme
        self.current_class_name_label.setMinimumWidth(120)
        top_class_layout.addWidget(self.current_class_name_label, 1)  # 1 = stretch factor

        # Create a frame to hold the class selector components
        self.class_selector_frame = QFrame()
        self.class_selector_frame.setMinimumHeight(30)
        self.class_selector_frame.setFrameShape(QFrame.StyledPanel)
        self.class_selector_frame.setFrameShadow(QFrame.Sunken)
        # Style will be set dynamically in the handler based on theme

        # Create layout for the frame
        class_selector_layout = QHBoxLayout(self.class_selector_frame)
        class_selector_layout.setSpacing(0)
        class_selector_layout.setContentsMargins(5, 0, 0, 0)

        # Create a label to display the current class name and icon
        self.class_display_label = QLabel("Select a class (scroll to change)")
        self.class_display_label.setToolTip("Use mouse wheel to scroll through classes")
        # Style will be set dynamically in the handler based on theme

        # Use standard QComboBox but make it invisible (0 width)
        self.class_selector = QComboBox()
        self.class_selector.setToolTip("Select the current class for assignment")
        self.class_selector.setIconSize(QSize(16, 16))
        self.class_selector.setFixedWidth(0)  # Make it invisible but functional
        self.class_selector.setMaximumHeight(0)  # Hide it completely

        # Add a dedicated button to show the dropdown
        self.show_class_dropdown_button = QPushButton("▼")
        self.show_class_dropdown_button.setToolTip("Click to show class dropdown")
        self.show_class_dropdown_button.setCursor(Qt.PointingHandCursor)  # Show hand cursor on hover using Qt's native method
        self.show_class_dropdown_button.setFixedWidth(30)  # Make it a square button
        self.show_class_dropdown_button.setStyleSheet("""
            QPushButton {
                background-color: #0784b5;
                color: white;
                border: none;
                padding: 4px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #0a97cc;
            }
            QPushButton:pressed {
                background-color: #056a94;
            }
        """)

        # Add widgets to frame layout
        class_selector_layout.addWidget(self.class_display_label, 1)  # 1 = stretch factor
        class_selector_layout.addWidget(self.class_selector, 0)  # 0 = no stretch, hidden
        class_selector_layout.addWidget(self.show_class_dropdown_button, 0)  # 0 = no stretch

        # Add the selector frame to the top layout
        top_class_layout.addWidget(self.class_selector_frame, 2)  # 2 = more stretch than the label

        # Add the top layout to the main layout
        classes_layout.addLayout(top_class_layout)

        # Class Color Indicator and Change Button
        class_color_layout = QHBoxLayout()
        self.class_color_indicator = QFrame()
        self.class_color_indicator.setFixedSize(20, 20)
        self.class_color_indicator.setFrameShape(QFrame.StyledPanel)
        self.class_color_indicator.setFrameShadow(QFrame.Sunken)
        self.class_color_indicator.setStyleSheet("border: 1px solid #555;")
        self.change_class_color_button = QPushButton("Change Color")
        class_color_layout.addWidget(QLabel("Color:"))
        class_color_layout.addWidget(self.class_color_indicator)
        class_color_layout.addWidget(self.change_class_color_button, stretch=1)
        classes_layout.addLayout(class_color_layout)

        # Class Management Buttons
        class_buttons_layout = QHBoxLayout()
        self.add_class_button = QPushButton("Add")
        self.remove_class_button = QPushButton("Remove")
        self.rename_class_button = QPushButton("Rename")
        class_buttons_layout.addWidget(self.add_class_button)
        class_buttons_layout.addWidget(self.remove_class_button)
        class_buttons_layout.addWidget(self.rename_class_button)
        classes_layout.addLayout(class_buttons_layout)

        # Quick Selector Toggle
        self.show_class_selector_button = QPushButton("Toggle Quick Selector")
        self.show_class_selector_button.setToolTip("Show/Hide floating class selector on image")
        self.show_class_selector_button.setCheckable(True) # Make it checkable
        classes_layout.addWidget(self.show_class_selector_button)

        right_panel_layout.addWidget(classes_group)

        # --- Summary Section (Moved to Right Panel) ---
        self.results_summary_group = QGroupBox("Summary")
        self.results_summary_group.setStyleSheet(group_box_style)
        # --- Summary Section (Moved to Right Panel) ---
        self.results_summary_group = QGroupBox("Summary")
        self.results_summary_group.setStyleSheet(group_box_style)
        results_summary_layout = QVBoxLayout(self.results_summary_group)
        results_summary_layout.setContentsMargins(5, 15, 5, 5)  # Add some margin
        self.summary_label = QTextEdit()  # Use QTextEdit for better formatting/scrolling
        self.summary_label.setReadOnly(True)
        self.summary_label.setPlaceholderText("No points classified yet.")
        self.summary_label.setMinimumHeight(80)  # Reasonable minimum height
        self.summary_label.setStyleSheet("padding: 5px; border-radius: 4px;")
        self.summary_label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        results_summary_layout.addWidget(self.summary_label)
        right_panel_layout.addWidget(self.results_summary_group, stretch=1)  # Balanced space for summary

        # --- Class Distribution Section (Moved to Right Panel) ---
        self.results_percentages_group = QGroupBox("Class Distribution")
        self.results_percentages_group.setStyleSheet(group_box_style)
        results_percentages_layout = QVBoxLayout(self.results_percentages_group)
        self.point_counting_percentages_widget = ClassPercentagesWidget()
        self.point_counting_percentages_widget.setMinimumHeight(150)  # Reasonable minimum height for class distribution
        self.point_counting_percentages_widget.setSizePolicy(
            QSizePolicy.Policy.Expanding,
            QSizePolicy.Policy.Expanding
        )
        results_percentages_layout.addWidget(self.point_counting_percentages_widget)
        right_panel_layout.addWidget(self.results_percentages_group, stretch=1)  # Balanced space for class distribution

        # Add stretch to push everything to the top
        right_panel_layout.addStretch()

    def setup_point_counting_controls(self, layout):
        """Sets up the controls in the left panel."""

        # Common GroupBox style - removed hardcoded background colors and font styles
        group_box_style = """
            QGroupBox {
                border-radius: 6px;
                margin-top: 6px;
                padding: 10px 5px 5px 5px; /* top, right, bottom, left */
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                left: 10px;
                padding: 0 3px 0 3px;
            }
        """

        # --- Point Counting Method ---
        methods_group = QGroupBox("Method")
        methods_group.setStyleSheet(group_box_style)
        methods_layout = QVBoxLayout(methods_group)
        methods_layout.setSpacing(5)

        self.grid_counting_radio = QRadioButton("Grid")
        self.random_counting_radio = QRadioButton("Random")
        self.custom_counting_radio = QRadioButton("Manual Click") # Renamed for clarity
        self.counting_method_group = QButtonGroup(self.point_counting_page) # Parent explicitly
        self.counting_method_group.addButton(self.grid_counting_radio, 0)
        self.counting_method_group.addButton(self.random_counting_radio, 1)
        self.counting_method_group.addButton(self.custom_counting_radio, 2)
        methods_layout.addWidget(self.grid_counting_radio)
        methods_layout.addWidget(self.random_counting_radio)
        methods_layout.addWidget(self.custom_counting_radio)
        self.grid_counting_radio.setChecked(True)
        layout.addWidget(methods_group)

        # --- Parameters ---
        # Grid Parameters (initially visible)
        self.grid_params_group = QGroupBox("Grid Parameters")
        self.grid_params_group.setStyleSheet(group_box_style)
        grid_params_layout = QFormLayout(self.grid_params_group)
        grid_params_layout.setLabelAlignment(Qt.AlignLeft)
        grid_params_layout.setFormAlignment(Qt.AlignLeft | Qt.AlignTop)
        grid_params_layout.setHorizontalSpacing(8)
        grid_params_layout.setVerticalSpacing(5)

        self.grid_size_spinner = QSpinBox()
        self.grid_size_spinner.setRange(5, 50); self.grid_size_spinner.setValue(10)
        grid_params_layout.addRow("Grid Size (NxN):", self.grid_size_spinner)

        self.grid_offset_x_spinner = QSpinBox()
        self.grid_offset_x_spinner.setRange(-50, 50); self.grid_offset_x_spinner.setValue(0)
        grid_params_layout.addRow("X Offset (%):", self.grid_offset_x_spinner)

        self.grid_offset_y_spinner = QSpinBox()
        self.grid_offset_y_spinner.setRange(-50, 50); self.grid_offset_y_spinner.setValue(0)
        grid_params_layout.addRow("Y Offset (%):", self.grid_offset_y_spinner)

        self.grid_color_button = QPushButton() # No text, just color background
        self.grid_color_button.setFixedSize(80, 24)
        self.grid_color_button.setToolTip("Select Grid Line Color")
        grid_params_layout.addRow("Grid Color:", self.grid_color_button)

        self.grid_opacity_slider = QSlider(Qt.Horizontal)
        self.grid_opacity_slider.setRange(10, 100); self.grid_opacity_slider.setValue(70)
        grid_params_layout.addRow("Grid Opacity:", self.grid_opacity_slider)

        self.show_grid_lines_checkbox = QCheckBox("Show Grid Lines")
        self.show_grid_lines_checkbox.setChecked(True)
        grid_params_layout.addRow("", self.show_grid_lines_checkbox)
        layout.addWidget(self.grid_params_group)

        # Random Parameters (initially hidden)
        self.random_params_group = QGroupBox("Random Parameters")
        self.random_params_group.setStyleSheet(group_box_style)
        random_params_layout = QFormLayout(self.random_params_group)
        random_params_layout.setLabelAlignment(Qt.AlignLeft)
        random_params_layout.setFormAlignment(Qt.AlignLeft | Qt.AlignTop)
        random_params_layout.setHorizontalSpacing(8)
        random_params_layout.setVerticalSpacing(5)

        self.random_points_spinner = QSpinBox()
        self.random_points_spinner.setRange(10, 10000); self.random_points_spinner.setValue(100)
        random_params_layout.addRow("Number of Points:", self.random_points_spinner)

        self.random_seed_spinner = QSpinBox()
        self.random_seed_spinner.setRange(0, 99999); self.random_seed_spinner.setValue(42)
        random_params_layout.addRow("Random Seed:", self.random_seed_spinner)
        layout.addWidget(self.random_params_group)
        self.random_params_group.setVisible(False) # Initially hidden

        # Classes section has been moved to the right panel

        # --- Point Navigation & Classification ---
        point_nav_group = QGroupBox("Point Navigation & Classification")
        point_nav_group.setStyleSheet(group_box_style)
        point_nav_layout = QVBoxLayout(point_nav_group)
        point_nav_layout.setSpacing(6)

        nav_buttons_layout = QHBoxLayout()
        self.prev_point_button = QPushButton("Previous (Bksp)")
        self.prev_point_button.setToolTip("Go to previous point (Backspace key)")
        self.next_point_button = QPushButton("Next (Space)")
        self.next_point_button.setToolTip("Go to next point (Spacebar)")
        nav_buttons_layout.addWidget(self.prev_point_button)
        nav_buttons_layout.addWidget(self.next_point_button)
        point_nav_layout.addLayout(nav_buttons_layout)

        # Auto-zoom toggle
        self.auto_zoom_checkbox = QCheckBox("Auto-Zoom to Points")
        self.auto_zoom_checkbox.setChecked(True)
        self.auto_zoom_checkbox.setToolTip("Automatically zoom in on points when navigating")
        point_nav_layout.addWidget(self.auto_zoom_checkbox)

        keyboard_info = QLabel("<small><i>Click point or use keys [1-9] to classify.<br>In <b>Manual Click</b> mode, click anywhere on the image to add a point with the currently selected class.</i></small>")
        keyboard_info.setTextFormat(Qt.RichText)
        keyboard_info.setWordWrap(True)
        keyboard_info.setStyleSheet("padding: 5px; border-radius: 4px;")
        point_nav_layout.addWidget(keyboard_info)
        layout.addWidget(point_nav_group)

        # --- Actions & Status ---
        actions_group = QGroupBox("Actions & Status")
        actions_group.setStyleSheet(group_box_style)
        actions_layout = QVBoxLayout(actions_group)
        actions_layout.setSpacing(8)

        # Generate/Clear Buttons
        gen_clear_layout = QHBoxLayout()
        self.generate_points_button = QPushButton("Generate Points")
        self.generate_points_button.setStyleSheet("padding: 6px;")
        self.clear_points_button = QPushButton("Clear Points")
        self.clear_points_button.setStyleSheet("padding: 6px;")
        gen_clear_layout.addWidget(self.generate_points_button)
        gen_clear_layout.addWidget(self.clear_points_button)
        actions_layout.addLayout(gen_clear_layout)

        # Save/Export Buttons
        save_export_layout = QHBoxLayout()
        self.save_results_button = QPushButton("Save Results")
        self.save_results_button.setToolTip("Save summary report (Not implemented yet)")
        self.export_points_button = QPushButton("Export Points (CSV)")
        self.export_points_button.setToolTip("Export point coordinates and classes to CSV")
        save_export_layout.addWidget(self.save_results_button)
        save_export_layout.addWidget(self.export_points_button)
        actions_layout.addLayout(save_export_layout)
        self.save_results_button.setEnabled(False) # Disabled for now

        # Semi-Auto Classification Buttons
        semi_auto_layout = QHBoxLayout()

        self.semi_auto_classify_button = QPushButton("Semi-Auto Classify")
        self.semi_auto_classify_button.setToolTip("Automatically classify points based on manually classified examples")
        self.semi_auto_classify_button.setStyleSheet("padding: 6px;")

        self.undo_auto_classify_button = QPushButton("Undo Auto-Classification")
        self.undo_auto_classify_button.setToolTip("Revert all auto-classified points back to unclassified")
        self.undo_auto_classify_button.setStyleSheet("padding: 6px;")

        semi_auto_layout.addWidget(self.semi_auto_classify_button)
        semi_auto_layout.addWidget(self.undo_auto_classify_button)
        actions_layout.addLayout(semi_auto_layout)

        # Classifier Export/Import Buttons
        classifier_layout = QHBoxLayout()

        self.export_classifier_button = QPushButton("Export Classifier")
        self.export_classifier_button.setToolTip("Export trained classifier to file for reuse")
        self.export_classifier_button.setStyleSheet("padding: 6px;")
        self.export_classifier_button.setEnabled(False)  # Initially disabled

        self.import_classifier_button = QPushButton("Import Classifier")
        self.import_classifier_button.setToolTip("Import pre-trained classifier from file")
        self.import_classifier_button.setStyleSheet("padding: 6px;")

        classifier_layout.addWidget(self.export_classifier_button)
        classifier_layout.addWidget(self.import_classifier_button)
        actions_layout.addLayout(classifier_layout)

        # Use Pre-trained Classifier Button
        self.use_pretrained_button = QPushButton("Use Pre-trained Classifier")
        self.use_pretrained_button.setToolTip("Apply loaded pre-trained classifier to classify all unclassified points")
        self.use_pretrained_button.setStyleSheet("padding: 6px;")
        self.use_pretrained_button.setEnabled(False)  # Initially disabled
        actions_layout.addWidget(self.use_pretrained_button)

        # Status Label
        self.status_label = QLabel("Load images from Project Hub")
        self.status_label.setStyleSheet("padding: 5px; border-radius: 4px;")
        self.status_label.setTextFormat(Qt.RichText)
        self.status_label.setWordWrap(True)
        self.status_label.setMinimumHeight(30) # Reduced height
        actions_layout.addWidget(self.status_label)
        layout.addWidget(actions_group)

        # Add stretch to push controls to the top
        layout.addStretch()