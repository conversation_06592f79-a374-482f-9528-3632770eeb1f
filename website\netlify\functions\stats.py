#!/usr/bin/env python3
"""
Netlify Function for Vision Lab Stats API
Serverless function to provide public statistics.
"""

import json
from datetime import datetime

def handler(event, context):
    """Netlify Function handler for stats"""
    
    # CORS headers
    headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Content-Type': 'application/json'
    }
    
    # Handle preflight requests
    if event['httpMethod'] == 'OPTIONS':
        return {
            'statusCode': 200,
            'headers': headers,
            'body': ''
        }
    
    # Only allow GET requests
    if event['httpMethod'] != 'GET':
        return {
            'statusCode': 405,
            'headers': headers,
            'body': json.dumps({
                'success': False,
                'error': 'Method not allowed'
            })
        }
    
    try:
        # In production, fetch real stats from database
        # For demo, return mock data
        stats = {
            'success': True,
            'data': {
                'total_signups': 1247,
                'active_beta_users': 89,
                'countries_represented': 23,
                'research_institutions': 45,
                'last_updated': datetime.now().isoformat(),
                'beta_release_date': '2024-03-15',
                'features_count': 12,
                'supported_formats': 8
            },
            'message': 'Statistics retrieved successfully'
        }
        
        return {
            'statusCode': 200,
            'headers': headers,
            'body': json.dumps(stats)
        }
        
    except Exception as e:
        print(f"Error in stats function: {e}")
        return {
            'statusCode': 500,
            'headers': headers,
            'body': json.dumps({
                'success': False,
                'error': 'Internal server error'
            })
        }

# For local testing
if __name__ == '__main__':
    test_event = {
        'httpMethod': 'GET',
        'headers': {}
    }
    
    result = handler(test_event, {})
    print(json.dumps(result, indent=2))