import logging
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QColor, QIcon, QPixmap
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                              QListWidget, QListWidgetItem, QLineEdit, QColorDialog,
                              QMessageBox, QWidget, QFormLayout, QSpinBox)

logger = logging.getLogger(__name__)

class ClassManagementDialog(QDialog):
    """Dialog for managing class names and colors."""
    
    class_data_changed = Signal(dict)  # Signal emitted when class data changes
    
    def __init__(self, parent=None, class_names=None, class_colors=None):
        super().__init__(parent)
        self.setWindowTitle("Manage Classes")
        self.setMinimumWidth(500)
        self.setMinimumHeight(400)
        
        # Initialize class data
        self.class_names = class_names or {}
        self.class_colors = class_colors or {}
        
        # Set up the UI
        self.setup_ui()
        
        # Populate the list with existing classes
        self.populate_class_list()
        
    def setup_ui(self):
        """Set up the dialog UI."""
        main_layout = QVBoxLayout(self)
        
        # Class list
        self.class_list = QListWidget()
        self.class_list.setSelectionMode(QListWidget.SingleSelection)
        self.class_list.currentItemChanged.connect(self.on_class_selected)
        main_layout.addWidget(QLabel("Classes:"))
        main_layout.addWidget(self.class_list)
        
        # Class editing form
        form_widget = QWidget()
        form_layout = QFormLayout(form_widget)
        
        # Class ID
        self.class_id_spin = QSpinBox()
        self.class_id_spin.setMinimum(1)
        self.class_id_spin.setMaximum(100)
        form_layout.addRow("Class ID:", self.class_id_spin)
        
        # Class name
        self.class_name_edit = QLineEdit()
        form_layout.addRow("Class Name:", self.class_name_edit)
        
        # Class color
        color_layout = QHBoxLayout()
        self.color_preview = QLabel()
        self.color_preview.setFixedSize(24, 24)
        self.color_preview.setStyleSheet("background-color: #FF0000; border: 1px solid #000000;")
        self.current_color = QColor(255, 0, 0)  # Default to red
        
        self.color_button = QPushButton("Choose Color")
        self.color_button.clicked.connect(self.choose_color)
        
        color_layout.addWidget(self.color_preview)
        color_layout.addWidget(self.color_button)
        color_layout.addStretch()
        
        form_layout.addRow("Class Color:", color_layout)
        
        main_layout.addWidget(form_widget)
        
        # Buttons for adding, updating, and removing classes
        buttons_layout = QHBoxLayout()
        
        self.add_button = QPushButton("Add Class")
        self.add_button.clicked.connect(self.add_class)
        
        self.update_button = QPushButton("Update Class")
        self.update_button.clicked.connect(self.update_class)
        self.update_button.setEnabled(False)
        
        self.remove_button = QPushButton("Remove Class")
        self.remove_button.clicked.connect(self.remove_class)
        self.remove_button.setEnabled(False)
        
        buttons_layout.addWidget(self.add_button)
        buttons_layout.addWidget(self.update_button)
        buttons_layout.addWidget(self.remove_button)
        
        main_layout.addLayout(buttons_layout)
        
        # Dialog buttons
        dialog_buttons_layout = QHBoxLayout()
        
        self.ok_button = QPushButton("OK")
        self.ok_button.clicked.connect(self.accept)
        
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.reject)
        
        dialog_buttons_layout.addStretch()
        dialog_buttons_layout.addWidget(self.ok_button)
        dialog_buttons_layout.addWidget(self.cancel_button)
        
        main_layout.addLayout(dialog_buttons_layout)
        
    def populate_class_list(self):
        """Populate the class list with existing classes."""
        self.class_list.clear()
        
        # Sort classes by ID
        sorted_class_ids = sorted(self.class_names.keys())
        
        for class_id in sorted_class_ids:
            class_name = self.class_names[class_id]
            class_color = self.class_colors.get(class_id, QColor(255, 0, 0))  # Default to red
            
            item = QListWidgetItem(f"{class_id}: {class_name}")
            
            # Create a colored square icon
            pixmap = QPixmap(16, 16)
            pixmap.fill(class_color)
            item.setIcon(QIcon(pixmap))
            
            # Store the class ID as item data
            item.setData(Qt.UserRole, class_id)
            
            self.class_list.addItem(item)
    
    def on_class_selected(self, current, previous):
        """Handle class selection in the list."""
        if current is None:
            self.update_button.setEnabled(False)
            self.remove_button.setEnabled(False)
            return
        
        # Get the class ID from the item data
        class_id = current.data(Qt.UserRole)
        
        # Update the form with the selected class data
        self.class_id_spin.setValue(class_id)
        self.class_name_edit.setText(self.class_names[class_id])
        
        # Update color preview
        self.current_color = self.class_colors.get(class_id, QColor(255, 0, 0))
        self.update_color_preview()
        
        # Enable update and remove buttons
        self.update_button.setEnabled(True)
        self.remove_button.setEnabled(True)
    
    def choose_color(self):
        """Open a color dialog to choose a class color."""
        color = QColorDialog.getColor(self.current_color, self, "Choose Class Color")
        if color.isValid():
            self.current_color = color
            self.update_color_preview()
    
    def update_color_preview(self):
        """Update the color preview label."""
        self.color_preview.setStyleSheet(f"background-color: {self.current_color.name()}; border: 1px solid #000000;")
    
    def add_class(self):
        """Add a new class."""
        class_id = self.class_id_spin.value()
        class_name = self.class_name_edit.text().strip()
        
        if not class_name:
            QMessageBox.warning(self, "Warning", "Please enter a class name.")
            return
        
        if class_id in self.class_names:
            QMessageBox.warning(self, "Warning", f"Class ID {class_id} already exists. Please choose a different ID.")
            return
        
        # Add the new class
        self.class_names[class_id] = class_name
        self.class_colors[class_id] = self.current_color
        
        # Update the list
        self.populate_class_list()
        
        # Select the new class in the list
        for i in range(self.class_list.count()):
            item = self.class_list.item(i)
            if item.data(Qt.UserRole) == class_id:
                self.class_list.setCurrentItem(item)
                break
        
        # Emit the signal
        self.class_data_changed.emit(self.get_class_data())
        
        logger.info(f"Added class {class_id}: {class_name}")
    
    def update_class(self):
        """Update the selected class."""
        current_item = self.class_list.currentItem()
        if current_item is None:
            return
        
        old_class_id = current_item.data(Qt.UserRole)
        new_class_id = self.class_id_spin.value()
        class_name = self.class_name_edit.text().strip()
        
        if not class_name:
            QMessageBox.warning(self, "Warning", "Please enter a class name.")
            return
        
        if new_class_id != old_class_id and new_class_id in self.class_names:
            QMessageBox.warning(self, "Warning", f"Class ID {new_class_id} already exists. Please choose a different ID.")
            return
        
        # Remove the old class if the ID changed
        if new_class_id != old_class_id:
            del self.class_names[old_class_id]
            if old_class_id in self.class_colors:
                del self.class_colors[old_class_id]
        
        # Update the class
        self.class_names[new_class_id] = class_name
        self.class_colors[new_class_id] = self.current_color
        
        # Update the list
        self.populate_class_list()
        
        # Select the updated class in the list
        for i in range(self.class_list.count()):
            item = self.class_list.item(i)
            if item.data(Qt.UserRole) == new_class_id:
                self.class_list.setCurrentItem(item)
                break
        
        # Emit the signal
        self.class_data_changed.emit(self.get_class_data())
        
        logger.info(f"Updated class {old_class_id} to {new_class_id}: {class_name}")
    
    def remove_class(self):
        """Remove the selected class."""
        current_item = self.class_list.currentItem()
        if current_item is None:
            return
        
        class_id = current_item.data(Qt.UserRole)
        
        # Confirm deletion
        result = QMessageBox.question(
            self,
            "Confirm Deletion",
            f"Are you sure you want to remove class {class_id}: {self.class_names[class_id]}?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if result == QMessageBox.Yes:
            # Remove the class
            del self.class_names[class_id]
            if class_id in self.class_colors:
                del self.class_colors[class_id]
            
            # Update the list
            self.populate_class_list()
            
            # Clear the form
            self.class_id_spin.setValue(1)
            self.class_name_edit.clear()
            self.current_color = QColor(255, 0, 0)
            self.update_color_preview()
            
            # Disable update and remove buttons
            self.update_button.setEnabled(False)
            self.remove_button.setEnabled(False)
            
            # Emit the signal
            self.class_data_changed.emit(self.get_class_data())
            
            logger.info(f"Removed class {class_id}")
    
    def get_class_data(self):
        """Get the current class data."""
        return {
            'class_names': self.class_names.copy(),
            'class_colors': self.class_colors.copy()
        }
    
    def accept(self):
        """Handle dialog acceptance."""
        # Emit the signal one last time to ensure the latest data is sent
        self.class_data_changed.emit(self.get_class_data())
        super().accept()
