<!DOCTYPE html>

<html lang="en">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>Vision Lab - Advanced Computer Vision Solutions</title>
<link href="styles.css" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet"/>
</head>
<body>
<!-- Navigation -->
<nav class="navbar">
<div class="nav-container">
<a class="nav-logo" href="index.html">
<img src="logo.png" alt="Vision Lab Logo" class="logo">
<h2>Vision Lab</h2>
</a>
<ul class="nav-menu">
<li class="nav-item">
<a class="nav-link" href="home.html">Home</a>
</li>
<li class="nav-item">
<a class="nav-link" href="software.html">Software</a>
</li>
<li class="nav-item">
<a class="nav-link" href="research.html">Research</a>
</li>
<li class="nav-item">
<a class="nav-link" href="services.html">Services</a>
</li>
<li class="nav-item">
<a class="nav-link" href="publications.html">Publications</a>
</li>
<li class="nav-item">
<a class="nav-link" href="news.html">News</a>
</li>
<li class="nav-item">
<a class="nav-link btn-primary" href="waitlist.html">Join Beta</a>
</li>
</ul>
<div class="hamburger">
<span class="bar"></span>
<span class="bar"></span>
<span class="bar"></span>
</div>
</div>
</nav>
<!-- Hero Section -->
<section class="hero software-hero">
<div class="container">
<div class="hero-content">
<h1>VisionLab AI - Advanced Image Analysis Suite</h1>
<p class="hero-subtitle">Professional-grade computer vision software for advanced image analysis and segmentation across multiple scientific domains</p>
<div class="hero-image">
<svg width="800" height="400" viewBox="0 0 800 400" xmlns="http://www.w3.org/2000/svg">
<!-- Background -->
<rect width="800" height="400" fill="#f8fafc"/>
<!-- Main Interface Window -->
<rect x="50" y="50" width="700" height="300" rx="10" fill="#ffffff" stroke="#e2e8f0" stroke-width="2"/>
<!-- Title Bar -->
<rect x="50" y="50" width="700" height="40" rx="10" fill="#4a5568"/>
<circle cx="70" cy="70" r="6" fill="#fc8181"/>
<circle cx="90" cy="70" r="6" fill="#f6e05e"/>
<circle cx="110" cy="70" r="6" fill="#68d391"/>
<text x="130" y="75" fill="white" font-family="Inter" font-size="14" font-weight="500">VisionLab AI - Professional Image Analysis</text>
<!-- Sidebar -->
<rect x="60" y="100" width="150" height="240" fill="#f7fafc" stroke="#e2e8f0"/>
<text x="70" y="120" fill="#2d3748" font-family="Inter" font-size="12" font-weight="600">Analysis Modules</text>
<rect x="70" y="130" width="130" height="20" fill="#4299e1" rx="3"/>
<text x="75" y="142" fill="white" font-family="Inter" font-size="10">Unsupervised Segmentation</text>
<rect x="70" y="155" width="130" height="20" fill="#e2e8f0" rx="3"/>
<text x="75" y="167" fill="#4a5568" font-family="Inter" font-size="10">Advanced Segmentation</text>
<rect x="70" y="180" width="130" height="20" fill="#e2e8f0" rx="3"/>
<text x="75" y="192" fill="#4a5568" font-family="Inter" font-size="10">Trainable Segmentation</text>
<rect x="70" y="205" width="130" height="20" fill="#e2e8f0" rx="3"/>
<text x="75" y="217" fill="#4a5568" font-family="Inter" font-size="10">Point Counting</text>
<rect x="70" y="230" width="130" height="20" fill="#e2e8f0" rx="3"/>
<text x="75" y="242" fill="#4a5568" font-family="Inter" font-size="10">Grain Analysis</text>
<rect x="70" y="255" width="130" height="20" fill="#e2e8f0" rx="3"/>
<text x="75" y="267" fill="#4a5568" font-family="Inter" font-size="10">Batch Processing</text>
<rect x="70" y="280" width="130" height="20" fill="#e2e8f0" rx="3"/>
<text x="75" y="292" fill="#4a5568" font-family="Inter" font-size="10">Image Lab</text>
<rect x="70" y="305" width="130" height="20" fill="#e2e8f0" rx="3"/>
<text x="75" y="317" fill="#4a5568" font-family="Inter" font-size="10">AI Assistant</text>
<!-- Main Content Area -->
<rect x="220" y="100" width="520" height="240" fill="#ffffff" stroke="#e2e8f0"/>
<!-- Sample Image -->
<rect x="240" y="120" width="200" height="150" fill="#f7fafc" stroke="#cbd5e0"/>
<text x="330" y="200" fill="#718096" font-family="Inter" font-size="12" text-anchor="middle">Sample Image</text>
<!-- Segmentation Results -->
<rect x="460" y="120" width="200" height="150" fill="#f7fafc" stroke="#cbd5e0"/>
<circle cx="500" cy="150" r="8" fill="#4299e1"/>
<circle cx="520" cy="170" r="6" fill="#48bb78"/>
<circle cx="540" cy="160" r="7" fill="#ed8936"/>
<circle cx="560" cy="180" r="5" fill="#9f7aea"/>
<circle cx="580" cy="155" r="9" fill="#38b2ac"/>
<circle cx="600" cy="175" r="6" fill="#f56565"/>
<circle cx="620" cy="165" r="8" fill="#4299e1"/>
<circle cx="640" cy="185" r="7" fill="#48bb78"/>
<text x="560" y="200" fill="#718096" font-family="Inter" font-size="12" text-anchor="middle">AI Segmentation</text>
<!-- Results Panel -->
<rect x="240" y="280" width="420" height="50" fill="#f7fafc" stroke="#e2e8f0"/>
<text x="250" y="295" fill="#2d3748" font-family="Inter" font-size="10" font-weight="600">Analysis Results:</text>
<text x="250" y="310" fill="#4a5568" font-family="Inter" font-size="9">Objects Detected: 127 | Accuracy: 94.2% | Processing Time: 2.3s</text>
<text x="250" y="320" fill="#4a5568" font-family="Inter" font-size="9">Classification: Mineral (45%), Matrix (35%), Pore (20%)</text>
<!-- AI Indicators -->
<circle cx="680" cy="130" r="15" fill="#4299e1"/>
<text x="680" y="135" fill="white" font-family="Inter" font-size="10" text-anchor="middle" font-weight="bold">AI</text>
<circle cx="710" cy="130" r="15" fill="#48bb78"/>
<text x="710" y="135" fill="white" font-family="Inter" font-size="8" text-anchor="middle" font-weight="bold">ML</text>
</svg>
</div>
</div>
</div>
</section>

<!-- Software Introduction Section -->
<section class="software-intro" id="software-intro">
<div class="container">
<div class="section-header">
<h2>Professional Image Analysis Platform</h2>
<p>Combining cutting-edge AI frameworks with an intuitive interface for researchers across multiple scientific domains</p>
</div>
<div class="intro-content">
<div class="intro-text">
<p>VisionLab AI represents the next generation of computer vision software, designed specifically for scientific research and professional image analysis. Our platform integrates state-of-the-art AI frameworks including Detectron2, SAM (Segment Anything Model), and XGBoost to deliver unprecedented accuracy and efficiency in image segmentation and analysis.</p>
<p>Whether you're working in geosciences, material sciences, biology, or any field requiring precise image analysis, VisionLab AI provides the tools and intelligence you need to accelerate your research and achieve reliable, reproducible results.</p>
</div>
<div class="comprehensive-image">
<img src="images/interface/projecthub_interface.png" alt="Comprehensive VisionLab AI Interface Overview" class="interface-overview-img">
<div class="image-caption">
<p>Comprehensive VisionLab AI Interface Overview</p>
<small>Complete software interface showcasing all modules and features</small>
</div>
</div>
<div class="feature-highlights">
<div class="highlight-card">
<i class="fas fa-brain"></i>
<h4>AI-Powered Intelligence</h4>
<p>Advanced machine learning algorithms for automatic object detection and classification</p>
</div>
<div class="highlight-card">
<i class="fas fa-microscope"></i>
<h4>Multi-Domain Support</h4>
<p>Optimized for geosciences, material sciences, biology, and beyond</p>
</div>
<div class="highlight-card">
<i class="fas fa-user-friendly"></i>
<h4>No-Code Interface</h4>
<p>Professional-grade analysis without programming requirements</p>
</div>
<div class="highlight-card">
<i class="fas fa-chart-line"></i>
<h4>Comprehensive Analytics</h4>
<p>Detailed statistics, reports, and visualization tools</p>
</div>
</div>
</div>
</div>
</section>

<!-- Core Features Section -->
<section class="core-features" id="core-features">
<div class="container">
<div class="section-header">
<h2>Core Analysis Modules</h2>
<p>Eight specialized modules designed for comprehensive image analysis workflows</p>
</div>
<div class="features-grid">
<div class="feature-card">
<div class="card-icon">
<i class="fas fa-magic"></i>
</div>
<h3>Unsupervised Segmentation</h3>
<p>Hybrid ML approach for automatic object identification and classification without manual training data</p>
<ul class="feature-list">
<li>Automatic object detection</li>
<li>Intelligent classification</li>
<li>No training data required</li>
<li>Real-time processing</li>
</ul>
<img src="images/interface/unsupervised_interface.png" alt="Unsupervised Segmentation Interface" class="interface-overview-img">
<div class="image-caption">
    Detailed interface screenshot with annotations explaining each component
</div>
</div>
<div class="feature-card">
<div class="card-icon">
<i class="fas fa-draw-polygon"></i>
</div>
<h3>Advanced Segmentation</h3>
<p>Professional annotation tools and deep learning model training for custom applications</p>
<ul class="feature-list">
<li>Professional annotation tools</li>
<li>Custom model training</li>
<li>Detectron2 integration</li>
<li>Export capabilities</li>
</ul>
</div>
<div class="feature-card">
<div class="card-icon">
<i class="fas fa-brain"></i>
</div>
<h3>Trainable Segmentation</h3>
<p>XGBoost-powered custom segmentation with interactive labeling and machine learning</p>
<ul class="feature-list">
<li>Interactive labeling</li>
<li>XGBoost algorithms</li>
<li>Custom feature extraction</li>
<li>Adaptive learning</li>
</ul>
<img src="images/interface/trainable_interface.png" alt="Trainable Segmentation Interface" class="interface-overview-img">
<div class="image-caption">
    Detailed interface screenshot with annotations explaining each component
</div>
</div>
</div>
</div>
</section>

<!-- Interface Sections for Each Functionality -->
<section class="interface-sections" id="interface-sections">
<div class="container">
<div class="section-header">
<h2>VisionLab AI Interface Guide</h2>
<p>Detailed interface documentation for each module with comprehensive explanations</p>
</div>

<!-- Unsupervised Segmentation Interface -->
<div class="interface-module" id="unsupervised-segmentation">
<div class="module-header">
<h3><i class="fas fa-magic"></i> Unsupervised Segmentation Interface</h3>
<p>Explore the hybrid ML approach interface for automatic object identification</p>
</div>
<div class="interface-content">
<div class="interface-image">
<div class="image-placeholder">
<i class="fas fa-image"></i>
<p>Unsupervised Segmentation Interface</p>
<small>Detailed interface screenshot with annotations explaining each component</small>
</div>
</div>
<div class="interface-description">
<h4>Key Interface Components:</h4>
<ul>
<li><strong>Image Viewer:</strong> Main canvas for image display and interaction</li>
<li><strong>Segmentation Controls:</strong> Algorithm selection and parameter tuning</li>
<li><strong>Results Panel:</strong> Real-time segmentation results and statistics</li>
<li><strong>Export Options:</strong> Multiple format export capabilities</li>
</ul>
</div>
</div>
</div>

<!-- Advanced Segmentation Interface -->
<div class="interface-module" id="advanced-segmentation">
<div class="module-header">
<h3><i class="fas fa-draw-polygon"></i> Advanced Segmentation Interface</h3>
<p>Professional annotation tools and deep learning model training interface</p>
</div>
<div class="interface-content">
<div class="interface-image">
<div class="image-placeholder">
<i class="fas fa-image"></i>
<p>Advanced Segmentation Interface</p>
<small>Professional annotation tools with Detectron2 integration</small>
</div>
</div>
<div class="interface-description">
<h4>Key Interface Components:</h4>
<ul>
<li><strong>Annotation Toolbar:</strong> Professional drawing and editing tools</li>
<li><strong>Model Training Panel:</strong> Detectron2 configuration and training controls</li>
<li><strong>Dataset Manager:</strong> Training data organization and validation</li>
<li><strong>Performance Metrics:</strong> Real-time training progress and accuracy</li>
</ul>
</div>
</div>
</div>

<!-- Trainable Segmentation Interface -->
<div class="interface-module" id="trainable-segmentation">
<div class="module-header">
<h3><i class="fas fa-brain"></i> Trainable Segmentation Interface</h3>
<p>XGBoost-powered custom segmentation with interactive labeling</p>
</div>
<div class="interface-content">
<div class="interface-image">
<div class="image-placeholder">
<i class="fas fa-image"></i>
<p>Trainable Segmentation Interface</p>
<small>Interactive labeling interface with XGBoost integration</small>
</div>
</div>
<div class="interface-description">
<h4>Key Interface Components:</h4>
<ul>
<li><strong>Interactive Labeling:</strong> Click-based training data creation</li>
<li><strong>Feature Extraction:</strong> Customizable feature selection panel</li>
<li><strong>XGBoost Controls:</strong> Model parameters and training options</li>
<li><strong>Prediction Overlay:</strong> Real-time segmentation preview</li>
</ul>
</div>
</div>
</div>

<!-- Point Counting Interface -->
<div class="interface-module" id="point-counting">
<div class="module-header">
<h3><i class="fas fa-crosshairs"></i> Point Counting Interface</h3>
<p>Modal analysis and percentage calculations with precision tools</p>
</div>
<div class="interface-content">
<div class="interface-image">
<div class="image-placeholder">
<i class="fas fa-image"></i>
<p>Point Counting Interface</p>
<small>Precision point counting tools with statistical analysis</small>
</div>
</div>
<div class="interface-description">
<h4>Key Interface Components:</h4>
<ul>
<li><strong>Point Grid Overlay:</strong> Customizable grid patterns and spacing</li>
<li><strong>Classification Panel:</strong> Category assignment and color coding</li>
<li><strong>Statistics Dashboard:</strong> Real-time percentage calculations</li>
<li><strong>Export Tools:</strong> Data export in multiple formats</li>
</ul>
</div>
</div>
</div>

<!-- Grain Analysis Interface -->
<div class="interface-module" id="grain-analysis">
<div class="module-header">
<h3><i class="fas fa-chart-bar"></i> Grain Analysis Interface</h3>
<p>Size and shape statistics with AI-powered measurements</p>
</div>
<div class="interface-content">
<div class="interface-image">
<div class="image-placeholder">
<i class="fas fa-image"></i>
<p>Grain Analysis Interface</p>
<small>Comprehensive grain measurement and analysis tools</small>
</div>
</div>
<div class="interface-description">
<h4>Key Interface Components:</h4>
<ul>
<li><strong>Measurement Tools:</strong> Automated size and shape analysis</li>
<li><strong>Statistical Panel:</strong> Distribution plots and summary statistics</li>
<li><strong>Filter Controls:</strong> Size and shape filtering options</li>
<li><strong>Report Generator:</strong> Comprehensive analysis reports</li>
</ul>
</div>
</div>
</div>

<!-- Batch Processing Interface -->
<div class="interface-module" id="batch-processing">
<div class="module-header">
<h3><i class="fas fa-layer-group"></i> Batch Processing Interface</h3>
<p>Automated workflows for large dataset processing</p>
</div>
<div class="interface-content">
<div class="interface-image">
<div class="image-placeholder">
<i class="fas fa-image"></i>
<p>Batch Processing Interface</p>
<small>Workflow automation and progress monitoring</small>
</div>
</div>
<div class="interface-description">
<h4>Key Interface Components:</h4>
<ul>
<li><strong>Workflow Builder:</strong> Drag-and-drop workflow creation</li>
<li><strong>Progress Monitor:</strong> Real-time processing status</li>
<li><strong>Queue Manager:</strong> Job scheduling and prioritization</li>
<li><strong>Results Aggregator:</strong> Batch result compilation</li>
</ul>
</div>
</div>
</div>

<!-- Image Lab Interface -->
<div class="interface-module" id="image-lab">
<div class="module-header">
<h3><i class="fas fa-image"></i> Image Lab Interface</h3>
<p>Comprehensive image processing and enhancement tools</p>
</div>
<div class="interface-content">
<div class="interface-image">
<div class="image-placeholder">
<i class="fas fa-image"></i>
<p>Image Lab Interface</p>
<small>Professional image processing and enhancement suite</small>
</div>
</div>
<div class="interface-description">
<h4>Key Interface Components:</h4>
<ul>
<li><strong>Processing Toolbar:</strong> Filters, adjustments, and enhancements</li>
<li><strong>Histogram Panel:</strong> Real-time image statistics</li>
<li><strong>Layer Manager:</strong> Multi-layer editing capabilities</li>
<li><strong>Template System:</strong> Saved processing workflows</li>
</ul>
</div>
</div>
</div>

<!-- AI Assistant Interface -->
<div class="interface-module" id="ai-assistant">
<div class="module-header">
<h3><i class="fas fa-robot"></i> AI Assistant Interface</h3>
<p>Gemini VLLM-powered guidance and intelligent support</p>
</div>
<div class="interface-content">
<div class="interface-image">
<div class="image-placeholder">
<i class="fas fa-image"></i>
<p>AI Assistant Interface</p>
<small>Intelligent chat interface with contextual guidance</small>
</div>
</div>
<div class="interface-description">
<h4>Key Interface Components:</h4>
<ul>
<li><strong>Chat Interface:</strong> Natural language interaction</li>
<li><strong>Context Panel:</strong> Current workflow awareness</li>
<li><strong>Suggestion Engine:</strong> Intelligent recommendations</li>
<li><strong>Help Integration:</strong> Contextual documentation</li>
</ul>
</div>
</div>
</div>
</div>
</section>

<!-- Quick Start Section -->
<section class="quick-start" id="quick-start">
<div class="container">
<div class="section-header">
<h2>Quick Start Guide</h2>
<p>Get up and running with VisionLab AI in 5 simple steps</p>
</div>
<div class="quick-start-content">
<div class="quick-start-intro">
<p>Our comprehensive workflow guides you from project creation to results export. Follow these steps to begin your image analysis journey with VisionLab AI.</p>
</div>
<div class="quick-start-steps">
<div class="step-item">
<div class="step-number">1</div>
<div class="step-content">
<h4>Create a Project</h4>
<p>Go to Project Hub → Click "New Project" → Set up your workspace with custom settings and preferences</p>
</div>
</div>
<div class="step-item">
<div class="step-number">2</div>
<div class="step-content">
<h4>Import Images</h4>
<p>Upload JPG, PNG, TIFF, BMP files → Organize in project folder → Preview and validate your dataset</p>
</div>
</div>
<div class="step-item">
<div class="step-number">3</div>
<div class="step-content">
<h4>Choose Analysis Type</h4>
<p>Select from 8 specialized modules based on your research needs → Configure analysis parameters</p>
</div>
</div>
<div class="step-item">
<div class="step-number">4</div>
<div class="step-content">
<h4>Process Your Data</h4>
<p>Follow module workflows → Use AI Assistant for guidance → Monitor progress and adjust settings</p>
</div>
</div>
<div class="step-item">
<div class="step-number">5</div>
<div class="step-content">
<h4>Export Results</h4>
<p>Generate comprehensive reports → Export data in multiple formats → Set up batch processing for efficiency</p>
</div>
</div>
</div>
<div class="pro-tip">
<i class="fas fa-lightbulb"></i>
<strong>Pro Tip:</strong> Start with the AI Assistant for personalized guidance and workflow recommendations!
</div>
</div>
</div>
</section>
<!-- Comprehensive Tutorials Section -->
<section class="tutorials-section" id="tutorials">
<div class="container">
<div class="section-header">
<h2><i class="fas fa-graduation-cap"></i> Comprehensive Tutorials</h2>
<p>Master VisionLab AI with our detailed tutorials covering all analysis modules and advanced workflows</p>
</div>

<div class="tutorial-categories">
<!-- Getting Started Tutorials -->
<div class="tutorial-category">
<h3>Getting Started</h3>
<div class="tutorial-grid">
<div class="tutorial-card">
<div class="tutorial-thumbnail">
<i class="fas fa-play-circle"></i>
<span class="duration">15 min</span>
</div>
<div class="tutorial-content">
<h4>VisionLab AI Overview</h4>
<p>Complete introduction to the software interface and core concepts</p>
<div class="tutorial-links">
<a href="#" class="btn btn-small"><i class="fab fa-youtube"></i> Watch Video</a>
<a href="#" class="btn btn-small btn-outline"><i class="fas fa-file-pdf"></i> PDF Guide</a>
</div>
</div>
</div>
<div class="tutorial-card">
<div class="tutorial-thumbnail">
<i class="fas fa-play-circle"></i>
<span class="duration">10 min</span>
</div>
<div class="tutorial-content">
<h4>Project Setup & Image Import</h4>
<p>Learn how to create projects and import your image datasets</p>
<div class="tutorial-links">
<a href="#" class="btn btn-small"><i class="fab fa-youtube"></i> Watch Video</a>
<a href="#" class="btn btn-small btn-outline"><i class="fas fa-file-pdf"></i> PDF Guide</a>
</div>
</div>
</div>
</div>
</div>

<!-- Module-Specific Tutorials -->
<div class="tutorial-category">
<h3>Analysis Modules</h3>
<div class="tutorial-grid">
<div class="tutorial-card">
<div class="tutorial-thumbnail">
<i class="fas fa-play-circle"></i>
<span class="duration">25 min</span>
</div>
<div class="tutorial-content">
<h4>Unsupervised Segmentation</h4>
<p>Master the hybrid ML approach for automatic object identification</p>
<div class="tutorial-links">
<a href="#" class="btn btn-small"><i class="fab fa-youtube"></i> Watch Video</a>
<a href="#" class="btn btn-small btn-outline"><i class="fas fa-file-pdf"></i> PDF Guide</a>
</div>
</div>
</div>
<div class="tutorial-card">
<div class="tutorial-thumbnail">
<i class="fas fa-play-circle"></i>
<span class="duration">35 min</span>
</div>
<div class="tutorial-content">
<h4>Advanced Segmentation</h4>
<p>Professional annotation tools and Detectron2 model training</p>
<div class="tutorial-links">
<a href="#" class="btn btn-small"><i class="fab fa-youtube"></i> Watch Video</a>
<a href="#" class="btn btn-small btn-outline"><i class="fas fa-file-pdf"></i> PDF Guide</a>
</div>
</div>
</div>
<div class="tutorial-card">
<div class="tutorial-thumbnail">
<i class="fas fa-play-circle"></i>
<span class="duration">20 min</span>
</div>
<div class="tutorial-content">
<h4>Trainable Segmentation</h4>
<p>XGBoost-powered custom segmentation with interactive labeling</p>
<div class="tutorial-links">
<a href="#" class="btn btn-small"><i class="fab fa-youtube"></i> Watch Video</a>
<a href="#" class="btn btn-small btn-outline"><i class="fas fa-file-pdf"></i> PDF Guide</a>
</div>
</div>
</div>
<div class="tutorial-card">
<div class="tutorial-thumbnail">
<i class="fas fa-play-circle"></i>
<span class="duration">18 min</span>
</div>
<div class="tutorial-content">
<h4>Point Counting</h4>
<p>Modal analysis and percentage calculations with precision tools</p>
<div class="tutorial-links">
<a href="#" class="btn btn-small"><i class="fab fa-youtube"></i> Watch Video</a>
<a href="#" class="btn btn-small btn-outline"><i class="fas fa-file-pdf"></i> PDF Guide</a>
</div>
</div>
</div>
<div class="tutorial-card">
<div class="tutorial-thumbnail">
<i class="fas fa-play-circle"></i>
<span class="duration">22 min</span>
</div>
<div class="tutorial-content">
<h4>Grain Analysis</h4>
<p>Size and shape statistics with AI-powered measurements</p>
<div class="tutorial-links">
<a href="#" class="btn btn-small"><i class="fab fa-youtube"></i> Watch Video</a>
<a href="#" class="btn btn-small btn-outline"><i class="fas fa-file-pdf"></i> PDF Guide</a>
</div>
</div>
</div>
<div class="tutorial-card">
<div class="tutorial-thumbnail">
<i class="fas fa-play-circle"></i>
<span class="duration">30 min</span>
</div>
<div class="tutorial-content">
<h4>Batch Processing</h4>
<p>Automated workflows for large dataset processing</p>
<div class="tutorial-links">
<a href="#" class="btn btn-small"><i class="fab fa-youtube"></i> Watch Video</a>
<a href="#" class="btn btn-small btn-outline"><i class="fas fa-file-pdf"></i> PDF Guide</a>
</div>
</div>
</div>
<div class="tutorial-card">
<div class="tutorial-thumbnail">
<i class="fas fa-play-circle"></i>
<span class="duration">28 min</span>
</div>
<div class="tutorial-content">
<h4>Image Lab</h4>
<p>Comprehensive image processing and enhancement tools</p>
<div class="tutorial-links">
<a href="#" class="btn btn-small"><i class="fab fa-youtube"></i> Watch Video</a>
<a href="#" class="btn btn-small btn-outline"><i class="fas fa-file-pdf"></i> PDF Guide</a>
</div>
</div>
</div>
<div class="tutorial-card">
<div class="tutorial-thumbnail">
<i class="fas fa-play-circle"></i>
<span class="duration">12 min</span>
</div>
<div class="tutorial-content">
<h4>AI Assistant</h4>
<p>Gemini VLLM-powered guidance and intelligent support</p>
<div class="tutorial-links">
<a href="#" class="btn btn-small"><i class="fab fa-youtube"></i> Watch Video</a>
<a href="#" class="btn btn-small btn-outline"><i class="fas fa-file-pdf"></i> PDF Guide</a>
</div>
</div>
</div>
</div>
</div>

<!-- Advanced Workflows -->
<div class="tutorial-category">
<h3>Advanced Workflows</h3>
<div class="tutorial-grid">
<div class="tutorial-card">
<div class="tutorial-thumbnail">
<i class="fas fa-play-circle"></i>
<span class="duration">45 min</span>
</div>
<div class="tutorial-content">
<h4>Custom Model Training</h4>
<p>End-to-end guide for training custom segmentation models</p>
<div class="tutorial-links">
<a href="#" class="btn btn-small"><i class="fab fa-youtube"></i> Watch Video</a>
<a href="#" class="btn btn-small btn-outline"><i class="fas fa-file-pdf"></i> PDF Guide</a>
</div>
</div>
</div>
<div class="tutorial-card">
<div class="tutorial-thumbnail">
<i class="fas fa-play-circle"></i>
<span class="duration">40 min</span>
</div>
<div class="tutorial-content">
<h4>Multi-Modal Analysis</h4>
<p>Combining multiple analysis modules for comprehensive results</p>
<div class="tutorial-links">
<a href="#" class="btn btn-small"><i class="fab fa-youtube"></i> Watch Video</a>
<a href="#" class="btn btn-small btn-outline"><i class="fas fa-file-pdf"></i> PDF Guide</a>
</div>
</div>
</div>
<div class="tutorial-card">
<div class="tutorial-thumbnail">
<i class="fas fa-play-circle"></i>
<span class="duration">35 min</span>
</div>
<div class="tutorial-content">
<h4>Report Generation & Export</h4>
<p>Creating professional reports and exporting results</p>
<div class="tutorial-links">
<a href="#" class="btn btn-small"><i class="fab fa-youtube"></i> Watch Video</a>
<a href="#" class="btn btn-small btn-outline"><i class="fas fa-file-pdf"></i> PDF Guide</a>
</div>
</div>
</div>
</div>
</div>

<!-- Tutorial Resources -->
<div class="tutorial-resources">
<h3>Additional Resources</h3>
<div class="resource-grid">
<div class="resource-card">
<i class="fas fa-book"></i>
<h4>Complete Documentation</h4>
<p>Comprehensive user manual with detailed explanations</p>
<a href="#" class="btn btn-small">Access Docs</a>
</div>
<div class="resource-card">
<i class="fas fa-download"></i>
<h4>Sample Datasets</h4>
<p>Practice datasets for all tutorial exercises</p>
<a href="#" class="btn btn-small">Download</a>
</div>
<div class="resource-card">
<i class="fas fa-users"></i>
<h4>Community Forum</h4>
<p>Connect with other users and get support</p>
<a href="#" class="btn btn-small">Join Forum</a>
</div>
<div class="resource-card">
<i class="fas fa-question-circle"></i>
<h4>FAQ & Troubleshooting</h4>
<p>Common questions and solutions</p>
<a href="#" class="btn btn-small">View FAQ</a>
</div>
</div>
</div>
</div>
</div>
</section>
<!-- Download Section -->
<section class="download-section" id="download">
<div class="container">
<div class="section-header">
<h2><i class="fas fa-download"></i> Download VisionLab AI</h2>
<p>Get the latest version of VisionLab AI - Professional image analysis and segmentation software with advanced AI capabilities for scientific research</p>
</div>

<div class="download-content">
<div class="download-main">
<div class="version-info">
<h3>Current Version: v4.0.0-beta</h3>
<p class="release-date">Released: December 2024</p>
<p class="release-notes">Latest updates include enhanced AI models, improved performance, and new batch processing capabilities.</p>
</div>

<div class="download-options">
<div class="download-card primary">
<div class="platform-icon">
<i class="fab fa-windows"></i>
</div>
<div class="platform-info">
<h4>Windows 10/11</h4>
<p>Full-featured version with all modules</p>
<div class="download-details">
<span class="file-size">~2.1 GB</span>
<span class="file-type">.exe installer</span>
</div>
</div>
<div class="download-actions">
<a class="btn btn-primary" href="waitlist.html">Join Beta</a>
<a class="btn btn-outline" href="#">Release Notes</a>
</div>
</div>

<div class="download-card">
<div class="platform-icon">
<i class="fab fa-apple"></i>
</div>
<div class="platform-info">
<h4>macOS</h4>
<p>Coming Soon - Q1 2025</p>
<div class="download-details">
<span class="status">In Development</span>
</div>
</div>
<div class="download-actions">
<a class="btn btn-secondary" href="waitlist.html">Join Beta</a>
<a class="btn btn-outline" href="#">Get Notified</a>
</div>
</div>

<div class="download-card">
<div class="platform-icon">
<i class="fab fa-linux"></i>
</div>
<div class="platform-info">
<h4>Linux</h4>
<p>Coming Soon - Q2 2025</p>
<div class="download-details">
<span class="status">Planned</span>
</div>
</div>
<div class="download-actions">
<a class="btn btn-secondary" href="waitlist.html">Join Beta</a>
<a class="btn btn-outline" href="#">Get Notified</a>
</div>
</div>
</div>
</div>

<div class="download-sidebar">
<div class="system-requirements">
<h4><i class="fas fa-desktop"></i> System Requirements</h4>
<div class="requirements-tabs">
<div class="tab-content active">
<h5>Minimum Requirements</h5>
<ul>
<li><strong>OS:</strong> Windows 10 (64-bit)</li>
<li><strong>RAM:</strong> 8GB</li>
<li><strong>Storage:</strong> 5GB free space</li>
<li><strong>GPU:</strong> DirectX 11 compatible</li>
<li><strong>CPU:</strong> Intel i5 or AMD equivalent</li>
</ul>
</div>
<div class="tab-content">
<h5>Recommended Requirements</h5>
<ul>
<li><strong>OS:</strong> Windows 11 (64-bit)</li>
<li><strong>RAM:</strong> 16GB or higher</li>
<li><strong>Storage:</strong> 10GB free space (SSD)</li>
<li><strong>GPU:</strong> NVIDIA GPU with CUDA support</li>
<li><strong>CPU:</strong> Intel i7 or AMD Ryzen 7</li>
</ul>
</div>
</div>
</div>

<div class="installation-guide">
<h4><i class="fas fa-cog"></i> Installation Guide</h4>
<ol>
<li>Download the installer from above</li>
<li>Run the .exe file as administrator</li>
<li>Follow the installation wizard</li>
<li>Launch VisionLab AI from desktop</li>
<li>Complete the initial setup</li>
</ol>
<a href="#" class="btn btn-small">Detailed Guide</a>
</div>

<div class="support-info">
<h4><i class="fas fa-life-ring"></i> Need Help?</h4>
<ul>
<li><a href="#">Installation Troubleshooting</a></li>
<li><a href="#">System Compatibility Check</a></li>
<li><a href="#">Contact Support</a></li>
<li><a href="#">Community Forum</a></li>
</ul>
</div>

<div class="license-info">
<h4><i class="fas fa-certificate"></i> License Information</h4>
<p>VisionLab AI is available under a commercial license for research and educational use.</p>
<a href="#" class="btn btn-small">View License</a>
</div>
</div>
</div>
</div>
</section><footer class="footer">
<div class="container">
<div class="footer-content">
<div class="footer-section">
<h3>Vision Lab</h3>
<p>Making advanced computer vision accessible to everyone through innovative no-code solutions.</p>
<div class="social-links">
<a aria-label="Twitter" href="#"><i class="fab fa-twitter"></i></a>
<a aria-label="LinkedIn" href="#"><i class="fab fa-linkedin"></i></a>
<a aria-label="GitHub" href="#"><i class="fab fa-github"></i></a>
<a aria-label="YouTube" href="#"><i class="fab fa-youtube"></i></a>
</div>
</div>
<div class="footer-section">
<h4>Software</h4>
<ul>
<li><a href="software.html">About Vision Lab</a></li>
<li><a href="software.html">Quick Starts</a></li>
<li><a href="software.html">Tutorials</a></li>
<li><a href="software.html">Download</a></li>
</ul>
</div>
<div class="footer-section">
<h4>Company</h4>
<ul>
<li><a href="services.html">Services</a></li>
<li><a href="publications.html">Publications</a></li>
<li><a href="news.html">News</a></li>
<li><a href="#">About Us</a></li>
</ul>
</div>
<div class="footer-section">
<h4>Support</h4>
<ul>
<li><a href="#">Documentation</a></li>
<li><a href="#">Community</a></li>
<li><a href="#">Contact</a></li>
<li><a href="#">Help Center</a></li>
</ul>
</div>
</div>
<div class="footer-bottom">
<p>© 2024 Vision Lab. All rights reserved.</p>
<div class="footer-links">
<a href="#">Privacy Policy</a>
<a href="#">Terms of Service</a>
<a href="#">Cookie Policy</a>
</div>
</div>
</div>
</footer>
</body>
</html>