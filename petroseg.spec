# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_all

datas = [('weights', 'weights'), ('src/gui/styles', 'src/gui/styles'), ('src/gui/icons', 'src/gui/icons'), ('src/grainsight_components/models', 'src/grainsight_components/models'), ('src/grainsight_components/weights', 'src/grainsight_components/weights'), ('src/detectron2', 'detectron2')]
binaries = []
hiddenimports = ['PySide6.QtCore', 'PySide6.QtGui', 'PySide6.QtWidgets', 'numpy', 'PIL', 'cv2', 'torch', 'torchvision', 'yaml', 'ultralytics', 'mobile_sam', 'xgboost', 'torch._C', 'torch.utils', 'torch.utils.data', 'torch.nn', 'torch.optim', 'torch._numpy', 'torch._numpy._ufuncs', 'torch._dynamo', 'torch._dynamo.utils', 'torch._dynamo.convert_frame', 'detectron2', 'fvcore']
tmp_ret = collect_all('xgboost')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]


a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='VisionLab Ai',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='VisionLab Ai',
)
