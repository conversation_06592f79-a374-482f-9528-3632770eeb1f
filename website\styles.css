/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #171726;
    overflow-x: hidden;
}

html {
    scroll-behavior: smooth;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: #446493;
    gap: 12px;
}

.nav-logo .logo {
    height: 40px;
    width: auto;
    object-fit: contain;
}

.nav-logo h2 {
    color: #446493;
    font-weight: 700;
    font-size: 1.5rem;
    margin: 0;
}

.nav-menu {
    display: flex;
    list-style: none;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #171726;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #446493;
}

.nav-link.btn-primary {
    background: #446493;
    color: white;
    padding: 8px 20px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.nav-link.btn-primary:hover {
    background: #51C8E5;
    color: white;
    transform: translateY(-1px);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #171726;
    margin: 3px 0;
    transition: 0.3s;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    text-align: center;
}

.btn-primary {
    background: #446493;
    color: white;
}

.btn-primary:hover {
    background: #51C8E5;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(68, 100, 147, 0.3);
}

.btn-secondary {
    background: transparent;
    color: #446493;
    border: 2px solid #446493;
}

.btn-secondary:hover {
    background: #446493;
    color: white;
    transform: translateY(-2px);
}

.btn-small {
    padding: 8px 16px;
    font-size: 0.9rem;
}

.btn-full {
    width: 100%;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #51C8E5 0%, #446493 100%);
    color: white;
    padding: 120px 0 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-logo {
    margin-bottom: 2rem;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-logo-img {
    height: 120px;
    width: auto;
    object-fit: contain;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
    animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
    0%, 100% {
        transform: translateY(0px);
    }
}

/* Card Styles */
.card-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    text-align: center;
    padding: 10px;
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.card-image {
    width: 100%;
    height: 150px;
    object-fit: cover;
    margin-bottom: 5px;
    border-radius: 8px 8px 0 0;
}

.card h3 {
    color: #446493;
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.card p {
    color: #171726;
    font-size: 0.85rem;
    line-height: 1.2;
    margin-bottom: 0;
}
    50% {
        transform: translateY(-10px);
    }
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-visual {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.hero-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    transition: transform 0.3s ease;
}

.hero-card:hover {
    transform: translateY(-5px);
}

.hero-card i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #51C8E5;
}

.hero-card h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #171726;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    color: #C4C6C6;
    max-width: 600px;
    margin: 0 auto;
}

/* Software Section */
.software {
    padding: 80px 0;
    background: #f8f9fa;
}

.software-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.software-card {
    background: white;
    padding: 2.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid #C4C6C6;
}

.software-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.card-icon {
    width: 60px;
    height: 60px;
    background: #446493;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.card-icon i {
    font-size: 1.5rem;
    color: white;
}

.software-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #171726;
}

.software-card p {
    color: #C4C6C6;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.feature-list {
    list-style: none;
    margin-bottom: 1.5rem;
}

.feature-list li {
    padding: 0.5rem 0;
    color: #171726;
    position: relative;
    padding-left: 1.5rem;
}

.feature-list li:before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #51C8E5;
    font-weight: bold;
}

.quick-start-steps {
    margin-top: 20px;
}

.step-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #446493;
    transition: all 0.3s ease;
}

.step-item:hover {
    transform: translateX(5px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.step-number {
    width: 30px;
    height: 30px;
    background: #446493;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    flex-shrink: 0;
}

.step-content h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
    font-weight: 600;
    color: #171726;
}

.step-content p {
    margin: 0;
    font-size: 14px;
    color: #C4C6C6;
    line-height: 1.4;
}

.pro-tip {
    margin-top: 20px;
    padding: 15px;
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    border-radius: 8px;
    border-left: 4px solid #0ea5e9;
    display: flex;
    align-items: center;
    gap: 10px;
}

.pro-tip i {
    color: #0ea5e9;
    font-size: 18px;
}

.pro-tip strong {
    color: #0c4a6e;
}

.tutorial-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.tutorial-item {
    text-align: center;
    padding: 15px;
    border-radius: 8px;
    background: white;
    border: 1px solid #C4C6C6;
    transition: all 0.3s ease;
}

.tutorial-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.tutorial-icon {
    width: 50px;
    height: 50px;
    background: #446493;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 10px;
    transition: all 0.3s ease;
}

.tutorial-item:hover .tutorial-icon {
    background: #51C8E5;
    transform: scale(1.1);
}

.tutorial-icon i {
    font-size: 20px;
    color: white;
}

.tutorial-item h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 600;
    color: #171726;
}

.tutorial-item p {
    margin: 0;
    font-size: 12px;
    color: #C4C6C6;
    line-height: 1.4;
}

.download-card {
    background: linear-gradient(135deg, #446493, #51C8E5);
    color: white;
}

.download-card .card-icon {
    background: rgba(255, 255, 255, 0.2);
}

.download-card h3,
.download-card p {
    color: white;
}

.download-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.download-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.download-item i {
    font-size: 1.5rem;
    margin-right: 1rem;
}

.download-item span {
    flex: 1;
    font-weight: 500;
}

.system-requirements {
    margin: 20px 0;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.system-requirements h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
}

.system-requirements ul {
    margin: 0;
    padding-left: 20px;
    list-style-type: disc;
}

.system-requirements li {
    font-size: 12px;
    margin-bottom: 5px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
}

.version-info {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 0;
}

/* Research Section */
.research {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
    overflow: hidden;
}

.research::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e2e8f0" stroke-width="0.5" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>') repeat;
    opacity: 0.5;
}

.research .container {
    position: relative;
    z-index: 1;
}

.research-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.research-card {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #C4C6C6;
    position: relative;
    overflow: hidden;
}

.research-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #446493, #51C8E5, #C4C6C6);
}

.research-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.research-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #446493, #51C8E5);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    position: relative;
}

.research-icon::after {
    content: '';
    position: absolute;
    inset: 2px;
    background: linear-gradient(135deg, #446493, #51C8E5);
    border-radius: 14px;
    z-index: -1;
}

.research-icon i {
    font-size: 28px;
    color: white;
    z-index: 1;
}

.research-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #171726;
    margin-bottom: 1rem;
}

.research-card p {
    color: #C4C6C6;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.research-metrics {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.metric {
    background: linear-gradient(135deg, #eff6ff, #dbeafe);
    color: #446493;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    border: 1px solid #C4C6C6;
}

.publications-section {
    margin-top: 4rem;
    padding: 2rem;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.publications-section h3 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #171726;
    margin-bottom: 2rem;
    text-align: center;
}

.publication-list {
    display: grid;
    gap: 1.5rem;
}

.publication-item {
    padding: 1.5rem;
    border-left: 4px solid #446493;
    background: #f8f9fa;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.publication-item:hover {
    background: #f8f9fa;
    transform: translateX(5px);
}

.publication-item h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #171726;
    margin-bottom: 0.5rem;
}

.publication-item p {
    color: #C4C6C6;
    font-style: italic;
    margin: 0;
}

/* Services Section */
.services {
    padding: 80px 0;
    background: white;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.service-card {
    padding: 2.5rem;
    border-radius: 12px;
    background: #f8f9fa;
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.service-card:hover {
    background: white;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.service-icon {
    width: 60px;
    height: 60px;
    background: #51C8E5;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.service-icon i {
    font-size: 1.5rem;
    color: white;
}

.service-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
}

.service-card p {
    color: #6b7280;
    line-height: 1.6;
}

/* Publications Section */
.publications {
    padding: 80px 0;
    background: #f9fafb;
}

.publications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.publication-card {
    background: white;
    padding: 2.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.publication-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.publication-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.publication-type {
    background: #446493;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.publication-date {
    color: #6b7280;
    font-size: 0.9rem;
}

.publication-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
    line-height: 1.4;
}

.publication-card p {
    color: #6b7280;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.publication-authors {
    color: #374151;
    font-style: italic;
    margin-bottom: 1rem;
}

.publication-link {
    color: #446493;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.publication-link:hover {
    color: #51C8E5;
}

.publication-link i {
    margin-left: 0.5rem;
    font-size: 0.8rem;
}

/* News Section */
.news {
    padding: 80px 0;
    background: white;
}

.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.news-card {
    background: #f9fafb;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.news-card.featured {
    grid-column: span 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    background: white;
}

.news-image {
    height: 200px;
    overflow: hidden;
}

.image-placeholder {
    width: 100%;
    height: 100%;
    background: #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-placeholder i {
    font-size: 3rem;
    color: #C4C6C6;
}

.interface-overview-img {
    width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transition: transform 0.3s ease;
}

.interface-overview-img:hover {
    transform: scale(1.02);
}

.image-caption {
    text-align: center;
    margin-top: 1rem;
}

.image-caption p {
    font-size: 1.1rem;
    font-weight: 600;
    color: #171726;
    margin-bottom: 0.5rem;
}

.image-caption small {
    color: #6b7280;
    font-size: 0.9rem;
}

.news-content {
    padding: 2rem;
}

.news-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.news-category {
    background: #10b981;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.news-date {
    color: #6b7280;
    font-size: 0.9rem;
}

.news-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
    line-height: 1.4;
}

.news-card p {
    color: #6b7280;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.news-link {
    color: #446493;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.news-link:hover {
    color: #1d4ed8;
}

/* Waitlist Section */
.waitlist {
    padding: 80px 0;
    background: linear-gradient(135deg, #171726 0%, #446493 100%);
    color: white;
}

.waitlist-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.waitlist-info h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: white;
}

.waitlist-info p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.waitlist-benefits {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.benefit-item i {
    color: #51C8E5;
    font-size: 1.2rem;
}

.waitlist-form-container {
    background: white;
    padding: 2.5rem;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.waitlist-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    color: #374151;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select {
    padding: 12px;
    border: 2px solid #C4C6C6;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #446493;
    box-shadow: 0 0 0 3px rgba(68, 100, 147, 0.1);
}

.checkbox-group {
    flex-direction: row;
    align-items: flex-start;
    gap: 0.75rem;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    cursor: pointer;
    font-size: 0.9rem;
    line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin: 0;
    cursor: pointer;
}

.checkbox-label a {
    color: #2563eb;
    text-decoration: none;
}

.checkbox-label a:hover {
    text-decoration: underline;
}

.btn-loading {
    display: none;
}

.success-message {
    text-align: center;
    padding: 2rem;
    color: #51C8E5;
}

.success-message i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.success-message h3 {
    color: #1f2937;
    margin-bottom: 1rem;
}

.success-message p {
    color: #6b7280;
}

/* Footer */
.footer {
    background: #171726;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-section h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: white;
}

.footer-section h4 {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    color: white;
}

.footer-section p {
    color: #9ca3af;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #9ca3af;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: white;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: #446493;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #446493;
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid #446493;
    padding-top: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-bottom p {
    color: #9ca3af;
    margin: 0;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: #9ca3af;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .waitlist-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .news-card.featured {
        grid-column: span 1;
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }
    
    .tutorial-grid {
        grid-template-columns: 1fr;
    }
    
    .software-grid,
    .services-grid,
    .publications-grid,
    .news-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .section-header h2 {
        font-size: 2rem;
    }
    
    .software-card,
    .service-card,
    .publication-card {
        padding: 1.5rem;
    }
    
    .waitlist-form-container {
        padding: 1.5rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 300px;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for better accessibility */
a:focus,
button:focus,
input:focus,
select:focus {
    outline: 2px solid #2563eb;
    outline-offset: 2px;
}

/* Smooth scrolling for all browsers */
@media (prefers-reduced-motion: no-preference) {
    html {
        scroll-behavior: smooth;
    }
}