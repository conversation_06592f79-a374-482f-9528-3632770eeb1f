# src/widgets/image_gallery.py

from PySide6.QtWidgets import QWidget, QHBoxLayout, QLabel, QScrollArea
from PySide6.QtGui import QPixmap
from PySide6.QtCore import Qt, Signal

class ImageGallery(QWidget):
    """A scrollable horizontal gallery for displaying multiple image thumbnails."""
    
    image_clicked = Signal(int)  # Signal emitted when an image is clicked, passes the image index
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.images = []  # List to store image data
        self.thumbnails = []  # List to store thumbnail widgets
        
    def setup_ui(self):
        """Sets up the gallery UI."""
        # Main layout
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(5)
        
        # Scroll area for thumbnails
        self.scroll_area = QScrollArea(self)
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.scroll_area.setMinimumHeight(100)  # Set minimum height for thumbnails
        
        # Container widget for thumbnails
        self.container = QWidget()
        self.container_layout = QHBoxLayout(self.container)
        self.container_layout.setContentsMargins(5, 5, 5, 5)
        self.container_layout.setSpacing(5)
        self.container_layout.addStretch()
        
        self.scroll_area.setWidget(self.container)
        self.layout.addWidget(self.scroll_area)
        
    def add_image(self, image, index):
        """Adds a new image thumbnail to the gallery.
        
        Args:
            image: The image data (numpy array or QPixmap)
            index: The index of the image in the collection
        """
        # Create thumbnail label
        thumbnail = QLabel()
        thumbnail.setFixedSize(80, 80)  # Fixed size for thumbnails
        thumbnail.setStyleSheet('border: 2px solid #ccc; border-radius: 4px;')
        thumbnail.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # Convert image to QPixmap if needed
        if not isinstance(image, QPixmap):
            pixmap = QPixmap.fromImage(image)
        else:
            pixmap = image
            
        # Scale the pixmap to fit the thumbnail size
        scaled_pixmap = pixmap.scaled(80, 80, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
        thumbnail.setPixmap(scaled_pixmap)
        
        # Make thumbnail clickable
        thumbnail.mousePressEvent = lambda e, idx=index: self.image_clicked.emit(idx)
        
        # Add thumbnail to container
        self.container_layout.insertWidget(self.container_layout.count() - 1, thumbnail)
        self.thumbnails.append(thumbnail)
        self.images.append(image)
        
    def clear(self):
        """Removes all thumbnails from the gallery."""
        for thumbnail in self.thumbnails:
            thumbnail.deleteLater()
        self.thumbnails.clear()
        self.images.clear()
        
    def set_selected(self, index):
        """Highlights the selected thumbnail.
        
        Args:
            index: The index of the thumbnail to highlight
        """
        for i, thumbnail in enumerate(self.thumbnails):
            if i == index:
                thumbnail.setStyleSheet('border: 2px solid #007bff; border-radius: 4px;')
            else:
                thumbnail.setStyleSheet('border: 2px solid #ccc; border-radius: 4px;')
                
    def update_image(self, image, index):
        """Updates an existing image thumbnail in the gallery.
        
        Args:
            image: The new image data (numpy array or QPixmap)
            index: The index of the image to update
        """
        if 0 <= index < len(self.thumbnails):
            # Convert image to QPixmap if needed
            if not isinstance(image, QPixmap):
                pixmap = QPixmap.fromImage(image)
            else:
                pixmap = image
                
            # Scale the pixmap to fit the thumbnail size
            scaled_pixmap = pixmap.scaled(80, 80, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
            self.thumbnails[index].setPixmap(scaled_pixmap)
            self.images[index] = image